exclude: '^docs/|/migrations/|devcontainer.json'
default_stages: [commit]

default_language_version:
  python: python3.12

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-yaml
      - id: debug-statements
      - id: check-builtin-literals
      - id: check-case-conflict
      - id: check-docstring-first
      - id: detect-private-key

  - repo: https://github.com/adamchainz/django-upgrade
    rev: '1.21.0'
    hooks:
      - id: django-upgrade
        args: ['--target-version', '5.0']

  # Run the Ruff linter.
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.6.7
    hooks:
      # Linter
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      # Formatter
      - id: ruff-format

  - repo: https://github.com/Riverside-Healthcare/djLint
    rev: v1.35.2
    hooks:
      - id: djlint-reformat-django
      - id: djlint-django

# sets up .pre-commit-ci.yaml to ensure pre-commit dependencies stay up to date
ci:
  autoupdate_schedule: weekly
  skip: []
  submodules: false
