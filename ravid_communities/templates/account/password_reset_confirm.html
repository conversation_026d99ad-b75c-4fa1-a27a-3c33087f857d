<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        h1 {
            color: #0f172a;
        }
        p {
            color: #777;
        }
        form {
            margin-top: 20px;
        }
        label {
            display: block;
            text-align: left;
            font-weight: bold;
            margin-bottom: 5px;
            color: #0f172a;
        }
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #333;
        }
        button {
            background-color: #0f172a;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .footer {
            margin-top: 20px;
            color: #888;
            font-size: 12px;
        }
        .footer a {
            color: #0f172a;
            text-decoration: none;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>Reset Your Password</h1>
        <p>Please enter your new password below to reset your account password.</p>
        <form method="post">
            {% csrf_token %}
            <label for="new_password">New Password</label>
            <input type="password" id="new_password" name="new_password" required placeholder="Enter new password">
            <input type="hidden" name="uidb64" value="{{ uidb64 }}">
            <input type="hidden" name="token" value="{{ token }}">
            <button type="submit">Reset Password</button>
        </form>
        <div class="footer">
            <p>If you did not request a password reset, please ignore this email or <a href="#">contact support</a> if you have questions.</p>
        </div>
    </div>

</body>
</html>
