<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{% block title %}R.A.V.I.D Notification{% endblock %}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .content {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .footer {
            text-align: center;
            font-size: 12px;
            color: #777;
            margin-top: 30px;
        }
        .button {
            background-color: #0f172a;
            border: none;
            color: white;
            padding: 15px 32px;
            border-radius: 12px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
        }
        .button-secondary {
            background-color: #6b7280;
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
        }
        .info-box {
            background-color: #e0f2fe;
            border-left: 4px solid #0284c7;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .warning-box {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .success-box {
            background-color: #dcfce7;
            border-left: 4px solid #16a34a;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .appointment-details {
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .appointment-details h3 {
            margin-top: 0;
            margin-bottom: 20px;
            color: #0f172a;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 15px;
            font-size: 18px;
        }
        .detail-row {
            padding: 15px 0;
            border-bottom: 1px solid #f3f4f6;
            margin-bottom: 8px;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .detail-label {
            font-weight: bold;
            color: #374151;
            {% comment %} display: block; {% endcomment %}
            margin-bottom: 5px;
        }
        .detail-value {
            color: #6b7280;
            {% comment %} display: block; {% endcomment %}
            padding-left: 5px;
        }
        {% block extra_styles %}{% endblock %}
    </style>
</head>
<body>
    <div class="header">
        <h1>{% block header_title %}R.A.V.I.D{% endblock %}</h1>
        {% block header_subtitle %}{% endblock %}
    </div>
    
    <div class="content">
        {% block greeting %}
        <p>Hello{% if user_name %} {{ user_name }}{% endif %},</p>
        {% endblock %}
        
        {% block main_content %}
        <p>This is a notification from R.A.V.I.D.</p>
        {% endblock %}
        
        {% block action_buttons %}{% endblock %}
    </div>
    
    {% block additional_content %}{% endblock %}
    
    <div class="footer">
        {% block footer_message %}
        <p>This is an automated message, please do not reply to this email.</p>
        {% endblock %}
        
        {% block footer_copyright %}
        <p>&copy; {% now "Y" %} R.A.V.I.D. All rights reserved.</p>
        {% endblock %}
        
        {% block footer_additional %}{% endblock %}
    </div>
</body>
</html>
