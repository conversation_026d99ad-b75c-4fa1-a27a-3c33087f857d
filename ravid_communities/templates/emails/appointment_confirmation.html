{% extends "emails/base_email.html" %}

{% block title %}Appointment Confirmation - R.A.V.I.D{% endblock %}

{% block header_title %}Appointment Confirmed{% endblock %}

{% block header_subtitle %}
<p style="color: #6b7280; font-size: 14px; margin: 5px 0;">Your appointment has been successfully scheduled</p>
{% endblock %}

{% block main_content %}
{% if patient_name %}
<p>You have confirmed an appointment with {{ patient_name }}. Please find the details below:</p>
{% else %}
<p>Your appointment with R.A.V.I.D has been confirmed. Please find the details below:</p>
{% endif %}

<div class="appointment-details">
    <h3>Appointment Details</h3>

    <div class="detail-row">
        <span class="detail-label">Date:</span>
        <span class="detail-value">{{ appointment_date|date:"F d, Y" }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Time:</span>
        <span class="detail-value">{{ appointment_time|time:"g:i A" }}</span>
    </div>

    {% if doctor_name %}
    <div class="detail-row">
        <span class="detail-label">Doctor:</span>
        <span class="detail-value">{{ doctor_name }}</span>
    </div>
    {% endif %}

    {% if patient_name %}
    <div class="detail-row">
        <span class="detail-label">Patient:</span>
        <span class="detail-value">{{ patient_name }}</span>
    </div>
    {% endif %}

    {% if appointment_type %}
    <div class="detail-row">
        <span class="detail-label">Type:</span>
        <span class="detail-value">{{ appointment_type }}</span>
    </div>
    {% endif %}

    {% if location %}
    <div class="detail-row">
        <span class="detail-label">Location:</span>
        <span class="detail-value">{{ location }}</span>
    </div>
    {% endif %}

    {% if meeting_link %}
    <div class="detail-row">
        <span class="detail-label">Video Call Link:</span>
        <span class="detail-value"><a href="{{ meeting_link }}" style="color: #0f172a;">Join Meeting</a></span>
    </div>
    {% endif %}

    {% if notes %}
    <div class="detail-row">
        <span class="detail-label">Notes:</span>
        <span class="detail-value">{{ notes }}</span>
    </div>
    {% endif %}
</div>

{% if patient_name %}
<div class="info-box">
    <strong>Doctor Reminder:</strong> Please review the patient's information and prepare any necessary materials for the appointment.
</div>
{% else %}
<div class="info-box">
    <strong>Important:</strong> Please arrive 15 minutes before your scheduled appointment time. If you need to reschedule or cancel, please contact us at least 24 hours in advance.
</div>
{% endif %}

{% if preparation_instructions %}
<div class="warning-box">
    <strong>Preparation Instructions:</strong><br>
    {{ preparation_instructions|linebreaks }}
</div>
{% endif %}
{% endblock %}

{% block action_buttons %}
{% if appointment_url %}
<a href="{{ appointment_url }}" class="button">View Appointment Details</a>
{% endif %}

{% if reschedule_url %}
<a href="{{ reschedule_url }}" class="button-secondary">Reschedule</a>
{% endif %}

{% if cancel_url %}
<a href="{{ cancel_url }}" class="button-secondary">Cancel Appointment</a>
{% endif %}
{% endblock %}

{% block footer_additional %}
<p style="font-size: 11px; color: #9ca3af;">
    If you have any questions, please contact our support team at 
    <a href="mailto:<EMAIL>" style="color: #0f172a;"><EMAIL></a>
</p>
{% endblock %}
