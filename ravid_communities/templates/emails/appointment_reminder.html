{% extends "emails/base_email.html" %}

{% block title %}Appointment Reminder - R.A.V.I.D{% endblock %}

{% block header_title %}Appointment Reminder{% endblock %}

{% block header_subtitle %}
<p style="color: #6b7280; font-size: 14px; margin: 5px 0;">Don't forget about your upcoming appointment</p>
{% endblock %}

{% block main_content %}
<p>This is a friendly reminder about your upcoming appointment with R.A.V.I.D.</p>

<div class="appointment-details">
    <h3>Appointment Details</h3>
    
    <div class="detail-row">
        <span class="detail-label">Date:</span>
        <span class="detail-value">{{ appointment_date|date:"F d, Y" }}</span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Time:</span>
        <span class="detail-value">{{ appointment_time|time:"g:i A" }}</span>
    </div>
    
    {% if doctor_name %}
    <div class="detail-row">
        <span class="detail-label">Doctor:</span>
        <span class="detail-value">{{ doctor_name }}</span>
    </div>
    {% endif %}
    
    {% if appointment_type %}
    <div class="detail-row">
        <span class="detail-label">Type:</span>
        <span class="detail-value">{{ appointment_type }}</span>
    </div>
    {% endif %}
    
    {% if location %}
    <div class="detail-row">
        <span class="detail-label">Location:</span>
        <span class="detail-value">{{ location }}</span>
    </div>
    {% endif %}
</div>

<div class="warning-box">
    <strong>Reminder:</strong> Your appointment is scheduled for {{ appointment_date|date:"F d, Y" }} at {{ appointment_time|time:"g:i A" }}. Please arrive 15 minutes early.
</div>

{% if preparation_instructions %}
<div class="info-box">
    <strong>Preparation Instructions:</strong><br>
    {{ preparation_instructions|linebreaks }}
</div>
{% endif %}
{% endblock %}

{% block action_buttons %}
{% if appointment_url %}
<a href="{{ appointment_url }}" class="button">View Details</a>
{% endif %}

{% if reschedule_url %}
<a href="{{ reschedule_url }}" class="button-secondary">Reschedule</a>
{% endif %}

{% if cancel_url %}
<a href="{{ cancel_url }}" class="button-secondary">Cancel</a>
{% endif %}
{% endblock %}

{% block footer_additional %}
<p style="font-size: 11px; color: #9ca3af;">
    If you need to make changes to your appointment, please contact us at 
    <a href="mailto:<EMAIL>" style="color: #0f172a;"><EMAIL></a>
</p>
{% endblock %}
