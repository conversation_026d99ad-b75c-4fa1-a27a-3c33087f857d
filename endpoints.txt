edpoints for roles app

1. Role Management:

# List all roles (admin only)
GET /api/roles/

# Create a new role (admin only)
POST /api/roles/
{
    "name": "Researcher",
    "description": "Can access and analyze data",
    "permissions": [1, 2, 3]  # IDs of permissions
}

# Retrieve a specific role (admin only)
GET /api/roles/{role_id}/

# Update a role (admin only)
PUT /api/roles/{role_id}/
{
    "name": "Senior Researcher",
    "description": "Can access, analyze, and manage data",
    "permissions": [1, 2, 3, 4]
}

# Delete a role (admin only)
DELETE /api/roles/{role_id}/

# Assign a role to a user (admin only)
POST /api/roles/assign_role/
{
    "email": "<EMAIL>",
    "role_id": 2
}

# List available roles (public)
GET /api/roles/available/

# List available permissions (admin only)
GET /api/roles/available_permissions/

# Check user permissions
GET /api/roles/check-permissions/




2. User Profile Management:

# Get user's own profile
GET /api/roles/profile/

# Update user's own profile
PATCH /api/roles/profile/
{
    "bio": "New bio",
    "credentials": "New credentials"  # Only for doctors
}

# Admin view/edit of a user's profile
GET /api/roles/profile/{user_id}/
PATCH /api/roles/profile/{user_id}/
{
    "is_credentials_verified": true  # Only for admins
}

3. Community Management:


# Create a community (Doctors and Admins only)
POST /api/roles/communities/
{
    "name": "New Community",
    "description": "Community description"
}

# List all communities
GET /api/roles/communities/

# Get a specific community
GET /api/roles/communities/{community_id}/

# Update a community (Admins and Moderators only)
PUT /api/roles/communities/{community_id}/
{
    "name": "Updated Community Name",
    "description": "Updated description"
}

# Delete a community (Admins only)
DELETE /api/roles/communities/{community_id}/

# Search communities
GET /api/roles/communities/search/?q=search_term

# Join a community
POST /api/roles/communities/{community_id}/join/

# Leave a community
POST /api/roles/communities/{community_id}/leave/

# Manage community membership (Admins, Doctors, and Moderators only)
POST /api/roles/communities/{community_id}/membership/
{
    "user_id": "user_uuid",
    "action": "approve"  # or "reject"
}


4. Post Management:

# Flag a post (Admins and Moderators only)
POST /api/roles/posts/{post_id}/flag/

# Delete a post (Admins and Moderators only)
DELETE /api/roles/posts/{post_id}/



