from rest_framework import serializers
from appointments.models import Appointment, <PERSON><PERSON><PERSON>ila<PERSON>, DoctorA<PERSON>ilabilityOverride, DoctorAvailabilityOverrideSlot

class DoctorAvailabilitySerializer(serializers.ModelSerializer):
    class Meta:
        model = DoctorAvailability
        fields = [
            'id', 'doctor', 'clinic', 'enterprise', 'title',
            'start_date', 'end_date', 'start_time', 'end_time',
            'is_active', 'recurrence_type', 'recurrence_interval',
            'recurrence_days', 'recurrence_month_day',
            'recurrence_end_date', 'recurrence_count', 'need_payment'
        ]
        read_only_fields = ['id', 'doctor']

    def validate(self, data):
        """
        Custom validation for availability data
        """
        # Validate recurrence settings
        recurrence_type = data.get('recurrence_type', 'none')
        
        if recurrence_type != 'none':
            # Weekly recurrence requires recurrence_days
            if recurrence_type == 'weekly' and not data.get('recurrence_days'):
                raise serializers.ValidationError({
                    'recurrence_days': 'Weekly recurrence requires recurrence_days to be set.'
                })
                
            # Monthly recurrence requires recurrence_month_day
            if recurrence_type == 'monthly' and not data.get('recurrence_month_day'):
                raise serializers.ValidationError({
                    'recurrence_month_day': 'Monthly recurrence requires recurrence_month_day to be set.'
                })
                
            # Recurrence must have either an end date or count
            if not data.get('recurrence_end_date') and not data.get('recurrence_count'):
                raise serializers.ValidationError({
                    'recurrence_end_date': 'Recurrence must have either an end date or count.',
                    'recurrence_count': 'Recurrence must have either an end date or count.'
                })
                
        # Validate date ranges
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if end_date and start_date and start_date > end_date:
            raise serializers.ValidationError({
                'end_date': 'End date must be after start date.'
            })
            
        # Validate time ranges
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        
        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError({
                'end_time': 'End time must be after start time.'
            })
            
        return data

class DoctorAvailabilityOverrideSlotSerializer(serializers.ModelSerializer):
    class Meta:
        model = DoctorAvailabilityOverrideSlot
        fields = ['id', 'start_time', 'end_time']

class DoctorAvailabilityOverrideSerializer(serializers.ModelSerializer):
    time_slots = DoctorAvailabilityOverrideSlotSerializer(many=True)

    class Meta:
        model = DoctorAvailabilityOverride
        fields = ['id', 'doctor', 'clinic', 'enterprise', 'date', 'is_active', 'time_slots']

    def create(self, validated_data):
        time_slots_data = validated_data.pop('time_slots')
        override = DoctorAvailabilityOverride.objects.create(**validated_data)
        for slot_data in time_slots_data:
            DoctorAvailabilityOverrideSlot.objects.create(override=override, **slot_data)
        return override

    def update(self, instance, validated_data):
        time_slots_data = validated_data.pop('time_slots', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if time_slots_data is not None:
            instance.time_slots.all().delete()
            for slot_data in time_slots_data:
                DoctorAvailabilityOverrideSlot.objects.create(override=instance, **slot_data)
        return instance

class AppointmentCreateSerializer(serializers.ModelSerializer):
    doctor_id = serializers.CharField(write_only=True, required=False)
    email = serializers.EmailField(write_only=True, required=False)

    class Meta:
        model = Appointment
        fields = '__all__'
        read_only_fields = (
            'creator', 'patient',
            'cancelled_by', 'doctor',
            'meeting_link', 'google_event_id'
        )

    def validate(self, data):
        """Validation for creating new appointment"""
        if not data.get('start_time') or not data.get('end_time'):
            raise serializers.ValidationError({
                'error': 'Missing required fields',
                'detail': 'start_time and end_time are required'
            })

        if data['start_time'] >= data['end_time']:
            raise serializers.ValidationError({
                'error': 'Invalid time range',
                'detail': 'End time must be after start time'
            })

        appointment_type = data.get('appointment_type', 'manual')

        if appointment_type == 'booking':
            # Validate required fields for booking type
            if not data.get('doctor_id'):
                raise serializers.ValidationError({
                    'error': 'Missing doctor ID',
                    'detail': 'Doctor ID is required for booking appointments'
                })

            if not data.get('slot_reference'):
                raise serializers.ValidationError({
                    'error': 'Missing slot reference',
                    'detail': 'Slot reference is required for booking appointments'
                })

            # Validate slot reference format
            try:
                ref_type, ref_id = data['slot_reference'].split(':')
                if ref_type not in ['availability', 'override']:
                    raise serializers.ValidationError({
                        'error': 'Invalid slot reference type',
                        'detail': f'Unknown reference type: {ref_type}'
                    })
            except ValueError:
                raise serializers.ValidationError({
                    'error': 'Invalid slot reference format',
                    'detail': 'Slot reference must be in format "type:id"'
                })

        elif appointment_type == 'manual':
            # For manual appointments, ensure no doctor is specified
            if data.get('doctor_id'):
                raise serializers.ValidationError({
                    'error': 'Invalid appointment type',
                    'detail': 'Manual appointments cannot have a doctor assigned'
                })

        return data

class AppointmentUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Appointment
        fields = '__all__'
        read_only_fields = (
            'creator', 'patient', 'doctor',
            'appointment_type', 'slot_reference',
            'start_time', 'end_time',
            'cancelled_by'
        )

    def validate(self, data):
        """Validation for updating appointment"""
        appointment = self.instance
        
        # Validate status transitions
        if 'status' in data:
            new_status = data['status']
            current_status = appointment.status
            
            valid_transitions = {
                'pending': ['confirmed', 'canceled'],
                'confirmed': ['completed', 'canceled'],
                'canceled': [],
                'completed': []
            }
            
            if new_status not in valid_transitions[current_status]:
                raise serializers.ValidationError({
                    'error': 'Invalid status transition',
                    'detail': f'Cannot change status from {current_status} to {new_status}'
                })

            # Validate cancellation reason
            if new_status == 'canceled' and not data.get('cancellation_reason'):
                raise serializers.ValidationError({
                    'error': 'Cancellation reason required',
                    'detail': 'Please provide a reason for cancellation'
                })

        return data