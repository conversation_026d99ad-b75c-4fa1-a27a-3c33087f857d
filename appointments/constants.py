from django.utils.translation import gettext_lazy as _

# Doctor Availability Constants
DAYS_OF_WEEK = [
    ('monday', _('Monday')),
    ('tuesday', _('Tuesday')),
    ('wednesday', _('Wednesday')),
    ('thursday', _('Thursday')),
    ('friday', _('Friday')),
    ('saturday', _('Saturday')),
    ('sunday', _('Sunday')),
]

# Recurrence Types
RECURRENCE_TYPE_CHOICES = [
    ('none', _('No Recurrence')),
    ('daily', _('Daily')),
    ('weekly', _('Weekly')),
    ('monthly', _('Monthly')),
    ('yearly', _('Yearly')),
]

# Appointment Constants
APPOINTMENT_STATUS_CHOICES = (
    ('pending', _('Pending')),
    ('confirmed', _('Confirmed')),
    ('canceled', _('Canceled')),
    ('completed', _('Completed')),
)

APPOINTMENT_TYPE_CHOICES = (
    ('manual', _('Manual Event')),
    ('booking', _('Doctor Booking')),
)

APPOINTMENT_MODE_CHOICES = (
    ('in_person', _('In-Person')),
    ('video_call', _('Video Call')),
    ('phone_call', _('Phone Call')),
)

# Validation Constants
MAX_DATE_RANGE_DAYS = 31
DEFAULT_UPCOMING_DAYS = 7 