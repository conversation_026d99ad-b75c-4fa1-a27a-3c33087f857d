from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON><PERSON>icated, AllowAny
from rest_framework.views import APIView
from django.utils import timezone
from django.db.models import Q
from django.shortcuts import render
from appointments.models.doctor_contact import DoctorContactInfo, PendingDoctorContactInfo, PendingDoctorAppointmentEmail
from appointments.models.doctor_message import DoctorMessage
from appointments.api.serializers.doctor_message import (
    Doctor<PERSON><PERSON><PERSON>InfoSerializer, 
    DoctorMessageSerializer,
    PendingDoctorContactInfoSerializer,
    PendingDoctorAppointmentEmailSerializer
)
from appointments.services.doctor_message import DoctorMessageService
from appointments.services.doctor_contact import Doctor<PERSON>ontactService, DoctorAppointmentEmailService
from rest_framework import serializers
from appointments.models.appointment import Appointment
import logging

logger = logging.getLogger(__name__)


class DoctorContactInfoViewSet(viewsets.ModelViewSet):
    serializer_class = DoctorContactInfoSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return DoctorContactInfo.objects.filter(doctor=self.request.user)
    
    def create(self, request):
        """
        Create or update doctor contact info and appointment notification emails, send verification as needed
        """
        # Handle contact info
        contact_serializer = PendingDoctorContactInfoSerializer(data=request.data)
        appointment_serializer = PendingDoctorAppointmentEmailSerializer(data=request.data)
        contact_valid = contact_serializer.is_valid()
        appointment_valid = appointment_serializer.is_valid()
        errors = {}
        if not contact_valid:
            errors['contact'] = contact_serializer.errors
        if not appointment_valid:
            errors['appointment'] = appointment_serializer.errors
        if errors:
            return Response(errors, status=status.HTTP_400_BAD_REQUEST)

        # Create pending contact info and send verification
        pending_contact = DoctorContactService.create_pending_contact(
            doctor=request.user,
            primary_email=contact_serializer.validated_data.get('primary_email'),
            secondary_email=contact_serializer.validated_data.get('secondary_email'),
            is_primary_email_active=contact_serializer.validated_data.get('is_primary_email_active', True),
            is_secondary_email_active=contact_serializer.validated_data.get('is_secondary_email_active', True)
        )
        # Create pending appointment notification emails and send verification
        pending_appointment = DoctorAppointmentEmailService.create_pending_appointment_email(
            doctor=request.user,
            primary_email_appointment=appointment_serializer.validated_data.get('primary_email_appointment'),
            secondary_email_appointment=appointment_serializer.validated_data.get('secondary_email_appointment'),
            is_primary_email_appointment_active=appointment_serializer.validated_data.get('is_primary_email_appointment_active', True),
            is_secondary_email_appointment_active=appointment_serializer.validated_data.get('is_secondary_email_appointment_active', True)
        )
        return Response({
            'message': 'Verification emails sent. Please check your inbox to verify your email addresses.',
            'pending_contact': PendingDoctorContactInfoSerializer(pending_contact).data,
            'pending_appointment': PendingDoctorAppointmentEmailSerializer(pending_appointment).data
        }, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        """
        Update doctor contact info and appointment notification emails, with verification flow if emails change
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        data = request.data.copy()
        # Contact info update
        new_primary_email = data.get('primary_email', instance.primary_email)
        new_secondary_email = data.get('secondary_email', instance.secondary_email)
        contact_email_changed = (
            new_primary_email != instance.primary_email or
            (instance.secondary_email or '') != (new_secondary_email or '')
        )
        # Appointment notification email update
        new_primary_appointment = data.get('primary_email_appointment', instance.primary_email_appointment)
        new_secondary_appointment = data.get('secondary_email_appointment', instance.secondary_email_appointment)
        appointment_email_changed = (
            new_primary_appointment != instance.primary_email_appointment or
            (instance.secondary_email_appointment or '') != (new_secondary_appointment or '')
        )
        # If either set of emails changed, start verification flow
        if contact_email_changed:
            pending_contact = DoctorContactService.create_pending_contact(
                doctor=request.user,
                primary_email=new_primary_email,
                secondary_email=new_secondary_email,
                is_primary_email_active=data.get('is_primary_email_active', instance.is_primary_email_active),
                is_secondary_email_active=data.get('is_secondary_email_active', instance.is_secondary_email_active)
            )
            contact_response = PendingDoctorContactInfoSerializer(pending_contact).data
        else:
            contact_response = None
        if appointment_email_changed:
            pending_appointment = DoctorAppointmentEmailService.create_pending_appointment_email(
                doctor=request.user,
                primary_email_appointment=new_primary_appointment,
                secondary_email_appointment=new_secondary_appointment,
                is_primary_email_appointment_active=data.get('is_primary_email_appointment_active', instance.is_primary_email_appointment_active),
                is_secondary_email_appointment_active=data.get('is_secondary_email_appointment_active', instance.is_secondary_email_appointment_active)
            )
            appointment_response = PendingDoctorAppointmentEmailSerializer(pending_appointment).data
        else:
            appointment_response = None
        # If only toggling active status, update directly
        allowed_fields = [
            'is_primary_email_active', 'is_secondary_email_active',
            'is_primary_email_appointment_active', 'is_secondary_email_appointment_active'
        ]
        update_data = {k: v for k, v in data.items() if k in allowed_fields}
        serializer = self.get_serializer(instance, data=update_data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response({
            'contact': contact_response,
            'appointment': appointment_response,
            'updated': serializer.data
        })

    @action(detail=True, methods=['patch'])
    def toggle_email(self, request, pk=None):
        """
        Toggle active status of any email (primary/secondary, contact/appointment)
        """
        contact_info = self.get_object()
        email_type = request.data.get('email_type')  # e.g. 'primary', 'secondary', 'primary_appointment', 'secondary_appointment'
        is_active = request.data.get('is_active', True)
        if email_type == 'primary':
            contact_info.is_primary_email_active = is_active
        elif email_type == 'secondary':
            contact_info.is_secondary_email_active = is_active
        elif email_type == 'primary_appointment':
            contact_info.is_primary_email_appointment_active = is_active
        elif email_type == 'secondary_appointment':
            contact_info.is_secondary_email_appointment_active = is_active
        else:
            return Response(
                {'error': 'Invalid email type. Use "primary", "secondary", "primary_appointment", or "secondary_appointment"'},
                status=status.HTTP_400_BAD_REQUEST
            )
        contact_info.save()
        return Response(self.get_serializer(contact_info).data)

    @action(detail=False, methods=['get'])
    def pending(self, request):
        """
        Get pending contact info and appointment notification emails for the current user
        """
        try:
            pending_contact = PendingDoctorContactInfo.objects.get(doctor=request.user)
            contact_serializer = PendingDoctorContactInfoSerializer(pending_contact)
        except PendingDoctorContactInfo.DoesNotExist:
            contact_serializer = None
        try:
            pending_appointment = PendingDoctorAppointmentEmail.objects.get(doctor=request.user)
            appointment_serializer = PendingDoctorAppointmentEmailSerializer(pending_appointment)
        except PendingDoctorAppointmentEmail.DoesNotExist:
            appointment_serializer = None
        return Response({
            'pending_contact': contact_serializer.data if contact_serializer else None,
            'pending_appointment': appointment_serializer.data if appointment_serializer else None
        })
    
    @action(detail=False, methods=['post'])
    def resend_verification(self, request):
        """
        Resend verification emails for pending contact and appointment notification emails
        """
        try:
            pending_contact = PendingDoctorContactInfo.objects.get(doctor=request.user)
            if not pending_contact.is_primary_email_verified:
                DoctorContactService.send_primary_verification_email(pending_contact)
            if pending_contact.secondary_email and not pending_contact.is_secondary_email_verified:
                DoctorContactService.send_secondary_verification_email(pending_contact)
        except PendingDoctorContactInfo.DoesNotExist:
            pass
        try:
            pending_appointment = PendingDoctorAppointmentEmail.objects.get(doctor=request.user)
            if not pending_appointment.is_primary_email_appointment_verified:
                DoctorAppointmentEmailService.send_primary_appointment_verification_email(pending_appointment)
            if pending_appointment.secondary_email_appointment and not pending_appointment.is_secondary_email_appointment_verified:
                DoctorAppointmentEmailService.send_secondary_appointment_verification_email(pending_appointment)
        except PendingDoctorAppointmentEmail.DoesNotExist:
            pass
        return Response({'message': 'Verification emails resent successfully'})

# Add these verification endpoint views for appointment notification emails
class VerifyPrimaryAppointmentEmailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, token):
        pending = DoctorAppointmentEmailService.verify_primary_appointment_email(token)
        if pending:
            return render(request, 'account/doctor_email_verification_success.html', {
                'level': 'Doctor Primary Appointment Notification',
                'home_url': request.build_absolute_uri('/'),
                'verified': True,
                'email': pending.primary_email_appointment
            })
        else:
            return render(request, '404.html', {
                'exception': "Invalid or expired verification link"
            })

class VerifySecondaryAppointmentEmailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, token):
        pending = DoctorAppointmentEmailService.verify_secondary_appointment_email(token)
        if pending:
            return render(request, 'account/doctor_email_verification_success.html', {
                'level': 'Doctor Secondary Appointment Notification',
                'home_url': request.build_absolute_uri('/'),
                'verified': True,
                'email': pending.secondary_email_appointment
            })
        else:
            return render(request, '404.html', {
                'exception': "Invalid or expired verification link"
            })


class DoctorMessageViewSet(viewsets.ModelViewSet):
    serializer_class = DoctorMessageSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        queryset = DoctorMessage.objects.filter(doctor=user)

        # Filter by query parameters
        is_read = self.request.query_params.get('is_read', None)
        if is_read is not None:
            queryset = queryset.filter(is_read=is_read.lower() == 'true')
#
        appointment_id = self.request.query_params.get('appointment', None)
        if appointment_id:
            queryset = queryset.filter(appointment_id=appointment_id)

        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(subject__icontains=search) | 
                Q(content__icontains=search)
            )

        # Optimize queries by prefetching related profile data for patient
        queryset = queryset.select_related(
            'patient__profile'
        ).order_by('-created_at')
        
        return queryset
    
    def perform_create(self, serializer):
        user = self.request.user
        logger.info(f"Creating new doctor message for user {user.id}")
        
        # Check if user is patient
        # is_patient = Appointment.objects.filter(patient=user).exists()
        # if not is_patient:
        #     raise serializers.ValidationError({
        #         'error': 'Permission denied',
        #         'detail': 'Only patients can send messages to doctors'
        #     })
        
        # Get data from serializer
        data = serializer.validated_data
        doctor = data.get('doctor')
        subject = data.get('subject')
        content = data.get('content')
        appointment = data.get('appointment')
        
        logger.debug(f"Message details - Doctor: {doctor.id}, Subject: {subject}, Appointment: {appointment.id if appointment else None}")
        
        # Validate required fields
        if not subject or not content:
            logger.warning("Missing required fields in message creation")
            raise serializers.ValidationError({
                'error': 'Missing required fields',
                'detail': 'Subject and content are required'
            })
        
        logger.info("Creating message using DoctorMessageService")
        # Use DoctorMessageService to create message and send email
        message = DoctorMessageService.send_message(
            patient=user,
            doctor=doctor,
            subject=subject,
            content=content,
            appointment=appointment
        )
        
        logger.info(f"Message {message.id} created successfully")
        # Update serializer instance with created message
        serializer.instance = message

    @action(detail=True, methods=['patch'])
    def mark_read(self, request, pk=None):
        """Mark a message as read"""
        message = self.get_object()
        message.is_read = True
        message.read_at = timezone.now()
        message.save()
        return Response(self.get_serializer(message).data)

    @action(detail=False, methods=['get'])
    def unread_count(self, request):
        """Get count of unread messages"""
        queryset = self.get_queryset()
        count = queryset.filter(is_read=False).count()
        return Response({'unread_count': count})

    @action(detail=False, methods=['get'])
    def by_appointment(self, request):
        """Get all messages for a specific appointment"""
        appointment_id = request.query_params.get('appointment_id')
        if not appointment_id:
            return Response(
                {'error': 'appointment_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        messages = self.get_queryset().filter(appointment_id=appointment_id)
        serializer = self.get_serializer(messages, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get message statistics"""
        queryset = self.get_queryset()
        total_messages = queryset.count()
        unread_messages = queryset.filter(is_read=False).count()
        messages_by_appointment = queryset.filter(
            appointment__isnull=False
        ).values('appointment').distinct().count()

        return Response({
            'total_messages': total_messages,
            'unread_messages': unread_messages,
            'messages_with_appointments': messages_by_appointment
        })
        
        
# Add these verification endpoint views
class VerifyPrimaryEmailView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request, token):
        """Verify primary email with token"""
        pending_contact = DoctorContactService.verify_primary_email(token)
        
        if pending_contact:
            return render(request, 'account/doctor_email_verification_success.html', {
                'level': 'Doctor Primary Contact',
                'home_url': request.build_absolute_uri('/'),
                'verified': True,
                'email': pending_contact.primary_email
            })
        else:
            return render(request, '404.html', {
                'exception': "Invalid or expired verification link"
            })


class VerifySecondaryEmailView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request, token):
        """Verify secondary email with token"""
        pending_contact = DoctorContactService.verify_secondary_email(token)
        
        if pending_contact:
            return render(request, 'account/doctor_email_verification_success.html', {
                'level': 'Doctor Secondary Contact',
                'home_url': request.build_absolute_uri('/'),
                'verified': True,
                'email': pending_contact.secondary_email
            })
        else:
            return render(request, '404.html', {
                'exception': "Invalid or expired verification link"
            })