import logging
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q

from appointments.models import DoctorConsultationProfile
from appointments.api.serializers.consultation_profile import (
    DoctorConsultationProfileSerializer,
    DoctorConsultationProfileListSerializer
)
from config.pagination_utils import paginate_queryset

logger = logging.getLogger(__name__)


class DoctorConsultationProfileViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing doctor consultation profiles - individual doctors only
    """
    queryset = DoctorConsultationProfile.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action == 'list':
            return DoctorConsultationProfileListSerializer
        return DoctorConsultationProfileSerializer
    
    def get_queryset(self):
        """Filter to only current doctor's profile"""
        # TEMPORARILY COMMENTED OUT - Only doctors can access this endpoint
        # if not self.request.user.role or self.request.user.role.name != 'doctor':
        #     return DoctorConsultationProfile.objects.none()
        
        # Doctors can only see their own profile
        return DoctorConsultationProfile.objects.filter(
            user=self.request.user
        ).select_related('user', 'user__role')
    
    def create(self, request, *args, **kwargs):
        """Create consultation profile for current doctor"""
        try:
            # TEMPORARILY COMMENTED OUT - Check if user is doctor
            # if not request.user.role or request.user.role.name != 'doctor':
            #     return Response(
            #         {'error': 'Only doctors can create consultation profiles'},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            serializer = self.get_serializer(data=request.data, context={'request': request})
            serializer.is_valid(raise_exception=True)
            
            consultation_profile = serializer.save()
            
            logger.info(f"Consultation profile created by user {consultation_profile.user.email}")
            
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED
            )
            
        except Exception as e:
            logger.error(f"Error creating consultation profile: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def list(self, request, *args, **kwargs):
        """List current doctor's consultation profile"""
        try:
            # TEMPORARILY COMMENTED OUT - Check if user is doctor
            # if not request.user.role or request.user.role.name != 'doctor':
            #     return Response(
            #         {'error': 'Only doctors can access consultation profiles'},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)
            
            return Response({
                'results': serializer.data,
                'count': queryset.count()
            })
            
        except Exception as e:
            logger.error(f"Error listing consultation profiles: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve consultation profiles'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def retrieve(self, request, *args, **kwargs):
        """Retrieve specific consultation profile (only own profile)"""
        try:
            # TEMPORARILY COMMENTED OUT - Check if user is doctor
            # if not request.user.role or request.user.role.name != 'doctor':
            #     return Response(
            #         {'error': 'Only doctors can access consultation profiles'},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            instance = self.get_object()
            
            # Additional check to ensure it's the doctor's own profile
            if instance.user != request.user:
                return Response(
                    {'error': 'You can only access your own consultation profile'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
            
        except DoctorConsultationProfile.DoesNotExist:
            return Response(
                {'error': 'Consultation profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error retrieving consultation profile: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve consultation profile'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def update(self, request, *args, **kwargs):
        """Update current doctor's consultation profile"""
        try:
            # TEMPORARILY COMMENTED OUT - Check if user is doctor
            # if not request.user.role or request.user.role.name != 'doctor':
            #     return Response(
            #         {'error': 'Only doctors can update consultation profiles'},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            instance = self.get_object()
            
            # Ensure it's the doctor's own profile
            if instance.user != request.user:
                return Response(
                    {'error': 'You can only update your own consultation profile'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            partial = kwargs.pop('partial', False)
            serializer = self.get_serializer(instance, data=request.data, partial=partial, context={'request': request})
            serializer.is_valid(raise_exception=True)
            
            consultation_profile = serializer.save()
            
            logger.info(f"Consultation profile updated by user {consultation_profile.user.email}")
            
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error updating consultation profile: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def partial_update(self, request, *args, **kwargs):
        """Partial update consultation profile"""
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)
    
    def destroy(self, request, *args, **kwargs):
        """Delete current doctor's consultation profile"""
        try:
            # TEMPORARILY COMMENTED OUT - Check if user is doctor
            # if not request.user.role or request.user.role.name != 'doctor':
            #     return Response(
            #         {'error': 'Only doctors can delete consultation profiles'},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            instance = self.get_object()
            
            # Ensure it's the doctor's own profile
            if instance.user != request.user:
                return Response(
                    {'error': 'You can only delete your own consultation profile'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            doctor_email = instance.user.email
            instance.delete()
            
            logger.info(f"Consultation profile deleted by user {doctor_email}")
            
            return Response(
                {'message': 'Consultation profile deleted successfully'},
                status=status.HTTP_204_NO_CONTENT
            )
            
        except Exception as e:
            logger.error(f"Error deleting consultation profile: {str(e)}")
            return Response(
                {'error': 'Failed to delete consultation profile'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def my_profile(self, request):
        """Get current doctor's consultation profile"""
        try:
            # TEMPORARILY COMMENTED OUT - Check if user is doctor
            # if not request.user.role or request.user.role.name != 'doctor':
            #     return Response(
            #         {'error': 'Only doctors can access this endpoint'},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            try:
                profile = DoctorConsultationProfile.objects.get(user=request.user)
                serializer = self.get_serializer(profile)
                return Response(serializer.data)
            except DoctorConsultationProfile.DoesNotExist:
                return Response(
                    {'error': 'Consultation profile not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
                
        except Exception as e:
            logger.error(f"Error retrieving user's own profile: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve consultation profile'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'])
    def toggle_telemedicine(self, request):
        """Toggle telemedicine acceptance for current doctor"""
        try:
            # TEMPORARILY COMMENTED OUT - Check if user is doctor
            # if not request.user.role or request.user.role.name != 'doctor':
            #     return Response(
            #         {'error': 'Only doctors can toggle telemedicine acceptance'},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            try:
                profile = DoctorConsultationProfile.objects.get(user=request.user)
                profile.accepts_telemedicine = not profile.accepts_telemedicine
                profile.save()
                
                logger.info(f"Telemedicine acceptance toggled to {profile.accepts_telemedicine} for user {profile.user.email}")
                
                serializer = self.get_serializer(profile)
                return Response(serializer.data)
                
            except DoctorConsultationProfile.DoesNotExist:
                return Response(
                    {'error': 'Consultation profile not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
                
        except Exception as e:
            logger.error(f"Error toggling telemedicine acceptance: {str(e)}")
            return Response(
                {'error': 'Failed to toggle telemedicine acceptance'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'])
    def toggle_active(self, request):
        """Toggle active status for current doctor's consultation profile"""
        try:
            # TEMPORARILY COMMENTED OUT - Check if user is doctor
            # if not request.user.role or request.user.role.name != 'doctor':
            #     return Response(
            #         {'error': 'Only doctors can toggle profile active status'},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            try:
                profile = DoctorConsultationProfile.objects.get(user=request.user)
                profile.is_active = not profile.is_active
                profile.save()
                
                logger.info(f"Profile active status toggled to {profile.is_active} for user {profile.user.email}")
                
                serializer = self.get_serializer(profile)
                return Response(serializer.data)
                
            except DoctorConsultationProfile.DoesNotExist:
                return Response(
                    {'error': 'Consultation profile not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
                
        except Exception as e:
            logger.error(f"Error toggling profile active status: {str(e)}")
            return Response(
                {'error': 'Failed to toggle profile active status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 