import logging
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from django.utils import timezone
from django.db import models
from datetime import datetime
from appointments.models import DoctorAvailabilityOverride
from appointments.api.serializers import DoctorAvailabilityOverrideSerializer
from clinic.models import ClinicDoctor

logger = logging.getLogger(__name__)

def is_doctor(user):
    """Check if user is a doctor by looking up in ClinicDoctor table"""
    return ClinicDoctor.objects.filter(doctor=user).exists()

class DoctorAvailabilityOverrideViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = DoctorAvailabilityOverrideSerializer
    queryset = DoctorAvailabilityOverride.objects.all()

    def get_queryset(self):
        """Override get_queryset to filter based on user role"""
        doctor_id = self.request.query_params.get('doctor_id')
        
        if doctor_id:
            return DoctorAvailabilityOverride.objects.filter(doctor_id=doctor_id)
        elif is_doctor(self.request.user):
            return DoctorAvailabilityOverride.objects.filter(doctor=self.request.user)
        else:
            return DoctorAvailabilityOverride.objects.filter(is_active=True)

    def list(self, request):
        """Get all overrides for a doctor"""
        try:
            doctor_id = request.query_params.get('doctor_id')
            
            if doctor_id:
                # If doctor_id is provided, get overrides for that doctor
                queryset = DoctorAvailabilityOverride.objects.filter(doctor_id=doctor_id)
            elif is_doctor(request.user):
                # If user is a doctor, get their own overrides
                queryset = DoctorAvailabilityOverride.objects.filter(doctor=request.user)
            else:
                # For other users, get only records they created
                queryset = DoctorAvailabilityOverride.objects.filter(doctor=request.user)
                
            serializer = self.serializer_class(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error in listing overrides: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _create_single_override(self, data):
        """Helper method to create a single override with time slots"""
        serializer = self.serializer_class(data=data)
        if serializer.is_valid():
            override = serializer.save(doctor=self.request.user)
            return {
                'status': 'created',
                'data': serializer.data,
                'errors': None
            }
        return {
            'status': 'error',
            'data': data,
            'errors': serializer.errors
        }

    def _update_single_override(self, instance, data):
        """Helper method to update a single override with time slots"""
        serializer = self.serializer_class(
            instance,
            data=data,
            partial=True
        )
        if serializer.is_valid():
            override = serializer.save()
            return {
                'status': 'updated',
                'data': serializer.data,
                'errors': None
            }
        return {
            'status': 'error',
            'data': data,
            'errors': serializer.errors
        }

    @action(detail=False, methods=['post'])
    def bulk_save(self, request):
        """Bulk create or update overrides"""
        if not isinstance(request.data, list):
            return Response(
                {'error': 'Data must be an array'},
                status=status.HTTP_400_BAD_REQUEST
            )

        results = []
        for item in request.data:
            override_id = item.get('id')
            
            try:
                if override_id:
                    # Update existing
                    try:
                        instance = DoctorAvailabilityOverride.objects.get(
                            id=override_id,
                            doctor=request.user
                        )
                        result = self._update_single_override(instance, item)
                    except DoctorAvailabilityOverride.DoesNotExist:
                        result = {
                            'status': 'error',
                            'data': item,
                            'errors': {'id': ['Override not found']}
                        }
                else:
                    # Create new
                    result = self._create_single_override(item)
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"Error processing override: {str(e)}")
                results.append({
                    'status': 'error',
                    'data': item,
                    'errors': {'detail': str(e)}
                })

        # Check if any operation was successful
        if not any(r['status'] in ['created', 'updated'] for r in results):
            return Response(
                {
                    'error': 'No overrides were processed successfully',
                    'results': results
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response({
            'message': 'Bulk operation completed',
            'results': results
        })

    def create(self, request):
        """Create new availability override"""
        try:
            serializer = self.serializer_class(data=request.data)
            if serializer.is_valid():
                serializer.save(doctor=request.user)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error in creating override: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, pk=None):
        """Update specific override"""
        try:
            override = DoctorAvailabilityOverride.objects.get(pk=pk)
            serializer = self.serializer_class(
                override, 
                data=request.data, 
                partial=True
            )
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except DoctorAvailabilityOverride.DoesNotExist:
            return Response(
                {'error': 'Override not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error in updating override: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def destroy(self, request, pk=None):
        """Delete specific override"""
        try:
            override = DoctorAvailabilityOverride.objects.get(pk=pk)
            override.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except DoctorAvailabilityOverride.DoesNotExist:
            return Response(
                {'error': 'Override not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error in deleting override: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def by_date_range(self, request):
        """
        Get overrides for a specific date range.
        Query params: start_date, end_date
        """
        try:
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')
            
            if not all([start_date, end_date]):
                return Response(
                    {'error': 'start_date and end_date are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': 'Invalid date format. Use YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            queryset = DoctorAvailabilityOverride.objects.filter(
                date__range=[start_date, end_date],
                is_active=True
            )

            if is_doctor(request.user):
                queryset = queryset.filter(doctor=request.user)

            serializer = self.serializer_class(queryset, many=True)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error in getting overrides by date range: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 