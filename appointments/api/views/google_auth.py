from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from django.conf import settings
import json
import logging
from appointments.models.google_credentials import UserGoogleCredentials

logger = logging.getLogger(__name__)

class GoogleAuthView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get Google OAuth URL for user to authorize calendar access
        """
        try:
            # Create OAuth flow
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": settings.GOOGLE_CLIENT_ID,
                        "client_secret": settings.GOOGLE_CLIENT_SECRET,
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                    }
                },
                scopes=['https://www.googleapis.com/auth/calendar']
            )
            flow.redirect_uri = settings.GOOGLE_REDIRECT_URI

            # Generate authorization URL
            authorization_url, state = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true'
            )

            return Response({
                'authorization_url': authorization_url,
                'state': state
            })

        except Exception as e:
            logger.error(f"Error generating Google auth URL: {str(e)}")
            return Response({
                'error': 'Failed to generate authorization URL'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GoogleAuthCallbackView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Handle Google OAuth callback and save credentials
        """
        try:
            # Get authorization code from request
            code = request.query_params.get('code')
            if not code:
                return Response({
                    'error': 'Authorization code not provided'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create OAuth flow
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": settings.GOOGLE_CLIENT_ID,
                        "client_secret": settings.GOOGLE_CLIENT_SECRET,
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                    }
                },
                scopes=['https://www.googleapis.com/auth/calendar']
            )
            flow.redirect_uri = settings.GOOGLE_REDIRECT_URI

            # Exchange code for credentials
            flow.fetch_token(code=code)
            credentials = flow.credentials

            # Save credentials to database
            UserGoogleCredentials.objects.update_or_create(
                user=request.user,
                defaults={
                    'access_token': credentials.token,
                    'refresh_token': credentials.refresh_token,
                    'token_expiry': credentials.expiry
                }
            )

            return Response({
                'message': 'Google Calendar connected successfully'
            })

        except Exception as e:
            logger.error(f"Error handling Google auth callback: {str(e)}")
            return Response({
                'error': 'Failed to connect Google Calendar'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GoogleAuthDisconnectView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Disconnect Google Calendar
        """
        try:
            UserGoogleCredentials.objects.filter(user=request.user).delete()
            return Response({
                'message': 'Google Calendar disconnected successfully'
            })
        except Exception as e:
            logger.error(f"Error disconnecting Google Calendar: {str(e)}")
            return Response({
                'error': 'Failed to disconnect Google Calendar'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 