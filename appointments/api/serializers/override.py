from rest_framework import serializers
from appointments.models import DoctorAvailabilityOverrideSlot, DoctorAvailabilityOverride

class DoctorAvailabilityOverrideSlotSerializer(serializers.ModelSerializer):
    class Meta:
        model = DoctorAvailabilityOverrideSlot
        fields = ['id', 'start_time', 'end_time']

class DoctorAvailabilityOverrideSerializer(serializers.ModelSerializer):
    time_slots = DoctorAvailabilityOverrideSlotSerializer(many=True)

    class Meta:
        model = DoctorAvailabilityOverride
        fields = ['id', 'doctor', 'clinic', 'enterprise', 'date', 'is_active', 'time_slots']

    def create(self, validated_data):
        time_slots_data = validated_data.pop('time_slots')
        override = DoctorAvailabilityOverride.objects.create(**validated_data)
        for slot_data in time_slots_data:
            DoctorAvailabilityOverrideSlot.objects.create(override=override, **slot_data)
        return override

    def update(self, instance, validated_data):
        time_slots_data = validated_data.pop('time_slots', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if time_slots_data is not None:
            instance.time_slots.all().delete()
            for slot_data in time_slots_data:
                DoctorAvailabilityOverrideSlot.objects.create(override=instance, **slot_data)
        return instance 