import logging
from rest_framework import serializers
from appointments.models.doctor_contact import DoctorContactInfo, PendingDoctorAppointmentEmail, PendingDoctorContactInfo
from appointments.models.doctor_message import DoctorMessage
from accounts.models import CustomUser
from django.utils import timezone
from roles.gcp_utils import get_signed_url
from datetime import timedelta

logger = logging.getLogger(__name__)

class DoctorContactInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = DoctorContactInfo
        fields = [
            'id', 'primary_email', 'secondary_email',
            'is_primary_email_active', 'is_secondary_email_active',
            'is_primary_email_verified', 'is_secondary_email_verified',
            'primary_email_appointment', 'secondary_email_appointment',
            'is_primary_email_appointment_active', 'is_secondary_email_appointment_active',
            'is_primary_email_appointment_verified', 'is_secondary_email_appointment_verified',
        ]
        read_only_fields = [
            'id', 'is_primary_email_verified', 'is_secondary_email_verified',
            'is_primary_email_appointment_verified', 'is_secondary_email_appointment_verified'
        ]


class PendingDoctorContactInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = PendingDoctorContactInfo
        fields = ['id', 'primary_email', 'secondary_email', 
                 'is_primary_email_active', 'is_secondary_email_active',
                 'is_primary_email_verified', 'is_secondary_email_verified']
        read_only_fields = ['id', 'is_primary_email_verified', 'is_secondary_email_verified']
    
    def validate(self, data):
        """Validate that primary and secondary emails are different if both provided"""
        primary_email = data.get('primary_email')
        secondary_email = data.get('secondary_email')
        
        if primary_email and secondary_email and primary_email == secondary_email:
            raise serializers.ValidationError({
                'secondary_email': 'Primary and secondary emails cannot be the same.'
            })
        
        return data

class PendingDoctorAppointmentEmailSerializer(serializers.ModelSerializer):
    class Meta:
        model = PendingDoctorAppointmentEmail
        fields = [
            'id', 'primary_email_appointment', 'secondary_email_appointment',
            'is_primary_email_appointment_active', 'is_secondary_email_appointment_active',
            'is_primary_email_appointment_verified', 'is_secondary_email_appointment_verified',
        ]
        read_only_fields = [
            'id', 'is_primary_email_appointment_verified', 'is_secondary_email_appointment_verified'
        ]

    def validate(self, data):
        primary = data.get('primary_email_appointment')
        secondary = data.get('secondary_email_appointment')
        if primary and secondary and primary == secondary:
            raise serializers.ValidationError({
                'secondary_email_appointment': 'Primary and secondary appointment emails cannot be the same.'
            })
        return data

class DoctorMessageSerializer(serializers.ModelSerializer):
    patient_name = serializers.SerializerMethodField()
    doctor_name = serializers.SerializerMethodField()
    patient_profile_picture = serializers.SerializerMethodField()
    
    class Meta:
        model = DoctorMessage
        fields = ['id', 'doctor', 'patient', 'appointment', 'subject', 
                 'content', 'is_read', 'read_at', 'created_at',
                 'patient_name', 'doctor_name', 'patient_profile_picture']
        read_only_fields = ['id', 'created_at', 'patient_name', 'doctor_name', 'patient', 'patient_profile_picture']
        extra_kwargs = {
            'subject': {'required': False},
            'content': {'required': False},
            'doctor': {'required': False}
        }
    
    def get_patient_name(self, obj):
        return obj.patient.get_full_name() if obj.patient else None
    
    def get_doctor_name(self, obj):
        return obj.doctor.get_full_name() if obj.doctor else None
    
    def get_patient_profile_picture(self, obj):
        if obj.patient and hasattr(obj.patient, 'profile') and obj.patient.profile.profile_picture:
            try:
                full_blob_name = f"UID_{obj.patient.id}/profile_picture/{obj.patient.profile.profile_picture}"
                return get_signed_url(full_blob_name, obj.patient.id, timedelta(days=7))
            except Exception as e:
                logger.error(f"Error getting patient profile picture URL: {str(e)}")
                return None
        return None

    def validate(self, data):
        """
        Custom validation to ensure required fields are present only during creation
        """
        if self.instance is None:  # Only validate during creation
            if not data.get('subject'):
                raise serializers.ValidationError({
                    'subject': 'Subject is required'
                })
            if not data.get('content'):
                raise serializers.ValidationError({
                    'content': 'Content is required'
                })
            if not data.get('doctor'):
                raise serializers.ValidationError({
                    'doctor': 'Doctor is required'
                })
        return data

    def update(self, instance, validated_data):
        """
        Only allow updating is_read and read_at fields
        """
        instance.is_read = validated_data.get('is_read', instance.is_read)
        if instance.is_read:
            instance.read_at = timezone.now()
        instance.save()
        return instance