from .availability import DoctorAvailabilitySerializer
from .override import DoctorA<PERSON>ilabilityOverrideSlotSerializer, DoctorAvailabilityOverrideSerializer
from .appointment import AppointmentCreateSerializer, AppointmentUpdateSerializer, AppointmentAttachmentSerializer, AppointmentSerializer
from .doctor_message import Doctor<PERSON><PERSON><PERSON>InfoSerializer, DoctorMessageSerializer
from .consultation_profile import DoctorConsultationProfileSerializer, DoctorConsultationProfileListSerializer

__all__ = [
    'DoctorAvailabilitySerializer',
    'DoctorAvailabilityOverrideSlotSerializer',
    'DoctorAvailabilityOverrideSerializer',
    'AppointmentCreateSerializer',
    'AppointmentUpdateSerializer',
    'AppointmentAttachmentSerializer',
    'AppointmentSerializer',
    'DoctorContactInfoSerializer',
    'DoctorMessageSerializer',
    'DoctorConsultationProfileSerializer',
    'DoctorConsultationProfileListSerializer',
]