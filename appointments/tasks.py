from celery import shared_task
import logging
from appointments.models import Appointment
from appointments.services.google_calendar import get_google_calendar_service, sync_to_calendar
from accounts.send_email import send_email

logger = logging.getLogger(__name__)

@shared_task
def sync_appointment_to_google_calendar(appointment_id):
    """
    Create or update Google Calendar event when Appointment is created/updated.
    Syncs to both doctor's and patient's calendars if they have Google Calendar connected.
    """
    try:
        appointment = Appointment.objects.get(id=appointment_id)
        
        # Sync for doctor
        if appointment.doctor:
            doctor_service = get_google_calendar_service(appointment.doctor)
            if doctor_service:
                sync_to_calendar(doctor_service, appointment)
        
        # Sync for patient
        if appointment.patient:
            patient_service = get_google_calendar_service(appointment.patient)
            if patient_service:
                sync_to_calendar(patient_service, appointment)
        
        # Sync to system calendar as backup
        system_service = get_google_calendar_service(None)  # Service account
        if system_service:
            sync_to_calendar(system_service, appointment)

    except Exception as e:
        logger.error(f"Error syncing Google Calendar for appointment {appointment_id}: {str(e)}")
        raise

@shared_task
def delete_google_calendar_event(appointment_id):
    """
    Delete Google Calendar event when Appointment is cancelled.
    Deletes from all connected calendars.
    """
    try:
        appointment = Appointment.objects.get(id=appointment_id)
        if not appointment.google_event_id:
            return

        # Delete from doctor's calendar
        if appointment.doctor:
            doctor_service = get_google_calendar_service(appointment.doctor)
            if doctor_service:
                try:
                    doctor_service.events().delete(
                        calendarId='primary',
                        eventId=appointment.google_event_id
                    ).execute()
                except Exception as e:
                    logger.error(f"Error deleting from doctor's calendar: {str(e)}")

        # Delete from patient's calendar
        if appointment.patient:
            patient_service = get_google_calendar_service(appointment.patient)
            if patient_service:
                try:
                    patient_service.events().delete(
                        calendarId='primary',
                        eventId=appointment.google_event_id
                    ).execute()
                except Exception as e:
                    logger.error(f"Error deleting from patient's calendar: {str(e)}")

        # Delete from system calendar
        system_service = get_google_calendar_service(None)
        if system_service:
            try:
                system_service.events().delete(
                    calendarId='primary',
                    eventId=appointment.google_event_id
                ).execute()
            except Exception as e:
                logger.error(f"Error deleting from system calendar: {str(e)}")

        # Clear google_event_id
        appointment.google_event_id = None
        appointment.save()

    except Exception as e:
        logger.error(f"Error deleting Google Calendar event for appointment {appointment_id}: {str(e)}")
        raise

@shared_task
def send_appointment_reminder(appointment_id):
    """
    Send reminder 24 hours before the appointment.
    """
    try:
        appointment = Appointment.objects.get(id=appointment_id)
        if appointment.appointment_type == 'booking':
            subject = "Appointment Reminder"
            message = f"Reminder: You have an appointment with {appointment.doctor.get_full_name()} on {appointment.start_time}."
            if appointment.mode == 'video_call' and appointment.meeting_link:
                message += f"\n\nJoin the video call at: {appointment.meeting_link}"
            send_email(appointment.patient.email, subject, message)
            logger.info(f"Sent reminder for appointment {appointment_id}")
    except Exception as e:
        logger.error(f"Error sending reminder for appointment {appointment_id}: {str(e)}")
        raise

@shared_task
def send_payment_confirmation_email(appointment_id, payment_amount=None, recipient_type='patient'):
    """
    Send payment confirmation email for appointment payment.
    
    Args:
        appointment_id: The appointment ID
        payment_amount: Payment amount (optional)
        recipient_type: Either 'patient' or 'doctor' or 'both'
    """
    try:
        appointment = Appointment.objects.get(id=appointment_id)
        
        if recipient_type in ['patient', 'both'] and appointment.patient:
            # Send email to patient
            subject = "Payment Confirmation - Appointment Booking"
            message = f"Dear {appointment.patient.get_full_name()},\n\n"
            message += f"Your payment for the appointment with Dr. {appointment.doctor.get_full_name()} has been confirmed.\n\n"
            message += f"Appointment Details:\n"
            message += f"- Date & Time: {appointment.start_time}\n"
            message += f"- Doctor: Dr. {appointment.doctor.get_full_name()}\n"
            if payment_amount:
                message += f"- Amount Paid: ${payment_amount}\n"
            if appointment.mode == 'video_call' and appointment.meeting_link:
                message += f"- Meeting Link: {appointment.meeting_link}\n"
            message += f"\nThank you for using our service.\n\nBest regards,\nThe Ravid Team"
            
            send_email(appointment.patient.email, subject, message)
            logger.info(f"Sent payment confirmation email to patient for appointment {appointment_id}")
        
        if recipient_type in ['doctor', 'both'] and appointment.doctor:
            # Send email to doctor
            subject = "Payment Received - Appointment Booking"
            message = f"Dear Dr. {appointment.doctor.get_full_name()},\n\n"
            message += f"Payment has been received for your appointment with {appointment.patient.get_full_name()}.\n\n"
            message += f"Appointment Details:\n"
            message += f"- Date & Time: {appointment.start_time}\n"
            message += f"- Patient: {appointment.patient.get_full_name()}\n"
            if payment_amount:
                message += f"- Amount: ${payment_amount}\n"
            message += f"\nThe appointment is confirmed and ready.\n\nBest regards,\nThe Ravid Team"
            
            send_email(appointment.doctor.email, subject, message)
            logger.info(f"Sent payment confirmation email to doctor for appointment {appointment_id}")
            
    except Appointment.DoesNotExist:
        logger.error(f"Appointment {appointment_id} not found for payment confirmation email")
        raise
    except Exception as e:
        logger.error(f"Error sending payment confirmation email for appointment {appointment_id}: {str(e)}")
        raise