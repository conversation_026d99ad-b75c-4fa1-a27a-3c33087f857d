# Generated by Django 5.0.9 on 2025-03-31 13:32

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0002_appointment_cancellation_reason_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='doctoravailability',
            unique_together={('doctor', 'day_of_week')},
        ),
        migrations.AlterUniqueTogether(
            name='doctoravailabilityoverride',
            unique_together={('doctor', 'date')},
        ),
        migrations.CreateModel(
            name='DoctorAvailabilityOverrideSlot',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('override', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_slots', to='appointments.doctoravailabilityoverride')),
            ],
            options={
                'ordering': ['start_time'],
            },
        ),
        migrations.CreateModel(
            name='DoctorTimeSlot',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('availability', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_slots', to='appointments.doctoravailability')),
            ],
            options={
                'ordering': ['start_time'],
            },
        ),
        migrations.RemoveField(
            model_name='doctoravailability',
            name='break_between_slots',
        ),
        migrations.RemoveField(
            model_name='doctoravailability',
            name='end_time',
        ),
        migrations.RemoveField(
            model_name='doctoravailability',
            name='slot_duration',
        ),
        migrations.RemoveField(
            model_name='doctoravailability',
            name='start_time',
        ),
        migrations.RemoveField(
            model_name='doctoravailabilityoverride',
            name='end_time',
        ),
        migrations.RemoveField(
            model_name='doctoravailabilityoverride',
            name='start_time',
        ),
    ]
