# Generated by Django 5.0.9 on 2025-06-16 12:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0014_appointment_consultation_service_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='appointment',
            name='consultation_fee',
            field=models.IntegerField(blank=True, help_text='Consultation fee in cents for direct payment appointments', null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='payment_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('failed', 'Failed'), ('refunded', 'Refunded')], default='pending', help_text='Payment status for direct payment appointments', max_length=20),
        ),
    ]
