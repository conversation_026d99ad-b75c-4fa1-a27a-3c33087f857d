# Generated by Django 5.0.9 on 2025-06-16 11:27

import django.core.validators
import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0013_doctorcontactinfo_is_primary_email_appointment_active_and_more'),
        ('billing', '0016_remove_enterpriseservice_enterprise_and_more'),
        ('content_management', '0017_alter_service_service_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='appointment',
            name='consultation_service',
            field=models.ForeignKey(blank=True, help_text='Selected consultation service package (future use)', limit_choices_to={'service_type': 'CONSULTATION'}, null=True, on_delete=django.db.models.deletion.SET_NULL, to='content_management.service'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='payment_transfer',
            field=models.ForeignKey(blank=True, help_text='Associated payment transfer for direct payment appointments', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='appointments', to='billing.usertransfer'),
        ),
        migrations.CreateModel(
            name='DoctorConsultationProfile',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('consultation_fee', models.IntegerField(default=5000, help_text='Consultation fee in cents (e.g., 5000 = $50.00)', validators=[django.core.validators.MinValueValidator(500), django.core.validators.MaxValueValidator(50000)])),
                ('consultation_duration', models.IntegerField(default=30, help_text='Default consultation duration in minutes', validators=[django.core.validators.MinValueValidator(15), django.core.validators.MaxValueValidator(180)])),
                ('accepts_telemedicine', models.BooleanField(default=False, help_text='Whether doctor accepts telemedicine appointments')),
                ('available_hours', models.JSONField(blank=True, default=dict, help_text='Available hours configuration (JSON format)')),
                ('bio', models.TextField(blank=True, help_text="Doctor's professional bio for telemedicine", max_length=1000)),
                ('specializations', models.JSONField(blank=True, default=list, help_text='List of medical specializations')),
                ('languages', models.JSONField(blank=True, default=list, help_text='Languages spoken by the doctor')),
                ('is_active', models.BooleanField(default=True, help_text='Whether consultation profile is active')),
                ('stripe_account_setup', models.BooleanField(default=False, help_text='Whether doctor has completed Stripe Connect setup')),
                ('user', models.OneToOneField(help_text='Doctor user account', limit_choices_to={'role__name': 'doctor'}, on_delete=django.db.models.deletion.CASCADE, related_name='consultation_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Doctor Consultation Profile',
                'verbose_name_plural': 'Doctor Consultation Profiles',
                'ordering': ['user__first_name', 'user__last_name'],
            },
        ),
        migrations.CreateModel(
            name='ConsultationPackage',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('custom_price', models.IntegerField(blank=True, help_text='Custom price override in cents (optional)', null=True, validators=[django.core.validators.MinValueValidator(500), django.core.validators.MaxValueValidator(50000)])),
                ('is_available', models.BooleanField(default=True, help_text='Whether this package is available for booking')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this package', max_length=500)),
                ('doctor', models.ForeignKey(help_text='Doctor offering this package', limit_choices_to={'role__name': 'doctor'}, on_delete=django.db.models.deletion.CASCADE, related_name='consultation_packages', to=settings.AUTH_USER_MODEL)),
                ('service', models.ForeignKey(help_text='Associated consultation service', limit_choices_to={'service_type': 'CONSULTATION'}, on_delete=django.db.models.deletion.CASCADE, related_name='consultation_packages', to='content_management.service')),
            ],
            options={
                'verbose_name': 'Consultation Package',
                'verbose_name_plural': 'Consultation Packages',
                'ordering': ['service__order', 'service__price'],
                'unique_together': {('doctor', 'service')},
            },
        ),
    ]
