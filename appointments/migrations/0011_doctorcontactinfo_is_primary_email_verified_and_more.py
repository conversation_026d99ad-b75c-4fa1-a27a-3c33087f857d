# Generated by Django 5.0.9 on 2025-05-15 13:13

import django.db.models.deletion
import uuid
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0010_alter_doctoravailability_mode'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='doctorcontactinfo',
            name='is_primary_email_verified',
            field=models.BooleanField(default=False, help_text='Whether the primary email has been verified'),
        ),
        migrations.AddField(
            model_name='doctorcontactinfo',
            name='is_secondary_email_verified',
            field=models.BooleanField(default=False, help_text='Whether the secondary email has been verified'),
        ),
        migrations.CreateModel(
            name='PendingDoctorContactInfo',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('primary_email', models.EmailField(help_text='Primary email for receiving patient messages', max_length=255)),
                ('secondary_email', models.EmailField(blank=True, help_text='Secondary email for receiving patient messages', max_length=255, null=True)),
                ('primary_email_token', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Token for verifying primary email')),
                ('secondary_email_token', models.UUIDField(blank=True, default=uuid.uuid4, editable=False, help_text='Token for verifying secondary email', null=True)),
                ('is_primary_email_active', models.BooleanField(default=True)),
                ('is_secondary_email_active', models.BooleanField(default=True)),
                ('is_primary_email_verified', models.BooleanField(default=False)),
                ('is_secondary_email_verified', models.BooleanField(default=False)),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='pending_contact_info', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
