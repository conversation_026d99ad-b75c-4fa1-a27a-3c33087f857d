# Generated by Django 5.0.9 on 2025-03-28 09:19

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0001_initial'),
        ('clinic', '0004_clinicadmin'),
        ('enterprise', '0005_alter_enterprise_user'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='appointment',
            name='cancellation_reason',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='cancelled_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cancelled_appointments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='appointment',
            name='meeting_link',
            field=models.URLField(blank=True, help_text='Video call link if applicable', null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='mode',
            field=models.CharField(choices=[('in_person', 'In-Person'), ('video_call', 'Video Call'), ('phone_call', 'Phone Call')], default='in_person', max_length=20),
        ),
        migrations.AddField(
            model_name='appointment',
            name='qr_code',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='appointment',
            name='reminder_sent',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='appointment',
            name='slot_reference',
            field=models.CharField(blank=True, help_text="Reference to the specific availability slot or override (e.g., 'availability:123' or 'override:456')", max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='break_between_slots',
            field=models.IntegerField(default=5, help_text='Break time between slots in minutes'),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='slot_duration',
            field=models.IntegerField(default=30, help_text='Duration of each appointment slot in minutes'),
        ),
        migrations.CreateModel(
            name='DoctorAvailabilityOverride',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('date', models.DateField()),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('clinic', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='doctor_overrides', to='clinic.clinic')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_overrides', to=settings.AUTH_USER_MODEL)),
                ('enterprise', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='doctor_overrides', to='enterprise.enterprise')),
            ],
            options={
                'unique_together': {('doctor', 'date', 'start_time', 'end_time')},
            },
        ),
    ]
