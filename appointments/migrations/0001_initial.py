# Generated by Django 5.0.9 on 2025-03-24 10:34

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('clinic', '0004_clinicadmin'),
        ('enterprise', '0005_alter_enterprise_user'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('title', models.CharField(blank=True, help_text="Title of the event (e.g., 'Check-up' or 'Personal Meeting')", max_length=255)),
                ('appointment_type', models.CharField(choices=[('manual', 'Manual Event'), ('booking', 'Doctor Booking')], default='manual', max_length=20)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField()),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('canceled', 'Canceled'), ('completed', 'Completed')], default='pending', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('google_event_id', models.CharField(blank=True, max_length=255, null=True)),
                ('location', models.CharField(blank=True, help_text='Physical or virtual location', max_length=255)),
                ('is_all_day', models.BooleanField(default=False, help_text='All-day event flag')),
                ('clinic', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='clinic.clinic')),
                ('creator', models.ForeignKey(help_text='User who created this appointment', on_delete=django.db.models.deletion.CASCADE, related_name='created_appointments', to=settings.AUTH_USER_MODEL)),
                ('doctor', models.ForeignKey(blank=True, help_text='Doctor (null for manual events)', limit_choices_to={'role__name': 'doctor'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='appointments_as_doctor', to=settings.AUTH_USER_MODEL)),
                ('enterprise', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='enterprise.enterprise')),
                ('patient', models.ForeignKey(blank=True, help_text='Patient (same as creator for manual, different for booking)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='appointments_as_patient', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='DoctorAvailability',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('day_of_week', models.CharField(choices=[('monday', 'Monday'), ('tuesday', 'Tuesday'), ('wednesday', 'Wednesday'), ('thursday', 'Thursday'), ('friday', 'Friday'), ('saturday', 'Saturday'), ('sunday', 'Sunday')], max_length=10)),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('clinic', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='doctor_availabilities', to='clinic.clinic')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availabilities', to=settings.AUTH_USER_MODEL)),
                ('enterprise', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='doctor_availabilities', to='enterprise.enterprise')),
            ],
            options={
                'unique_together': {('doctor', 'day_of_week', 'start_time', 'end_time')},
            },
        ),
    ]
