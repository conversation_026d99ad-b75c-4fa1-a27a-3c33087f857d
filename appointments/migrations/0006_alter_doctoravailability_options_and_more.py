# Generated by Django 5.0.9 on 2025-04-06 04:25

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0005_alter_doctormessage_doctor_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='doctoravailability',
            name='start_date',
            field=models.DateField(blank=True, help_text='Start date of availability', null=True),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='start_time',
            field=models.TimeField(blank=True, help_text='Start time of availability', null=True),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='end_date',
            field=models.DateField(blank=True, help_text='End date of availability (null for indefinite)', null=True),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='end_time',
            field=models.TimeField(blank=True, help_text='End time of availability', null=True),
        ),
        
        migrations.AddField(
            model_name='doctoravailability',
            name='recurrence_count',
            field=models.PositiveIntegerField(blank=True, help_text='Number of occurrences for recurrence', null=True),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='recurrence_days',
            field=models.CharField(blank=True, help_text="Comma-separated days for weekly recurrence (e.g., 'monday,wednesday,friday')", max_length=50),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='recurrence_end_date',
            field=models.DateField(blank=True, help_text='End date for recurrence', null=True),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='recurrence_interval',
            field=models.PositiveIntegerField(default=1, help_text='Interval for recurrence (e.g., every 2 weeks)'),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='recurrence_month_day',
            field=models.PositiveIntegerField(blank=True, help_text='Day of month for monthly recurrence', null=True),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='recurrence_type',
            field=models.CharField(choices=[('none', 'No Recurrence'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], default='none', max_length=20),
        ),
        migrations.AddField(
            model_name='doctoravailability',
            name='title',
            field=models.CharField(blank=True, help_text='Title of the availability', max_length=255),
        ),
        migrations.DeleteModel(
            name='DoctorTimeSlot',
        ),
        migrations.AlterModelOptions(
            name='doctoravailability',
            options={'ordering': ['start_date', 'start_time']},
        ),
        migrations.AlterUniqueTogether(
            name='doctoravailability',
            unique_together={('doctor', 'start_date', 'start_time', 'end_time', 'recurrence_type')},
        ),
        migrations.RemoveField(
            model_name='doctoravailability',
            name='day_of_week',
        ),
    ]
