# Generated by Django 5.0.9 on 2025-05-20 16:12

import django.db.models.deletion
import uuid
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0012_appointment_direct_payment_appointment_insurance'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='doctorcontactinfo',
            name='is_primary_email_appointment_active',
            field=models.BooleanField(default=True, help_text='Whether to send notifications to primary appointment email'),
        ),
        migrations.AddField(
            model_name='doctorcontactinfo',
            name='is_primary_email_appointment_verified',
            field=models.Bo<PERSON>an<PERSON>ield(default=False, help_text='Whether the primary appointment email has been verified'),
        ),
        migrations.AddField(
            model_name='doctorcontactinfo',
            name='is_secondary_email_appointment_active',
            field=models.<PERSON><PERSON>anField(default=True, help_text='Whether to send notifications to secondary appointment email'),
        ),
        migrations.AddField(
            model_name='doctorcontactinfo',
            name='is_secondary_email_appointment_verified',
            field=models.<PERSON><PERSON>anField(default=False, help_text='Whether the secondary appointment email has been verified'),
        ),
        migrations.AddField(
            model_name='doctorcontactinfo',
            name='primary_email_appointment',
            field=models.EmailField(blank=True, help_text='Primary email for receiving booking appointment notifications', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='doctorcontactinfo',
            name='secondary_email_appointment',
            field=models.EmailField(blank=True, help_text='Secondary email for receiving booking appointment notifications', max_length=255, null=True),
        ),
        migrations.CreateModel(
            name='PendingDoctorAppointmentEmail',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('primary_email_appointment', models.EmailField(help_text='Primary email for receiving booking appointment notifications', max_length=255)),
                ('secondary_email_appointment', models.EmailField(blank=True, help_text='Secondary email for receiving booking appointment notifications', max_length=255, null=True)),
                ('primary_email_appointment_token', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Token for verifying primary appointment email')),
                ('secondary_email_appointment_token', models.UUIDField(blank=True, default=uuid.uuid4, editable=False, help_text='Token for verifying secondary appointment email', null=True)),
                ('is_primary_email_appointment_active', models.BooleanField(default=True)),
                ('is_secondary_email_appointment_active', models.BooleanField(default=True)),
                ('is_primary_email_appointment_verified', models.BooleanField(default=False)),
                ('is_secondary_email_appointment_verified', models.BooleanField(default=False)),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='pending_appointment_email', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
