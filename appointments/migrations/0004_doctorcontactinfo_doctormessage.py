# Generated by Django 5.0.9 on 2025-04-02 14:34

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0003_alter_doctoravailability_unique_together_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DoctorContactInfo',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('primary_email', models.EmailField(help_text='Primary email for receiving patient messages', max_length=255)),
                ('secondary_email', models.EmailField(blank=True, help_text='Secondary email for receiving patient messages', max_length=255, null=True)),
                ('is_primary_email_active', models.<PERSON>oleanField(default=True, help_text='Whether to send notifications to primary email')),
                ('is_secondary_email_active', models.<PERSON><PERSON>an<PERSON>ield(default=True, help_text='Whether to send notifications to secondary email')),
                ('doctor', models.OneToOneField(limit_choices_to={'role__name': 'doctor'}, on_delete=django.db.models.deletion.CASCADE, related_name='contact_info', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='DoctorMessage',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('subject', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('is_reply', models.BooleanField(default=False)),
                ('appointment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='appointments.appointment')),
                ('doctor', models.ForeignKey(limit_choices_to={'role__name': 'doctor'}, on_delete=django.db.models.deletion.CASCADE, related_name='doctor_appointment_messages', to=settings.AUTH_USER_MODEL)),
                ('parent_message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='appointments.doctormessage')),
                ('patient', models.ForeignKey(limit_choices_to={'role__name': 'patient'}, on_delete=django.db.models.deletion.CASCADE, related_name='patient_appointment_messages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
