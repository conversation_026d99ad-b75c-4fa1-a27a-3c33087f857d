# Generated by Django 5.0.9 on 2025-04-22 16:10

import django.db.models.deletion
import uuid6
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0006_alter_doctoravailability_options_and_more'),
        ('upload', '0002_uploadedfile_file_type_uploadedfile_file_url'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppointmentAttachment',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('description', models.CharField(blank=True, help_text='Optional description of the attachment', max_length=255)),
                ('appointment', models.ForeignKey(help_text='Appointment this file is attached to', on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='appointments.appointment')),
                ('file', models.ForeignKey(help_text='Uploaded file', on_delete=django.db.models.deletion.CASCADE, related_name='appointment_attachments', to='upload.uploadedfile')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
