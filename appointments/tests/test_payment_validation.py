"""
Test cases for appointment payment method validation.
Tests the mutual exclusivity of insurance and direct_payment fields.
"""

import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from appointments.models import Appointment
from appointments.api.serializers.appointment import AppointmentSerializer
from datetime import datetime, timedelta
from django.utils import timezone

User = get_user_model()


class AppointmentPaymentValidationTestCase(TestCase):
    """Test payment method validation at model level"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.doctor = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Set up basic appointment data
        self.appointment_data = {
            'creator': self.user,
            'patient': self.user,
            'doctor': self.doctor,
            'title': 'Test Appointment',
            'appointment_type': 'booking',
            'start_time': timezone.now() + timedelta(days=1),
            'end_time': timezone.now() + timedelta(days=1, hours=1),
            'status': 'pending'
        }
    
    def test_appointment_with_insurance_only_valid(self):
        """Test that appointment with only insurance=True is valid"""
        appointment = Appointment(**self.appointment_data, insurance=True, direct_payment=False)
        try:
            appointment.full_clean()
            appointment.save()
            self.assertTrue(appointment.insurance)
            self.assertFalse(appointment.direct_payment)
        except ValidationError:
            self.fail("Appointment with insurance only should be valid")
    
    def test_appointment_with_direct_payment_only_valid(self):
        """Test that appointment with only direct_payment=True is valid"""
        appointment = Appointment(**self.appointment_data, insurance=False, direct_payment=True)
        try:
            appointment.full_clean()
            appointment.save()
            self.assertFalse(appointment.insurance)
            self.assertTrue(appointment.direct_payment)
        except ValidationError:
            self.fail("Appointment with direct payment only should be valid")
    
    def test_appointment_with_both_payment_methods_invalid(self):
        """Test that appointment with both insurance=True and direct_payment=True is invalid"""
        appointment = Appointment(**self.appointment_data, insurance=True, direct_payment=True)
        with self.assertRaises(ValidationError) as context:
            appointment.full_clean()
        
        self.assertIn("cannot use both insurance and direct payment", str(context.exception))
    
    def test_appointment_with_no_payment_methods_valid(self):
        """Test that appointment with both payment methods False is valid"""
        appointment = Appointment(**self.appointment_data, insurance=False, direct_payment=False)
        try:
            appointment.full_clean()
            appointment.save()
            self.assertFalse(appointment.insurance)
            self.assertFalse(appointment.direct_payment)
        except ValidationError:
            self.fail("Appointment with no payment methods should be valid")
    
    def test_manual_appointment_payment_fields_ignored(self):
        """Test that manual appointments don't validate payment fields"""
        manual_data = self.appointment_data.copy()
        manual_data['appointment_type'] = 'manual'
        manual_data['doctor'] = None  # Manual appointments don't have doctors
        
        appointment = Appointment(**manual_data, insurance=True, direct_payment=True)
        try:
            appointment.full_clean()
            # Manual appointments should not validate payment exclusivity
        except ValidationError as e:
            # Should only fail on other validations, not payment method conflict
            self.assertNotIn("cannot use both insurance and direct payment", str(e))


class AppointmentPaymentSerializerTestCase(APITestCase):
    """Test payment method validation at serializer level"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.doctor = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.client.force_authenticate(user=self.user)
        
        # Basic appointment data for API requests
        self.appointment_data = {
            'title': 'Test Appointment',
            'appointment_type': 'booking',
            'doctor_id': self.doctor.id,
            'start_time': (timezone.now() + timedelta(days=1)).isoformat(),
            'end_time': (timezone.now() + timedelta(days=1, hours=1)).isoformat(),
            'mode': 'in_person'
        }
    
    def test_serializer_validates_insurance_only(self):
        """Test serializer accepts insurance=True, direct_payment=False"""
        data = self.appointment_data.copy()
        data.update({'insurance': True, 'direct_payment': False})
        
        serializer = AppointmentSerializer(data=data, context={'request': self.client.request()})
        # Note: This test may need adjustment based on actual serializer validation logic
    
    def test_serializer_validates_direct_payment_only(self):
        """Test serializer accepts insurance=False, direct_payment=True"""
        data = self.appointment_data.copy()
        data.update({'insurance': False, 'direct_payment': True})
        
        serializer = AppointmentSerializer(data=data, context={'request': self.client.request()})
        # Note: This test may need adjustment based on actual serializer validation logic
    
    def test_serializer_rejects_both_payment_methods(self):
        """Test serializer rejects insurance=True, direct_payment=True"""
        data = self.appointment_data.copy()
        data.update({'insurance': True, 'direct_payment': True})
        
        serializer = AppointmentSerializer(data=data, context={'request': self.client.request()})
        # Should raise validation error
        # Note: This test may need adjustment based on actual serializer validation logic


class AppointmentPaymentAPITestCase(APITestCase):
    """Test payment method validation through API endpoints"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.doctor = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.client.force_authenticate(user=self.user)
        
        self.appointment_data = {
            'title': 'Test Appointment',
            'appointment_type': 'booking',
            'doctor_id': self.doctor.id,
            'start_time': (timezone.now() + timedelta(days=1)).isoformat(),
            'end_time': (timezone.now() + timedelta(days=1, hours=1)).isoformat(),
            'mode': 'in_person'
        }
    
    def test_api_accepts_insurance_only(self):
        """Test API accepts appointment with insurance only"""
        data = self.appointment_data.copy()
        data.update({'insurance': True, 'direct_payment': False})
        
        response = self.client.post('/api/appointments/', data, format='json')
        # Should succeed (status may vary based on other validations)
    
    def test_api_accepts_direct_payment_only(self):
        """Test API accepts appointment with direct payment only"""
        data = self.appointment_data.copy()
        data.update({'insurance': False, 'direct_payment': True})
        
        response = self.client.post('/api/appointments/', data, format='json')
        # Should succeed (status may vary based on other validations)
    
    def test_api_rejects_both_payment_methods(self):
        """Test API rejects appointment with both payment methods"""
        data = self.appointment_data.copy()
        data.update({'insurance': True, 'direct_payment': True})
        
        response = self.client.post('/api/appointments/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Payment method conflict', str(response.data))
