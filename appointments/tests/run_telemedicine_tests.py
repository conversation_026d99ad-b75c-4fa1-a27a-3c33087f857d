#!/usr/bin/env python
"""
Script to run telemedicine payment tests
Usage: python manage.py shell < appointments/tests/run_telemedicine_tests.py
"""
import os
import sys
import django
from django.test.utils import get_runner
from django.conf import settings

def run_telemedicine_tests():
    """Run all telemedicine-related tests"""
    print("🏥 Running Telemedicine Payment Tests")
    print("=" * 50)
    
    # Import test modules
    from appointments.tests.test_telemedicine_payments import (
        TelemedicinePaymentModelTests,
        TelemedicinePaymentServiceTests,
        TelemedicineAPITests
    )
    
    from appointments.tests.test_payment_validation import (
        AppointmentPaymentValidationTestCase,
        AppointmentPaymentSerializerTestCase,
        AppointmentPaymentAPITestCase
    )
    
    try:
        from appointments.tests.test_telemedicine_integration import (
            TelemedicineIntegrationTestCase,
            TelemedicineAPIIntegrationTests,
            TelemedicinePerformanceTests
        )
        integration_tests_available = True
    except ImportError:
        integration_tests_available = False
        print("⚠️  Integration tests not available")
    
    # Test Discovery
    test_classes = [
        TelemedicinePaymentModelTests,
        TelemedicinePaymentServiceTests,
        TelemedicineAPITests,
        AppointmentPaymentValidationTestCase,
        AppointmentPaymentSerializerTestCase,
        AppointmentPaymentAPITestCase
    ]
    
    if integration_tests_available:
        test_classes.extend([
            TelemedicineIntegrationTestCase,
            TelemedicineAPIIntegrationTests,
            TelemedicinePerformanceTests
        ])
    
    # Run tests
    test_results = {}
    
    for test_class in test_classes:
        print(f"\n🧪 Running {test_class.__name__}")
        print("-" * 30)
        
        # Get test runner
        TestRunner = get_runner(settings)
        test_runner = TestRunner(verbosity=2, interactive=False)
        
        # Create test suite
        import unittest
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        
        # Run tests
        result = test_runner.run_tests([test_class])
        test_results[test_class.__name__] = result
        
        if result == 0:
            print(f"✅ {test_class.__name__} - PASSED")
        else:
            print(f"❌ {test_class.__name__} - FAILED")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    total_passed = sum(1 for result in test_results.values() if result == 0)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result == 0 else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {total_passed}/{total_tests} test classes passed")
    
    if total_passed == total_tests:
        print("🎉 All telemedicine tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False


def check_telemedicine_implementation():
    """Check current telemedicine implementation status"""
    print("\n🔍 Checking Telemedicine Implementation Status")
    print("=" * 50)
    
    implementation_status = {}
    
    # Check models
    try:
        from appointments.models import DoctorConsultationProfile, Appointment
        from billing.models import UserPaymentProfile, UserTransfer
        
        # Check DoctorConsultationProfile
        profile_fields = [
            'consultation_fee', 'consultation_duration', 'accepts_telemedicine',
            'is_active', 'stripe_account_setup', 'bio', 'specializations', 'languages'
        ]
        
        missing_fields = []
        for field in profile_fields:
            if not hasattr(DoctorConsultationProfile, field):
                missing_fields.append(field)
        
        if not missing_fields:
            implementation_status['DoctorConsultationProfile'] = "✅ IMPLEMENTED"
        else:
            implementation_status['DoctorConsultationProfile'] = f"⚠️  MISSING: {missing_fields}"
        
        # Check Appointment model extensions
        appointment_fields = ['payment_transfer', 'consultation_service', 'direct_payment', 'insurance']
        missing_appointment_fields = []
        for field in appointment_fields:
            if not hasattr(Appointment, field):
                missing_appointment_fields.append(field)
        
        if not missing_appointment_fields:
            implementation_status['Appointment Extensions'] = "✅ IMPLEMENTED"
        else:
            implementation_status['Appointment Extensions'] = f"⚠️  MISSING: {missing_appointment_fields}"
        
    except ImportError as e:
        implementation_status['Models'] = f"❌ IMPORT ERROR: {e}"
    
    # Check services
    try:
        from appointments.services.telemedicine_payment_service import TelemedicinePaymentService
        from billing.services.appointment_payment_service import AppointmentPaymentService
        
        # Check TelemedicinePaymentService methods
        telemedicine_service = TelemedicinePaymentService()
        required_methods = [
            'process_consultation_payment', 'confirm_consultation_payment',
            'get_consultation_fee', 'validate_doctor_availability'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(telemedicine_service, method):
                missing_methods.append(method)
        
        if not missing_methods:
            implementation_status['TelemedicinePaymentService'] = "✅ IMPLEMENTED"
        else:
            implementation_status['TelemedicinePaymentService'] = f"⚠️  MISSING: {missing_methods}"
        
        # Check AppointmentPaymentService
        appointment_service = AppointmentPaymentService()
        appointment_methods = ['create_appointment_payment_transfer', 'get_doctor_consultation_fee']
        
        missing_appointment_methods = []
        for method in appointment_methods:
            if not hasattr(appointment_service, method):
                missing_appointment_methods.append(method)
        
        if not missing_appointment_methods:
            implementation_status['AppointmentPaymentService'] = "✅ IMPLEMENTED"
        else:
            implementation_status['AppointmentPaymentService'] = f"⚠️  MISSING: {missing_appointment_methods}"
        
    except ImportError as e:
        implementation_status['Services'] = f"❌ IMPORT ERROR: {e}"
    
    # Check API endpoints
    try:
        from appointments.api.views.appointment import AppointmentViewSet
        
        # Check create_telemedicine_appointment endpoint
        if hasattr(AppointmentViewSet, 'create_telemedicine_appointment'):
            implementation_status['Telemedicine API'] = "✅ IMPLEMENTED"
        else:
            implementation_status['Telemedicine API'] = "⚠️  MISSING: create_telemedicine_appointment endpoint"
        
    except ImportError as e:
        implementation_status['API'] = f"❌ IMPORT ERROR: {e}"
    
    # Print status
    for component, status in implementation_status.items():
        print(f"{component}: {status}")
    
    # Overall assessment
    implemented_count = sum(1 for status in implementation_status.values() if status.startswith("✅"))
    total_count = len(implementation_status)
    
    print(f"\n📈 Implementation Progress: {implemented_count}/{total_count} components")
    
    if implemented_count == total_count:
        print("🎉 Telemedicine implementation is COMPLETE!")
        return True
    else:
        print("⚠️  Telemedicine implementation is PARTIAL. See missing components above.")
        return False


def validate_telemedicine_flow():
    """Validate the complete telemedicine flow"""
    print("\n🔄 Validating Telemedicine Flow")
    print("=" * 50)
    
    flow_steps = {
        "1. Doctor Setup": "DoctorConsultationProfile model",
        "2. Payment Profile": "UserPaymentProfile integration", 
        "3. Appointment Creation": "Telemedicine appointment API",
        "4. Payment Processing": "UserTransfer integration",
        "5. Payment Confirmation": "Webhook handling",
        "6. Meeting Link": "Google Meet integration"
    }
    
    validation_results = {}
    
    for step, description in flow_steps.items():
        try:
            if "Doctor Setup" in step:
                from appointments.models import DoctorConsultationProfile
                # Check if we can create a profile
                validation_results[step] = "✅ Model available"
                
            elif "Payment Profile" in step:
                from billing.models import UserPaymentProfile
                validation_results[step] = "✅ Model available"
                
            elif "Appointment Creation" in step:
                from appointments.api.views.appointment import AppointmentViewSet
                if hasattr(AppointmentViewSet, 'create_telemedicine_appointment'):
                    validation_results[step] = "✅ API endpoint available"
                else:
                    validation_results[step] = "⚠️  API endpoint missing"
                    
            elif "Payment Processing" in step:
                from billing.services.user_transfer_service import UserTransferService
                validation_results[step] = "✅ Service available"
                
            elif "Payment Confirmation" in step:
                from billing.services.webhook_service import WebhookService
                validation_results[step] = "✅ Webhook service available"
                
            elif "Meeting Link" in step:
                from appointments.services.google_meet import create_meet_link
                validation_results[step] = "✅ Google Meet integration available"
                
        except ImportError as e:
            validation_results[step] = f"❌ MISSING: {e}"
        except Exception as e:
            validation_results[step] = f"⚠️  ERROR: {e}"
    
    # Print validation results
    for step, result in validation_results.items():
        print(f"{step}: {result}")
    
    # Flow completeness
    complete_steps = sum(1 for result in validation_results.values() if result.startswith("✅"))
    total_steps = len(validation_results)
    
    print(f"\n📊 Flow Completeness: {complete_steps}/{total_steps} steps")
    
    if complete_steps == total_steps:
        print("🎉 Telemedicine flow is COMPLETE!")
        return True
    else:
        print("⚠️  Telemedicine flow has missing components.")
        return False


if __name__ == "__main__":
    print("🏥 Telemedicine Implementation Checker")
    print("=" * 60)
    
    # Step 1: Check implementation status
    implementation_complete = check_telemedicine_implementation()
    
    # Step 2: Validate flow
    flow_complete = validate_telemedicine_flow()
    
    # Step 3: Run tests if implementation looks good
    if implementation_complete and flow_complete:
        print("\n🧪 Implementation looks good. Running tests...")
        tests_passed = run_telemedicine_tests()
        
        if tests_passed:
            print("\n🎉 ALL TELEMEDICINE SYSTEMS ARE WORKING!")
        else:
            print("\n⚠️  Tests failed. Implementation needs fixes.")
    else:
        print("\n⚠️  Implementation incomplete. Please fix missing components before running tests.")
        print("\n📋 TO FIX:")
        print("1. Ensure all models are properly migrated")
        print("2. Check service imports and implementations")
        print("3. Verify API endpoints are registered")
        print("4. Test with: python manage.py test appointments.tests.test_telemedicine_payments") 