"""
Test cases for doctor availability API user ID masking.
Only public APIs should mask user IDs with custom_url_username.
"""
import json
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from accounts.models import Role
from appointments.models import DoctorAvailability
from datetime import date, time

User = get_user_model()


class AvailabilityAPIMaskingTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create doctor role
        self.doctor_role = Role.objects.create(name='doctor')
        
        # Create a doctor user with custom_url_username
        self.doctor = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='<PERSON>',
            last_name='Doe',
            custom_url_username='john.doe.1234',
            role=self.doctor_role
        )
        
        # Create a patient user
        self.patient = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='<PERSON>',
            last_name='<PERSON>',
            custom_url_username='jane.smith.5678'
        )
        
        # Create doctor availability
        self.availability = DoctorAvailability.objects.create(
            doctor=self.doctor,
            title='Test Availability',
            start_date=date(2025, 6, 20),
            end_date=date(2025, 6, 20),
            start_time=time(9, 0),
            end_time=time(17, 0),
            mode='video_call,in_person',
            need_payment=True,
            is_active=True
        )

    def test_public_available_slots_masks_doctor_id(self):
        """Test that public available_slots API masks doctor_id with custom_url_username"""
        url = reverse('availabilities-available-slots')
        params = {
            'doctor_id': self.doctor.custom_url_username,
            'start_date': '2025-06-20',
            'end_date': '2025-06-20'
        }
        
        # Make request without authentication (public API)
        response = self.client.get(url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Verify doctor_id is masked with custom_url_username
        self.assertEqual(data['doctor_id'], self.doctor.custom_url_username)
        self.assertNotEqual(data['doctor_id'], str(self.doctor.id))

    def test_public_by_doctor_masks_doctor_id(self):
        """Test that public by_doctor API masks doctor_id with custom_url_username"""
        url = reverse('availabilities-by-doctor')
        params = {
            'doctor': self.doctor.custom_url_username
        }
        
        # Make request without authentication (public API)
        response = self.client.get(url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Verify doctor_id in availabilities is masked
        self.assertTrue(len(data['availabilities']) > 0)
        availability_data = data['availabilities'][0]
        self.assertEqual(availability_data['doctor_id'], self.doctor.custom_url_username)
        self.assertNotEqual(availability_data['doctor_id'], str(self.doctor.id))

    def test_authenticated_get_available_slots_uses_internal_id(self):
        """Test that authenticated get_available_slots API uses internal doctor UUID"""
        # Authenticate as patient
        self.client.force_authenticate(user=self.patient)
        
        url = reverse('availabilities-get-available-slots')
        params = {
            'doctor_id': str(self.doctor.id),  # Use internal UUID
            'start_date': '2025-06-20',
            'end_date': '2025-06-20',
            'mode': 'video_call'
        }
        
        response = self.client.get(url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # This API returns array of slots, not object with doctor_id
        self.assertIsInstance(data, list)

    def test_authenticated_list_uses_internal_id(self):
        """Test that authenticated list API uses internal doctor UUID"""
        # Authenticate as doctor
        self.client.force_authenticate(user=self.doctor)
        
        url = reverse('availabilities-list')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Verify doctor_id is NOT masked (uses internal UUID)
        if 'results' in data:
            availabilities = data['results']
        else:
            availabilities = data
            
        self.assertTrue(len(availabilities) > 0)
        availability_data = availabilities[0]
        self.assertEqual(availability_data['doctor_id'], str(self.doctor.id))
        self.assertNotEqual(availability_data['doctor_id'], self.doctor.custom_url_username)

    def test_public_api_accepts_both_custom_url_and_uuid(self):
        """Test that public APIs accept both custom_url_username and UUID as fallback"""
        url = reverse('availabilities-available-slots')
        
        # Test with custom_url_username
        params = {
            'doctor_id': self.doctor.custom_url_username,
            'start_date': '2025-06-20',
            'end_date': '2025-06-20'
        }
        response = self.client.get(url, params)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test with UUID (fallback)
        params = {
            'doctor_id': str(self.doctor.id),
            'start_date': '2025-06-20',
            'end_date': '2025-06-20'
        }
        response = self.client.get(url, params)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Both should return the same masked doctor_id
        data = response.json()
        self.assertEqual(data['doctor_id'], self.doctor.custom_url_username)
