"""
Comprehensive Integration Tests for Telemedicine Payments
Tests the complete flow from guide TELEMEDICINE_PAYMENTS_GUIDE.md
"""
import pytest
from unittest.mock import patch, MagicMock
from django.test import TestCase, TransactionTestCase
from django.core.exceptions import ValidationError
from rest_framework.test import APITestCase
from rest_framework import status
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from accounts.models import CustomUser
from appointments.models import Appointment, DoctorConsultationProfile
from appointments.services.telemedicine_payment_service import TelemedicinePaymentService
from billing.models import UserPaymentProfile, UserTransfer
from billing.services.appointment_payment_service import AppointmentPaymentService
from billing.services.user_transfer_service import UserTransferService
from billing.exceptions import PaymentError
from roles.models import Role


class TelemedicineIntegrationTestCase(TestCase):
    """Test telemedicine integration following the complete flow"""
    
    def setUp(self):
        """Setup test data"""
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create doctor user
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Dr. John',
            last_name='Smith',
            role=self.doctor_role
        )
        
        # Create patient user
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Doe',
            role=self.patient_role
        )
        
        # Create doctor consultation profile (Step 1 from guide)
        self.consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=15000,  # $150.00 - premium consultation
            consultation_duration=60,  # 1 hour
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True,
            bio="Specialist in internal medicine with 15+ years experience",
            specializations=["Internal Medicine", "Cardiology", "Telemedicine"],
            languages=["English", "Spanish", "Vietnamese"]
        )
        
        # Create doctor payment profile (Step 2 from guide)
        self.doctor_payment_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor_premium',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True,
            verification_date=timezone.now() - timedelta(days=30)
        )
        
        # Create patient payment profile
        self.patient_payment_profile = UserPaymentProfile.objects.create(
            user=self.patient,
            stripe_account_id='acct_test_patient_premium'
        )
        
        # Initialize services
        self.telemedicine_service = TelemedicinePaymentService()
        self.appointment_service = AppointmentPaymentService()

    def test_step1_doctor_setup_validation(self):
        """Test Step 1: Doctor Setup Process - Consultation Profile"""
        # Test consultation profile creation
        self.assertEqual(self.consultation_profile.user, self.doctor)
        self.assertEqual(self.consultation_profile.consultation_fee, 15000)
        self.assertEqual(self.consultation_profile.get_consultation_fee_usd(), 150.0)
        self.assertEqual(self.consultation_profile.get_consultation_fee_display(), "$150.00")
        
        # Test specializations display
        expected_specializations = "Internal Medicine, Cardiology, Telemedicine"
        self.assertEqual(self.consultation_profile.get_specializations_display(), expected_specializations)
        
        # Test availability validation
        self.assertTrue(self.consultation_profile.is_available_for_telemedicine())
        
        # Test doctor availability through service
        result = self.telemedicine_service.validate_doctor_availability(self.doctor)
        self.assertTrue(result)

    def test_step2_doctor_payment_profile_validation(self):
        """Test Step 2: Doctor Payment Profile Setup"""
        # Test payment profile setup
        self.assertTrue(self.doctor_payment_profile.can_receive_payments())
        self.assertTrue(self.doctor_payment_profile.charges_enabled)
        self.assertTrue(self.doctor_payment_profile.payouts_enabled)
        self.assertTrue(self.doctor_payment_profile.is_verified)
        
        # Test payment profile validation through service
        fee = self.telemedicine_service.get_consultation_fee(self.doctor)
        self.assertEqual(fee, 15000)

    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.create')
    @patch('billing.services.user_transfer_service.stripe.Account.retrieve')
    def test_step3_consultation_booking_with_payment(self, mock_account, mock_payment):
        """Test Step 3: Patient Payment Process - Consultation Booking"""
        # Mock Stripe responses
        mock_account.return_value = MagicMock(
            charges_enabled=True,
            payouts_enabled=True
        )
        
        mock_payment.return_value = MagicMock(
            id='pi_telemedicine_test_premium',
            client_secret='pi_telemedicine_test_premium_secret_abc123'
        )
        
        # Create telemedicine appointment
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=2),
            end_time=timezone.now() + timedelta(hours=3),
            title="Premium Telemedicine Consultation"
        )
        
        # Process consultation payment (Step 3.1 from guide)
        transfer, client_secret = self.telemedicine_service.process_consultation_payment(
            patient=self.patient,
            doctor=self.doctor,
            appointment=appointment
        )
        
        # Verify payment creation
        self.assertIsNotNone(transfer)
        self.assertIsNotNone(client_secret)
        self.assertEqual(transfer.sender, self.patient)
        self.assertEqual(transfer.receiver, self.doctor)
        self.assertEqual(transfer.amount, 15000)
        self.assertEqual(transfer.transfer_type, 'payment')
        self.assertEqual(transfer.status, 'pending')
        
        # Verify appointment updated
        appointment.refresh_from_db()
        self.assertEqual(appointment.payment_transfer, transfer)
        self.assertEqual(appointment.payment_status, 'pending')
        
        # Verify transfer metadata
        self.assertEqual(transfer.metadata.get('appointment_id'), str(appointment.id))
        self.assertIn('Telemedicine consultation', transfer.message)
        
        return appointment, transfer, client_secret

    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.create')
    @patch('billing.services.user_transfer_service.stripe.Account.retrieve')
    def test_step4_payment_confirmation(self, mock_account, mock_payment):
        """Test Step 4: Payment Confirmation Process"""
        # Setup from previous step
        appointment, transfer, client_secret = self.test_step3_consultation_booking_with_payment()
        
        # Simulate successful Stripe payment
        transfer.status = 'completed'
        transfer.stripe_payment_intent_id = 'pi_telemedicine_test_premium'
        transfer.save()
        
        # Confirm consultation payment (Step 3.2 from guide)
        updated_appointment = self.telemedicine_service.confirm_consultation_payment(appointment)
        
        # Verify appointment status updated
        self.assertEqual(updated_appointment.status, 'confirmed')
        self.assertEqual(updated_appointment.payment_status, 'paid')
        
        # Verify transfer status
        transfer.refresh_from_db()
        self.assertEqual(transfer.status, 'completed')

    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.create')
    @patch('billing.services.user_transfer_service.stripe.Account.retrieve')
    def test_step5_payment_summary_and_reporting(self, mock_account, mock_payment):
        """Test Step 5: Payment Summary and Reporting"""
        # Setup completed payment
        appointment, transfer, client_secret = self.test_step3_consultation_booking_with_payment()
        
        # Complete the payment
        transfer.status = 'completed'
        transfer.platform_fee_amount = 450  # 3% of $150
        transfer.save()
        
        # Get payment summary
        summary = self.telemedicine_service.get_payment_summary(appointment)
        
        # Verify summary structure
        self.assertEqual(summary['status'], 'completed')
        self.assertEqual(summary['amount_cents'], 15000)
        self.assertEqual(summary['amount_usd'], 150.0)
        self.assertEqual(summary['platform_fee_cents'], 450)
        self.assertEqual(summary['platform_fee_usd'], 4.5)
        self.assertEqual(summary['consultation_duration'], 60)
        self.assertEqual(summary['transfer_id'], str(transfer.id))

    def test_error_handling_doctor_not_available(self):
        """Test error handling when doctor is not available"""
        # Disable telemedicine for doctor
        self.consultation_profile.accepts_telemedicine = False
        self.consultation_profile.save()
        
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=2),
            end_time=timezone.now() + timedelta(hours=3)
        )
        
        # Should raise ValidationError
        with self.assertRaises(PaymentError) as context:
            self.telemedicine_service.process_consultation_payment(
                patient=self.patient,
                doctor=self.doctor,
                appointment=appointment
            )
        
        self.assertIn("not available for telemedicine", str(context.exception))

    def test_error_handling_no_consultation_profile(self):
        """Test error handling when doctor has no consultation profile"""
        # Create doctor without consultation profile
        doctor_no_profile = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=self.doctor_role
        )
        
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=doctor_no_profile,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=2),
            end_time=timezone.now() + timedelta(hours=3)
        )
        
        # Should raise ValidationError
        with self.assertRaises(PaymentError) as context:
            self.telemedicine_service.process_consultation_payment(
                patient=self.patient,
                doctor=doctor_no_profile,
                appointment=appointment
            )
        
        self.assertIn("consultation profile not found", str(context.exception))

    @patch('billing.services.user_transfer_service.stripe.Refund.create')
    def test_refund_consultation_payment(self, mock_refund):
        """Test refunding consultation payment"""
        # Mock Stripe refund response
        mock_refund.return_value = MagicMock(
            id='re_test_refund_123',
            status='succeeded'
        )
        
        # Create appointment with completed payment
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=2),
            end_time=timezone.now() + timedelta(hours=3),
            status='confirmed',
            payment_status='paid'
        )
        
        # Create completed transfer
        transfer = UserTransfer.objects.create(
            sender=self.patient,
            receiver=self.doctor,
            amount=15000,
            currency='usd',
            transfer_type='payment',
            status='completed',
            stripe_payment_intent_id='pi_test_completed',
            platform_fee_amount=450,
            message=f"Telemedicine consultation - {appointment.id}"
        )
        
        appointment.payment_transfer = transfer
        appointment.save()
        
        # Process refund
        reason = "Doctor unavailable due to emergency"
        success = self.telemedicine_service.refund_consultation_payment(
            appointment=appointment,
            reason=reason
        )
        
        # Verify refund processed
        self.assertTrue(success)
        
        # Verify appointment status updated
        appointment.refresh_from_db()
        self.assertEqual(appointment.payment_status, 'refunded')
        self.assertEqual(appointment.status, 'cancelled')
        self.assertIn(reason, appointment.cancellation_reason)

    def test_platform_fee_calculation(self):
        """Test platform fee calculation for telemedicine"""
        # Test 3% platform fee
        fee = self.telemedicine_service.calculate_platform_fee(15000)  # $150
        expected_fee = int(15000 * 0.03)  # $4.50
        self.assertEqual(fee, expected_fee)
        
        # Test with different amounts
        fee_small = self.telemedicine_service.calculate_platform_fee(2500)  # $25
        self.assertEqual(fee_small, 75)  # $0.75
        
        fee_large = self.telemedicine_service.calculate_platform_fee(50000)  # $500
        self.assertEqual(fee_large, 1500)  # $15.00


class TelemedicineAPIIntegrationTests(APITestCase):
    """Test telemedicine API integration end-to-end"""
    
    def setUp(self):
        """Setup API test data"""
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='API',
            last_name='Doctor',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='API',
            last_name='Patient',
            role=self.patient_role
        )
        
        # Create consultation profile
        self.consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=8000,  # $80.00
            consultation_duration=45,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True
        )
        
        # Create payment profiles
        self.doctor_payment_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_api_test_doctor',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True
        )

    @patch('billing.services.appointment_payment_service.UserTransferService.create_transfer')
    def test_complete_api_telemedicine_flow(self, mock_create_transfer):
        """Test complete telemedicine flow via API"""
        # Mock transfer service response
        mock_transfer = MagicMock()
        mock_transfer.id = 'transfer_api_test_123'
        mock_transfer.amount = 8000
        mock_create_transfer.return_value = (mock_transfer, 'pi_api_test_secret_456')
        
        self.client.force_authenticate(user=self.patient)
        
        # Create telemedicine appointment via API
        appointment_data = {
            'doctor_id': str(self.doctor.id),
            'start_time': (timezone.now() + timedelta(hours=3)).isoformat(),
            'end_time': (timezone.now() + timedelta(hours=3, minutes=45)).isoformat(),
            'notes': 'API test consultation - anxiety and stress management',
            'title': 'Telemedicine Consultation - Anxiety Treatment'
        }
        
        response = self.client.post(
            '/appointments/appointments/create_telemedicine_appointment/',
            data=appointment_data,
            format='json'
        )
        
        # Verify API response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify response structure
        self.assertIn('appointment', response.data)
        self.assertIn('payment', response.data)
        
        appointment_response = response.data['appointment']
        payment_response = response.data['payment']
        
        # Verify appointment details
        self.assertEqual(appointment_response['appointment_type'], 'booking')
        self.assertEqual(appointment_response['mode'], 'video_call')
        self.assertTrue(appointment_response['direct_payment'])
        self.assertFalse(appointment_response['insurance'])
        self.assertEqual(appointment_response['doctor'], str(self.doctor.id))
        self.assertEqual(appointment_response['patient'], str(self.patient.id))
        
        # Verify payment details
        self.assertEqual(payment_response['amount'], 8000)
        self.assertEqual(payment_response['amount_usd'], 80.0)
        self.assertEqual(payment_response['consultation_duration'], 45)
        self.assertEqual(payment_response['doctor_name'], 'API Doctor')
        self.assertIn('client_secret', payment_response)
        
        # Verify database state
        appointment_id = appointment_response['id']
        appointment = Appointment.objects.get(id=appointment_id)
        self.assertEqual(appointment.mode, 'video_call')
        self.assertTrue(appointment.direct_payment)
        self.assertEqual(appointment.doctor, self.doctor)
        self.assertEqual(appointment.patient, self.patient)

    def test_api_error_handling_scenarios(self):
        """Test various API error scenarios"""
        self.client.force_authenticate(user=self.patient)
        
        # Test 1: Missing doctor_id
        response = self.client.post(
            '/appointments/appointments/create_telemedicine_appointment/',
            data={
                'start_time': (timezone.now() + timedelta(hours=2)).isoformat(),
                'end_time': (timezone.now() + timedelta(hours=3)).isoformat()
            },
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('doctor_id is required', response.data['error'])
        
        # Test 2: Invalid doctor_id
        response = self.client.post(
            '/appointments/appointments/create_telemedicine_appointment/',
            data={
                'doctor_id': '00000000-0000-0000-0000-000000000000',
                'start_time': (timezone.now() + timedelta(hours=2)).isoformat(),
                'end_time': (timezone.now() + timedelta(hours=3)).isoformat()
            },
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('Doctor not found', response.data['error'])
        
        # Test 3: Doctor not available for telemedicine
        self.consultation_profile.accepts_telemedicine = False
        self.consultation_profile.save()
        
        response = self.client.post(
            '/appointments/appointments/create_telemedicine_appointment/',
            data={
                'doctor_id': str(self.doctor.id),
                'start_time': (timezone.now() + timedelta(hours=2)).isoformat(),
                'end_time': (timezone.now() + timedelta(hours=3)).isoformat()
            },
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('not available for telemedicine', response.data['error'])

    def test_consultation_profile_api_access(self):
        """Test accessing consultation profile via API"""
        self.client.force_authenticate(user=self.doctor)
        
        # This would require implementing consultation profile API endpoints
        # For now, we test the model directly
        profile = self.doctor.consultation_profile
        self.assertEqual(profile.consultation_fee, 8000)
        self.assertTrue(profile.is_available_for_telemedicine())
        self.assertEqual(profile.consultation_duration, 45)


# Performance and Load Testing
class TelemedicinePerformanceTests(TransactionTestCase):
    """Test performance and concurrent scenarios"""
    
    def setUp(self):
        """Setup performance test data"""
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create multiple doctors and patients
        self.doctors = []
        self.patients = []
        
        for i in range(5):
            doctor = CustomUser.objects.create_user(
                email=f'doctor{i}@performance.com',
                password='testpass123',
                role=self.doctor_role
            )
            
            # Create consultation profile
            DoctorConsultationProfile.objects.create(
                user=doctor,
                consultation_fee=5000 + (i * 1000),  # $50-90
                consultation_duration=30 + (i * 15),  # 30-90 minutes
                accepts_telemedicine=True,
                is_active=True,
                stripe_account_setup=True
            )
            
            # Create payment profile
            UserPaymentProfile.objects.create(
                user=doctor,
                stripe_account_id=f'acct_perf_doctor_{i}',
                charges_enabled=True,
                payouts_enabled=True,
                details_submitted=True,
                is_verified=True
            )
            
            self.doctors.append(doctor)
            
            # Create patient
            patient = CustomUser.objects.create_user(
                email=f'patient{i}@performance.com',
                password='testpass123',
                role=self.patient_role
            )
            self.patients.append(patient)
    
    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.create')
    @patch('billing.services.user_transfer_service.stripe.Account.retrieve')
    def test_concurrent_appointment_creation(self, mock_account, mock_payment):
        """Test creating multiple appointments concurrently"""
        # Mock Stripe responses
        mock_account.return_value = MagicMock(
            charges_enabled=True,
            payouts_enabled=True
        )
        
        mock_payment.return_value = MagicMock(
            id='pi_concurrent_test',
            client_secret='pi_concurrent_test_secret'
        )
        
        service = TelemedicinePaymentService()
        
        # Create multiple appointments
        appointments = []
        for i in range(len(self.doctors)):
            appointment = Appointment.objects.create(
                creator=self.patients[i],
                patient=self.patients[i],
                doctor=self.doctors[i],
                appointment_type='booking',
                mode='video_call',
                direct_payment=True,
                start_time=timezone.now() + timedelta(hours=i+1),
                end_time=timezone.now() + timedelta(hours=i+2),
                title=f"Performance test consultation {i}"
            )
            appointments.append(appointment)
        
        # Process payments for all appointments
        transfers = []
        for i, appointment in enumerate(appointments):
            transfer, client_secret = service.process_consultation_payment(
                patient=self.patients[i],
                doctor=self.doctors[i],
                appointment=appointment
            )
            transfers.append(transfer)
        
        # Verify all appointments have payments
        for i, appointment in enumerate(appointments):
            appointment.refresh_from_db()
            self.assertIsNotNone(appointment.payment_transfer)
            self.assertEqual(appointment.payment_transfer, transfers[i])
            self.assertEqual(appointment.payment_status, 'pending')
        
        # Verify different consultation fees
        expected_fees = [5000, 6000, 7000, 8000, 9000]
        for i, transfer in enumerate(transfers):
            self.assertEqual(transfer.amount, expected_fees[i])


if __name__ == '__main__':
    # Run specific test
    import sys
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        suite = unittest.TestLoader().loadTestsFromName(test_name)
        unittest.TextTestRunner(verbosity=2).run(suite)
    else:
        # Run all tests
        unittest.main(verbosity=2) 