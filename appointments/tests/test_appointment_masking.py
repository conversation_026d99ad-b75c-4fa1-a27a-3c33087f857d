import uuid
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from unittest.mock import patch
from django.utils import timezone
from datetime import datetime, timedelta

from accounts.models import CustomUser
from roles.models import Role
from appointments.models import Appointment, DoctorAvailability
from clinic.models import Clinic, ClinicDoctor
from enterprise.models import Enterprise

User = get_user_model()


class AppointmentMaskingTestCase(APITestCase):
    """
    Test appointment API doctor_id and patient_id masking behavior
    """

    def setUp(self):
        """Set up test data"""
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor')
        self.patient_role = Role.objects.create(name='patient')

        # Create test users with custom_url_username
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Dr',
            last_name='<PERSON>',
            role=self.doctor_role,
            custom_url_username='dr-smith-001'
        )

        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='<PERSON>',
            last_name='Doe',
            role=self.patient_role,
            custom_url_username='john-doe-123'
        )

        # Create clinic and associate doctor
        self.clinic = Clinic.objects.create(
            name='Test Clinic',
            email='<EMAIL>'
        )
        
        ClinicDoctor.objects.create(
            clinic=self.clinic,
            doctor=self.doctor
        )

        # Create doctor availability
        self.availability = DoctorAvailability.objects.create(
            doctor=self.doctor,
            clinic=self.clinic,
            title='Available for appointments',
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=30),
            start_time=timezone.now().time().replace(hour=9, minute=0),
            end_time=timezone.now().time().replace(hour=17, minute=0),
            mode='video_call,in_person',
            is_active=True
        )

        self.client = APIClient()

    def test_booking_appointment_masks_doctor_id_with_custom_url_username(self):
        """Test that booking appointments mask doctor_id with custom_url_username in response"""
        self.client.force_authenticate(user=self.patient)
        
        start_time = timezone.now() + timedelta(hours=1)
        end_time = start_time + timedelta(hours=1)
        
        data = {
            'appointment_type': 'booking',
            'doctor_id': 'dr-smith-001',  # Using custom_url_username
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'title': 'Test Appointment',
            'mode': 'video_call',
            'clinic_id': str(self.clinic.id)
        }
        
        response = self.client.post('/api/appointments/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['appointment_type'], 'booking')
        # Doctor ID should be masked as custom_url_username
        self.assertEqual(response.data['doctor_id'], 'dr-smith-001')
        # Patient ID should also be masked as custom_url_username
        self.assertEqual(response.data['patient_id'], 'john-doe-123')

    def test_booking_appointment_accepts_uuid_doctor_id(self):
        """Test that booking appointments accept UUID doctor_id in request"""
        self.client.force_authenticate(user=self.patient)
        
        start_time = timezone.now() + timedelta(hours=1)
        end_time = start_time + timedelta(hours=1)
        
        data = {
            'appointment_type': 'booking',
            'doctor_id': str(self.doctor.id),  # Using UUID
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'title': 'Test Appointment',
            'mode': 'video_call',
            'clinic_id': str(self.clinic.id)
        }
        
        response = self.client.post('/api/appointments/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['appointment_type'], 'booking')
        # Doctor ID should be masked as custom_url_username in response
        self.assertEqual(response.data['doctor_id'], 'dr-smith-001')
        self.assertEqual(response.data['patient_id'], 'john-doe-123')

    def test_manual_appointment_does_not_mask_ids(self):
        """Test that manual appointments do not mask user IDs"""
        self.client.force_authenticate(user=self.patient)
        
        start_time = timezone.now() + timedelta(hours=1)
        end_time = start_time + timedelta(hours=1)
        
        data = {
            'appointment_type': 'manual',
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'title': 'Personal Meeting',
            'notes': 'Personal appointment'
        }
        
        response = self.client.post('/api/appointments/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['appointment_type'], 'manual')
        # For manual appointments, IDs should remain as UUIDs
        self.assertEqual(response.data['doctor_id'], None)  # No doctor for manual
        # Creator ID should be UUID, not masked
        self.assertNotEqual(response.data.get('creator'), 'john-doe-123')

    def test_invalid_doctor_identifier_returns_error(self):
        """Test that invalid doctor identifier returns proper error"""
        self.client.force_authenticate(user=self.patient)
        
        start_time = timezone.now() + timedelta(hours=1)
        end_time = start_time + timedelta(hours=1)
        
        data = {
            'appointment_type': 'booking',
            'doctor_id': 'invalid-doctor-id',
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'title': 'Test Appointment',
            'mode': 'video_call'
        }
        
        response = self.client.post('/api/appointments/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Doctor not found', str(response.data))

    def test_appointment_serializer_masking_logic(self):
        """Test AppointmentSerializer masking logic directly"""
        from appointments.api.serializers import AppointmentSerializer
        
        # Create a booking appointment
        booking_appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title='Test Booking'
        )
        
        # Create a manual appointment
        manual_appointment = Appointment.objects.create(
            creator=self.patient,
            appointment_type='manual',
            start_time=timezone.now() + timedelta(hours=3),
            end_time=timezone.now() + timedelta(hours=4),
            title='Test Manual'
        )
        
        # Test booking appointment serialization
        booking_serializer = AppointmentSerializer(booking_appointment)
        booking_data = booking_serializer.data
        self.assertEqual(booking_data['doctor_id'], 'dr-smith-001')
        self.assertEqual(booking_data['patient_id'], 'john-doe-123')
        
        # Test manual appointment serialization
        manual_serializer = AppointmentSerializer(manual_appointment)
        manual_data = manual_serializer.data
        self.assertEqual(manual_data['doctor_id'], None)
        # For manual appointments, patient_id should be UUID (not masked)
        self.assertNotEqual(manual_data['patient_id'], 'john-doe-123')

    def test_unauthenticated_booking_with_email(self):
        """Test that unauthenticated users can create booking appointments with email"""
        start_time = timezone.now() + timedelta(hours=1)
        end_time = start_time + timedelta(hours=1)
        
        data = {
            'email': '<EMAIL>',
            'appointment_type': 'booking',
            'doctor_id': 'dr-smith-001',
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'title': 'Test Appointment',
            'mode': 'video_call',
            'clinic_id': str(self.clinic.id)
        }
        
        response = self.client.post('/api/appointments/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['appointment_type'], 'booking')
        self.assertEqual(response.data['doctor_id'], 'dr-smith-001')
        # Patient ID should be masked for the created/found user
        self.assertIsNotNone(response.data['patient_id']) 