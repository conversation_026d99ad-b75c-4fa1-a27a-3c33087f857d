"""
Test cases for Telemedicine Payments Implementation
"""
import pytest
from decimal import Decimal
from unittest.mock import patch, MagicMock
from django.test import TestCase, TransactionTestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from accounts.models import CustomUser
from appointments.models import Appointment, DoctorConsultationProfile
from billing.models import UserPaymentProfile, UserTransfer
from billing.services.appointment_payment_service import AppointmentPaymentService
from billing.services.user_transfer_service import UserTransferService
from billing.constants import TransferType
from roles.models import Role

User = get_user_model()


class TelemedicinePaymentModelTests(TestCase):
    """Test telemedicine payment models"""
    
    def setUp(self):
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='<PERSON>',
            last_name='Doctor',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Patient',
            role=self.patient_role
        )
        
        # Create doctor consultation profile
        self.consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=5000,  # $50.00
            consultation_duration=30,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True,
            bio="Experienced doctor for telemedicine",
            specializations=["General Practice", "Telemedicine"],
            languages=["English", "Vietnamese"]
        )
        
        # Create doctor payment profile
        self.doctor_payment_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True,
            verification_date=timezone.now()
        )
        
        # Create patient payment profile  
        self.patient_payment_profile = UserPaymentProfile.objects.create(
            user=self.patient,
            stripe_account_id='acct_test_patient',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True
        )
    
    def test_doctor_consultation_profile_creation(self):
        """Test creating doctor consultation profile"""
        self.assertEqual(self.consultation_profile.user, self.doctor)
        self.assertEqual(self.consultation_profile.consultation_fee, 5000)
        self.assertEqual(self.consultation_profile.get_consultation_fee_usd(), 50.0)
        self.assertEqual(self.consultation_profile.get_consultation_fee_display(), "$50.00")
        self.assertTrue(self.consultation_profile.is_available_for_telemedicine())
        
    def test_consultation_profile_validation(self):
        """Test consultation profile validation"""
        # Test minimum fee validation
        with self.assertRaises(ValidationError):
            profile = DoctorConsultationProfile(
                user=self.doctor,
                consultation_fee=100  # Below minimum $5
            )
            profile.full_clean()
            
        # Test maximum fee validation  
        with self.assertRaises(ValidationError):
            profile = DoctorConsultationProfile(
                user=self.doctor,
                consultation_fee=100000  # Above maximum $500
            )
            profile.full_clean()
    
    def test_consultation_profile_non_doctor_user(self):
        """Test that consultation profile can only be created for doctors"""
        with self.assertRaises(ValueError):
            DoctorConsultationProfile.objects.create(
                user=self.patient,  # Patient, not doctor
                consultation_fee=5000
            )
    
    def test_appointment_with_payment_fields(self):
        """Test appointment model with payment integration fields"""
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Telemedicine consultation"
        )
        
        # Test payment fields
        self.assertTrue(appointment.direct_payment)
        self.assertFalse(appointment.insurance)
        self.assertEqual(appointment.mode, 'video_call')
        self.assertIsNone(appointment.payment_transfer)
        self.assertIsNone(appointment.consultation_service)
    
    def test_appointment_payment_validation(self):
        """Test appointment payment method validation"""
        # Test that both insurance and direct_payment cannot be True
        with self.assertRaises(ValidationError):
            appointment = Appointment(
                creator=self.patient,
                patient=self.patient,
                doctor=self.doctor,
                appointment_type='booking',
                insurance=True,
                direct_payment=True,
                start_time=timezone.now() + timedelta(hours=1),
                end_time=timezone.now() + timedelta(hours=2)
            )
            appointment.clean()


class TelemedicinePaymentServiceTests(TestCase):
    """Test telemedicine payment services"""
    
    def setUp(self):
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='John',
            last_name='Doctor',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Patient',
            role=self.patient_role
        )
        
        # Create consultation profile
        self.consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=7500,  # $75.00
            consultation_duration=45,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True
        )
        
        # Create payment profiles
        self.doctor_payment_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True
        )
        
        # Create appointment
        self.appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Telemedicine consultation"
        )
    
    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.create')
    def test_create_appointment_payment_transfer(self, mock_stripe_create):
        """Test creating payment transfer for appointment"""
        # Mock Stripe response
        mock_stripe_create.return_value = MagicMock(
            id='pi_test_123',
            client_secret='pi_test_123_secret_123'
        )
        
        service = AppointmentPaymentService()
        
        transfer, client_secret = service.create_appointment_payment_transfer(
            patient=self.patient,
            doctor=self.doctor,
            appointment=self.appointment,
            consultation_fee=self.consultation_profile.consultation_fee
        )
        
        # Verify transfer creation
        self.assertIsNotNone(transfer)
        self.assertIsNotNone(client_secret)
        self.assertEqual(transfer.sender, self.patient)
        self.assertEqual(transfer.receiver, self.doctor)
        self.assertEqual(transfer.amount, 7500)
        self.assertEqual(transfer.transfer_type, 'payment')
        
        # Verify appointment linked to transfer
        self.appointment.refresh_from_db()
        self.assertEqual(self.appointment.payment_transfer, transfer)
        
        # Verify metadata
        self.assertEqual(transfer.metadata.get('appointment_id'), str(self.appointment.id))
        self.assertEqual(transfer.metadata.get('consultation_type'), 'telemedicine')
        self.assertEqual(transfer.metadata.get('payment_type'), 'appointment')
    
    def test_get_doctor_consultation_fee(self):
        """Test getting doctor consultation fee"""
        service = AppointmentPaymentService()
        
        fee = service.get_doctor_consultation_fee(self.doctor)
        self.assertEqual(fee, 7500)
        
        # Test with doctor without consultation profile
        doctor_no_profile = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=self.doctor_role
        )
        
        default_fee = service.get_doctor_consultation_fee(doctor_no_profile)
        self.assertEqual(default_fee, 5000)  # Default $50.00
    
    @patch('billing.services.appointment_payment_service.logger')
    def test_appointment_payment_success_handling(self, mock_logger):
        """Test handling successful appointment payment"""
        # Create a transfer first
        transfer = UserTransfer.objects.create(
            sender=self.patient,
            receiver=self.doctor,
            amount=7500,
            currency='usd',
            transfer_type='payment',
            status='pending',
            stripe_payment_intent_id='pi_test_123',
            platform_fee_amount=225,  # 3% fee
            message=f"Telemedicine consultation - Appointment {self.appointment.id}",
            metadata={
                'appointment_id': str(self.appointment.id),
                'consultation_type': 'telemedicine',
                'payment_type': 'appointment'
            }
        )
        
        # Link transfer to appointment
        self.appointment.payment_transfer = transfer
        self.appointment.save()
        
        # Mock Stripe session
        mock_session = MagicMock()
        mock_session.id = 'cs_test_123'
        mock_session.payment_intent = 'pi_test_123'
        
        service = AppointmentPaymentService()
        
        metadata = {
            'appointment_id': str(self.appointment.id)
        }
        
        result = service.handle_appointment_payment_success(mock_session, metadata)
        
        self.assertTrue(result)
        
        # Verify appointment status updated
        self.appointment.refresh_from_db()
        self.assertEqual(self.appointment.status, 'confirmed')
        
        # Verify transfer status updated
        transfer.refresh_from_db()
        self.assertEqual(transfer.status, 'completed')
        self.assertEqual(transfer.stripe_payment_intent_id, 'pi_test_123')


class TelemedicineAPITests(APITestCase):
    """Test telemedicine appointment API endpoints"""
    
    def setUp(self):
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='John',
            last_name='Doctor',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Patient',
            role=self.patient_role
        )
        
        # Create consultation profile
        self.consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=9900,  # $99.00
            consultation_duration=60,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True
        )
        
        # Create payment profiles
        self.doctor_payment_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True
        )
        
    @patch('billing.services.appointment_payment_service.UserTransferService.create_transfer')
    def test_create_telemedicine_appointment_api(self, mock_create_transfer):
        """Test creating telemedicine appointment via API"""
        # Mock transfer service response
        mock_transfer = MagicMock()
        mock_transfer.id = 'transfer_123'
        mock_transfer.amount = 9900
        mock_create_transfer.return_value = (mock_transfer, 'pi_test_123_secret')
        
        self.client.force_authenticate(user=self.patient)
        
        appointment_data = {
            'doctor_id': str(self.doctor.id),
            'start_time': (timezone.now() + timedelta(hours=2)).isoformat(),
            'end_time': (timezone.now() + timedelta(hours=3)).isoformat(),
            'notes': 'Test telemedicine consultation',
            'title': 'Video consultation with Dr. Doctor'
        }
        
        response = self.client.post(
            '/appointments/appointments/create_telemedicine_appointment/',
            data=appointment_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify response structure
        self.assertIn('appointment', response.data)
        self.assertIn('payment', response.data)
        
        appointment_data = response.data['appointment']
        payment_data = response.data['payment']
        
        # Verify appointment details
        self.assertEqual(appointment_data['appointment_type'], 'booking')
        self.assertEqual(appointment_data['mode'], 'video_call')
        self.assertTrue(appointment_data['direct_payment'])
        self.assertFalse(appointment_data['insurance'])
        
        # Verify payment details
        self.assertEqual(payment_data['amount'], 9900)
        self.assertEqual(payment_data['amount_usd'], 99.0)
        self.assertEqual(payment_data['consultation_duration'], 60)
        self.assertEqual(payment_data['doctor_name'], 'John Doctor')
        self.assertIn('client_secret', payment_data)
    
    def test_create_telemedicine_appointment_doctor_not_found(self):
        """Test API with non-existent doctor"""
        self.client.force_authenticate(user=self.patient)
        
        appointment_data = {
            'doctor_id': '00000000-0000-0000-0000-000000000000',
            'start_time': (timezone.now() + timedelta(hours=2)).isoformat(),
            'end_time': (timezone.now() + timedelta(hours=3)).isoformat()
        }
        
        response = self.client.post(
            '/appointments/appointments/create_telemedicine_appointment/',
            data=appointment_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['error'], 'Doctor not found')
    
    def test_create_telemedicine_appointment_no_consultation_profile(self):
        """Test API with doctor without consultation profile"""
        # Create doctor without consultation profile
        doctor_no_profile = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=self.doctor_role
        )
        
        self.client.force_authenticate(user=self.patient)
        
        appointment_data = {
            'doctor_id': str(doctor_no_profile.id),
            'start_time': (timezone.now() + timedelta(hours=2)).isoformat(),
            'end_time': (timezone.now() + timedelta(hours=3)).isoformat()
        }
        
        response = self.client.post(
            '/appointments/appointments/create_telemedicine_appointment/',
            data=appointment_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['error'], 'Doctor has not set up consultation profile')
    
    def test_create_telemedicine_appointment_doctor_not_available(self):
        """Test API with doctor not available for telemedicine"""
        # Update consultation profile to disable telemedicine
        self.consultation_profile.accepts_telemedicine = False
        self.consultation_profile.save()
        
        self.client.force_authenticate(user=self.patient)
        
        appointment_data = {
            'doctor_id': str(self.doctor.id),
            'start_time': (timezone.now() + timedelta(hours=2)).isoformat(),
            'end_time': (timezone.now() + timedelta(hours=3)).isoformat()
        }
        
        response = self.client.post(
            '/appointments/appointments/create_telemedicine_appointment/',
            data=appointment_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['error'], 'Doctor is not available for telemedicine consultations')
    
    def test_create_telemedicine_appointment_missing_doctor_id(self):
        """Test API without doctor_id"""
        self.client.force_authenticate(user=self.patient)
        
        appointment_data = {
            'start_time': (timezone.now() + timedelta(hours=2)).isoformat(),
            'end_time': (timezone.now() + timedelta(hours=3)).isoformat()
        }
        
        response = self.client.post(
            '/appointments/appointments/create_telemedicine_appointment/',
            data=appointment_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['error'], 'doctor_id is required')


class TelemedicineIntegrationTests(TransactionTestCase):
    """Integration tests for complete telemedicine payment flow"""
    
    def setUp(self):
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='John',
            last_name='Doctor',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Patient', 
            role=self.patient_role
        )
        
        # Create consultation profile
        self.consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=12000,  # $120.00
            consultation_duration=90,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True,
            bio="Specialist doctor",
            specializations=["Cardiology", "Telemedicine"],
            languages=["English"]
        )
        
        # Create payment profiles
        self.doctor_payment_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True
        )
        
        self.patient_payment_profile = UserPaymentProfile.objects.create(
            user=self.patient,
            stripe_account_id='acct_test_patient',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True
        )
    
    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.create')
    @patch('billing.services.user_transfer_service.stripe.Account.retrieve')
    def test_complete_telemedicine_payment_flow(self, mock_account_retrieve, mock_payment_create):
        """Test complete end-to-end telemedicine payment flow"""
        # Mock Stripe responses
        mock_account_retrieve.return_value = MagicMock(
            charges_enabled=True,
            payouts_enabled=True
        )
        
        mock_payment_create.return_value = MagicMock(
            id='pi_test_complete_flow',
            client_secret='pi_test_complete_flow_secret_123'
        )
        
        # Step 1: Create appointment
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2.5),
            title="Cardiology telemedicine consultation"
        )
        
        # Step 2: Create payment transfer
        service = AppointmentPaymentService()
        transfer, client_secret = service.create_appointment_payment_transfer(
            patient=self.patient,
            doctor=self.doctor,
            appointment=appointment,
            consultation_fee=self.consultation_profile.consultation_fee
        )
        
        # Verify transfer creation
        self.assertIsNotNone(transfer)
        self.assertEqual(transfer.amount, 12000)
        self.assertEqual(transfer.sender, self.patient)
        self.assertEqual(transfer.receiver, self.doctor)
        self.assertEqual(transfer.status, 'pending')
        
        # Verify appointment linked to transfer
        appointment.refresh_from_db()
        self.assertEqual(appointment.payment_transfer, transfer)
        
        # Step 3: Simulate successful payment (webhook)
        mock_session = MagicMock()
        mock_session.id = 'cs_test_complete_flow'
        mock_session.payment_intent = 'pi_test_complete_flow'
        
        metadata = {
            'appointment_id': str(appointment.id)
        }
        
        success = service.handle_appointment_payment_success(mock_session, metadata)
        
        # Verify payment success handling
        self.assertTrue(success)
        
        # Verify final states
        appointment.refresh_from_db()
        transfer.refresh_from_db()
        
        self.assertEqual(appointment.status, 'confirmed')
        self.assertEqual(transfer.status, 'completed')
        self.assertEqual(transfer.stripe_payment_intent_id, 'pi_test_complete_flow')
        
        # Verify platform fee calculation (should be ~3% for appointment payments)
        expected_fee = int(12000 * 0.03)  # 3% of $120
        self.assertAlmostEqual(transfer.platform_fee_amount, expected_fee, delta=50)
    
    def test_consultation_profile_features(self):
        """Test consultation profile features and methods"""
        # Test display methods
        self.assertEqual(self.consultation_profile.get_consultation_fee_display(), "$120.00")
        self.assertEqual(self.consultation_profile.get_specializations_display(), "Cardiology, Telemedicine")
        self.assertEqual(self.consultation_profile.get_languages_display(), "English")
        
        # Test availability check
        self.assertTrue(self.consultation_profile.is_available_for_telemedicine())
        
        # Test when not available
        self.consultation_profile.accepts_telemedicine = False
        self.assertFalse(self.consultation_profile.is_available_for_telemedicine())
        
        # Test when stripe not setup
        self.consultation_profile.accepts_telemedicine = True
        self.consultation_profile.stripe_account_setup = False
        self.assertFalse(self.consultation_profile.is_available_for_telemedicine())


class TelemedicineValidationTests(TestCase):
    """Test validation and edge cases for telemedicine payments"""
    
    def setUp(self):
        # Create roles  
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>', 
            password='testpass123',
            role=self.patient_role
        )
    
    def test_consultation_fee_validation(self):
        """Test consultation fee validation ranges"""
        # Test valid fee
        profile = DoctorConsultationProfile(
            user=self.doctor,
            consultation_fee=2500  # $25.00
        )
        profile.full_clean()  # Should not raise
        
        # Test fee too low
        with self.assertRaises(ValidationError):
            profile = DoctorConsultationProfile(
                user=self.doctor,
                consultation_fee=400  # $4.00 - below minimum
            )
            profile.full_clean()
        
        # Test fee too high  
        with self.assertRaises(ValidationError):
            profile = DoctorConsultationProfile(
                user=self.doctor,
                consultation_fee=60000  # $600.00 - above maximum
            )
            profile.full_clean()
    
    def test_consultation_duration_validation(self):
        """Test consultation duration validation"""
        # Test valid duration
        profile = DoctorConsultationProfile(
            user=self.doctor,
            consultation_fee=5000,
            consultation_duration=45
        )
        profile.full_clean()  # Should not raise
        
        # Test duration too short
        with self.assertRaises(ValidationError):
            profile = DoctorConsultationProfile(
                user=self.doctor,
                consultation_fee=5000,
                consultation_duration=10  # Below minimum 15 minutes
            )
            profile.full_clean()
        
        # Test duration too long
        with self.assertRaises(ValidationError):
            profile = DoctorConsultationProfile(
                user=self.doctor,
                consultation_fee=5000,
                consultation_duration=200  # Above maximum 180 minutes
            )
            profile.full_clean()
    
    def test_appointment_mode_validation(self):
        """Test appointment mode validation for telemedicine"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=5000,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True
        )
        
        # Test valid video call appointment
        appointment = Appointment(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2)
        )
        appointment.clean()  # Should not raise
        
        # Test in-person appointment with direct payment (should be allowed)
        appointment.mode = 'in_person'
        appointment.clean()  # Should not raise


@pytest.mark.django_db
class TestTelemedicinePaymentsPytest:
    """Pytest-style tests for telemedicine payments"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test data"""
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role') 
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123', 
            role=self.patient_role
        )
        
        # Create consultation profile
        self.consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=6000,  # $60.00
            consultation_duration=30,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True
        )
    
    def test_consultation_profile_str_representation(self):
        """Test string representation of consultation profile"""
        expected = f"Consultation Profile - {self.doctor.get_full_name()}"
        assert str(self.consultation_profile) == expected
    
    def test_consultation_fee_conversion_methods(self):
        """Test fee conversion methods"""
        assert self.consultation_profile.get_consultation_fee_usd() == 60.0
        assert self.consultation_profile.get_consultation_fee_display() == "$60.00"
    
    @patch('billing.services.appointment_payment_service.UserTransferService')
    def test_appointment_payment_service_integration(self, mock_transfer_service):
        """Test appointment payment service integration"""
        # Mock the transfer service
        mock_service_instance = MagicMock()
        mock_transfer_service.return_value = mock_service_instance
        
        mock_transfer = MagicMock()
        mock_transfer.id = 'transfer_pytest_123'
        mock_service_instance.create_transfer.return_value = (mock_transfer, 'client_secret_123')
        
        # Create appointment
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2)
        )
        
        # Test payment service
        service = AppointmentPaymentService()
        transfer, client_secret = service.create_appointment_payment_transfer(
            patient=self.patient,
            doctor=self.doctor,
            appointment=appointment,
            consultation_fee=self.consultation_profile.consultation_fee
        )
        
        # Verify service was called correctly
        mock_service_instance.create_transfer.assert_called_once()
        args, kwargs = mock_service_instance.create_transfer.call_args
        
        assert kwargs['sender'] == self.patient
        assert kwargs['receiver'] == self.doctor
        assert kwargs['amount'] == 6000
        assert kwargs['transfer_type'] == 'payment'
        assert 'Telemedicine consultation' in kwargs['message']
        assert kwargs['metadata']['appointment_id'] == str(appointment.id) 