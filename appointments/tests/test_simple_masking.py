from django.test import TestCase
from unittest.mock import Mock
from appointments.api.serializers import AppointmentSerializer


class AppointmentSerializerMaskingTest(TestCase):
    """Simple test for AppointmentSerializer masking logic"""

    def test_booking_appointment_masks_doctor_and_patient_ids(self):
        """Test that booking appointments mask doctor_id and patient_id"""
        # Mock doctor and patient with custom_url_username
        mock_doctor = <PERSON><PERSON>()
        mock_doctor.custom_url_username = 'dr-smith-001'
        mock_doctor.id = 'doctor-uuid-123'

        mock_patient = Mock()
        mock_patient.custom_url_username = 'john-doe-123'
        mock_patient.id = 'patient-uuid-456'

        # Mock booking appointment
        mock_appointment = Mock()
        mock_appointment.appointment_type = 'booking'
        mock_appointment.doctor = mock_doctor
        mock_appointment.patient = mock_patient

        serializer = AppointmentSerializer(mock_appointment)
        
        # Test masking methods
        doctor_id = serializer.get_doctor_id(mock_appointment)
        patient_id = serializer.get_patient_id(mock_appointment)

        self.assertEqual(doctor_id, 'dr-smith-001')
        self.assertEqual(patient_id, 'john-doe-123')

    def test_manual_appointment_does_not_mask_ids(self):
        """Test that manual appointments do not mask IDs"""
        # Mock doctor and patient with custom_url_username
        mock_doctor = Mock()
        mock_doctor.custom_url_username = 'dr-smith-001'
        mock_doctor.id = 'doctor-uuid-123'

        mock_patient = Mock()
        mock_patient.custom_url_username = 'john-doe-123'
        mock_patient.id = 'patient-uuid-456'

        # Mock manual appointment
        mock_appointment = Mock()
        mock_appointment.appointment_type = 'manual'
        mock_appointment.doctor = mock_doctor
        mock_appointment.patient = mock_patient

        serializer = AppointmentSerializer(mock_appointment)
        
        # Test non-masking for manual appointments
        doctor_id = serializer.get_doctor_id(mock_appointment)
        patient_id = serializer.get_patient_id(mock_appointment)

        self.assertEqual(doctor_id, 'doctor-uuid-123')
        self.assertEqual(patient_id, 'patient-uuid-456')

    def test_no_doctor_or_patient_returns_none(self):
        """Test that None is returned when doctor or patient is None"""
        # Mock appointment with no doctor or patient
        mock_appointment = Mock()
        mock_appointment.appointment_type = 'booking'
        mock_appointment.doctor = None
        mock_appointment.patient = None

        serializer = AppointmentSerializer(mock_appointment)
        
        doctor_id = serializer.get_doctor_id(mock_appointment)
        patient_id = serializer.get_patient_id(mock_appointment)

        self.assertEqual(doctor_id, None)
        self.assertEqual(patient_id, None) 