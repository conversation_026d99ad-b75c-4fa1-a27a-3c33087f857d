"""
Tests for need_payment functionality in doctor availability and appointments
"""
import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta, time
from rest_framework.test import APIClient
from rest_framework import status

from appointments.models import DoctorAvailability, Appointment
from roles.models import Role
from clinic.models import Clinic, ClinicDoctor
from accounts.models import DoctorConsultationProfile

User = get_user_model()


class NeedPaymentFunctionalityTestCase(TestCase):
    """Test cases for need_payment functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create roles
        self.doctor_role, _ = Role.objects.get_or_create(name='doctor')
        self.patient_role, _ = Role.objects.get_or_create(name='patient')
        
        # Create clinic
        self.clinic = Clinic.objects.create(
            name="Test Clinic",
            address="123 Test St",
            phone="************"
        )
        
        # Create doctor user
        self.doctor = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Dr',
            last_name='Smith',
            role=self.doctor_role
        )
        
        # Create patient user
        self.patient = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='John',
            last_name='Doe',
            role=self.patient_role
        )
        
        # Link doctor to clinic
        ClinicDoctor.objects.create(
            clinic=self.clinic,
            doctor=self.doctor
        )
        
        # Create doctor consultation profile
        self.consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=5000,  # $50.00 in cents
            consultation_duration=30,
            is_available=True
        )
        
        # Set up dates for testing
        self.tomorrow = timezone.now().date() + timedelta(days=1)
        self.start_time = time(9, 0)  # 9:00 AM
        self.end_time = time(10, 0)   # 10:00 AM
    
    def test_availability_with_need_payment_true(self):
        """Test creating availability with need_payment=True"""
        availability = DoctorAvailability.objects.create(
            doctor=self.doctor,
            clinic=self.clinic,
            title="Paid Consultation",
            start_date=self.tomorrow,
            start_time=self.start_time,
            end_time=self.end_time,
            mode="video_call",
            need_payment=True
        )
        
        self.assertTrue(availability.need_payment)
        self.assertEqual(availability.mode, "video_call")
    
    def test_availability_with_need_payment_false(self):
        """Test creating availability with need_payment=False (default)"""
        availability = DoctorAvailability.objects.create(
            doctor=self.doctor,
            clinic=self.clinic,
            title="Free Consultation",
            start_date=self.tomorrow,
            start_time=self.start_time,
            end_time=self.end_time,
            mode="in_person"
        )
        
        self.assertFalse(availability.need_payment)  # Default should be False
    
    def test_appointment_creation_with_payment_required_availability(self):
        """Test appointment creation when availability requires payment"""
        # Create availability that requires payment
        availability = DoctorAvailability.objects.create(
            doctor=self.doctor,
            clinic=self.clinic,
            title="Paid Consultation",
            start_date=self.tomorrow,
            start_time=self.start_time,
            end_time=self.end_time,
            mode="video_call",
            need_payment=True
        )
        
        # Authenticate as patient
        self.client.force_authenticate(user=self.patient)
        
        # Create appointment data
        appointment_data = {
            'appointment_type': 'booking',
            'doctor_id': str(self.doctor.id),
            'start_time': datetime.combine(self.tomorrow, self.start_time).isoformat(),
            'end_time': datetime.combine(self.tomorrow, self.end_time).isoformat(),
            'mode': 'video_call',
            'title': 'Test Appointment'
        }
        
        # Create appointment
        response = self.client.post('/api/appointments/', appointment_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that direct_payment was automatically set to True
        appointment = Appointment.objects.get(id=response.data['id'])
        self.assertTrue(appointment.direct_payment)
        self.assertFalse(appointment.insurance)
        self.assertTrue(appointment.is_payment_required())
        
        # Check response includes payment information
        self.assertTrue(response.data['payment_required'])
        self.assertIn('payment_info', response.data)
    
    def test_appointment_creation_with_no_payment_required_availability(self):
        """Test appointment creation when availability doesn't require payment"""
        # Create availability that doesn't require payment
        availability = DoctorAvailability.objects.create(
            doctor=self.doctor,
            clinic=self.clinic,
            title="Free Consultation",
            start_date=self.tomorrow,
            start_time=self.start_time,
            end_time=self.end_time,
            mode="in_person",
            need_payment=False
        )
        
        # Authenticate as patient
        self.client.force_authenticate(user=self.patient)
        
        # Create appointment data
        appointment_data = {
            'appointment_type': 'booking',
            'doctor_id': str(self.doctor.id),
            'start_time': datetime.combine(self.tomorrow, self.start_time).isoformat(),
            'end_time': datetime.combine(self.tomorrow, self.end_time).isoformat(),
            'mode': 'in_person',
            'title': 'Test Appointment'
        }
        
        # Create appointment
        response = self.client.post('/api/appointments/', appointment_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that payment fields remain as user specified (default False)
        appointment = Appointment.objects.get(id=response.data['id'])
        self.assertFalse(appointment.direct_payment)
        self.assertFalse(appointment.insurance)
        self.assertFalse(appointment.is_payment_required())
        
        # Check response indicates no payment required
        self.assertFalse(response.data['payment_required'])
    
    def test_available_slots_api_includes_need_payment(self):
        """Test that available_slots API includes need_payment information"""
        # Create availability with payment required
        availability_paid = DoctorAvailability.objects.create(
            doctor=self.doctor,
            clinic=self.clinic,
            title="Paid Consultation",
            start_date=self.tomorrow,
            start_time=self.start_time,
            end_time=self.end_time,
            mode="video_call",
            need_payment=True
        )
        
        # Create availability without payment required
        availability_free = DoctorAvailability.objects.create(
            doctor=self.doctor,
            clinic=self.clinic,
            title="Free Consultation",
            start_date=self.tomorrow,
            start_time=time(11, 0),
            end_time=time(12, 0),
            mode="in_person",
            need_payment=False
        )
        
        # Call available_slots API
        response = self.client.get(
            '/api/appointments/availability/available_slots/',
            {
                'doctor_id': str(self.doctor.id),
                'date': self.tomorrow.strftime('%Y-%m-%d')
            }
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that slots include need_payment information
        schedule = response.data['schedule']
        self.assertEqual(len(schedule), 1)  # One day
        
        slots = schedule[0]['slots']
        self.assertEqual(len(slots), 2)  # Two slots
        
        # Find paid and free slots
        paid_slot = next(slot for slot in slots if slot['need_payment'])
        free_slot = next(slot for slot in slots if not slot['need_payment'])
        
        self.assertTrue(paid_slot['need_payment'])
        self.assertEqual(paid_slot['mode'], 'video_call')
        
        self.assertFalse(free_slot['need_payment'])
        self.assertEqual(free_slot['mode'], 'in_person')

    def test_availability_serializer_includes_need_payment(self):
        """Test that DoctorAvailability serializer includes need_payment field"""
        from appointments.api.serializers import DoctorAvailabilitySerializer

        availability = DoctorAvailability.objects.create(
            doctor=self.doctor,
            clinic=self.clinic,
            title="Test Availability",
            start_date=self.tomorrow,
            start_time=self.start_time,
            end_time=self.end_time,
            mode="video_call",
            need_payment=True
        )

        serializer = DoctorAvailabilitySerializer(availability)
        self.assertIn('need_payment', serializer.data)
        self.assertTrue(serializer.data['need_payment'])

    def test_appointment_payment_override_when_availability_requires_payment(self):
        """Test that user cannot override payment when availability requires it"""
        # Create availability that requires payment
        availability = DoctorAvailability.objects.create(
            doctor=self.doctor,
            clinic=self.clinic,
            title="Paid Consultation",
            start_date=self.tomorrow,
            start_time=self.start_time,
            end_time=self.end_time,
            mode="video_call",
            need_payment=True
        )

        # Authenticate as patient
        self.client.force_authenticate(user=self.patient)

        # Try to create appointment with insurance=True (should be overridden)
        appointment_data = {
            'appointment_type': 'booking',
            'doctor_id': str(self.doctor.id),
            'start_time': datetime.combine(self.tomorrow, self.start_time).isoformat(),
            'end_time': datetime.combine(self.tomorrow, self.end_time).isoformat(),
            'mode': 'video_call',
            'title': 'Test Appointment',
            'insurance': True,  # This should be overridden
            'direct_payment': False  # This should be overridden
        }

        response = self.client.post('/api/appointments/', appointment_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that availability's need_payment setting overrode user preferences
        appointment = Appointment.objects.get(id=response.data['id'])
        self.assertTrue(appointment.direct_payment)  # Should be True due to need_payment
        self.assertFalse(appointment.insurance)      # Should be False due to need_payment

    def test_appointment_is_payment_required_method(self):
        """Test the updated is_payment_required method"""
        # Create appointment with direct_payment=True
        appointment_paid = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            mode='video_call',
            direct_payment=True
        )

        # Create appointment with direct_payment=False
        appointment_free = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            start_time=timezone.now() + timedelta(hours=3),
            end_time=timezone.now() + timedelta(hours=4),
            mode='in_person',
            direct_payment=False
        )

        # Create manual appointment
        appointment_manual = Appointment.objects.create(
            creator=self.patient,
            appointment_type='manual',
            start_time=timezone.now() + timedelta(hours=5),
            end_time=timezone.now() + timedelta(hours=6),
            title='Personal Event'
        )

        # Test payment requirements
        self.assertTrue(appointment_paid.is_payment_required())
        self.assertFalse(appointment_free.is_payment_required())
        self.assertFalse(appointment_manual.is_payment_required())

    def test_doctor_can_confirm_appointment_based_on_payment(self):
        """Test doctor confirmation logic based on payment requirements"""
        # Create paid appointment
        appointment_paid = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            mode='video_call',
            direct_payment=True,
            payment_status='pending'
        )

        # Create free appointment
        appointment_free = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            start_time=timezone.now() + timedelta(hours=3),
            end_time=timezone.now() + timedelta(hours=4),
            mode='in_person',
            direct_payment=False
        )

        # Test confirmation logic
        self.assertFalse(appointment_paid.can_be_confirmed_by_doctor())  # Payment pending
        self.assertTrue(appointment_free.can_be_confirmed_by_doctor())   # No payment required

        # Mark paid appointment as paid
        appointment_paid.payment_status = 'paid'
        appointment_paid.save()

        self.assertTrue(appointment_paid.can_be_confirmed_by_doctor())   # Now paid
