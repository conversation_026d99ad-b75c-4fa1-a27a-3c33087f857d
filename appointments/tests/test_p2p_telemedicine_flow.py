"""
Test cases for P2P Telemedicine Flow - Complete End-to-End Testing

This file tests the complete peer-to-peer telemedicine payment flow as documented 
in appointments/docs/TELEMEDICINE_P2P_PAYMENT_FLOW.md

Test Coverage:
- Phase 1: Doctor Setup Process
- Phase 2: Patient Booking and Payment Process  
- Phase 3: Webhook Processing and Integration
- Edge Cases and Error Handling
"""

import pytest
import json
from decimal import Decimal
from unittest.mock import patch, Mock, MagicMock
from django.test import TestCase, TransactionTestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.utils import timezone
from datetime import timedelta

from accounts.models import CustomUser
from appointments.models import Appointment, DoctorConsultationProfile
from billing.models import UserPaymentProfile, UserTransfer
from billing.services.appointment_payment_service import AppointmentPaymentService
from billing.services.user_transfer_service import UserTransferService
from billing.constants import TransferType
from roles.models import Role

User = get_user_model()


class P2PTelemedicineFlowSetupTest(TestCase):
    """Test Phase 1: Doctor Setup Process"""
    
    def setUp(self):
        """Setup test data"""
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create doctor user
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Dr. John',
            last_name='Smith',
            role=self.doctor_role
        )
        
        # Create patient user
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Patient',
            role=self.patient_role
        )
        
        self.client = APIClient()
    
    @patch('billing.services.stripe_connect_service.stripe.Account.create')
    @patch('billing.services.stripe_connect_service.stripe.AccountLink.create')
    def test_step1_create_stripe_connect_account(self, mock_account_link, mock_account_create):
        """Test Step 1: Create Stripe Connect Account"""
        # Mock Stripe responses
        mock_account_create.return_value = Mock(id='acct_test_doctor_123')
        mock_account_link.return_value = Mock(
            url='https://connect.stripe.com/express/onboarding/test_link'
        )
        
        self.client.force_authenticate(user=self.doctor)
        
        response = self.client.post(
            '/billing/doctor-payments/setup_payment_account/',
            data=json.dumps({
                'account_type': 'express',
                'country': 'US',
                'business_type': 'individual'
            }),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('account_link', response.data)
        self.assertEqual(response.data['setup_type'], 'doctor_payment_account')
        
        # Verify UserPaymentProfile was created
        payment_profile = UserPaymentProfile.objects.get(user=self.doctor)
        self.assertEqual(payment_profile.stripe_account_id, 'acct_test_doctor_123')
        self.assertFalse(payment_profile.charges_enabled)  # Initially false
        self.assertFalse(payment_profile.payouts_enabled)  # Initially false
        self.assertFalse(payment_profile.details_submitted)  # Initially false
    
    @patch('billing.webhooks.views.stripe.Webhook.construct_event')
    def test_step2_complete_stripe_onboarding_webhook(self, mock_construct_event):
        """Test Step 2: Complete Stripe Onboarding via Webhook"""
        # Create initial payment profile
        payment_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor_123',
            charges_enabled=False,
            payouts_enabled=False,
            details_submitted=False
        )
        
        # Mock webhook event
        mock_event = Mock()
        mock_event.type = 'account.updated'
        mock_event.data.object = Mock(
            id='acct_test_doctor_123',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True
        )
        mock_construct_event.return_value = mock_event
        
        # Send webhook
        response = self.client.post(
            '/billing/webhook/connect/',
            data=json.dumps({
                'type': 'account.updated',
                'data': {
                    'object': {
                        'id': 'acct_test_doctor_123',
                        'charges_enabled': True,
                        'payouts_enabled': True,
                        'details_submitted': True
                    }
                }
            }),
            content_type='application/json',
            HTTP_STRIPE_SIGNATURE='test_signature'
        )
        
        # Verify payment profile was updated
        payment_profile.refresh_from_db()
        self.assertTrue(payment_profile.charges_enabled)
        self.assertTrue(payment_profile.payouts_enabled)
        self.assertTrue(payment_profile.details_submitted)
        self.assertTrue(payment_profile.is_verified)
        self.assertIsNotNone(payment_profile.verification_date)
    
    @patch('billing.services.stripe_connect_service.stripe.Account.create_external_account')
    def test_step3_add_bank_account(self, mock_create_external_account):
        """Test Step 3: Add Bank Account"""
        # Create payment profile with Connect account
        payment_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor_123',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True
        )
        
        # Mock Stripe response
        mock_create_external_account.return_value = Mock(
            id='ba_test_bank_account',
            object='bank_account',
            account_holder_name='Dr. John Smith',
            routing_number='*********',
            last4='6789'
        )
        
        self.client.force_authenticate(user=self.doctor)
        
        response = self.client.post(
            '/billing/doctor-payments/add_bank_account/',
            data=json.dumps({
                'account_holder_name': 'Dr. John Smith',
                'account_holder_type': 'individual',
                'routing_number': '*********',
                'account_number': '************',
                'country': 'US',
                'currency': 'usd'
            }),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('Bank account added successfully', response.data['message'])
    
    def test_step4_create_consultation_profile(self):
        """Test Step 4: Create Consultation Profile"""
        # Create payment profile first
        UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor_123',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True
        )
        
        self.client.force_authenticate(user=self.doctor)
        
        response = self.client.post(
            '/appointments/doctor-consultation-profiles/',
            data=json.dumps({
                'consultation_fee': 7500,  # $75.00
                'consultation_duration': 45,
                'accepts_telemedicine': True,
                'bio': 'Experienced family medicine doctor specializing in telemedicine consultations',
                'specializations': ['Family Medicine', 'Telemedicine', 'Preventive Care'],
                'languages': ['English', 'Spanish'],
                'stripe_account_setup': True
            }),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify consultation profile was created correctly
        profile = DoctorConsultationProfile.objects.get(user=self.doctor)
        self.assertEqual(profile.consultation_fee, 7500)
        self.assertEqual(profile.consultation_duration, 45)
        self.assertTrue(profile.accepts_telemedicine)
        self.assertTrue(profile.is_active)
        self.assertTrue(profile.stripe_account_setup)
        self.assertTrue(profile.is_available_for_telemedicine())
    
    def test_doctor_setup_validation_rules(self):
        """Test validation rules during doctor setup"""
        self.client.force_authenticate(user=self.doctor)
        
        # Test consultation fee validation
        with self.assertRaises(ValidationError):
            DoctorConsultationProfile.objects.create(
                user=self.doctor,
                consultation_fee=100,  # Below $5 minimum
                consultation_duration=30
            )
        
        with self.assertRaises(ValidationError):
            DoctorConsultationProfile.objects.create(
                user=self.doctor,
                consultation_fee=100000,  # Above $500 maximum
                consultation_duration=30
            )
        
        # Test consultation duration validation
        with self.assertRaises(ValidationError):
            DoctorConsultationProfile.objects.create(
                user=self.doctor,
                consultation_fee=5000,
                consultation_duration=10  # Below 15 minute minimum
            )
        
        with self.assertRaises(ValidationError):
            DoctorConsultationProfile.objects.create(
                user=self.doctor,
                consultation_fee=5000,
                consultation_duration=200  # Above 180 minute maximum
            )


class P2PTelemedicineBookingTest(TestCase):
    """Test Phase 2: Patient Booking and Payment Process"""
    
    def setUp(self):
        """Setup test data"""
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Dr. John',
            last_name='Smith',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Patient',
            role=self.patient_role
        )
        
        # Create doctor consultation profile
        self.consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=7500,  # $75.00
            consultation_duration=45,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True,
            bio="Experienced doctor for telemedicine",
            specializations=["Family Medicine", "Telemedicine"],
            languages=["English", "Spanish"]
        )
        
        # Create doctor payment profile
        self.doctor_payment_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True,
            verification_date=timezone.now()
        )
        
        # Create patient payment profile
        self.patient_payment_profile = UserPaymentProfile.objects.create(
            user=self.patient,
            stripe_account_id='acct_test_patient',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True
        )
        
        self.client = APIClient()
    
    def test_step5_patient_creates_appointment(self):
        """Test Step 5: Patient Creates Appointment"""
        self.client.force_authenticate(user=self.patient)
        
        future_time = timezone.now() + timedelta(hours=24)
        
        response = self.client.post(
            '/appointments/',
            data=json.dumps({
                'doctor': str(self.doctor.id),
                'start_time': future_time.isoformat(),
                'end_time': (future_time + timedelta(minutes=45)).isoformat(),
                'appointment_type': 'booking',
                'mode': 'video_call',
                'direct_payment': True,
                'notes': 'Follow-up consultation for hypertension'
            }),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify appointment was created correctly
        appointment = Appointment.objects.get(id=response.data['id'])
        self.assertEqual(appointment.patient, self.patient)
        self.assertEqual(appointment.doctor, self.doctor)
        self.assertEqual(appointment.appointment_type, 'booking')
        self.assertEqual(appointment.mode, 'video_call')
        self.assertTrue(appointment.direct_payment)
        self.assertEqual(appointment.status, 'pending')
        self.assertEqual(appointment.payment_status, 'pending')
        self.assertEqual(appointment.consultation_fee, 7500)
    
    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.create')
    @patch('billing.services.user_transfer_service.stripe.Account.retrieve')
    def test_step6_process_consultation_payment(self, mock_account_retrieve, mock_payment_create):
        """Test Step 6: Process Consultation Payment"""
        # Create appointment
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Telemedicine consultation",
            status='pending',
            payment_status='pending',
            consultation_fee=7500
        )
        
        # Mock Stripe responses
        mock_account_retrieve.return_value = Mock(
            charges_enabled=True,
            details_submitted=True
        )
        mock_payment_create.return_value = Mock(
            id='pi_test_payment_123',
            client_secret='pi_test_payment_123_secret_test',
            status='requires_payment_method',
            amount=7500,
            currency='usd'
        )
        
        self.client.force_authenticate(user=self.patient)
        
        response = self.client.post(
            f'/appointments/{appointment.id}/process_payment/',
            data=json.dumps({
                'payment_method': 'pm_test_card'
            }),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('client_secret', response.data)
        
        # Verify UserTransfer was created
        transfer = UserTransfer.objects.get(appointments=appointment)
        self.assertEqual(transfer.sender, self.patient)
        self.assertEqual(transfer.receiver, self.doctor)
        self.assertEqual(transfer.amount, 7500)
        self.assertEqual(transfer.transfer_type, TransferType.PAYMENT)
        self.assertEqual(transfer.status, 'pending')
        
        # Verify platform fee calculation
        expected_platform_fee = int(7500 * 0.03)  # 3% = 225 cents
        self.assertEqual(transfer.platform_fee_amount, expected_platform_fee)
        self.assertEqual(transfer.net_amount, 7500 - expected_platform_fee)
    
    @patch('appointments.services.google_meet_service.GoogleMeetService.create_meet')
    def test_step7_google_meet_integration(self, mock_create_meet):
        """Test Step 7: Google Meet Integration after Payment"""
        # Create paid appointment
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Telemedicine consultation",
            status='confirmed',
            payment_status='paid'
        )
        
        # Create corresponding transfer
        transfer = UserTransfer.objects.create(
            sender=self.patient,
            receiver=self.doctor,
            amount=7500,
            transfer_type=TransferType.PAYMENT,
            status='completed',
            stripe_payment_intent_id='pi_test_payment_123'
        )
        appointment.payment_transfer = transfer
        appointment.save()
        
        # Mock Google Meet creation
        mock_create_meet.return_value = {
            'hangoutLink': 'https://meet.google.com/test-meet-link',
            'id': 'test_event_id'
        }
        
        # Trigger Google Meet creation (usually happens in webhook)
        from appointments.services.google_meet_service import GoogleMeetService
        meet_service = GoogleMeetService()
        meet_link = meet_service.create_meet(appointment)
        
        appointment.refresh_from_db()
        self.assertIsNotNone(appointment.google_event_id)
        self.assertIn('meet.google.com', appointment.location)
    
    def test_appointment_payment_validation_rules(self):
        """Test appointment payment validation rules"""
        self.client.force_authenticate(user=self.patient)
        
        # Test that appointment requires payment when direct_payment=True
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Test appointment"
        )
        
        self.assertTrue(appointment.is_payment_required())
        
        # Test that both insurance and direct_payment cannot be True
        with self.assertRaises(ValidationError):
            invalid_appointment = Appointment(
                creator=self.patient,
                patient=self.patient,
                doctor=self.doctor,
                appointment_type='booking',
                insurance=True,
                direct_payment=True,
                start_time=timezone.now() + timedelta(hours=1),
                end_time=timezone.now() + timedelta(hours=2)
            )
            invalid_appointment.clean()
    
    def test_doctor_availability_validation(self):
        """Test doctor availability validation for telemedicine"""
        # Test with complete setup
        self.assertTrue(self.consultation_profile.is_available_for_telemedicine())
        
        # Test with incomplete Stripe setup
        self.consultation_profile.stripe_account_setup = False
        self.consultation_profile.save()
        self.assertFalse(self.consultation_profile.is_available_for_telemedicine())
        
        # Test with telemedicine disabled
        self.consultation_profile.stripe_account_setup = True
        self.consultation_profile.accepts_telemedicine = False
        self.consultation_profile.save()
        self.assertFalse(self.consultation_profile.is_available_for_telemedicine())
        
        # Test with inactive profile
        self.consultation_profile.accepts_telemedicine = True
        self.consultation_profile.is_active = False
        self.consultation_profile.save()
        self.assertFalse(self.consultation_profile.is_available_for_telemedicine())


class P2PTelemedicineWebhookTest(TestCase):
    """Test Phase 3: Webhook Processing and Integration"""
    
    def setUp(self):
        """Setup test data"""
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Dr. John',
            last_name='Smith',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Patient',
            role=self.patient_role
        )
        
        # Create consultation profile and payment profiles
        self.consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=7500,
            consultation_duration=45,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True
        )
        
        self.doctor_payment_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True
        )
        
        self.client = APIClient()
    
    @patch('billing.webhooks.views.stripe.Webhook.construct_event')
    @patch('appointments.services.google_meet_service.GoogleMeetService.create_meet')
    def test_payment_intent_succeeded_webhook(self, mock_create_meet, mock_construct_event):
        """Test payment_intent.succeeded webhook processing"""
        # Create appointment and transfer
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Test appointment",
            status='pending',
            payment_status='pending'
        )
        
        transfer = UserTransfer.objects.create(
            sender=self.patient,
            receiver=self.doctor,
            amount=7500,
            transfer_type=TransferType.PAYMENT,
            status='pending',
            stripe_payment_intent_id='pi_test_payment_123'
        )
        appointment.payment_transfer = transfer
        appointment.save()
        
        # Mock webhook event
        mock_event = Mock()
        mock_event.type = 'payment_intent.succeeded'
        mock_event.data.object = Mock(
            id='pi_test_payment_123',
            status='succeeded',
            amount=7500,
            currency='usd'
        )
        mock_construct_event.return_value = mock_event
        
        # Mock Google Meet creation
        mock_create_meet.return_value = {
            'hangoutLink': 'https://meet.google.com/test-meet-link',
            'id': 'test_event_id'
        }
        
        # Send webhook
        response = self.client.post(
            '/billing/webhooks/payment/',
            data=json.dumps({
                'type': 'payment_intent.succeeded',
                'data': {
                    'object': {
                        'id': 'pi_test_payment_123',
                        'status': 'succeeded',
                        'amount': 7500,
                        'currency': 'usd'
                    }
                }
            }),
            content_type='application/json',
            HTTP_STRIPE_SIGNATURE='test_signature'
        )
        
        # Verify webhook processed successfully
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify transfer was updated
        transfer.refresh_from_db()
        self.assertEqual(transfer.status, 'completed')
        
        # Verify appointment was updated
        appointment.refresh_from_db()
        self.assertEqual(appointment.status, 'confirmed')
        self.assertEqual(appointment.payment_status, 'paid')
    
    @patch('billing.webhooks.views.stripe.Webhook.construct_event')
    def test_payment_intent_payment_failed_webhook(self, mock_construct_event):
        """Test payment_intent.payment_failed webhook processing"""
        # Create appointment and transfer
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Test appointment",
            status='pending',
            payment_status='pending'
        )
        
        transfer = UserTransfer.objects.create(
            sender=self.patient,
            receiver=self.doctor,
            amount=7500,
            transfer_type=TransferType.PAYMENT,
            status='pending',
            stripe_payment_intent_id='pi_test_payment_456'
        )
        appointment.payment_transfer = transfer
        appointment.save()
        
        # Mock webhook event
        mock_event = Mock()
        mock_event.type = 'payment_intent.payment_failed'
        mock_event.data.object = Mock(
            id='pi_test_payment_456',
            status='requires_payment_method',
            last_payment_error={'message': 'Card declined'}
        )
        mock_construct_event.return_value = mock_event
        
        # Send webhook
        response = self.client.post(
            '/billing/webhooks/payment/',
            data=json.dumps({
                'type': 'payment_intent.payment_failed',
                'data': {
                    'object': {
                        'id': 'pi_test_payment_456',
                        'status': 'requires_payment_method',
                        'last_payment_error': {'message': 'Card declined'}
                    }
                }
            }),
            content_type='application/json',
            HTTP_STRIPE_SIGNATURE='test_signature'
        )
        
        # Verify webhook processed successfully
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify transfer was updated
        transfer.refresh_from_db()
        self.assertEqual(transfer.status, 'failed')
        
        # Verify appointment was updated
        appointment.refresh_from_db()
        self.assertEqual(appointment.status, 'cancelled')
        self.assertEqual(appointment.payment_status, 'failed')


class P2PTelemedicineEdgeCasesTest(TestCase):
    """Test Edge Cases and Error Handling"""
    
    def setUp(self):
        """Setup test data"""
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Dr. John',
            last_name='Smith',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Patient',
            role=self.patient_role
        )
        
        self.client = APIClient()
    
    def test_patient_cannot_access_doctor_payment_features(self):
        """Test that patients cannot access doctor payment features"""
        self.client.force_authenticate(user=self.patient)
        
        # Test setup payment account
        response = self.client.post('/billing/doctor-payments/setup_payment_account/')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('Only doctors can access', str(response.data))
        
        # Test add bank account
        response = self.client.post('/billing/doctor-payments/add_bank_account/')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Test payment status
        response = self.client.get('/billing/doctor-payments/payment_status/')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_appointment_with_incomplete_doctor_setup(self):
        """Test appointment creation fails with incomplete doctor setup"""
        # Create doctor with incomplete setup (no consultation profile)
        self.client.force_authenticate(user=self.patient)
        
        response = self.client.post(
            '/appointments/',
            data=json.dumps({
                'doctor': str(self.doctor.id),
                'start_time': (timezone.now() + timedelta(hours=1)).isoformat(),
                'end_time': (timezone.now() + timedelta(hours=2)).isoformat(),
                'appointment_type': 'booking',
                'mode': 'video_call',
                'direct_payment': True
            }),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('consultation profile not found', str(response.data))
    
    def test_appointment_with_doctor_not_available_for_telemedicine(self):
        """Test appointment creation fails when doctor not available for telemedicine"""
        # Create consultation profile that doesn't accept telemedicine
        DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=5000,
            consultation_duration=30,
            accepts_telemedicine=False,  # Not accepting telemedicine
            is_active=True,
            stripe_account_setup=False
        )
        
        self.client.force_authenticate(user=self.patient)
        
        response = self.client.post(
            '/appointments/',
            data=json.dumps({
                'doctor': str(self.doctor.id),
                'start_time': (timezone.now() + timedelta(hours=1)).isoformat(),
                'end_time': (timezone.now() + timedelta(hours=2)).isoformat(),
                'appointment_type': 'booking',
                'mode': 'video_call',
                'direct_payment': True
            }),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('not available for telemedicine', str(response.data))
    
    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.create')
    def test_payment_processing_stripe_error(self, mock_payment_create):
        """Test payment processing handles Stripe errors"""
        # Create complete setup
        consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=7500,
            consultation_duration=45,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True
        )
        
        UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_doctor',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True
        )
        
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Test appointment"
        )
        
        # Mock Stripe error
        import stripe
        mock_payment_create.side_effect = stripe.error.CardError(
            message='Your card was declined.',
            param='card_number',
            code='card_declined'
        )
        
        self.client.force_authenticate(user=self.patient)
        
        response = self.client.post(
            f'/appointments/{appointment.id}/process_payment/',
            data=json.dumps({
                'payment_method': 'pm_test_card_declined'
            }),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('payment failed', str(response.data).lower())
    
    def test_appointment_cancellation_with_refund(self):
        """Test appointment cancellation with refund processing"""
        # Create completed payment
        consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.doctor,
            consultation_fee=7500,
            consultation_duration=45,
            accepts_telemedicine=True,
            is_active=True,
            stripe_account_setup=True
        )
        
        appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Test appointment",
            status='confirmed',
            payment_status='paid'
        )
        
        transfer = UserTransfer.objects.create(
            sender=self.patient,
            receiver=self.doctor,
            amount=7500,
            transfer_type=TransferType.PAYMENT,
            status='completed',
            stripe_payment_intent_id='pi_test_payment_123'
        )
        appointment.payment_transfer = transfer
        appointment.save()
        
        self.client.force_authenticate(user=self.patient)
        
        with patch('billing.services.refund_service.stripe.Refund.create') as mock_refund:
            mock_refund.return_value = Mock(
                id='re_test_refund_123',
                status='succeeded',
                amount=7500
            )
            
            response = self.client.post(
                f'/appointments/{appointment.id}/cancel/',
                data=json.dumps({
                    'reason': 'Patient needs to reschedule',
                    'refund_requested': True
                }),
                content_type='application/json'
            )
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            
            # Verify appointment was cancelled
            appointment.refresh_from_db()
            self.assertEqual(appointment.status, 'cancelled')
    
    def test_webhook_replay_protection(self):
        """Test webhook replay attack protection"""
        # This test ensures the same webhook event isn't processed twice
        # Implementation would depend on webhook service's idempotency handling
        pass
    
    def test_platform_fee_calculation_accuracy(self):
        """Test platform fee calculation accuracy for various amounts"""
        test_amounts = [500, 1000, 2500, 5000, 7500, 10000, 25000, 50000]
        
        for amount in test_amounts:
            platform_fee = int(amount * 0.03)  # 3%
            net_amount = amount - platform_fee
            
            # Verify calculations are correct
            self.assertEqual(platform_fee, int(amount * 0.03))
            self.assertEqual(net_amount, amount - platform_fee)
            
            # Verify minimum fee constraints
            if amount >= 500:  # $5 minimum
                self.assertGreaterEqual(platform_fee, 15)  # At least 15 cents


class P2PTelemedicineIntegrationTest(TransactionTestCase):
    """Integration test for complete P2P telemedicine flow"""
    
    def setUp(self):
        """Setup test data"""
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor', description='Doctor role')
        self.patient_role = Role.objects.create(name='patient', description='Patient role')
        
        # Create users
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Dr. John',
            last_name='Smith',
            role=self.doctor_role
        )
        
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Patient',
            role=self.patient_role
        )
        
        self.client = APIClient()
    
    @patch('billing.services.stripe_connect_service.stripe.Account.create')
    @patch('billing.services.stripe_connect_service.stripe.AccountLink.create')
    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.create')
    @patch('billing.services.user_transfer_service.stripe.Account.retrieve')
    @patch('appointments.services.google_meet_service.GoogleMeetService.create_meet')
    def test_complete_p2p_telemedicine_flow(self, mock_create_meet, mock_account_retrieve, 
                                          mock_payment_create, mock_account_link, mock_account_create):
        """Test complete end-to-end P2P telemedicine flow"""
        
        # Setup mocks
        mock_account_create.return_value = Mock(id='acct_test_doctor_123')
        mock_account_link.return_value = Mock(
            url='https://connect.stripe.com/express/onboarding/test_link'
        )
        mock_account_retrieve.return_value = Mock(
            charges_enabled=True,
            details_submitted=True
        )
        mock_payment_create.return_value = Mock(
            id='pi_test_payment_123',
            client_secret='pi_test_payment_123_secret_test',
            status='requires_payment_method',
            amount=7500,
            currency='usd'
        )
        mock_create_meet.return_value = {
            'hangoutLink': 'https://meet.google.com/test-meet-link',
            'id': 'test_event_id'
        }
        
        # Phase 1: Doctor Setup
        self.client.force_authenticate(user=self.doctor)
        
        # Step 1: Create Stripe Connect Account
        response = self.client.post(
            '/billing/doctor-payments/setup_payment_account/',
            data=json.dumps({
                'account_type': 'express',
                'country': 'US',
                'business_type': 'individual'
            }),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Simulate webhook completion of onboarding
        payment_profile = UserPaymentProfile.objects.get(user=self.doctor)
        payment_profile.charges_enabled = True
        payment_profile.payouts_enabled = True
        payment_profile.details_submitted = True
        payment_profile.is_verified = True
        payment_profile.verification_date = timezone.now()
        payment_profile.save()
        
        # Step 2: Create Consultation Profile
        response = self.client.post(
            '/appointments/doctor-consultation-profiles/',
            data=json.dumps({
                'consultation_fee': 7500,
                'consultation_duration': 45,
                'accepts_telemedicine': True,
                'bio': 'Experienced doctor for telemedicine',
                'specializations': ['Family Medicine'],
                'languages': ['English'],
                'stripe_account_setup': True
            }),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Phase 2: Patient Booking
        self.client.force_authenticate(user=self.patient)
        
        # Step 3: Create Appointment
        future_time = timezone.now() + timedelta(hours=24)
        response = self.client.post(
            '/appointments/',
            data=json.dumps({
                'doctor': str(self.doctor.id),
                'start_time': future_time.isoformat(),
                'end_time': (future_time + timedelta(minutes=45)).isoformat(),
                'appointment_type': 'booking',
                'mode': 'video_call',
                'direct_payment': True,
                'notes': 'Follow-up consultation'
            }),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        appointment_id = response.data['id']
        
        # Step 4: Process Payment
        response = self.client.post(
            f'/appointments/{appointment_id}/process_payment/',
            data=json.dumps({
                'payment_method': 'pm_test_card'
            }),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('client_secret', response.data)
        
        # Verify complete flow
        appointment = Appointment.objects.get(id=appointment_id)
        self.assertEqual(appointment.patient, self.patient)
        self.assertEqual(appointment.doctor, self.doctor)
        self.assertTrue(appointment.direct_payment)
        self.assertEqual(appointment.consultation_fee, 7500)
        
        transfer = UserTransfer.objects.get(appointments=appointment)
        self.assertEqual(transfer.sender, self.patient)
        self.assertEqual(transfer.receiver, self.doctor)
        self.assertEqual(transfer.amount, 7500)
        self.assertEqual(transfer.platform_fee_amount, 225)  # 3%
        self.assertEqual(transfer.net_amount, 7275)  # 7500 - 225


# Performance and monitoring tests
class P2PTelemedicinePerformanceTest(TestCase):
    """Test performance aspects of P2P telemedicine flow"""
    
    def test_database_queries_optimization(self):
        """Test that database queries are optimized for common operations"""
        # This would test query count for appointment creation, payment processing, etc.
        # Implementation depends on specific performance requirements
        pass
    
    def test_concurrent_appointment_booking(self):
        """Test handling of concurrent appointment bookings"""
        # This would test race conditions in appointment booking
        # Implementation depends on concurrency requirements
        pass


# Security tests
class P2PTelemedicineSecurityTest(TestCase):
    """Test security aspects of P2P telemedicine flow"""
    
    def test_payment_data_encryption(self):
        """Test that sensitive payment data is properly encrypted"""
        # Implementation would test encryption of sensitive fields
        pass
    
    def test_access_control_enforcement(self):
        """Test that access controls are properly enforced"""
        # Implementation would test authorization at various endpoints
        pass


if __name__ == '__main__':
    pytest.main([__file__, '-v']) 