import os
from django.test import TestCase
from django.conf import settings
from appointments.services.google_meet import get_google_meet_service

class GoogleMeetServiceTest(TestCase):
    def test_service_account_credentials(self):
        """Test that service account credentials file exists and is valid"""
        creds_path = settings.GOOGLE_CALENDAR_API['SERVICE_ACCOUNT_FILE']
        self.assertTrue(
            os.path.exists(creds_path),
            f"Service account credentials file not found at {creds_path}"
        )
        
        # Try to initialize service
        service = get_google_meet_service()
        self.assertIsNotNone(
            service,
            "Failed to initialize Google Calendar service with service account"
        ) 