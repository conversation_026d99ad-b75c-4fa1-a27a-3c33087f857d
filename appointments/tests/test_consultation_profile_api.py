"""
Test cases for Doctor Consultation Profile API endpoints - Individual Doctor Only
"""

import json
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model

from appointments.models import DoctorConsultationProfile
from roles.models import Role

User = get_user_model()


class DoctorConsultationProfileAPITestCase(TestCase):
    """Test cases for DoctorConsultationProfile API - Individual Doctor Only"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create roles
        self.doctor_role = Role.objects.create(name='doctor')
        self.patient_role = Role.objects.create(name='patient')
        
        # Create users
        self.doctor_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Dr. <PERSON>',
            last_name='Smith',
            role=self.doctor_role
        )
        
        self.doctor_user2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Dr. <PERSON>',
            last_name='Doe',
            role=self.doctor_role
        )
        
        self.patient_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Patient',
            last_name='User',
            role=self.patient_role
        )
        
        # Valid consultation profile data
        self.valid_profile_data = {
            'consultation_fee': 5000,  # $50.00
            'consultation_duration': 30,
            'accepts_telemedicine': True,
            'bio': 'Experienced doctor specializing in telemedicine',
            'specializations': ['General Practice', 'Internal Medicine'],
            'languages': ['English', 'Vietnamese'],
            'stripe_account_setup': True
        }
        
        # API endpoints
        self.list_create_url = reverse('doctor-consultation-profiles-list')
    
    def test_doctor_create_own_consultation_profile_success(self):
        """Test doctor can create their own consultation profile"""
        self.client.force_authenticate(user=self.doctor_user)
        
        response = self.client.post(
            self.list_create_url,
            data=json.dumps(self.valid_profile_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(DoctorConsultationProfile.objects.count(), 1)
        
        profile = DoctorConsultationProfile.objects.first()
        self.assertEqual(profile.user, self.doctor_user)
        self.assertEqual(profile.consultation_fee, 5000)
        self.assertEqual(profile.consultation_duration, 30)
        self.assertTrue(profile.accepts_telemedicine)
        self.assertEqual(profile.bio, 'Experienced doctor specializing in telemedicine')
        self.assertEqual(profile.specializations, ['General Practice', 'Internal Medicine'])
        self.assertEqual(profile.languages, ['English', 'Vietnamese'])
        self.assertTrue(profile.stripe_account_setup)
    
    def test_doctor_create_duplicate_profile_forbidden(self):
        """Test doctor cannot create second consultation profile"""
        # Create first profile
        DoctorConsultationProfile.objects.create(
            user=self.doctor_user,
            consultation_fee=4000,
            consultation_duration=45
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        response = self.client.post(
            self.list_create_url,
            data=json.dumps(self.valid_profile_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('already have a consultation profile', str(response.data))
        self.assertEqual(DoctorConsultationProfile.objects.count(), 1)
    
    def test_patient_cannot_create_consultation_profile(self):
        """Test patient cannot create consultation profile"""
        self.client.force_authenticate(user=self.patient_user)
        
        response = self.client.post(
            self.list_create_url,
            data=json.dumps(self.valid_profile_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('Only doctors can create', str(response.data))
        self.assertEqual(DoctorConsultationProfile.objects.count(), 0)
    
    def test_create_consultation_profile_invalid_fee_range(self):
        """Test create consultation profile with fee outside valid range"""
        self.client.force_authenticate(user=self.doctor_user)
        
        # Test fee too low
        invalid_data = self.valid_profile_data.copy()
        invalid_data['consultation_fee'] = 100  # Less than $5 minimum
        
        response = self.client.post(
            self.list_create_url,
            data=json.dumps(invalid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test fee too high
        invalid_data['consultation_fee'] = 100000  # More than $500 maximum
        
        response = self.client.post(
            self.list_create_url,
            data=json.dumps(invalid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_create_consultation_profile_telemedicine_requires_bio(self):
        """Test create consultation profile requires bio when accepts_telemedicine is true"""
        self.client.force_authenticate(user=self.doctor_user)
        
        invalid_data = self.valid_profile_data.copy()
        invalid_data['bio'] = ''  # Empty bio
        invalid_data['accepts_telemedicine'] = True
        
        response = self.client.post(
            self.list_create_url,
            data=json.dumps(invalid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Bio is required', str(response.data))
    
    def test_list_consultation_profiles_as_doctor_own_only(self):
        """Test doctor can only see their own consultation profile"""
        # Create test profiles
        profile1 = DoctorConsultationProfile.objects.create(
            user=self.doctor_user,
            consultation_fee=5000,
            consultation_duration=30
        )
        
        profile2 = DoctorConsultationProfile.objects.create(
            user=self.doctor_user2,
            consultation_fee=4000,
            consultation_duration=45
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        response = self.client.get(self.list_create_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['doctor_email'], self.doctor_user.email)
    
    def test_list_consultation_profiles_non_doctor_forbidden(self):
        """Test non-doctor cannot list consultation profiles"""
        self.client.force_authenticate(user=self.patient_user)
        
        response = self.client.get(self.list_create_url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('Only doctors can access', str(response.data))
    
    def test_retrieve_own_consultation_profile(self):
        """Test doctor can retrieve their own consultation profile"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor_user,
            consultation_fee=5000,
            consultation_duration=30,
            bio='Test bio'
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        response = self.client.get(f"{self.list_create_url}{profile.id}/")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(profile.id))
        self.assertEqual(response.data['doctor_email'], self.doctor_user.email)
    
    def test_retrieve_other_doctor_profile_forbidden(self):
        """Test doctor cannot retrieve another doctor's profile"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor_user2,
            consultation_fee=5000,
            consultation_duration=30,
            bio='Test bio'
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        response = self.client.get(f"{self.list_create_url}{profile.id}/")
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_update_own_consultation_profile(self):
        """Test doctor can update their own consultation profile"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor_user,
            consultation_fee=5000,
            consultation_duration=30,
            bio='Original bio'
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        update_data = {
            'consultation_fee': 6000,
            'bio': 'Updated bio'
        }
        
        response = self.client.patch(
            f"{self.list_create_url}{profile.id}/",
            data=json.dumps(update_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        profile.refresh_from_db()
        self.assertEqual(profile.consultation_fee, 6000)
        self.assertEqual(profile.bio, 'Updated bio')
    
    def test_update_other_doctor_profile_forbidden(self):
        """Test doctor cannot update another doctor's profile"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor_user2,
            consultation_fee=5000,
            consultation_duration=30,
            bio='Original bio'
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        update_data = {'bio': 'Hacked bio'}
        
        response = self.client.patch(
            f"{self.list_create_url}{profile.id}/",
            data=json.dumps(update_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_delete_own_consultation_profile(self):
        """Test doctor can delete their own consultation profile"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor_user,
            consultation_fee=5000,
            consultation_duration=30
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        response = self.client.delete(f"{self.list_create_url}{profile.id}/")
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(DoctorConsultationProfile.objects.count(), 0)
    
    def test_delete_other_doctor_profile_forbidden(self):
        """Test doctor cannot delete another doctor's profile"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor_user2,
            consultation_fee=5000,
            consultation_duration=30
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        response = self.client.delete(f"{self.list_create_url}{profile.id}/")
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(DoctorConsultationProfile.objects.count(), 1)
    
    def test_my_profile_endpoint(self):
        """Test my_profile endpoint returns doctor's own profile"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor_user,
            consultation_fee=5000,
            consultation_duration=30,
            bio='Test bio'
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        my_profile_url = reverse('doctor-consultation-profiles-my-profile')
        response = self.client.get(my_profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(profile.id))
        self.assertEqual(response.data['doctor_email'], self.doctor_user.email)
    
    def test_my_profile_not_found(self):
        """Test my_profile returns 404 when no profile exists"""
        self.client.force_authenticate(user=self.doctor_user)
        
        my_profile_url = reverse('doctor-consultation-profiles-my-profile')
        response = self.client.get(my_profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_my_profile_non_doctor_forbidden(self):
        """Test my_profile endpoint forbidden for non-doctors"""
        self.client.force_authenticate(user=self.patient_user)
        
        my_profile_url = reverse('doctor-consultation-profiles-my-profile')
        response = self.client.get(my_profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_toggle_telemedicine_endpoint(self):
        """Test toggle_telemedicine endpoint"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor_user,
            consultation_fee=5000,
            consultation_duration=30,
            accepts_telemedicine=False
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        toggle_url = reverse('doctor-consultation-profiles-toggle-telemedicine')
        response = self.client.post(toggle_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        profile.refresh_from_db()
        self.assertTrue(profile.accepts_telemedicine)
        
        # Toggle back
        response = self.client.post(toggle_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        profile.refresh_from_db()
        self.assertFalse(profile.accepts_telemedicine)
    
    def test_toggle_active_endpoint(self):
        """Test toggle_active endpoint"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor_user,
            consultation_fee=5000,
            consultation_duration=30,
            is_active=True
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        toggle_url = reverse('doctor-consultation-profiles-toggle-active')
        response = self.client.post(toggle_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        profile.refresh_from_db()
        self.assertFalse(profile.is_active)
        
        # Toggle back
        response = self.client.post(toggle_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        profile.refresh_from_db()
        self.assertTrue(profile.is_active)
    
    def test_toggle_endpoints_non_doctor_forbidden(self):
        """Test toggle endpoints forbidden for non-doctors"""
        self.client.force_authenticate(user=self.patient_user)
        
        toggle_telemedicine_url = reverse('doctor-consultation-profiles-toggle-telemedicine')
        response = self.client.post(toggle_telemedicine_url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        toggle_active_url = reverse('doctor-consultation-profiles-toggle-active')
        response = self.client.post(toggle_active_url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_consultation_profile_response_structure(self):
        """Test consultation profile response contains expected fields"""
        profile = DoctorConsultationProfile.objects.create(
            user=self.doctor_user,
            consultation_fee=5000,
            consultation_duration=30,
            accepts_telemedicine=True,
            bio='Test bio',
            specializations=['General Practice'],
            languages=['English'],
            is_active=True,
            stripe_account_setup=True
        )
        
        self.client.force_authenticate(user=self.doctor_user)
        
        response = self.client.get(f"{self.list_create_url}{profile.id}/")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        expected_fields = [
            'id', 'consultation_fee', 'consultation_duration', 'accepts_telemedicine',
            'bio', 'specializations', 'languages', 'is_active', 'stripe_account_setup',
            'consultation_fee_usd', 'consultation_fee_display', 'specializations_display',
            'languages_display', 'is_available_for_telemedicine', 'doctor_name', 'doctor_email'
        ]
        
        for field in expected_fields:
            self.assertIn(field, response.data)
        
        # Check computed fields
        self.assertEqual(response.data['consultation_fee_usd'], 50.0)
        self.assertEqual(response.data['consultation_fee_display'], '$50.00')
        self.assertEqual(response.data['specializations_display'], 'General Practice')
        self.assertEqual(response.data['languages_display'], 'English')
        self.assertTrue(response.data['is_available_for_telemedicine'])
        self.assertEqual(response.data['doctor_name'], 'Dr. John Smith')
        self.assertEqual(response.data['doctor_email'], self.doctor_user.email)
    
    def test_consultation_profile_unauthenticated_access(self):
        """Test unauthenticated users cannot access consultation profile endpoints"""
        response = self.client.get(self.list_create_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        response = self.client.post(self.list_create_url, data=self.valid_profile_data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED) 