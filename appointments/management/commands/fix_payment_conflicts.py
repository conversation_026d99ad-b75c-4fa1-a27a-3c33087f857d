"""
Django management command to fix appointments with conflicting payment methods.
Usage: python manage.py fix_payment_conflicts [--dry-run] [--strategy=<strategy>]
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from appointments.models import Appointment


class Command(BaseCommand):
    help = 'Fix appointments with conflicting payment methods (both insurance and direct_payment True)'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without actually making changes',
        )
        parser.add_argument(
            '--strategy',
            type=str,
            choices=['prefer-insurance', 'prefer-direct', 'interactive'],
            default='interactive',
            help='Strategy for resolving conflicts: prefer-insurance, prefer-direct, or interactive',
        )
        parser.add_argument(
            '--auto-confirm',
            action='store_true',
            help='Automatically confirm changes without prompting (use with caution)',
        )
    
    def handle(self, *args, **options):
        dry_run = options['dry_run']
        strategy = options['strategy']
        auto_confirm = options['auto_confirm']
        
        self.stdout.write(self.style.SUCCESS('🔍 Checking for payment method conflicts...'))
        
        # Find conflicting appointments
        conflicting_appointments = Appointment.objects.filter(
            insurance=True,
            direct_payment=True
        ).select_related('patient', 'doctor')
        
        count = conflicting_appointments.count()
        
        if count == 0:
            self.stdout.write(self.style.SUCCESS('✅ No conflicting appointments found!'))
            return
        
        self.stdout.write(
            self.style.WARNING(f'⚠️  Found {count} appointment(s) with conflicting payment methods')
        )
        
        if dry_run:
            self.stdout.write(self.style.NOTICE('🔍 DRY RUN MODE - No changes will be made'))
        
        # Process each conflicting appointment
        fixed_count = 0
        skipped_count = 0
        
        for appointment in conflicting_appointments:
            self.stdout.write('\n' + '='*60)
            self.display_appointment_info(appointment)
            
            if strategy == 'interactive' and not auto_confirm:
                action = self.get_user_choice()
            else:
                action = self.get_automatic_action(strategy)
            
            if action == 'skip':
                skipped_count += 1
                self.stdout.write(self.style.NOTICE('⏭️  Skipped'))
                continue
            
            # Apply the fix
            if not dry_run:
                try:
                    with transaction.atomic():
                        if action == 'insurance':
                            appointment.direct_payment = False
                            appointment.save()
                            self.stdout.write(self.style.SUCCESS('✅ Set to insurance only'))
                        elif action == 'direct':
                            appointment.insurance = False
                            appointment.save()
                            self.stdout.write(self.style.SUCCESS('✅ Set to direct payment only'))
                        
                        fixed_count += 1
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'❌ Error fixing appointment {appointment.id}: {str(e)}'))
            else:
                if action == 'insurance':
                    self.stdout.write(self.style.NOTICE('🔍 Would set to insurance only'))
                elif action == 'direct':
                    self.stdout.write(self.style.NOTICE('🔍 Would set to direct payment only'))
                fixed_count += 1
        
        # Summary
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('📊 SUMMARY:'))
        self.stdout.write(f'Total conflicts found: {count}')
        self.stdout.write(f'Fixed: {fixed_count}')
        self.stdout.write(f'Skipped: {skipped_count}')
        
        if dry_run:
            self.stdout.write(self.style.NOTICE('🔍 This was a dry run - no actual changes were made'))
            self.stdout.write('Run without --dry-run to apply changes')
        elif fixed_count > 0:
            self.stdout.write(self.style.SUCCESS('✅ All conflicts resolved!'))
            self.stdout.write('You can now safely run the migration:')
            self.stdout.write('python manage.py migrate appointments 0013')
    
    def display_appointment_info(self, appointment):
        """Display detailed information about the conflicting appointment"""
        self.stdout.write(f'Appointment ID: {appointment.id}')
        self.stdout.write(f'Title: {appointment.title or "N/A"}')
        self.stdout.write(f'Type: {appointment.appointment_type}')
        self.stdout.write(f'Patient: {appointment.patient.email if appointment.patient else "N/A"}')
        self.stdout.write(f'Doctor: {appointment.doctor.email if appointment.doctor else "N/A"}')
        self.stdout.write(f'Start Time: {appointment.start_time}')
        self.stdout.write(f'Status: {appointment.status}')
        self.stdout.write(f'Insurance: {appointment.insurance}')
        self.stdout.write(f'Direct Payment: {appointment.direct_payment}')
        self.stdout.write(f'Created: {appointment.created_at}')
    
    def get_user_choice(self):
        """Get user's choice for resolving the conflict"""
        while True:
            choice = input('\nHow should this conflict be resolved?\n'
                         '1. Keep insurance only (set direct_payment=False)\n'
                         '2. Keep direct payment only (set insurance=False)\n'
                         '3. Skip this appointment\n'
                         'Enter choice (1/2/3): ').strip()
            
            if choice == '1':
                return 'insurance'
            elif choice == '2':
                return 'direct'
            elif choice == '3':
                return 'skip'
            else:
                self.stdout.write(self.style.ERROR('Invalid choice. Please enter 1, 2, or 3.'))
    
    def get_automatic_action(self, strategy):
        """Get automatic action based on strategy"""
        if strategy == 'prefer-insurance':
            return 'insurance'
        elif strategy == 'prefer-direct':
            return 'direct'
        else:
            return 'skip'
