from django.core.management.base import BaseCommand
from accounts.models import CustomUser
from accounts.helpers import get_user_by_identifier


class Command(BaseCommand):
    help = 'Check if doctor exists with given identifier'

    def add_arguments(self, parser):
        parser.add_argument('identifier', type=str, help='Doctor identifier (custom_url_username or UUID)')
        parser.add_argument(
            '--list-doctors',
            action='store_true',
            help='List all doctors with their custom_url_username',
        )

    def handle(self, *args, **options):
        identifier = options['identifier']
        
        if options['list_doctors']:
            self.stdout.write(self.style.SUCCESS('\n=== All Doctors ==='))
            doctors = CustomUser.objects.filter(role__name='doctor').values(
                'id', 'email', 'first_name', 'last_name', 'custom_url_username'
            )
            for doctor in doctors:
                self.stdout.write(f"ID: {doctor['id']}")
                self.stdout.write(f"Email: {doctor['email']}")
                self.stdout.write(f"Name: {doctor['first_name']} {doctor['last_name']}")
                self.stdout.write(f"Custom URL: {doctor['custom_url_username']}")
                self.stdout.write("---")
        
        self.stdout.write(f"\n=== Checking identifier: '{identifier}' ===")
        
        # Test get_user_by_identifier function
        try:
            user = get_user_by_identifier(identifier)
            if user:
                self.stdout.write(self.style.SUCCESS(f"✓ Found user via get_user_by_identifier:"))
                self.stdout.write(f"  ID: {user.id}")
                self.stdout.write(f"  Email: {user.email}")
                self.stdout.write(f"  Name: {user.first_name} {user.last_name}")
                self.stdout.write(f"  Custom URL: {user.custom_url_username}")
                self.stdout.write(f"  Role: {user.role.name if user.role else 'No role'}")
            else:
                self.stdout.write(self.style.ERROR(f"✗ get_user_by_identifier returned None"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Exception in get_user_by_identifier: {e}"))
        
        # Test direct queries
        self.stdout.write(f"\n=== Direct Database Queries ===")
        
        # Query by custom_url_username
        try:
            by_custom_url = CustomUser.objects.filter(custom_url_username=identifier).first()
            if by_custom_url:
                self.stdout.write(self.style.SUCCESS(f"✓ Found by custom_url_username: {by_custom_url.email}"))
            else:
                self.stdout.write(self.style.WARNING(f"⚠ Not found by custom_url_username"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Error querying by custom_url_username: {e}"))
        
        # Query by ID
        try:
            by_id = CustomUser.objects.filter(id=identifier).first()
            if by_id:
                self.stdout.write(self.style.SUCCESS(f"✓ Found by ID: {by_id.email}"))
            else:
                self.stdout.write(self.style.WARNING(f"⚠ Not found by ID"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Error querying by ID: {e}"))
        
        # Query all users with similar custom_url_username
        self.stdout.write(f"\n=== Similar Custom URLs ===")
        try:
            similar = CustomUser.objects.filter(
                custom_url_username__icontains=identifier
            ).values('custom_url_username', 'email')[:5]
            
            if similar:
                self.stdout.write("Similar custom_url_usernames found:")
                for user in similar:
                    self.stdout.write(f"  {user['custom_url_username']} ({user['email']})")
            else:
                self.stdout.write("No similar custom_url_usernames found")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Error querying similar: {e}")) 