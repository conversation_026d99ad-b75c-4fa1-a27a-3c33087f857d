# Appointment Payment Method Validation

## Overview

This document describes the validation implemented to prevent appointments from having conflicting payment methods. Specifically, an appointment cannot have both `insurance=True` and `direct_payment=True` simultaneously.

## Business Logic

In the healthcare appointment system, a patient can pay for an appointment through:
1. **Insurance**: The appointment cost is covered by the patient's insurance provider
2. **Direct Payment**: The patient pays directly out-of-pocket
3. **No Payment**: For free consultations or manual events

These payment methods are mutually exclusive - an appointment cannot use both insurance and direct payment at the same time.

## Implementation

### 1. Model Level Validation

**File**: `appointments/models/appointment.py`

```python
def clean(self):
    # ... existing validations ...
    
    # Validate payment method exclusivity for booking appointments
    if self.appointment_type == 'booking' and self.insurance and self.direct_payment:
        raise ValidationError("Appointment cannot use both insurance and direct payment simultaneously. Please choose one payment method.")
```

### 2. Serializer Level Validation

**File**: `appointments/api/serializers/appointment.py`

```python
# Validate insurance/direct_payment mutual exclusion
insurance = data.get('insurance', False)
direct_payment = data.get('direct_payment', False)

if insurance and direct_payment:
    raise serializers.ValidationError({
        'error': 'Payment method conflict',
        'detail': 'Appointment cannot use both insurance and direct payment simultaneously. Please choose one payment method.'
    })
```

### 3. Database Constraint

**File**: `appointments/migrations/0013_appointment_payment_method_constraint.py`

A database-level constraint ensures data integrity:

```python
migrations.AddConstraint(
    model_name='appointment',
    constraint=CheckConstraint(
        check=~(Q(insurance=True) & Q(direct_payment=True)),
        name='appointment_payment_method_exclusive',
        violation_error_message='Appointment cannot use both insurance and direct payment simultaneously.'
    ),
)
```

## Data Migration Process

### Step 1: Check for Existing Conflicts

Before applying the constraint, check for existing data conflicts:

```bash
# Run the check script
python scripts/check_payment_conflicts.py
```

### Step 2: Fix Existing Conflicts

Use the management command to resolve conflicts:

```bash
# Interactive mode (recommended)
python manage.py fix_payment_conflicts

# Dry run to see what would be changed
python manage.py fix_payment_conflicts --dry-run

# Automatic resolution (prefer insurance)
python manage.py fix_payment_conflicts --strategy=prefer-insurance --auto-confirm

# Automatic resolution (prefer direct payment)
python manage.py fix_payment_conflicts --strategy=prefer-direct --auto-confirm
```

### Step 3: Apply Migration

After resolving conflicts, apply the database constraint:

```bash
python manage.py migrate appointments 0013
```

## Testing

### Running Tests

```bash
# Run payment validation tests
python manage.py test appointments.tests.test_payment_validation

# Run all appointment tests
python manage.py test appointments
```

### Test Coverage

The test suite covers:
- Model validation for all payment method combinations
- Serializer validation for API requests
- Database constraint enforcement
- Edge cases for manual vs booking appointments

## API Behavior

### Valid Requests

```json
// Insurance only
{
    "insurance": true,
    "direct_payment": false
}

// Direct payment only
{
    "insurance": false,
    "direct_payment": true
}

// No payment method specified (both default to false)
{
    // insurance and direct_payment not specified
}
```

### Invalid Requests

```json
// This will be rejected
{
    "insurance": true,
    "direct_payment": true
}
```

**Response**:
```json
{
    "error": "Payment method conflict",
    "detail": "Appointment cannot use both insurance and direct payment simultaneously. Please choose one payment method."
}
```

## Monitoring and Maintenance

### Checking for Violations

Periodically run the check script to ensure no conflicts exist:

```bash
python scripts/check_payment_conflicts.py
```

### Database Queries

To manually check for conflicts:

```sql
SELECT id, title, insurance, direct_payment, created_at
FROM appointments_appointment 
WHERE insurance = true AND direct_payment = true;
```

## Rollback Plan

If needed, the constraint can be removed:

```python
# Create a new migration
python manage.py makemigrations appointments --empty

# In the migration file:
operations = [
    migrations.RemoveConstraint(
        model_name='appointment',
        name='appointment_payment_method_exclusive',
    ),
]
```

## Future Considerations

1. **Additional Payment Methods**: If new payment methods are added (e.g., `insurance_copay`, `payment_plan`), update the validation logic accordingly.

2. **Partial Payments**: If the system needs to support split payments (part insurance, part direct), the business logic and validation will need to be redesigned.

3. **Audit Trail**: Consider adding an audit trail to track payment method changes for compliance purposes.

## Related Files

- `appointments/models/appointment.py` - Model definition and validation
- `appointments/api/serializers/appointment.py` - API serializer validation
- `appointments/migrations/0013_appointment_payment_method_constraint.py` - Database constraint
- `appointments/tests/test_payment_validation.py` - Test cases
- `appointments/management/commands/fix_payment_conflicts.py` - Data fix command
- `scripts/check_payment_conflicts.py` - Conflict detection script
