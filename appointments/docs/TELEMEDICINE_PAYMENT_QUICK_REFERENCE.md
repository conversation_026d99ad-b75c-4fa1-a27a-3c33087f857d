# Telemedicine Payment Quick Reference Guide
## Ravid Healthcare Platform

### **API Endpoints Summary**

#### **Doctor Setup APIs**
```bash
# 1. Create Stripe Connect Account
POST /billing/doctor-payments/setup_payment_account/
# Response: account_link for onboarding

# 2. Add Bank Account
POST /billing/doctor-payments/add_bank_account/
# Body: account_holder_name, routing_number, account_number

# 3. Create Consultation Profile
POST /appointments/doctor-consultation-profiles/
# Body: consultation_fee, consultation_duration, accepts_telemedicine

# 4. Check Payment Status
GET /billing/doctor-payments/payment_status/
# Response: charges_enabled, payouts_enabled, can_receive_payments
```

#### **Patient Booking APIs**
```bash
# 1. Create Appointment
POST /appointments/
# Body: doctor, start_time, end_time, mode: "video_call", direct_payment: true

# 2. Process Payment (automatic)
POST /appointments/<id>/process_payment/
# Body: payment_method

# 3. Get Appointment Details
GET /appointments/<id>/
# Response: includes payment_status, google_meet_link
```

#### **Management APIs**
```bash
# Doctor Earnings
GET /billing/doctor-payments/earnings_summary/?days=30

# Payment History
GET /billing/transfers/

# Cancel with Refund
POST /appointments/<id>/cancel/
# Body: reason, refund_requested: true
```

### **Model Relationships**

```
CustomUser (Doctor)
├── UserPaymentProfile (1:1) - Stripe Connect account
├── DoctorConsultationProfile (1:1) - Fee & settings
└── received_transfers (1:M) - Payment history

CustomUser (Patient)
├── sent_transfers (1:M) - Payment history
└── patient_appointments (1:M) - Booking history

Appointment
├── payment_transfer (1:1) - UserTransfer
├── doctor (FK) - CustomUser
└── patient (FK) - CustomUser

UserTransfer
├── sender (FK) - Patient
├── receiver (FK) - Doctor
└── appointments (1:M) - Related appointments
```

### **Key Validation Rules**

```python
# Doctor can receive telemedicine payments
doctor.consultation_profile.is_available_for_telemedicine()
# = accepts_telemedicine AND is_active AND stripe_account_setup

# Appointment requires payment
appointment.is_payment_required()
# = appointment_type == 'booking' AND direct_payment AND mode == 'video_call'

# Fee validation
500 <= consultation_fee <= 50000  # $5.00 - $500.00
15 <= consultation_duration <= 180  # 15 min - 3 hours
```

### **Payment Flow States**

```
Doctor Setup:
stripe_account_setup: false → true
charges_enabled: false → true
payouts_enabled: false → true

Appointment Payment:
payment_status: pending → paid
appointment.status: pending → confirmed → completed

Transfer Status:
status: pending → completed
```

### **Platform Fee Calculation**

```python
consultation_fee = 7500  # $75.00
platform_fee = int(consultation_fee * 0.03)  # 3% = 225 cents ($2.25)
doctor_receives = consultation_fee - platform_fee  # 7275 cents ($72.75)
```

### **Common Error Codes**

| Error | Cause | Solution |
|-------|-------|----------|
| `ValidationError: Doctor consultation profile not found` | No consultation profile | Create profile first |
| `ValidationError: Doctor is not available for telemedicine` | Setup incomplete | Complete Stripe onboarding |
| `PaymentError: Failed to process consultation payment` | Stripe issue | Check account status |
| `PermissionDeniedError: Only doctors can access payment features` | Wrong user role | Verify user permissions |

### **Testing Checklist**

#### **Doctor Setup Testing**
- [ ] Create Stripe Connect account
- [ ] Complete onboarding flow
- [ ] Add bank account
- [ ] Create consultation profile
- [ ] Verify payment status API

#### **Payment Flow Testing**
- [ ] Create appointment with direct_payment=true
- [ ] Process payment with test card
- [ ] Verify webhook processing
- [ ] Check Google Meet link creation
- [ ] Confirm email notifications

#### **Edge Cases Testing**
- [ ] Payment failure scenarios
- [ ] Appointment cancellation with refund
- [ ] Invalid fee amounts
- [ ] Incomplete doctor setup
- [ ] Webhook replay scenarios

### **Environment Configuration**

```python
# Required Stripe settings
STRIPE_PUBLISHABLE_KEY = "pk_test_..."
STRIPE_SECRET_KEY = "sk_test_..."
STRIPE_WEBHOOK_SECRET = "whsec_..."

# Platform fee configuration
TELEMEDICINE_PLATFORM_FEE_PERCENTAGE = 0.03  # 3%

# Frontend URLs for Stripe onboarding
FRONTEND_URL = "https://app.ravid.cloud"
```

### **Database Queries for Monitoring**

```sql
-- Doctor setup completion rate
SELECT 
    COUNT(*) as total_doctors,
    COUNT(CASE WHEN up.charges_enabled = true THEN 1 END) as setup_complete
FROM accounts_customuser u
LEFT JOIN billing_user_payment_profile up ON u.id = up.user_id
WHERE u.role_id = (SELECT id FROM roles_role WHERE name = 'doctor');

-- Daily telemedicine revenue
SELECT 
    DATE(created_at) as date,
    COUNT(*) as appointments,
    SUM(amount) as gross_revenue,
    SUM(platform_fee_amount) as platform_fees
FROM billing_user_transfer 
WHERE transfer_type = 'payment' 
    AND status = 'completed'
    AND created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Payment failure analysis
SELECT 
    status,
    COUNT(*) as count,
    AVG(amount) as avg_amount
FROM billing_user_transfer 
WHERE transfer_type = 'payment'
    AND created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY status;
```

### **Webhook Event Handling**

```python
# Key Stripe webhook events to handle
WEBHOOK_EVENTS = [
    'account.updated',           # Connect account status changes
    'payment_intent.succeeded',  # Payment completed
    'payment_intent.payment_failed',  # Payment failed
    'transfer.created',          # Payout to doctor
    'payout.paid',              # Bank transfer completed
]
```

### **Security Best Practices**

- Always validate webhook signatures
- Use HTTPS for all API calls
- Implement rate limiting on payment endpoints
- Log all payment operations for audit
- Encrypt sensitive data in database
- Regular security audits of payment flow

### **Performance Optimization**

- Cache doctor consultation profiles
- Use database indexes on payment queries
- Implement async webhook processing
- Monitor API response times
- Optimize email notification delivery

This quick reference provides developers with essential information for implementing and maintaining the telemedicine payment system.
