# Frontend API Guide - Appointments Module
## Ravid Healthcare Platform

### Overview
This guide provides comprehensive documentation for frontend developers to integrate with the Ravid Healthcare Platform's appointment system. The system supports both manual events and doctor bookings with integrated payment processing.

---

## Table of Contents
1. [Authentication](#authentication)
2. [Appointment Management](#appointment-management)
3. [Doctor Availability](#doctor-availability)
4. [Payment Integration](#payment-integration)
5. [File Attachments](#file-attachments)
6. [<PERSON>rro<PERSON> Handling](#error-handling)
7. [Code Examples](#code-examples)

---

## Authentication

All authenticated endpoints require a Bearer token in the Authorization header:
```javascript
headers: {
  'Authorization': `Bearer ${userToken}`,
  'Content-Type': 'application/json'
}
```

Some endpoints (like appointment creation) support both authenticated and unauthenticated users.

---

## Appointment Management

### 1. Create Appointment

#### Basic Appointment Creation
```http
POST /appointments/
Content-Type: application/json
Authorization: Bearer <token> (optional)

{
  "doctor": "doctor_user_id",
  "start_time": "2025-01-20T14:00:00Z",
  "end_time": "2025-01-20T14:45:00Z",
  "appointment_type": "booking",
  "mode": "video_call",
  "notes": "Follow-up consultation",
  "email": "<EMAIL>" // Required if not authenticated
}
```

#### Response
```json
{
  "id": "appointment_id",
  "creator": "user_id",
  "patient": "user_id",
  "doctor": "doctor_id",
  "title": "Appointment with Dr. Smith",
  "appointment_type": "booking",
  "start_time": "2025-01-20T14:00:00Z",
  "end_time": "2025-01-20T14:45:00Z",
  "status": "pending",
  "mode": "video_call",
  "notes": "Follow-up consultation",
  "meeting_link": null,
  "consultation_fee": 5000, // in cents
  "direct_payment": false,
  "payment_status": "pending"
}
```

### 2. Create Telemedicine Appointment with Payment

For streamlined telemedicine appointments with automatic payment processing:

```http
POST /appointments/create_telemedicine_appointment/
Authorization: Bearer <patient_token>
Content-Type: application/json

{
  "doctor_id": "doctor_user_id",
  "start_time": "2025-01-20T14:00:00Z",
  "end_time": "2025-01-20T14:45:00Z",
  "notes": "Consultation for hypertension"
}
```

#### Response
```json
{
  "appointment": {
    "id": "appointment_id",
    "title": "Telemedicine Consultation with Dr. Smith",
    "appointment_type": "booking",
    "mode": "video_call",
    "direct_payment": true,
    "status": "pending",
    // ... other appointment fields
  },
  "payment": {
    "checkout_url": "https://checkout.stripe.com/...",
    "amount": 5000, // in cents
    "amount_usd": 50.00,
    "doctor_name": "Dr. John Smith",
    "consultation_duration": 45
  }
}
```

### 3. Get Appointment Details

```http
GET /appointments/{appointment_id}/
Authorization: Bearer <token>
```

#### Response
```json
{
  "id": "appointment_id",
  "creator": "user_id",
  "patient": "user_id",
  "doctor": {
    "id": "doctor_id",
    "first_name": "John",
    "last_name": "Smith",
    "email": "<EMAIL>"
  },
  "title": "Appointment with Dr. Smith",
  "start_time": "2025-01-20T14:00:00Z",
  "end_time": "2025-01-20T14:45:00Z",
  "status": "confirmed",
  "mode": "video_call",
  "meeting_link": "https://meet.google.com/...",
  "payment_status": "paid",
  "consultation_fee": 5000,
  "attachments": [
    {
      "id": "attachment_id",
      "file": {
        "id": "file_id",
        "filename": "medical_report.pdf",
        "file_url": "https://..."
      }
    }
  ]
}
```

### 4. Update Appointment

```http
PUT /appointments/{appointment_id}/
Authorization: Bearer <token>
Content-Type: application/json

{
  "notes": "Updated consultation notes",
  "status": "confirmed"
}
```

### 5. Cancel Appointment

```http
POST /appointments/{appointment_id}/cancel/
Authorization: Bearer <token>
Content-Type: application/json

{
  "reason": "Patient requested cancellation",
  "refund_requested": true
}
```

### 6. Process Payment for Existing Appointment

```http
POST /appointments/{appointment_id}/process_payment/
Authorization: Bearer <patient_token>
Content-Type: application/json

{
  "payment_method_id": "pm_xxx" // Optional for saved payment methods
}
```

#### Response
```json
{
  "checkout_url": "https://checkout.stripe.com/...",
  "amount": 5000,
  "amount_usd": 50.00,
  "doctor_name": "Dr. John Smith",
  "appointment_id": "appointment_id",
  "consultation_duration": 45,
  "status": "checkout_session_created"
}
```

---

## Doctor Availability

### 1. Get Available Slots (Authenticated)

**For authenticated users - uses internal doctor UUIDs**

```http
GET /appointments/availabilities/get_available_slots/?doctor_id={doctor_uuid}&start_date=2025-01-20&end_date=2025-01-27&mode=video_call
Authorization: Bearer <token>
```

#### Response
```json
[
  {
    "date": "2025-01-20",
    "start_time": "09:00:00",
    "end_time": "09:45:00",
    "mode": "video_call,in_person"
  },
  {
    "date": "2025-01-20",
    "start_time": "10:00:00",
    "end_time": "10:45:00",
    "mode": "video_call"
  }
]
```

### 1b. Get Available Slots (Public)

**For public access - uses custom_url_username and returns masked IDs**

```http
GET /appointments/availabilities/available_slots/?doctor_id={custom_url_username}&start_date=2025-01-20&end_date=2025-01-27
```

#### Response
```json
{
  "doctor_id": "john.doe.1234",
  "start_date": "2025-01-20",
  "end_date": "2025-01-27",
  "schedule": [
    {
      "date": "2025-01-20",
      "slots": [
        {
          "start_time": "09:00",
          "end_time": "09:45",
          "availability_id": "avail_id",
          "need_payment": true,
          "mode": "video_call,in_person",
          "status": "available"
        }
      ]
    }
  ]
}
```

### 2. Create Doctor Availability

```http
POST /appointments/availabilities/
Authorization: Bearer <doctor_token>
Content-Type: application/json

{
  "title": "Morning Consultations",
  "start_date": "2025-01-20",
  "start_time": "09:00:00",
  "end_time": "12:00:00",
  "mode": "video_call,in_person",
  "need_payment": true,
  "recurrence_type": "weekly",
  "recurrence_days": "monday,wednesday,friday",
  "recurrence_end_date": "2025-06-20"
}
```

### 3. Bulk Save Availabilities

```http
POST /appointments/availabilities/bulk_save/
Authorization: Bearer <doctor_token>
Content-Type: application/json

[
  {
    "title": "Morning Slots",
    "start_date": "2025-01-20",
    "start_time": "09:00:00",
    "end_time": "12:00:00",
    "mode": "video_call",
    "need_payment": true
  },
  {
    "id": "existing_availability_id",
    "need_payment": false,
    "is_active": false
  }
]
```

---

## Payment Integration

### 1. Doctor Consultation Profile

#### Create Profile
```http
POST /appointments/doctor-consultation-profiles/
Authorization: Bearer <doctor_token>
Content-Type: application/json

{
  "consultation_fee": 5000, // in cents ($50.00)
  "consultation_duration": 45, // minutes
  "accepts_telemedicine": true,
  "description": "General consultation for common health issues"
}
```

#### Get Doctor's Profile
```http
GET /appointments/doctor-consultation-profiles/my_profile/
Authorization: Bearer <doctor_token>
```

#### Response
```json
{
  "id": "profile_id",
  "user": "doctor_id",
  "consultation_fee": 5000,
  "consultation_duration": 45,
  "accepts_telemedicine": true,
  "description": "General consultation",
  "created_at": "2025-01-15T10:00:00Z"
}
```

### 2. Payment Status Check

After payment processing, check appointment status:

```http
GET /appointments/{appointment_id}/
Authorization: Bearer <token>
```

Look for these payment-related fields:
- `payment_status`: "pending", "paid", "failed", "refunded"
- `direct_payment`: boolean indicating if payment was required
- `consultation_fee`: amount in cents

---

## File Attachments

### 1. Add Attachments During Appointment Creation

```http
POST /appointments/
Content-Type: application/json
Authorization: Bearer <token>

{
  "doctor": "doctor_id",
  "start_time": "2025-01-20T14:00:00Z",
  "end_time": "2025-01-20T14:45:00Z",
  "appointment_type": "booking",
  "file_ids": ["file_id_1", "file_id_2"]
}
```

### 2. Add Attachments to Existing Appointment

```http
PUT /appointments/{appointment_id}/
Authorization: Bearer <token>
Content-Type: application/json

{
  "file_ids": ["new_file_id_1", "new_file_id_2"]
}
```

---

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "error": "Validation failed",
  "detail": {
    "start_time": ["This field is required"],
    "doctor": ["Doctor not found"]
  }
}
```

#### 403 Forbidden
```json
{
  "error": "Permission denied",
  "detail": "Only doctors can create consultation profiles"
}
```

#### 404 Not Found
```json
{
  "error": "Appointment not found",
  "detail": "Appointment with id 'invalid_id' does not exist"
}
```

#### 409 Conflict
```json
{
  "error": "Scheduling conflict",
  "detail": "Doctor is not available at the requested time"
}
```

### Payment Errors

#### Insufficient Payment Setup
```json
{
  "error": "Payment setup incomplete",
  "detail": "Doctor has not completed payment account setup"
}
```

#### Payment Processing Failed
```json
{
  "error": "Payment failed",
  "detail": "Unable to process payment. Please try again."
}
```

---

## Code Examples

### React/JavaScript Integration

#### 1. Create Telemedicine Appointment with Payment

```javascript
const createTelemedicineAppointment = async (doctorId, startTime, endTime, notes) => {
  try {
    const response = await fetch('/appointments/create_telemedicine_appointment/', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        doctor_id: doctorId,
        start_time: startTime,
        end_time: endTime,
        notes: notes
      })
    });

    const data = await response.json();

    if (response.ok) {
      // Redirect to payment checkout
      window.location.href = data.payment.checkout_url;
    } else {
      console.error('Error creating appointment:', data);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};
```

#### 2. Get Available Slots (Authenticated)

```javascript
// For authenticated users - uses internal doctor UUID
const getAvailableSlotsAuth = async (doctorUuid, startDate, endDate, mode = 'video_call') => {
  try {
    const params = new URLSearchParams({
      doctor_id: doctorUuid, // Internal UUID
      start_date: startDate,
      end_date: endDate,
      mode: mode
    });

    const response = await fetch(`/appointments/availabilities/get_available_slots/?${params}`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });

    const data = await response.json();
    return data; // Returns array of slots
  } catch (error) {
    console.error('Error fetching slots:', error);
    return [];
  }
};
```

#### 2b. Get Available Slots (Public)

```javascript
// For public access - uses custom_url_username
const getAvailableSlotsPublic = async (customUrlUsername, startDate, endDate) => {
  try {
    const params = new URLSearchParams({
      doctor_id: customUrlUsername, // Custom URL username like "john.doe.1234"
      start_date: startDate,
      end_date: endDate
    });

    const response = await fetch(`/appointments/availabilities/available_slots/?${params}`);
    const data = await response.json();
    return data.schedule; // Returns schedule array with masked doctor_id
  } catch (error) {
    console.error('Error fetching public slots:', error);
    return [];
  }
};
```

#### 3. Handle Payment Success Callback

```javascript
// After successful payment, user is redirected back to your app
const handlePaymentSuccess = async (appointmentId) => {
  try {
    const response = await fetch(`/appointments/${appointmentId}/`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });

    const appointment = await response.json();

    if (appointment.payment_status === 'paid') {
      // Show success message and meeting link
      console.log('Payment successful!');
      console.log('Meeting link:', appointment.meeting_link);
    }
  } catch (error) {
    console.error('Error checking payment status:', error);
  }
};
```

#### 4. Create Doctor Availability

```javascript
const createAvailability = async (availabilityData) => {
  try {
    const response = await fetch('/appointments/availabilities/', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${doctorToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: availabilityData.title,
        start_date: availabilityData.startDate,
        start_time: availabilityData.startTime,
        end_time: availabilityData.endTime,
        mode: availabilityData.modes.join(','), // ['video_call', 'in_person']
        need_payment: availabilityData.requiresPayment,
        recurrence_type: availabilityData.recurrenceType || 'none'
      })
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating availability:', error);
  }
};
```

#### 5. Appointment List Component

```javascript
const AppointmentList = ({ userType }) => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAppointments();
  }, []);

  const fetchAppointments = async () => {
    try {
      const response = await fetch('/appointments/', {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });
      const data = await response.json();
      setAppointments(data.results || data);
    } catch (error) {
      console.error('Error fetching appointments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelAppointment = async (appointmentId) => {
    try {
      const response = await fetch(`/appointments/${appointmentId}/cancel/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reason: 'User requested cancellation',
          refund_requested: true
        })
      });

      if (response.ok) {
        fetchAppointments(); // Refresh list
      }
    } catch (error) {
      console.error('Error cancelling appointment:', error);
    }
  };

  if (loading) return <div>Loading appointments...</div>;

  return (
    <div className="appointment-list">
      {appointments.map(appointment => (
        <div key={appointment.id} className="appointment-card">
          <h3>{appointment.title}</h3>
          <p>Date: {new Date(appointment.start_time).toLocaleDateString()}</p>
          <p>Time: {new Date(appointment.start_time).toLocaleTimeString()}</p>
          <p>Status: {appointment.status}</p>
          {appointment.payment_status && (
            <p>Payment: {appointment.payment_status}</p>
          )}
          {appointment.meeting_link && (
            <a href={appointment.meeting_link} target="_blank" rel="noopener noreferrer">
              Join Meeting
            </a>
          )}
          <button onClick={() => handleCancelAppointment(appointment.id)}>
            Cancel
          </button>
        </div>
      ))}
    </div>
  );
};
```

#### 6. Availability Calendar Component

```javascript
const AvailabilityCalendar = ({ doctorId }) => {
  const [availableSlots, setAvailableSlots] = useState([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedSlot, setSelectedSlot] = useState(null);

  const fetchAvailableSlots = async (startDate, endDate) => {
    try {
      const params = new URLSearchParams({
        doctor_id: doctorId, // Can be UUID (authenticated) or custom_url_username (public)
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0]
      });

      // Use public API if no authentication token, otherwise use authenticated API
      const endpoint = userToken
        ? `/appointments/availabilities/get_available_slots/?${params}`
        : `/appointments/availabilities/available_slots/?${params}`;

      const headers = userToken
        ? { 'Authorization': `Bearer ${userToken}` }
        : {};

      const response = await fetch(endpoint, { headers });
      const data = await response.json();

      // Handle different response formats
      if (userToken) {
        setAvailableSlots(data || []); // Authenticated API returns array directly
      } else {
        setAvailableSlots(data.schedule || []); // Public API returns {schedule: [...]}
      }
    } catch (error) {
      console.error('Error fetching available slots:', error);
    }
  };

  const bookAppointment = async (slot) => {
    try {
      const startDateTime = new Date(`${slot.date}T${slot.start_time}`);
      const endDateTime = new Date(`${slot.date}T${slot.end_time}`);

      const response = await fetch('/appointments/create_telemedicine_appointment/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          doctor_id: doctorId,
          start_time: startDateTime.toISOString(),
          end_time: endDateTime.toISOString(),
          notes: 'Online consultation'
        })
      });

      const data = await response.json();

      if (response.ok && data.payment.checkout_url) {
        window.location.href = data.payment.checkout_url;
      }
    } catch (error) {
      console.error('Error booking appointment:', error);
    }
  };

  return (
    <div className="availability-calendar">
      <h3>Available Appointment Slots</h3>
      {availableSlots.map(daySlots => (
        <div key={daySlots.date} className="day-slots">
          <h4>{daySlots.date}</h4>
          <div className="slots-grid">
            {daySlots.slots.map((slot, index) => (
              <button
                key={index}
                className={`slot-button ${slot.need_payment ? 'paid-slot' : 'free-slot'}`}
                onClick={() => bookAppointment({ ...slot, date: daySlots.date })}
              >
                {slot.start_time} - {slot.end_time}
                {slot.need_payment && <span className="payment-indicator">💳</span>}
              </button>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
```

---

## Important Notes

### Payment Flow
1. **Doctor Setup**: Doctor must complete Stripe Connect onboarding and create consultation profile
2. **Availability**: Doctor sets availability with `need_payment: true` for paid slots
3. **Booking**: Patient books appointment, gets redirected to Stripe checkout
4. **Confirmation**: After successful payment, appointment status updates and meeting link is generated

### Appointment Modes
- `in_person`: Physical appointment at clinic
- `video_call`: Online consultation via Google Meet
- `phone_call`: Phone consultation

### Availability Features
- **Recurrence**: Support for daily, weekly, monthly patterns
- **Multiple Modes**: Single availability can support multiple appointment modes
- **Payment Configuration**: Each availability slot can require payment or be free

### Best Practices
1. Always check payment status before showing meeting links
2. Handle payment redirects gracefully in your frontend routing
3. Validate appointment times against doctor availability before creation
4. Show clear error messages for payment and scheduling conflicts
5. Cache availability data to reduce API calls
6. Implement proper loading states for async operations
7. Use optimistic updates for better user experience

### Security Considerations
1. Validate all user inputs on both frontend and backend
2. Use HTTPS for all API communications
3. Store tokens securely (avoid localStorage for sensitive data)
4. Implement proper CORS policies
5. Rate limit API calls to prevent abuse

### Performance Tips
1. Implement pagination for appointment lists
2. Use debouncing for search/filter inputs
3. Cache frequently accessed data (doctor profiles, availability)
4. Lazy load appointment details
5. Use WebSocket connections for real-time updates

---

## API Endpoints Summary

### Appointments
- `POST /appointments/` - Create appointment
- `GET /appointments/{id}/` - Get appointment details
- `PUT /appointments/{id}/` - Update appointment
- `POST /appointments/{id}/cancel/` - Cancel appointment
- `POST /appointments/{id}/process_payment/` - Process payment
- `POST /appointments/create_telemedicine_appointment/` - Create telemedicine appointment

### Availability
- `GET /appointments/availabilities/get_available_slots/` - Get available slots
- `POST /appointments/availabilities/` - Create availability
- `PUT /appointments/availabilities/{id}/` - Update availability
- `POST /appointments/availabilities/bulk_save/` - Bulk save availabilities

### Consultation Profiles
- `POST /appointments/doctor-consultation-profiles/` - Create profile
- `GET /appointments/doctor-consultation-profiles/my_profile/` - Get doctor's profile
- `PUT /appointments/doctor-consultation-profiles/{id}/` - Update profile

### Payment Integration
- `POST /billing/doctor-payments/setup_payment_account/` - Setup Stripe account
- `GET /billing/doctor-payments/payment_status/` - Check payment status
- `GET /billing/doctor-payments/earnings_summary/` - Get earnings

---

## Support

For technical support or questions about API integration, please contact the development team or refer to the main platform documentation.

### Common Issues and Solutions

1. **Payment not processing**: Ensure doctor has completed Stripe onboarding
2. **Meeting link not generated**: Check appointment status and payment status
3. **Availability conflicts**: Validate against existing appointments before booking
4. **Authentication errors**: Verify token validity and user permissions
5. **CORS issues**: Ensure proper CORS configuration on backend
```
