from django.test import TestCase
from django.utils import timezone
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
from accounts.models import CustomUser
from appointments.models import Appointment
from appointments.services.email_service import AppointmentEmailService
from roles.models import Role


class AppointmentEmailServiceTest(TestCase):
    """Test cases for appointment email notifications"""

    def setUp(self):
        """Set up test data"""
        # Create roles
        self.doctor_role, _ = Role.objects.get_or_create(name='doctor')
        self.patient_role, _ = Role.objects.get_or_create(name='patient')

        # Create test users
        self.doctor = CustomUser.objects.create(
            email='<EMAIL>',
            first_name='Dr. <PERSON>',
            last_name='<PERSON>',
            role=self.doctor_role
        )

        self.patient = CustomUser.objects.create(
            email='<EMAIL>',
            first_name='<PERSON>',
            last_name='<PERSON>e',
            role=self.patient_role
        )

        # Create test appointment
        self.appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            title='Test Appointment',
            appointment_type='booking',
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            status='pending',
            mode='video_call',
            notes='Test appointment notes'
        )

    @patch('appointments.services.email_service.send_email')
    @patch('appointments.services.email_service.render_to_string')
    @patch('appointments.services.email_service.strip_tags')
    def test_send_appointment_confirmation_emails_success(self, mock_strip_tags, mock_render, mock_send_email):
        """Test successful sending of confirmation emails to both patient and doctor"""
        # Mock template rendering
        mock_render.return_value = '<html>Test email content</html>'
        mock_strip_tags.return_value = 'Test email content'
        mock_send_email.return_value = None  # send_email doesn't return anything on success

        # Call the service
        results = AppointmentEmailService.send_appointment_confirmation_emails(self.appointment)

        # Verify results
        self.assertTrue(results['patient_email_sent'])
        self.assertTrue(results['doctor_email_sent'])
        self.assertEqual(len(results['errors']), 0)

        # Verify send_email was called twice (patient and doctor)
        self.assertEqual(mock_send_email.call_count, 2)

        # Verify template was rendered twice
        self.assertEqual(mock_render.call_count, 2)

    @patch('appointments.services.email_service.send_email')
    def test_send_appointment_confirmation_emails_patient_email_failure(self, mock_send_email):
        """Test handling of patient email failure"""
        # Mock send_email to fail for patient (first call) but succeed for doctor (second call)
        mock_send_email.side_effect = [Exception('Email failed'), None]

        # Call the service
        results = AppointmentEmailService.send_appointment_confirmation_emails(self.appointment)

        # Verify results
        self.assertFalse(results['patient_email_sent'])
        self.assertTrue(results['doctor_email_sent'])
        self.assertEqual(len(results['errors']), 1)
        self.assertIn('Failed to send confirmation email to patient', results['errors'][0])

    @patch('appointments.services.email_service.send_email')
    def test_send_appointment_confirmation_emails_no_patient_email(self, mock_send_email):
        """Test handling when patient has no email"""
        # Remove patient email
        self.appointment.patient.email = ''
        self.appointment.patient.save()

        # Call the service
        results = AppointmentEmailService.send_appointment_confirmation_emails(self.appointment)

        # Verify results
        self.assertFalse(results['patient_email_sent'])
        self.assertTrue(results['doctor_email_sent'])
        self.assertEqual(len(results['errors']), 0)

        # Verify send_email was called only once (for doctor)
        self.assertEqual(mock_send_email.call_count, 1)

    @patch('appointments.services.email_service.send_email')
    @patch('appointments.services.email_service.render_to_string')
    @patch('appointments.services.email_service.strip_tags')
    def test_patient_email_context(self, mock_strip_tags, mock_render, mock_send_email):
        """Test that patient email context is correctly prepared"""
        mock_render.return_value = '<html>Test email content</html>'
        mock_strip_tags.return_value = 'Test email content'

        # Call the service
        AppointmentEmailService.send_appointment_confirmation_emails(self.appointment)

        # Get the context passed to render_to_string for patient email (first call)
        patient_context = mock_render.call_args_list[0][0][1]

        # Verify patient context
        self.assertEqual(patient_context['user_name'], 'Jane Doe')
        self.assertEqual(patient_context['doctor_name'], 'Dr. John Smith')
        self.assertEqual(patient_context['appointment_type'], 'Video Call')
        self.assertEqual(patient_context['notes'], 'Test appointment notes')
        self.assertIsNone(patient_context.get('patient_name'))  # Should not be present for patient email

    @patch('appointments.services.email_service.send_email')
    @patch('appointments.services.email_service.render_to_string')
    @patch('appointments.services.email_service.strip_tags')
    def test_doctor_email_context(self, mock_strip_tags, mock_render, mock_send_email):
        """Test that doctor email context is correctly prepared"""
        mock_render.return_value = '<html>Test email content</html>'
        mock_strip_tags.return_value = 'Test email content'

        # Call the service
        AppointmentEmailService.send_appointment_confirmation_emails(self.appointment)

        # Get the context passed to render_to_string for doctor email (second call)
        doctor_context = mock_render.call_args_list[1][0][1]

        # Verify doctor context
        self.assertEqual(doctor_context['user_name'], 'Dr. John Smith')
        self.assertEqual(doctor_context['patient_name'], 'Jane Doe')
        self.assertEqual(doctor_context['appointment_type'], 'Video Call')
        self.assertEqual(doctor_context['notes'], 'Test appointment notes')


class AppointmentViewSetEmailIntegrationTest(TestCase):
    """Integration test for appointment confirmation emails in ViewSet"""

    def setUp(self):
        """Set up test data"""
        # Create roles
        self.doctor_role, _ = Role.objects.get_or_create(name='doctor')
        self.patient_role, _ = Role.objects.get_or_create(name='patient')

        # Create test users
        self.doctor = CustomUser.objects.create(
            email='<EMAIL>',
            first_name='Dr. John',
            last_name='Smith',
            role=self.doctor_role
        )

        self.patient = CustomUser.objects.create(
            email='<EMAIL>',
            first_name='Jane',
            last_name='Doe',
            role=self.patient_role
        )

        # Create test appointment
        self.appointment = Appointment.objects.create(
            creator=self.patient,
            patient=self.patient,
            doctor=self.doctor,
            title='Test Appointment',
            appointment_type='booking',
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            status='pending',
            mode='video_call',
            notes='Test appointment notes'
        )

    @patch('appointments.services.email_service.AppointmentEmailService.send_appointment_confirmation_emails')
    def test_appointment_update_triggers_email_on_confirmation(self, mock_send_emails):
        """Test that updating appointment status to confirmed triggers email sending"""
        from rest_framework.test import APIClient
        from rest_framework import status as http_status

        # Mock the email service
        mock_send_emails.return_value = {
            'patient_email_sent': True,
            'doctor_email_sent': True,
            'errors': []
        }

        # Create API client and authenticate as doctor
        client = APIClient()
        client.force_authenticate(user=self.doctor)

        # Update appointment status to confirmed
        response = client.patch(
            f'/api/appointments/{self.appointment.id}/',
            {'status': 'confirmed'},
            format='json'
        )

        # Verify response
        self.assertEqual(response.status_code, http_status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'confirmed')

        # Verify email service was called
        mock_send_emails.assert_called_once_with(self.appointment)

        # Verify appointment status was updated
        self.appointment.refresh_from_db()
        self.assertEqual(self.appointment.status, 'confirmed')

    @patch('appointments.services.email_service.AppointmentEmailService.send_appointment_confirmation_emails')
    def test_appointment_update_no_email_on_other_status_changes(self, mock_send_emails):
        """Test that updating appointment to other statuses doesn't trigger emails"""
        from rest_framework.test import APIClient
        from rest_framework import status as http_status

        # Create API client and authenticate as patient
        client = APIClient()
        client.force_authenticate(user=self.patient)

        # Update appointment status to canceled (should not trigger emails)
        response = client.patch(
            f'/api/appointments/{self.appointment.id}/',
            {'status': 'canceled', 'cancellation_reason': 'Test cancellation'},
            format='json'
        )

        # Verify response
        self.assertEqual(response.status_code, http_status.HTTP_200_OK)

        # Verify email service was NOT called
        mock_send_emails.assert_not_called()

    @patch('appointments.services.email_service.AppointmentEmailService.send_appointment_confirmation_emails')
    def test_appointment_update_no_email_for_manual_appointments(self, mock_send_emails):
        """Test that manual appointments don't trigger confirmation emails"""
        from rest_framework.test import APIClient
        from rest_framework import status as http_status

        # Create manual appointment
        manual_appointment = Appointment.objects.create(
            creator=self.doctor,
            patient=self.doctor,  # For manual appointments, creator is also patient
            title='Manual Event',
            appointment_type='manual',
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            status='pending',
            mode='in_person'
        )

        # Create API client and authenticate as creator
        client = APIClient()
        client.force_authenticate(user=self.doctor)

        # Update manual appointment (should not trigger emails)
        response = client.patch(
            f'/api/appointments/{manual_appointment.id}/',
            {'notes': 'Updated notes'},
            format='json'
        )

        # Verify response
        self.assertEqual(response.status_code, http_status.HTTP_200_OK)

        # Verify email service was NOT called
        mock_send_emails.assert_not_called()
