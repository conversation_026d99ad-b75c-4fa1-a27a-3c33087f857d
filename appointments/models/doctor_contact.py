from django.forms import ValidationError
from accounts.models import CustomUser
from config.models import BaseModel
from django.db import models
from uuid import uuid4


class DoctorContactInfo(BaseModel):
    """
    Store doctor's contact information for appointment messaging
    """
    doctor = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='contact_info',
        limit_choices_to={'role__name': 'doctor'}
    )
    primary_email = models.EmailField(
        max_length=255,
        help_text="Primary email for receiving patient messages"
    )
    secondary_email = models.EmailField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Secondary email for receiving patient messages"
    )
    is_primary_email_active = models.BooleanField(
        default=True,
        help_text="Whether to send notifications to primary email"
    )
    is_secondary_email_active = models.BooleanField(
        default=True,
        help_text="Whether to send notifications to secondary email"
    )
    is_primary_email_verified = models.BooleanField(
        default=False,
        help_text="Whether the primary email has been verified"
    )
    is_secondary_email_verified = models.<PERSON><PERSON><PERSON><PERSON><PERSON>(
        default=False,
        help_text="Whether the secondary email has been verified"
    )
    primary_email_appointment = models.EmailField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Primary email for receiving booking appointment notifications"
    )
    secondary_email_appointment = models.EmailField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Secondary email for receiving booking appointment notifications"
    )
    is_primary_email_appointment_active = models.BooleanField(
        default=True,
        help_text="Whether to send notifications to primary appointment email"
    )
    is_secondary_email_appointment_active = models.BooleanField(
        default=True,
        help_text="Whether to send notifications to secondary appointment email"
    )
    is_primary_email_appointment_verified = models.BooleanField(
        default=False,
        help_text="Whether the primary appointment email has been verified"
    )
    is_secondary_email_appointment_verified = models.BooleanField(
        default=False,
        help_text="Whether the secondary appointment email has been verified"
    )

    def clean(self):
        if self.primary_email == self.secondary_email:
            raise ValidationError("Primary and secondary emails cannot be the same.")

    def __str__(self):
        return f"{self.doctor.name} - {self.primary_email}"


class PendingDoctorContactInfo(BaseModel):
    """
    Store pending doctor contact info until emails are verified
    """
    doctor = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='pending_contact_info'
    )
    primary_email = models.EmailField(
        max_length=255,
        help_text="Primary email for receiving patient messages"
    )
    secondary_email = models.EmailField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Secondary email for receiving patient messages"
    )
    primary_email_token = models.UUIDField(
        default=uuid4,
        editable=False,
        help_text="Token for verifying primary email"
    )
    secondary_email_token = models.UUIDField(
        default=uuid4,
        editable=False,
        null=True,
        blank=True,
        help_text="Token for verifying secondary email"
    )
    
    # Default values will match DoctorContactInfo default values
    is_primary_email_active = models.BooleanField(default=True)
    is_secondary_email_active = models.BooleanField(default=True)
    
    # Track verification status
    is_primary_email_verified = models.BooleanField(default=False)
    is_secondary_email_verified = models.BooleanField(default=False)
    
    def __str__(self):
        return f"Pending: {self.doctor.email} - {self.primary_email}"


class PendingDoctorAppointmentEmail(BaseModel):
    """
    Store pending doctor appointment notification emails until verified
    """
    doctor = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='pending_appointment_email'
    )
    primary_email_appointment = models.EmailField(
        max_length=255,
        help_text="Primary email for receiving booking appointment notifications"
    )
    secondary_email_appointment = models.EmailField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Secondary email for receiving booking appointment notifications"
    )
    primary_email_appointment_token = models.UUIDField(
        default=uuid4,
        editable=False,
        help_text="Token for verifying primary appointment email"
    )
    secondary_email_appointment_token = models.UUIDField(
        default=uuid4,
        editable=False,
        null=True,
        blank=True,
        help_text="Token for verifying secondary appointment email"
    )
    is_primary_email_appointment_active = models.BooleanField(default=True)
    is_secondary_email_appointment_active = models.BooleanField(default=True)
    is_primary_email_appointment_verified = models.BooleanField(default=False)
    is_secondary_email_appointment_verified = models.BooleanField(default=False)

    def __str__(self):
        return f"Pending Appointment Email: {self.doctor.email} - {self.primary_email_appointment}"
