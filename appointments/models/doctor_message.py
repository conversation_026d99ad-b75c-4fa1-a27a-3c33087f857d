from config.models import BaseModel
from django.db import models
from accounts.models import CustomUser
from appointments.models.appointment import Appointment


class DoctorMessage(BaseModel):
    """
    Store messages from patients to doctors
    """
    doctor = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='doctor_appointment_messages'
    )
    patient = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='patient_appointment_messages'
    )
    appointment = models.ForeignKey(
        Appointment,
        on_delete=models.CASCADE,
        related_name='messages',
        null=True,
        blank=True
    )
    subject = models.CharField(max_length=255)
    content = models.TextField()
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    parent_message = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        related_name='replies',
        null=True,
        blank=True
    )
    is_reply = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.doctor.name} - {self.patient.name} - {self.subject}"
