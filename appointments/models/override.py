from django.db import models
from django.core.exceptions import ValidationError
from accounts.models import CustomUser
from clinic.models import Clinic
from enterprise.models import Enterprise
from config.models import BaseModel

class DoctorAvailabilityOverrideSlot(BaseModel):
    override = models.ForeignKey(
        'DoctorAvailabilityOverride',
        on_delete=models.CASCADE,
        related_name='time_slots'
    )
    start_time = models.TimeField()
    end_time = models.TimeField()

    class Meta:
        ordering = ['start_time']

    def clean(self):
        if self.start_time >= self.end_time:
            raise ValidationError("End time must be after start time.")

class DoctorAvailabilityOverride(BaseModel):
    doctor = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='availability_overrides',
    )
    clinic = models.ForeignKey(
        Clinic,
        on_delete=models.CASCADE,
        related_name='doctor_overrides',
        null=True,
        blank=True
    )
    enterprise = models.ForeignKey(
        Enterprise,
        on_delete=models.CASCADE,
        related_name='doctor_overrides',
        null=True,
        blank=True
    )
    date = models.DateField()
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ('doctor', 'date')

    def clean(self):
        if self.clinic and self.enterprise:
            raise ValidationError("Cannot belong to both clinic and enterprise.")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs) 