from django.db import models
from accounts.models import CustomUser

class UserGoogleCredentials(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    access_token = models.CharField(max_length=255)
    refresh_token = models.CharField(max_length=255)
    token_expiry = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Google Credentials"
        verbose_name_plural = "User Google Credentials"

    def __str__(self):
        return f"Google credentials for {self.user.email}" 