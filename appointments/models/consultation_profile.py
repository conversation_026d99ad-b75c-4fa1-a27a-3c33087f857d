"""
Doctor consultation profile model for telemedicine pricing
"""
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from accounts.models import CustomUser
from config.models import BaseModel


class DoctorConsultationProfile(BaseModel):
    """
    Profile for doctor's consultation settings and pricing
    """
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='consultation_profile',
        limit_choices_to={'role__name': 'doctor'},
        help_text="Doctor user account"
    )
    
    consultation_fee = models.IntegerField(
        default=5000,  # $50.00 default
        validators=[MinValueValidator(500), MaxValueValidator(50000)],  # $5 - $500 range
        help_text="Consultation fee in cents (e.g., 5000 = $50.00)"
    )
    
    consultation_duration = models.IntegerField(
        default=30,
        validators=[MinValueValidator(15), MaxValueValidator(180)],  # 15 min - 3 hours
        help_text="Default consultation duration in minutes"
    )
    
    accepts_telemedicine = models.<PERSON><PERSON><PERSON><PERSON><PERSON>(
        default=False,
        help_text="Whether doctor accepts telemedicine appointments"
    )
    
    available_hours = models.JSONField(
        default=dict,
        blank=True,
        help_text="Available hours configuration (JSON format)"
    )
    
    bio = models.TextField(
        blank=True,
        max_length=1000,
        help_text="Doctor's professional bio for telemedicine"
    )
    
    specializations = models.JSONField(
        default=list,
        blank=True,
        help_text="List of medical specializations"
    )
    
    languages = models.JSONField(
        default=list,
        blank=True,
        help_text="Languages spoken by the doctor"
    )
    
    is_active = models.BooleanField(
        default=True,
        help_text="Whether consultation profile is active"
    )
    
    # Stripe Connect integration
    stripe_account_setup = models.BooleanField(
        default=False,
        help_text="Whether doctor has completed Stripe Connect setup"
    )
    
    class Meta:
        verbose_name = "Doctor Consultation Profile"
        verbose_name_plural = "Doctor Consultation Profiles"
        ordering = ['user__first_name', 'user__last_name']
    
    def __str__(self):
        return f"Consultation Profile - {self.user.get_full_name()}"
    
    def get_consultation_fee_usd(self):
        """Get consultation fee in USD format"""
        return self.consultation_fee / 100
    
    def get_consultation_fee_display(self):
        """Get formatted consultation fee for display"""
        return f"${self.get_consultation_fee_usd():.2f}"
    
    def is_available_for_telemedicine(self):
        """Check if doctor is available for telemedicine"""
        return (
            self.accepts_telemedicine and 
            self.is_active and 
            self.stripe_account_setup
        )
    
    def get_specializations_display(self):
        """Get comma-separated specializations"""
        if self.specializations:
            return ", ".join(self.specializations)
        return "General Practice"
    
    def get_languages_display(self):
        """Get comma-separated languages"""
        if self.languages:
            return ", ".join(self.languages)
        return "English"
    
    def save(self, *args, **kwargs):
        """Override save to validate doctor role"""
        # if self.user and not self.user.role or self.user.role.name != 'doctor':
        #     raise ValueError("Consultation profile can only be created for doctors")
        super().save(*args, **kwargs)


class ConsultationPackage(BaseModel):
    """
    Predefined consultation packages (future use)
    Links to content_management.Service model
    """
    doctor = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='consultation_packages',
        limit_choices_to={'role__name': 'doctor'},
        help_text="Doctor offering this package"
    )
    
    service = models.ForeignKey(
        'content_management.Service',
        on_delete=models.CASCADE,
        related_name='consultation_packages',
        limit_choices_to={'service_type': 'CONSULTATION'},
        help_text="Associated consultation service"
    )
    
    custom_price = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(500), MaxValueValidator(50000)],
        help_text="Custom price override in cents (optional)"
    )
    
    is_available = models.BooleanField(
        default=True,
        help_text="Whether this package is available for booking"
    )
    
    notes = models.TextField(
        blank=True,
        max_length=500,
        help_text="Additional notes about this package"
    )
    
    class Meta:
        verbose_name = "Consultation Package"
        verbose_name_plural = "Consultation Packages"
        unique_together = ['doctor', 'service']
        ordering = ['service__order', 'service__price']
    
    def __str__(self):
        return f"{self.doctor.get_full_name()} - {self.service.name}"
    
    def get_effective_price(self):
        """Get effective price (custom or service default)"""
        return self.custom_price if self.custom_price else int(self.service.price * 100)
    
    def get_effective_price_usd(self):
        """Get effective price in USD"""
        return self.get_effective_price() / 100
    
    def get_effective_price_display(self):
        """Get formatted effective price"""
        return f"${self.get_effective_price_usd():.2f}"
