"""
Telemedicine Payment Service Implementation
Based on TELEMEDICINE_PAYMENTS_GUIDE.md
"""
import logging
from django.db import transaction
from django.core.exceptions import ValidationError
from billing.services.user_transfer_service import UserTransferService
from billing.constants import TransferType
from billing.exceptions import PaymentError
# Models imported in methods to avoid circular imports

logger = logging.getLogger(__name__)


class TelemedicinePaymentService:
    """
    Service for handling telemedicine payment operations
    Implements the flow described in TELEMEDICINE_PAYMENTS_GUIDE.md
    """
    
    def __init__(self):
        self.transfer_service = UserTransferService()
    
    def process_consultation_payment(self, patient, doctor, appointment):
        """
        Process payment for telemedicine consultation
        
        Args:
            patient: Patient user instance
            doctor: Doctor user instance  
            appointment: Appointment instance
            
        Returns:
            tuple: (transfer, client_secret)
        """
        try:
            # Get doctor's consultation fee
            doctor_profile = getattr(doctor, 'consultation_profile', None)
            if not doctor_profile:
                raise ValidationError("Doctor consultation profile not found")
            
            if not doctor_profile.is_available_for_telemedicine():
                raise ValidationError("Doctor is not available for telemedicine consultations")
            
            # Create transfer
            transfer, client_secret = self.transfer_service.create_transfer(
                sender=patient,
                receiver=doctor,
                amount=doctor_profile.consultation_fee,
                message=f"Telemedicine consultation - {appointment.id}",
                transfer_type=TransferType.PAYMENT
            )
            
            # Link transfer to appointment and store consultation fee
            appointment.payment_transfer = transfer
            appointment.payment_status = 'pending'
            appointment.consultation_fee = doctor_profile.consultation_fee
            appointment.save()
            
            logger.info(f"Created telemedicine payment transfer {transfer.id} for appointment {appointment.id}")
            
            return transfer, client_secret
            
        except Exception as e:
            logger.error(f"Consultation payment failed: {str(e)}")
            raise PaymentError(f"Failed to process consultation payment: {str(e)}")
    
    def confirm_consultation_payment(self, appointment):
        """
        Confirm consultation payment and activate appointment
        
        Args:
            appointment: Appointment instance
            
        Returns:
            Appointment: Updated appointment instance
        """
        try:
            if not appointment.payment_transfer:
                raise ValidationError("No payment transfer found")
            
            # Confirm transfer
            updated_transfer = self.transfer_service.confirm_transfer(
                appointment.payment_transfer
            )
            
            # Update appointment status
            with transaction.atomic():
                if updated_transfer.status == 'completed':
                    appointment.payment_status = 'paid'
                    appointment.status = 'confirmed'
                else:
                    appointment.payment_status = 'failed'
                    appointment.status = 'cancelled'
                
                appointment.save()
            
            logger.info(f"Confirmed consultation payment for appointment {appointment.id}")
            return appointment
            
        except Exception as e:
            logger.error(f"Payment confirmation failed: {str(e)}")
            raise PaymentError(f"Failed to confirm consultation payment: {str(e)}")
    
    def refund_consultation_payment(self, appointment, reason=""):
        """
        Process refund for consultation payment
        
        Args:
            appointment: Appointment instance
            reason: Refund reason
            
        Returns:
            bool: True if successful
        """
        try:
            if not appointment.payment_transfer:
                raise ValidationError("No payment transfer found")
            
            # Process refund through transfer service
            success = self.transfer_service.refund_transfer(
                appointment.payment_transfer, 
                reason=reason
            )
            
            if success:
                with transaction.atomic():
                    appointment.payment_status = 'refunded'
                    appointment.status = 'cancelled'
                    appointment.cancellation_reason = f"Payment refunded: {reason}"
                    appointment.save()
                
                logger.info(f"Refunded consultation payment for appointment {appointment.id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Payment refund failed: {str(e)}")
            raise PaymentError(f"Failed to refund consultation payment: {str(e)}")
    
    def get_consultation_fee(self, doctor):
        """
        Get doctor's consultation fee
        
        Args:
            doctor: Doctor user instance
            
        Returns:
            int: Consultation fee in cents
        """
        try:
            if hasattr(doctor, 'consultation_profile'):
                return doctor.consultation_profile.consultation_fee
            else:
                # Default consultation fee if no profile set
                return 5000  # $50.00 default
                
        except Exception as e:
            logger.error(f"Error getting doctor consultation fee: {str(e)}")
            return 5000  # Default fallback
    
    def validate_doctor_availability(self, doctor):
        """
        Validate if doctor is available for telemedicine
        
        Args:
            doctor: Doctor user instance
            
        Returns:
            bool: True if available
            
        Raises:
            ValidationError: If not available
        """
        try:
            if not hasattr(doctor, 'consultation_profile'):
                raise ValidationError("Doctor has not set up consultation profile")
            
            profile = doctor.consultation_profile
            
            if not profile.is_available_for_telemedicine():
                raise ValidationError("Doctor is not available for telemedicine consultations")
            
            # Check if doctor has payment profile setup
            if not hasattr(doctor, 'payment_profile'):
                raise ValidationError("Doctor payment profile not setup")
            
            payment_profile = doctor.payment_profile
            if not payment_profile.can_receive_payments():
                raise ValidationError("Doctor cannot receive payments")
            
            return True
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Error validating doctor availability: {str(e)}")
            raise ValidationError(f"Unable to validate doctor availability: {str(e)}")
    
    def calculate_platform_fee(self, amount):
        """
        Calculate platform fee for telemedicine consultation
        
        Args:
            amount: Consultation amount in cents
            
        Returns:
            int: Platform fee in cents
        """
        # 3% platform fee for telemedicine consultations
        return int(amount * 0.03)
    
    def get_payment_summary(self, appointment):
        """
        Get payment summary for appointment
        
        Args:
            appointment: Appointment instance
            
        Returns:
            dict: Payment summary information
        """
        try:
            if not appointment.payment_transfer:
                return {
                    'status': 'no_payment',
                    'message': 'No payment transfer found'
                }
            
            transfer = appointment.payment_transfer
            doctor_profile = appointment.doctor.consultation_profile
            
            return {
                'status': transfer.status,
                'amount_cents': transfer.amount,
                'amount_usd': transfer.amount / 100,
                'platform_fee_cents': transfer.platform_fee_amount,
                'platform_fee_usd': transfer.platform_fee_amount / 100,
                'net_amount_cents': transfer.net_amount,
                'net_amount_usd': transfer.net_amount / 100,
                'consultation_duration': doctor_profile.consultation_duration,
                'transfer_id': str(transfer.id),
                'stripe_payment_intent_id': transfer.stripe_payment_intent_id,
                'created_at': transfer.created_at,
                'updated_at': transfer.updated_at
            }
            
        except Exception as e:
            logger.error(f"Error getting payment summary: {str(e)}")
            return {
                'status': 'error',
                'message': f'Error retrieving payment summary: {str(e)}'
            } 