from googleapiclient.discovery import build
from google.oauth2 import service_account
from django.conf import settings
import os
import logging
from google.auth.exceptions import GoogleAuthError
from googleapiclient.errors import HttpError

logger = logging.getLogger(__name__)

def get_google_meet_service():
    """
    Build Google Calendar service using service account credentials with domain-wide delegation.
    """
    try:
        # Path to service account credentials
        creds_path = os.path.join(settings.BASE_DIR, 'credentials', 'credentials.json')
        logger.info(f"Looking for credentials file at: {creds_path}")
        
        if not os.path.exists(creds_path):
            logger.error(f"Service account credentials file not found at {creds_path}")
            return None

        logger.info("Found credentials file, attempting to create credentials object")
        
        # Define required scopes - make sure these match exactly with what's in Google Workspace Admin Console
        SCOPES = [
            'https://www.googleapis.com/auth/calendar',
            'https://www.googleapis.com/auth/calendar.events',
            'https://www.googleapis.com/auth/meetings.space.created',
            # 'https://www.googleapis.com/auth/gmail.send'  # Add scope for sending emails
        ]
        
        logger.info(f"Using scopes: {SCOPES}")
        logger.info(f"Impersonating user: {settings.GOOGLE_SERVICE_ACCOUNT_SUBJECT}")
        
        # Create credentials from service account file with impersonation
        credentials = service_account.Credentials.from_service_account_file(
            creds_path,
            scopes=SCOPES,
            subject=settings.GOOGLE_SERVICE_ACCOUNT_SUBJECT  # The user to impersonate
        )
        logger.info("Successfully created credentials object")
        
        # Log service account details (without sensitive info)
        logger.info(f"Service account email: {credentials.service_account_email}")
        logger.info(f"Has valid token: {credentials.valid}")
        
        logger.info("Building Google Calendar service")
        service = build('calendar', 'v3', credentials=credentials)
        logger.info("Successfully created Google Calendar service")
        return service
    except GoogleAuthError as e:
        logger.error(f"Google authentication error: {str(e)}")
        logger.error(f"Error details: {e.error_details if hasattr(e, 'error_details') else 'No details'}")
        return None
    except Exception as e:
        logger.error(f"Error building Google service: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        return None

def create_meet_link(appointment):
    """
    Create a Google Meet link for a video call appointment.
    Returns the meeting link and event ID.
    """
    try:
        logger.info(f"Starting to create Meet link for appointment {appointment.id}")
        logger.info(f"Appointment details - Type: {appointment.appointment_type}, Mode: {appointment.mode}")
        logger.info(f"Doctor: {appointment.doctor.email if appointment.doctor else 'None'}")
        logger.info(f"Patient: {appointment.patient.email if appointment.patient else 'None'}")
        
        service = get_google_meet_service()
        if not service:
            logger.error("Could not get Google service")
            return None
        
        # Create calendar event with Meet conference
        event = {
            'summary': appointment.title,
            'description': appointment.notes,
            'start': {
                'dateTime': appointment.start_time.isoformat(),
                'timeZone': 'UTC',
            },
            'end': {
                'dateTime': appointment.end_time.isoformat(),
                'timeZone': 'UTC',
            },
            'conferenceData': {
                'createRequest': {
                    'requestId': f"{appointment.id}-{appointment.start_time.timestamp()}",
                    'conferenceSolutionKey': {'type': 'hangoutsMeet'}
                }
            },
            'guestsCanModify': False,
            'guestsCanSeeOtherGuests': True,
            'guestsCanInviteOthers': False,
            'transparency': 'opaque',
            'visibility': 'default',
            'reminders': {
                'useDefault': False,
                'overrides': [
                    {'method': 'email', 'minutes': 60},
                    {'method': 'popup', 'minutes': 15}
                ]
            }
        }

        if appointment.appointment_type == 'booking' and appointment.doctor:
            event['attendees'] = [
                {'email': appointment.patient.email, 'responseStatus': 'needsAction'},
                {'email': appointment.doctor.email, 'responseStatus': 'needsAction'},
            ]
            logger.info(f"Added attendees - Doctor: {appointment.doctor.email}, Patient: {appointment.patient.email}")

        logger.info("Attempting to create calendar event with Meet")
        logger.info(f"Full event data: {event}")
        
        # Create event with Meet
        created_event = service.events().insert(
            calendarId='primary',
            body=event,
            conferenceDataVersion=1,
            sendUpdates='all',
            sendNotifications=True
        ).execute()
        
        logger.info(f"Full API response: {created_event}")
        logger.info(f"Successfully created calendar event with ID: {created_event.get('id')}")
        
        # Log notification status
        logger.info(f"Event status: {created_event.get('status')}")
        logger.info(f"Event creator: {created_event.get('creator', {}).get('email')}")
        logger.info(f"Event organizer: {created_event.get('organizer', {}).get('email')}")
        logger.info(f"Attendees response status: {[{'email': a.get('email'), 'status': a.get('responseStatus')} for a in created_event.get('attendees', [])]}")
        
        # Extract Meet link
        meet_link = created_event.get('conferenceData', {}).get('entryPoints', [{}])[0].get('uri')
        logger.info(f"Extracted Meet link: {meet_link}")
        
        # For external users, we need to send them the event details directly
        try:
            if appointment.patient and appointment.patient.email != settings.GOOGLE_SERVICE_ACCOUNT_SUBJECT:
                logger.info(f"Sending event details to external user: {appointment.patient.email}")
                # The event will be sent via email notification from Google Calendar
                # since we used sendUpdates='all' and sendNotifications=True
        except Exception as e:
            logger.warning(f"Error during notification process: {str(e)}")
        
        return {
            'meet_link': meet_link,
            'event_id': created_event['id']
        }

    except HttpError as e:
        logger.error(f"Google API error creating Meet link for appointment {appointment.id}: {str(e)}")
        logger.error(f"Error details: {e.error_details if hasattr(e, 'error_details') else 'No details'}")
        return None
    except Exception as e:
        logger.error(f"Error creating Meet link for appointment {appointment.id}: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        return None 