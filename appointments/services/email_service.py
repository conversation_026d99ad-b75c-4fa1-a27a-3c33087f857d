import logging
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from accounts.send_email import send_email

logger = logging.getLogger(__name__)

class AppointmentEmailService:
    """
    Service for handling appointment-related email notifications
    """
    
    @staticmethod
    def send_appointment_confirmation_emails(appointment):
        """
        Send confirmation emails to both patient and doctor when appointment is confirmed
        
        Args:
            appointment: Appointment instance that was just confirmed
            
        Returns:
            dict: Status of email sending for patient and doctor
        """
        results = {
            'patient_email_sent': False,
            'doctor_email_sent': False,
            'errors': []
        }
        
        try:
            # Send email to patient
            if appointment.patient and appointment.patient.email:
                patient_success = AppointmentEmailService._send_patient_confirmation_email(appointment)
                results['patient_email_sent'] = patient_success
                if not patient_success:
                    results['errors'].append(f"Failed to send confirmation email to patient: {appointment.patient.email}")
            
            # Send email to doctor (optional)
            if appointment.doctor and appointment.doctor.email:
                doctor_success = AppointmentEmailService._send_doctor_confirmation_email(appointment)
                results['doctor_email_sent'] = doctor_success
                if not doctor_success:
                    results['errors'].append(f"Failed to send confirmation email to doctor: {appointment.doctor.email}")
                    
        except Exception as e:
            logger.error(f"Error in send_appointment_confirmation_emails for appointment {appointment.id}: {str(e)}")
            results['errors'].append(f"Unexpected error: {str(e)}")
        
        return results
    
    @staticmethod
    def _send_patient_confirmation_email(appointment):
        """
        Send confirmation email to the patient
        
        Args:
            appointment: Appointment instance
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            subject = f"Appointment Confirmed - {appointment.start_time.strftime('%B %d, %Y')}"
            
            # Prepare context for email template
            context = {
                'user_name': appointment.patient.get_full_name(),
                'appointment_date': appointment.start_time.date(),
                'appointment_time': appointment.start_time.time(),
                'doctor_name': appointment.doctor.get_full_name() if appointment.doctor else 'R.A.V.I.D Team',
                'appointment_type': appointment.get_mode_display(),
                'location': appointment.location or ('Video Call' if appointment.mode == 'video_call' else 'To be confirmed'),
                'notes': appointment.notes,
                # 'appointment_url': f"{settings.SITE_URL}/appointments/{appointment.id}/",
                'meeting_link': appointment.meeting_link if appointment.mode == 'video_call' else None,
                'preparation_instructions': None,  # Can be added later if needed
            }
            
            # Render email content
            html_content = render_to_string('emails/appointment_confirmation.html', context)
            plain_content = strip_tags(html_content)
            
            # Send email
            send_email(
                to_email=appointment.patient.email,
                subject=subject,
                html_content=html_content,
                plain_content=plain_content
            )
            
            logger.info(f"Appointment confirmation email sent to patient: {appointment.patient.email} for appointment {appointment.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send patient confirmation email for appointment {appointment.id}: {str(e)}")
            return False
    
    @staticmethod
    def _send_doctor_confirmation_email(appointment):
        """
        Send confirmation email to the doctor
        
        Args:
            appointment: Appointment instance
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            subject = f"Appointment Confirmed - {appointment.patient.get_full_name()} on {appointment.start_time.strftime('%B %d, %Y')}"
            
            # Prepare context for email template
            context = {
                'user_name': appointment.doctor.get_full_name(),
                'appointment_date': appointment.start_time.date(),
                'appointment_time': appointment.start_time.time(),
                'patient_name': appointment.patient.get_full_name(),
                'appointment_type': appointment.get_mode_display(),
                'location': appointment.location or ('Video Call' if appointment.mode == 'video_call' else 'To be confirmed'),
                'notes': appointment.notes,
                # 'appointment_url': f"{settings.SITE_URL}/appointments/{appointment.id}/",
                'meeting_link': appointment.meeting_link if appointment.mode == 'video_call' else None,
            }
            
            # Use a slightly modified template context for doctor
            # We'll use the same template but with different greeting
            html_content = render_to_string('emails/appointment_confirmation.html', context)
            plain_content = strip_tags(html_content)
            
            # Send email
            send_email(
                to_email=appointment.doctor.email,
                subject=subject,
                html_content=html_content,
                plain_content=plain_content
            )
            
            logger.info(f"Appointment confirmation email sent to doctor: {appointment.doctor.email} for appointment {appointment.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send doctor confirmation email for appointment {appointment.id}: {str(e)}")
            return False
