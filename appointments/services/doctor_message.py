from appointments.models.doctor_message import Doctor<PERSON><PERSON><PERSON>
from appointments.models.doctor_contact import DoctorContactInfo
from accounts.send_email import send_email
import logging

logger = logging.getLogger(__name__)


class DoctorMessageService:
    @staticmethod
    def send_message(patient, doctor, subject, content, appointment=None):
        logger.info(f"Creating message from patient {patient.id} to doctor {doctor.id}")
        
        # Create message record
        message = DoctorMessage.objects.create(
            doctor=doctor,
            patient=patient,
            appointment=appointment,
            subject=subject,
            content=content
        )
        
        logger.info(f"Message {message.id} created successfully")
        
        # Get doctor's contact info
        try:
            contact_info = DoctorContactInfo.objects.get(doctor=doctor)
            logger.info(f"Found contact info for doctor {doctor.id}")
            
            # Prepare email content with patient info
            patient_name = patient.get_full_name()
            patient_email = patient.email
            patient_phone = patient.phone if hasattr(patient, 'phone') else 'N/A'
            
            html_content = f"""
            <h3>New Message from Patient</h3>
            <p><strong>Patient Information:</strong></p>
            <ul>
                <li>Name: {patient_name}</li>
                <li>Email: {patient_email}</li>
                <li>Phone: {patient_phone}</li>
            </ul>
            <p><strong>Message Subject:</strong> {subject}</p>
            <p><strong>Message Content:</strong></p>
            <p>{content}</p>
            """
            
            plain_content = f"""
            New Message from Patient
            
            Patient Information:
            - Name: {patient_name}
            - Email: {patient_email}
            - Phone: {patient_phone}
            
            Message Subject: {subject}
            Message Content:
            {content}
            """
            
            # Send email to doctor's primary and secondary emails
            if (
                contact_info.primary_email and 
                contact_info.is_primary_email_active and 
                contact_info.is_primary_email_verified
            ):
                logger.info(f"Sending email to doctor's primary email: {contact_info.primary_email}")
                send_email(
                    to_email=contact_info.primary_email,
                    subject=f"New message from patient: {subject}",
                    html_content=html_content,
                    plain_content=plain_content
                )
                
            if (
                contact_info.secondary_email and 
                contact_info.is_secondary_email_active and 
                contact_info.is_secondary_email_verified
            ):
                logger.info(f"Sending email to doctor's secondary email: {contact_info.secondary_email}")
                send_email(
                    to_email=contact_info.secondary_email,
                    subject=f"New message from patient: {subject}",
                    html_content=html_content,
                    plain_content=plain_content
                )
        except DoctorContactInfo.DoesNotExist:
            # Log that no contact info was found
            logger.warning(f"No contact info found for doctor {doctor.id}")
            
        logger.info(f"Message handling completed for message {message.id}")
        return message