from django.conf import settings
from django.urls import reverse
from django.utils.html import strip_tags
from django.template.loader import render_to_string
from accounts.send_email import send_email
from appointments.models.doctor_contact import DoctorContactInfo, PendingDoctorAppointmentEmail, PendingDoctorContactInfo
import logging

logger = logging.getLogger(__name__)

class DoctorContactService:
    """
    Service class for managing doctor contact information and email verification
    """
    
    @staticmethod
    def create_pending_contact(doctor, primary_email, secondary_email=None, 
                              is_primary_email_active=True, is_secondary_email_active=True):
        """
        Create a pending doctor contact record and send verification emails
        """
        logger.info(f"Creating pending contact info for doctor {doctor.id}")
        
        # Create or update pending contact
        pending_contact, created = PendingDoctorContactInfo.objects.update_or_create(
            doctor=doctor,
            defaults={
                'primary_email': primary_email,
                'secondary_email': secondary_email,
                'is_primary_email_active': is_primary_email_active,
                'is_secondary_email_active': is_secondary_email_active,
                'is_primary_email_verified': False,
                'is_secondary_email_verified': False
            }
        )
        
        # Send verification emails
        DoctorContactService.send_primary_verification_email(pending_contact)
        
        if secondary_email:
            Doctor<PERSON>ontactService.send_secondary_verification_email(pending_contact)
        
        return pending_contact
    
    @staticmethod
    def send_primary_verification_email(pending_contact):
        """Send verification email for primary email"""
        doctor = pending_contact.doctor
        email = pending_contact.primary_email
        
        verification_link = f"{settings.SITE_URL}/api/appointments/doctor-contact-info/verify-primary-email/{pending_contact.primary_email_token}/"
        
        subject = "Verify Your Doctor Contact Email"
        context = {
            'doctor_name': doctor.get_full_name(),
            'verification_link': verification_link,
            'email': email,
            'email_type': 'primary'
        }
        
        html_content = render_to_string('account/doctor_email_verification.html', context)
        plain_content = strip_tags(html_content)
        
        try:
            send_email(
                to_email=email,
                subject=subject,
                html_content=html_content,
                plain_content=plain_content
            )
            logger.info(f"Primary email verification sent to {email}")
            return True
        except Exception as e:
            logger.error(f"Failed to send primary verification email: {str(e)}")
            return False
    
    @staticmethod
    def send_secondary_verification_email(pending_contact):
        """Send verification email for secondary email"""
        if not pending_contact.secondary_email:
            logger.warning("Attempted to send secondary email verification, but no secondary email exists")
            return False
            
        doctor = pending_contact.doctor
        email = pending_contact.secondary_email
        
        verification_link = f"{settings.SITE_URL}/api/appointments/doctor-contact-info/verify-secondary-email/{pending_contact.secondary_email_token}/"
        
        subject = "Verify Your Doctor Contact Email"
        context = {
            'doctor_name': doctor.get_full_name(),
            'verification_link': verification_link,
            'email': email,
            'email_type': 'secondary'
        }
        
        html_content = render_to_string('account/doctor_email_verification.html', context)
        plain_content = strip_tags(html_content)
        
        try:
            send_email(
                to_email=email,
                subject=subject,
                html_content=html_content,
                plain_content=plain_content
            )
            logger.info(f"Secondary email verification sent to {email}")
            return True
        except Exception as e:
            logger.error(f"Failed to send secondary verification email: {str(e)}")
            return False
    
    @staticmethod
    def verify_primary_email(token):
        """Verify primary email with token and update pending contact"""
        try:
            pending_contact = PendingDoctorContactInfo.objects.get(primary_email_token=token)
            pending_contact.is_primary_email_verified = True
            pending_contact.save()
            
            # If both emails verified or no secondary email, create/update actual contact
            DoctorContactService.finalize_contact_if_ready(pending_contact)
            
            return pending_contact
        except PendingDoctorContactInfo.DoesNotExist:
            logger.error(f"No pending contact found with primary token: {token}")
            return None
    
    @staticmethod
    def verify_secondary_email(token):
        """Verify secondary email with token and update pending contact"""
        try:
            pending_contact = PendingDoctorContactInfo.objects.get(secondary_email_token=token)
            pending_contact.is_secondary_email_verified = True
            pending_contact.save()
            
            # If both emails verified or primary already verified, create/update actual contact
            DoctorContactService.finalize_contact_if_ready(pending_contact)
            
            return pending_contact
        except PendingDoctorContactInfo.DoesNotExist:
            logger.error(f"No pending contact found with secondary token: {token}")
            return None
    
    @staticmethod
    def finalize_contact_if_ready(pending_contact):
        """
        Create/update the actual contact info if all required verifications are complete
        """
        # Required: Primary email must always be verified
        if not pending_contact.is_primary_email_verified:
            return False
            
        # If secondary email exists, it must be verified too
        if pending_contact.secondary_email and not pending_contact.is_secondary_email_verified:
            return False
            
        # All required verifications complete, create or update actual contact info
        contact_info, created = DoctorContactInfo.objects.update_or_create(
            doctor=pending_contact.doctor,
            defaults={
                'primary_email': pending_contact.primary_email,
                'secondary_email': pending_contact.secondary_email,
                'is_primary_email_active': pending_contact.is_primary_email_active,
                'is_secondary_email_active': pending_contact.is_secondary_email_active,
                'is_primary_email_verified': pending_contact.is_primary_email_verified,
                'is_secondary_email_verified': pending_contact.is_secondary_email_verified
            }
        )
        
        logger.info(f"Contact info {'created' if created else 'updated'} for doctor {pending_contact.doctor.id}")
        
        # Don't delete the pending contact - keep it for reference and possible future updates
        return True

class DoctorAppointmentEmailService:
    """
    Service for managing doctor appointment notification emails and verification
    """
    @staticmethod
    def create_pending_appointment_email(doctor, primary_email_appointment, secondary_email_appointment=None,
                                         is_primary_email_appointment_active=True, is_secondary_email_appointment_active=True):
        pending, created = PendingDoctorAppointmentEmail.objects.update_or_create(
            doctor=doctor,
            defaults={
                'primary_email_appointment': primary_email_appointment,
                'secondary_email_appointment': secondary_email_appointment,
                'is_primary_email_appointment_active': is_primary_email_appointment_active,
                'is_secondary_email_appointment_active': is_secondary_email_appointment_active,
                'is_primary_email_appointment_verified': False,
                'is_secondary_email_appointment_verified': False
            }
        )
        DoctorAppointmentEmailService.send_primary_appointment_verification_email(pending)
        if secondary_email_appointment:
            DoctorAppointmentEmailService.send_secondary_appointment_verification_email(pending)
        return pending

    @staticmethod
    def send_primary_appointment_verification_email(pending):
        doctor = pending.doctor
        email = pending.primary_email_appointment
        
        verification_link = f"{settings.SITE_URL}/api/appointments/doctor-appointment-email/verify-primary-email/{pending.primary_email_appointment_token}/"
        subject = "Verify Your Appointment Notification Email"
        context = {
            'doctor_name': doctor.get_full_name(),
            'verification_link': verification_link,
            'email': email,
            'email_type': 'primary_appointment'
        }
        html_content = render_to_string('account/doctor_email_verification.html', context)
        plain_content = strip_tags(html_content)
        send_email(
            to_email=email,
            subject=subject,
            html_content=html_content,
            plain_content=plain_content
        )

    @staticmethod
    def send_secondary_appointment_verification_email(pending):
        if not pending.secondary_email_appointment:
            return False
        doctor = pending.doctor
        email = pending.secondary_email_appointment
        
        verification_link = f"{settings.SITE_URL}/api/appointments/doctor-appointment-email/verify-secondary-email/{pending.secondary_email_appointment_token}/"
        subject = "Verify Your Appointment Notification Email"
        context = {
            'doctor_name': doctor.get_full_name(),
            'verification_link': verification_link,
            'email': email,
            'email_type': 'secondary_appointment'
        }
        html_content = render_to_string('account/doctor_email_verification.html', context)
        plain_content = strip_tags(html_content)
        send_email(
            to_email=email,
            subject=subject,
            html_content=html_content,
            plain_content=plain_content
        )

    @staticmethod
    def verify_primary_appointment_email(token):
        try:
            pending = PendingDoctorAppointmentEmail.objects.get(primary_email_appointment_token=token)
            pending.is_primary_email_appointment_verified = True
            pending.save()
            DoctorAppointmentEmailService.finalize_appointment_email_if_ready(pending)
            return pending
        except PendingDoctorAppointmentEmail.DoesNotExist:
            return None

    @staticmethod
    def verify_secondary_appointment_email(token):
        try:
            pending = PendingDoctorAppointmentEmail.objects.get(secondary_email_appointment_token=token)
            pending.is_secondary_email_appointment_verified = True
            pending.save()
            DoctorAppointmentEmailService.finalize_appointment_email_if_ready(pending)
            return pending
        except PendingDoctorAppointmentEmail.DoesNotExist:
            return None

    @staticmethod
    def finalize_appointment_email_if_ready(pending):
        if not pending.is_primary_email_appointment_verified:
            return False
        if pending.secondary_email_appointment and not pending.is_secondary_email_appointment_verified:
            return False
        contact_info, created = DoctorContactInfo.objects.update_or_create(
            doctor=pending.doctor,
            defaults={
                'primary_email_appointment': pending.primary_email_appointment,
                'secondary_email_appointment': pending.secondary_email_appointment,
                'is_primary_email_appointment_active': pending.is_primary_email_appointment_active,
                'is_secondary_email_appointment_active': pending.is_secondary_email_appointment_active,
                'is_primary_email_appointment_verified': pending.is_primary_email_appointment_verified,
                'is_secondary_email_appointment_verified': pending.is_secondary_email_appointment_verified
            }
        )
        return True 