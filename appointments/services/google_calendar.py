from googleapiclient.discovery import build
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from django.conf import settings
import os
import logging
from appointments.models.google_credentials import UserGoogleCredentials
from google.auth.exceptions import GoogleAuthError
from googleapiclient.errors import HttpError

logger = logging.getLogger(__name__)

def get_google_calendar_service(user=None):
    """
    Build Google Calendar service with user credentials.
    If user is None, use service account credentials.
    """
    try:
        if user:
            # Get user credentials from database
            try:
                user_creds = UserGoogleCredentials.objects.get(user=user)
                
                # Create credentials object
                credentials = Credentials(
                    token=user_creds.access_token,
                    refresh_token=user_creds.refresh_token,
                    token_uri="https://oauth2.googleapis.com/token",
                    client_id=settings.GOOGLE_CLIENT_ID,
                    client_secret=settings.GOOGLE_CLIENT_SECRET,
                    scopes=['https://www.googleapis.com/auth/calendar']
                )
                
                # Refresh token if needed
                if credentials.expired:
                    credentials.refresh(Request())
                    # Save new token to database
                    user_creds.access_token = credentials.token
                    user_creds.token_expiry = credentials.expiry
                    user_creds.save()
                
                return build('calendar', 'v3', credentials=credentials)
            except UserGoogleCredentials.DoesNotExist:
                logger.warning(f"No Google credentials found for user {user.email}")
                return None
        else:
            # Use service account credentials
            creds_path = os.path.join(settings.BASE_DIR, 'credentials', 'credentials.json')
            if not os.path.exists(creds_path):
                logger.error("Service account credentials file not found")
                return None
            
            from google.oauth2 import service_account
            credentials = service_account.Credentials.from_service_account_file(
                creds_path,
                scopes=['https://www.googleapis.com/auth/calendar']
            )
            return build('calendar', 'v3', credentials=credentials)
            
    except GoogleAuthError as e:
        logger.error(f"Google authentication error: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error building Google service: {str(e)}")
        return None

def sync_to_calendar(service, appointment):
    """
    Sync appointment to a specific Google Calendar.
    """
    try:
        event = {
            'summary': appointment.title,
            'description': appointment.notes,
            'start': {
                'dateTime': appointment.start_time.isoformat(),
                'timeZone': 'UTC',
            },
            'end': {
                'dateTime': appointment.end_time.isoformat(),
                'timeZone': 'UTC',
            },
            'location': appointment.location,
        }

        # Add Meet conference data for video calls
        if appointment.mode == 'video_call' and appointment.meeting_link:
            event['conferenceData'] = {
                'entryPoints': [{
                    'entryPointType': 'video',
                    'uri': appointment.meeting_link,
                    'label': 'meet.google.com'
                }]
            }

        if appointment.appointment_type == 'booking' and appointment.doctor:
            event['attendees'] = [
                {'email': appointment.patient.email},
                {'email': appointment.doctor.email},
            ]

        if appointment.google_event_id:
            # Update existing event
            updated_event = service.events().update(
                calendarId='primary',
                eventId=appointment.google_event_id,
                body=event,
                conferenceDataVersion=1 if appointment.mode == 'video_call' else 0
            ).execute()
            logger.info(f"Updated Google Calendar event {appointment.google_event_id} for appointment {appointment.id}")
        else:
            # Create new event
            created_event = service.events().insert(
                calendarId='primary',
                body=event,
                conferenceDataVersion=1 if appointment.mode == 'video_call' else 0
            ).execute()
            appointment.google_event_id = created_event['id']
            appointment.save()
            logger.info(f"Created Google Calendar event {appointment.google_event_id} for appointment {appointment.id}")

    except HttpError as e:
        logger.error(f"Google API error syncing calendar for appointment {appointment.id}: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Error syncing calendar for appointment {appointment.id}: {str(e)}")
        raise 