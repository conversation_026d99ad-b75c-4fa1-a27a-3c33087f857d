# Appointment Email Notifications

This document describes the email notification system for appointment confirmations in the Ravid Healthcare Platform.

## Overview

When a doctor confirms an appointment (changes status from 'pending' to 'confirmed'), the system automatically sends email notifications to both the patient and the doctor.

## Implementation

### Files Modified/Created

1. **`appointments/services/email_service.py`** - New service for handling appointment email notifications
2. **`appointments/api/views/appointment.py`** - Modified to integrate email notifications in the update method
3. **`ravid_communities/templates/emails/base_email.html`** - New base template for emails
4. **`ravid_communities/templates/emails/appointment_confirmation.html`** - New template for appointment confirmations
5. **`appointments/tests.py`** - Added comprehensive tests for email functionality

### How It Works

1. **Trigger**: When `AppointmentViewSet.update` is called and the status changes from 'pending' to 'confirmed'
2. **Condition**: Only applies to 'booking' type appointments (not manual appointments)
3. **Email Service**: `AppointmentEmailService.send_appointment_confirmation_emails()` is called
4. **Recipients**: 
   - Patient receives confirmation with appointment details
   - Doctor receives notification about confirmed appointment with patient details

### Email Content

#### Patient Email
- Subject: "Appointment Confirmed - [Date]"
- Contains: Doctor name, appointment details, meeting link (if video call), preparation instructions
- Template: Uses `emails/appointment_confirmation.html` with patient context

#### Doctor Email  
- Subject: "Appointment Confirmed - [Patient Name] on [Date]"
- Contains: Patient name, appointment details, meeting link (if video call)
- Template: Uses same template but with doctor context (includes patient_name variable)

### Error Handling

- Email failures do NOT prevent appointment status updates
- All email errors are logged but don't affect the API response
- Individual email failures are tracked (patient vs doctor email)
- Service returns detailed results about email sending success/failure

### Email Template Features

- **Responsive Design**: Works on mobile and desktop
- **Professional Styling**: Matches R.A.V.I.D brand colors and fonts
- **Conditional Content**: Shows different information based on recipient (patient vs doctor)
- **Meeting Links**: Automatically includes video call links when applicable
- **Extensible**: Base template allows easy creation of other email types

## Usage Examples

### Confirming an Appointment (Doctor)

```bash
PATCH /api/appointments/{id}/
{
    "status": "confirmed"
}
```

This will:
1. Update appointment status to 'confirmed'
2. Create Google Meet link (if video call)
3. Send confirmation emails to patient and doctor
4. Log all email sending results

### Email Service Direct Usage

```python
from appointments.services.email_service import AppointmentEmailService

# Send confirmation emails for an appointment
results = AppointmentEmailService.send_appointment_confirmation_emails(appointment)

# Check results
if results['patient_email_sent']:
    print("Patient email sent successfully")
if results['doctor_email_sent']:
    print("Doctor email sent successfully")
if results['errors']:
    print(f"Errors: {results['errors']}")
```

## Testing

### Running Tests

```bash
# Run all appointment tests
python manage.py test appointments

# Run only email notification tests
python manage.py test appointments.tests.AppointmentEmailServiceTest
python manage.py test appointments.tests.AppointmentViewSetEmailIntegrationTest
```

### Test Coverage

- ✅ Successful email sending to both patient and doctor
- ✅ Handling of email failures (patient or doctor)
- ✅ Missing email addresses
- ✅ Correct email context preparation
- ✅ Integration with AppointmentViewSet.update
- ✅ Only booking appointments trigger emails
- ✅ Only status changes to 'confirmed' trigger emails

## Configuration

### Email Settings

The system uses the existing email configuration from `accounts/send_email.py`:
- SMTP settings from environment variables
- Uses `RELAYHOST_USERNAME` for from_email
- Integrates with existing Django email backend

### Template Customization

Email templates can be customized by modifying:
- `ravid_communities/templates/emails/base_email.html` - Base styling and structure
- `ravid_communities/templates/emails/appointment_confirmation.html` - Appointment-specific content

## Future Enhancements

Potential improvements that could be added:

1. **Email Preferences**: Allow users to opt-out of certain email types
2. **Email Templates**: Add more email types (reminders, cancellations, rescheduling)
3. **Internationalization**: Support multiple languages
4. **Rich Content**: Add appointment QR codes, calendar attachments
5. **Email Analytics**: Track email open rates and engagement
6. **Batch Processing**: Queue emails for better performance

## Troubleshooting

### Common Issues

1. **Emails not sending**: Check SMTP configuration and logs
2. **Template errors**: Verify template syntax and context variables
3. **Missing emails**: Check user email addresses in database
4. **Permission errors**: Ensure proper user roles and permissions

### Logging

All email activities are logged with appropriate levels:
- `INFO`: Successful email sending
- `WARNING`: Email sending failures (non-critical)
- `ERROR`: Critical email service errors

Check Django logs for email-related messages with prefix: `appointments.services.email_service`
