# Stripe Integration Overview - Healthcare AI Platform

## Executive Summary

Using both **Stripe Checkout** and **Stripe Connect** strategically:

- **Stripe Checkout**: Platform services (DNA analysis, AI features, subscriptions)
- **Stripe Connect**: User-to-user payments (donations, telemedicine)

## 🏗️ Current Stripe Architecture

### **1. Stripe Checkout (Primary Revenue)**

```
User → content_management.Service → Stripe Checkout → Platform Revenue
```

**Active Implementations:**
- **DNA Analysis**: `billing/views/dna_analysis_views.py`
- **AI Services**: `content_management.Service` models
- **Enterprise Solutions**: `content_management.Solution` subscriptions
- **Individual Plans**: `content_management.SubscriptionPlan`

### **2. Stripe Connect (P2P Payments)**

```
User A → UserPaymentProfile → Stripe Connect → Platform Fee → User B
```

**Use Cases:**
- Healthcare provider donations
- Telemedicine consultations  
- General user transfers

## 💳 Stripe Checkout Implementation

### **DNA Analysis Payment Flow**
```python
# billing/views/dna_analysis_views.py
@action(detail=False, methods=['post'])
def checkout_dna_services(self, request):
    services_data = request.data.get('services', [])
    
    # Multi-service checkout session
    session = stripe.checkout.Session.create(
        customer=customer.stripe_customer_id,
        payment_method_types=['card'],
        line_items=[{
            'price': service['price_id'],
            'quantity': 1
        } for service in services],
        mode='payment',
        metadata={
            'service_ids': json.dumps([str(s['service'].id) for s in services]),
            'payment_type': 'dna_analysis'
        }
    )
```

### **Content Management Integration**
```python
# Platform services sync to Stripe automatically
content_management.Service.objects.all()           # DNA, AI services
content_management.Solution.objects.all()          # Enterprise packages
content_management.SubscriptionPlan.objects.all()  # Individual subscriptions  
content_management.ServicePromotion.objects.all()  # Discount codes
```

### **Payment Processing**
```python
# billing/services/payment_service.py - Standard Checkout
# billing/services/subscription_service.py - Recurring billing
# billing/services/webhook_service.py - Event processing
```

## 🔗 Stripe Connect Implementation

### **User Payment Profiles**
```python
# billing/models/customer.py
class UserPaymentProfile(BillingBaseModel):
    user = models.OneToOneField('accounts.CustomUser')
    stripe_account_id = models.CharField(max_length=255, unique=True)
    charges_enabled = models.BooleanField(default=False)
    payouts_enabled = models.BooleanField(default=False)
    is_healthcare_provider = models.BooleanField(default=False)
    provider_type = models.CharField(max_length=100, blank=True)
```

### **Donation Flow**
```python
# billing/services/user_transfer_service.py
def create_donation_payment(self, donor, recipient, amount, message=''):
    payment_intent = stripe.PaymentIntent.create(
        amount=amount,
        currency='usd',
        customer=donor.stripe_customer_id,
        transfer_data={
            'destination': recipient_profile.stripe_account_id,
            'amount': amount - platform_fee_amount,
        },
        application_fee_amount=platform_fee_amount,
        metadata={
            'transfer_type': 'donation',
            'donor_id': str(donor.id),
            'recipient_id': str(recipient.id)
        }
    )
```

### **Telemedicine Payments**
```python
def create_telemedicine_payment(self, patient, provider, consultation_fee):
    session = stripe.checkout.Session.create(
        customer=patient.stripe_customer_id,
        line_items=[{
            'price_data': {
                'currency': 'usd',
                'unit_amount': consultation_fee,
                'product_data': {
                    'name': f'Telemedicine - Dr. {provider.get_full_name()}',
                },
            },
            'quantity': 1,
        }],
        payment_intent_data={
            'transfer_data': {
                'destination': provider_profile.stripe_account_id,
            },
            'application_fee_amount': platform_fee_amount,
        },
        metadata={'payment_type': 'telemedicine'}
    )
```

## 📊 Payment Routing

### **Unified Payment Service**
```python
# billing/services/unified_payment_service.py
class UnifiedPaymentService:
    def create_payment_session(self, payment_type: str, **kwargs):
        if payment_type == PaymentType.ONE_TIME.value:
            return self._create_one_time_session(**kwargs)  # → Stripe Checkout
        elif payment_type == PaymentType.SUBSCRIPTION.value:
            return self._create_subscription_session(**kwargs)  # → Stripe Checkout
        elif payment_type == PaymentType.USER_TRANSFER.value:
            return self._create_user_transfer_session(**kwargs)  # → Stripe Connect
        elif payment_type == PaymentType.APPOINTMENT.value:
            return self._create_appointment_session(**kwargs)  # → Stripe Connect
```

## 🏥 Healthcare Use Cases

### **1. DNA Analysis Services**
- **Method**: Stripe Checkout
- **Features**: Multi-service checkout, promo codes, file uploads

### **2. AI Health Insights**  
- **Method**: Stripe Checkout

### **3. Enterprise Solutions**
- **Method**: Stripe Checkout  
- **Features**: Multi-seat billing, usage tracking

### **4. Healthcare Donations**
- **Method**: Stripe Connect
- **Features**: Direct provider payments

### **5. Telemedicine**
- **Method**: Stripe Connect
- **Features**: Professional consultation payments

### **Primary Revenue (Stripe Checkout)**
```python
billing.ServicePayment     # DNA analysis, AI services
billing.Subscription      # Recurring subscriptions  
billing.ServiceAccess     # Access control for paid services
```

### **Secondary Revenue (Stripe Connect)**  
```python
billing.UserTransfer         # P2P payments
billing.UserPaymentProfile   # Provider accounts
billing.PlatformFee          # Fee structure
```

### **Fee Structure**
```python
# billing/models/usage.py
class PlatformFee(BillingBaseModel):
    percentage = models.DecimalField(max_digits=5, decimal_places=2)  # 5-10%
    applies_to = models.CharField(max_length=50)  # 'donation', 'telemedicine'
    active = models.BooleanField(default=True)
```

## 🔄 Implementation Status

### **✅ Fully Implemented**
- Stripe Checkout for all platform services
- DNA analysis multi-service payments
- Content management integration
- Webhook processing
- Service access control

### **✅ Connect Foundation Ready**
- UserPaymentProfile model
- UserTransferService 
- Platform fee collection
- Express account creation

### **🏗️ Ready for Enhancement**
- Telemedicine payment flows
- Advanced donation features
- Provider earnings dashboards
- International expansion

## 📝 Conclusion

### **Core Platform (Stripe Checkout)**
- DNA analysis services
- AI health features  
- Enterprise solutions
- Individual subscriptions

### **Provider Ecosystem (Stripe Connect)**
- Healthcare provider donations
- Telemedicine consultations
- User-to-user transfers