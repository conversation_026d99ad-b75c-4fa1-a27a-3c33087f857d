"""
Base view classes and mixins for billing operations
"""
import logging
from django.views import View
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.core.cache import cache
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status, viewsets

from ..services import CustomerService
from ..exceptions import BillingError, CustomerNotFoundError, RateLimitError
from ..constants import CacheKeys, RateLimits

logger = logging.getLogger(__name__)


class BasePaymentView(View):
    """Base view for payment-related functionality"""
    
    @method_decorator(login_required)
    @method_decorator(never_cache)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)


class CustomerRequiredMixin:
    """Mixin that ensures a customer exists for the current user"""
    
    def get_or_create_customer(self, user):
        """Get or create customer for user"""
        try:
            customer_service = CustomerService()
            return customer_service.create_or_update_customer(user)
        except Exception as e:
            logger.error(f"Error creating/updating customer for user {user.id}: {str(e)}")
            raise CustomerNotFoundError(f"Error creating customer: {str(e)}")


class RateLimitMixin:
    """Mixin for rate limiting functionality"""
    
    def check_rate_limit(self, key, timeout=None):
        """Check if operation is rate limited"""
        if timeout is None:
            timeout = RateLimits.CHECKOUT_SESSION_TIMEOUT
            
        if cache.get(key):
            raise RateLimitError("Please wait before performing this action again")
    
    def set_rate_limit(self, key, timeout=None):
        """Set rate limit for operation"""
        if timeout is None:
            timeout = RateLimits.CHECKOUT_SESSION_TIMEOUT
            
        cache.set(key, True, timeout)


class ErrorHandlingMixin:
    """Mixin for standardized error handling"""
    
    def handle_billing_error(self, error):
        """Handle billing-specific errors"""
        if isinstance(error, BillingError):
            return JsonResponse({
                'error': str(error),
                'error_code': getattr(error, 'error_code', None),
                'details': getattr(error, 'details', {})
            }, status=400)
        
        logger.error(f"Unexpected error: {str(error)}")
        return JsonResponse({
            'error': 'An unexpected error occurred'
        }, status=500)
    
    def handle_validation_error(self, error):
        """Handle validation errors"""
        return JsonResponse({
            'error': str(error)
        }, status=400)


class APIErrorHandlingMixin:
    """Mixin for DRF API error handling"""
    
    def handle_billing_error(self, error):
        """Handle billing-specific errors for API views"""
        if isinstance(error, BillingError):
            return Response({
                'error': str(error),
                'error_code': getattr(error, 'error_code', None),
                'details': getattr(error, 'details', {})
            }, status=status.HTTP_400_BAD_REQUEST)
        
        logger.error(f"Unexpected error: {str(error)}")
        return Response({
            'error': 'An unexpected error occurred'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EnterpriseRequiredMixin:
    """Mixin that ensures user is associated with an enterprise"""
    
    def get_user_enterprise(self, user):
        """Get enterprise for user or raise error"""
        if not hasattr(user, 'enterprise') or not user.enterprise:
            raise BillingError('User is not associated with an enterprise')
        return user.enterprise


class PaginationMixin:
    """Mixin for consistent pagination"""
    
    def paginate_response(self, queryset, serializer_class, request):
        """Paginate queryset and return response"""
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = serializer_class(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = serializer_class(queryset, many=True)
        return Response(serializer.data)


class CacheKeyMixin:
    """Mixin for generating cache keys"""
    
    def get_checkout_cache_key(self, user_id):
        """Get cache key for checkout session rate limiting"""
        return CacheKeys.CHECKOUT_SESSION.format(user_id=user_id)
    
    def get_enterprise_checkout_cache_key(self, user_id):
        """Get cache key for enterprise checkout rate limiting"""
        return CacheKeys.ENTERPRISE_CHECKOUT.format(user_id=user_id)
    
    def get_customer_cache_key(self, user_id):
        """Get cache key for customer data"""
        return CacheKeys.CUSTOMER_DATA.format(user_id=user_id)


class MetadataHelperMixin:
    """Mixin for handling metadata operations"""
    
    def extract_metadata(self, request_data, required_fields=None, optional_fields=None):
        """Extract and validate metadata from request"""
        metadata = {}
        
        # Extract required fields
        if required_fields:
            for field in required_fields:
                if field not in request_data:
                    raise BillingError(f"Required field '{field}' is missing")
                metadata[field] = request_data[field]
        
        # Extract optional fields
        if optional_fields:
            for field in optional_fields:
                if field in request_data:
                    metadata[field] = request_data[field]
        
        return metadata
    
    def build_success_url(self, request, path):
        """Build absolute success URL"""
        return request.build_absolute_uri(path)
    
    def build_cancel_url(self, request, path):
        """Build absolute cancel URL"""
        return request.build_absolute_uri(path)


class LoggingMixin:
    """Mixin for consistent logging"""
    
    def log_operation(self, operation, user_id=None, details=None):
        """Log billing operation"""
        log_data = {
            'operation': operation,
            'user_id': user_id,
        }
        if details:
            log_data.update(details)
        
        logger.info(f"Billing operation: {operation}", extra=log_data)
    
    def log_error(self, operation, error, user_id=None, details=None):
        """Log billing error"""
        log_data = {
            'operation': operation,
            'error': str(error),
            'user_id': user_id,
        }
        if details:
            log_data.update(details)
        
        logger.error(f"Billing error in {operation}: {str(error)}", extra=log_data)


class BaseAPIView:
    """Base class for API views with common functionality"""
    
    permission_classes = [IsAuthenticated]
    
    def get_user_customer(self):
        """Get customer for authenticated user"""
        customer_service = CustomerService()
        return customer_service.get_customer_by_user(self.request.user)
    
    def get_or_create_user_customer(self):
        """Get or create customer for authenticated user"""
        customer_service = CustomerService()
        return customer_service.create_or_update_customer(self.request.user)


class BaseViewSet(viewsets.GenericViewSet):
    """Base ViewSet with common functionality"""

    permission_classes = [IsAuthenticated]
    
    def get_user_customer(self):
        """Get customer for authenticated user"""
        customer_service = CustomerService()
        return customer_service.get_customer_by_user(self.request.user)
    
    def get_or_create_user_customer(self):
        """Get or create customer for authenticated user"""
        customer_service = CustomerService()
        return customer_service.create_or_update_customer(self.request.user)
