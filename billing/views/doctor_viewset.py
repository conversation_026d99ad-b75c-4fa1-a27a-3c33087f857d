"""
Doctor payment management ViewSet
"""
import logging
import stripe
from rest_framework import viewsets, status
from rest_framework.decorators import action  
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.conf import settings
from datetime import timed<PERSON>ta
from django.db.models import Sum, Count

from .base import BaseViewSet, APIErrorHandlingMixin, LoggingMixin
from ..services import UserTransferService
from ..models import UserPaymentProfile, UserTransfer
from ..serializers import UserPaymentProfileSerializer, UserTransferSerializer
from ..services.error_handling_service import EdgeCaseHandler
from ..exceptions import (
    ValidationError, PaymentError, ServiceNotFoundError,
    PermissionDeniedError, ConfigurationError
)
from roles.constants import RoleNames

logger = logging.getLogger(__name__)


class DoctorPaymentViewSet(
    BaseViewSet,
    APIErrorHandlingMixin,
    LoggingMixin,
    viewsets.ViewSet
):
    """Specialized ViewSet for doctor payment functionality"""
    
    permission_classes = [IsAuthenticated]

    def get_doctor_profile(self):
        """Get or create payment profile for doctor"""
        profile, _ = UserPaymentProfile.objects.get_or_create(user=self.request.user)
        return profile

    def _verify_doctor_permission(self, request):
        """Verify user is a doctor"""
        if not RoleNames.user_is_doctor(request.user):
            raise PermissionDeniedError('Only doctors can access payment account features')

    @EdgeCaseHandler.handle_stripe_api_error
    @action(detail=False, methods=['post'])
    def setup_payment_account(self, request):
        """Setup Stripe Connect account for doctor"""
        try:
            self._verify_doctor_permission(request)
            EdgeCaseHandler.check_configuration()
            EdgeCaseHandler.check_rate_limit(str(request.user.id), 'account_setup', max_per_minute=3)

            transfer_service = UserTransferService()
            account_link = transfer_service.create_stripe_connect_account(request.user)
            
            self.log_operation('doctor_payment_account_setup', request.user.id, {
                'account_link_created': True
            })
            
            return Response({
                'account_link': account_link,
                'setup_type': 'doctor_payment_account',
                'message': 'Please complete the account setup process'
            })
            
        except (PermissionDeniedError, ValidationError, ConfigurationError) as e:
            raise
        except Exception as e:
            self.log_error('doctor_payment_account_setup', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['get'])
    def payment_status(self, request):
        """Get doctor-specific payment status"""
        try:
            self._verify_doctor_permission(request)
            profile = UserPaymentProfile.objects.filter(user=request.user).first()
            
            if not profile:
                return Response({
                    'setup_required': True,
                    'message': 'Doctor payment account setup required',
                    'can_receive_payments': False,
                    'can_receive_appointment_payments': False,
                    'can_receive_consultation_payments': False,
                    'bank_account_connected': False
                })

            return Response({
                'stripe_account_id': profile.stripe_account_id,
                'charges_enabled': profile.charges_enabled,
                'payouts_enabled': profile.payouts_enabled,
                'details_submitted': profile.details_submitted,
                'is_verified': profile.is_verified,
                'can_receive_payments': profile.can_receive_payments(),
                'can_receive_appointment_payments': profile.can_receive_payments(),
                'can_receive_consultation_payments': profile.can_receive_payments(),
                'bank_account_connected': profile.payouts_enabled and profile.details_submitted,
                'setup_required': False
            })
            
        except (PermissionDeniedError, ValidationError) as e:
            raise
        except Exception as e:
            self.log_error('doctor_payment_status', e, request.user.id)
            return self.handle_billing_error(e)

    @EdgeCaseHandler.handle_stripe_api_error
    @action(detail=False, methods=['post'])
    def add_bank_account(self, request):
        """Add bank account information for doctor"""
        try:
            self._verify_doctor_permission(request)
            EdgeCaseHandler.check_configuration()
            EdgeCaseHandler.check_rate_limit(str(request.user.id), 'bank_account_update', max_per_minute=5)
            
            profile = self.get_doctor_profile()
            if not profile.stripe_account_id:
                raise ConfigurationError("Stripe Connect account must be created first")

            # Validate required bank account data
            account_holder_name = EdgeCaseHandler.safe_dict_get(
                request.data, 'account_holder_name', required=True
            )
            account_holder_type = EdgeCaseHandler.safe_dict_get(
                request.data, 'account_holder_type', default='individual'
            )
            routing_number = EdgeCaseHandler.safe_dict_get(
                request.data, 'routing_number', required=True
            )
            account_number = EdgeCaseHandler.safe_dict_get(
                request.data, 'account_number', required=True
            )
            country = EdgeCaseHandler.safe_dict_get(
                request.data, 'country', default='US'
            )
            currency = EdgeCaseHandler.safe_dict_get(
                request.data, 'currency', default='usd'
            )

            # Validate data format
            if not account_holder_name.strip():
                raise ValidationError("Account holder name cannot be empty")
            
            if account_holder_type not in ['individual', 'company']:
                raise ValidationError("Account holder type must be 'individual' or 'company'")
            
            if len(routing_number) != 9 or not routing_number.isdigit():
                raise ValidationError("Routing number must be 9 digits")
                
            if len(account_number) < 4 or len(account_number) > 17:
                raise ValidationError("Account number must be between 4 and 17 characters")

            # Create external account in Stripe
            stripe.api_key = settings.STRIPE_SECRET_KEY
            
            external_account = stripe.Account.create_external_account(
                profile.stripe_account_id,
                external_account={
                    'object': 'bank_account',
                    'account_holder_name': account_holder_name,
                    'account_holder_type': account_holder_type,
                    'routing_number': routing_number,
                    'account_number': account_number,
                    'country': country,
                    'currency': currency
                }
            )

            self.log_operation('doctor_bank_account_added', request.user.id, {
                'external_account_id': external_account.id,
                'account_holder_type': account_holder_type,
                'country': country
            })

            return Response({
                'success': True,
                'message': 'Bank account added successfully',
                'external_account': {
                    'id': external_account.id,
                    'account_holder_name': external_account.account_holder_name,
                    'account_holder_type': external_account.account_holder_type,
                    'bank_name': external_account.bank_name,
                    'country': external_account.country,
                    'currency': external_account.currency,
                    'last4': external_account.last4,
                    'routing_number': external_account.routing_number,
                    'status': external_account.status
                }
            })

        except (PermissionDeniedError, ValidationError, ConfigurationError, PaymentError) as e:
            raise
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error adding bank account: {str(e)}")
            raise PaymentError(f"Failed to add bank account: {str(e)}")
        except Exception as e:
            self.log_error('doctor_bank_account_addition', e, request.user.id)
            return self.handle_billing_error(e)

    @EdgeCaseHandler.handle_stripe_api_error  
    @action(detail=False, methods=['get'])
    def list_bank_accounts(self, request):
        """List connected bank accounts for doctor"""
        try:
            self._verify_doctor_permission(request)
            profile = self.get_doctor_profile()
            
            if not profile.stripe_account_id:
                return Response({
                    'bank_accounts': [],
                    'message': 'No Stripe Connect account found'
                })

            # Retrieve external accounts from Stripe
            stripe.api_key = settings.STRIPE_SECRET_KEY
            
            external_accounts = stripe.Account.list_external_accounts(
                profile.stripe_account_id,
                object='bank_account',
                limit=10
            )

            bank_accounts = []
            for account in external_accounts.data:
                bank_accounts.append({
                    'id': account.id,
                    'account_holder_name': account.account_holder_name,
                    'account_holder_type': account.account_holder_type,
                    'bank_name': account.bank_name,
                    'country': account.country,
                    'currency': account.currency,
                    'last4': account.last4,
                    'routing_number': account.routing_number,
                    'status': account.status,
                    'is_default': account.default_for_currency
                })

            return Response({
                'bank_accounts': bank_accounts,
                'total_count': len(bank_accounts),
                'stripe_account_id': profile.stripe_account_id
            })

        except (PermissionDeniedError, ValidationError) as e:
            raise
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error listing bank accounts: {str(e)}")
            raise PaymentError(f"Failed to retrieve bank accounts: {str(e)}")
        except Exception as e:
            self.log_error('doctor_bank_account_list', e, request.user.id)
            return self.handle_billing_error(e)

    @EdgeCaseHandler.handle_stripe_api_error
    @action(detail=False, methods=['post'])
    def update_bank_account(self, request):
        """Update bank account information"""
        try:
            self._verify_doctor_permission(request)
            EdgeCaseHandler.check_rate_limit(str(request.user.id), 'bank_account_update', max_per_minute=5)
            
            profile = self.get_doctor_profile()
            if not profile.stripe_account_id:
                raise ConfigurationError("Stripe Connect account not found")

            external_account_id = EdgeCaseHandler.safe_dict_get(
                request.data, 'external_account_id', required=True
            )
            
            # Validate updatable fields
            update_data = {}
            if 'account_holder_name' in request.data:
                account_holder_name = request.data['account_holder_name'].strip()
                if not account_holder_name:
                    raise ValidationError("Account holder name cannot be empty")
                update_data['account_holder_name'] = account_holder_name
                
            if 'account_holder_type' in request.data:
                account_holder_type = request.data['account_holder_type']
                if account_holder_type not in ['individual', 'company']:
                    raise ValidationError("Account holder type must be 'individual' or 'company'")
                update_data['account_holder_type'] = account_holder_type

            if not update_data:
                raise ValidationError("No valid fields to update")

            # Update external account in Stripe
            stripe.api_key = settings.STRIPE_SECRET_KEY
            
            updated_account = stripe.Account.modify_external_account(
                profile.stripe_account_id,
                external_account_id,
                **update_data
            )

            self.log_operation('doctor_bank_account_updated', request.user.id, {
                'external_account_id': external_account_id,
                'updated_fields': list(update_data.keys())
            })

            return Response({
                'success': True,
                'message': 'Bank account updated successfully',
                'external_account': {
                    'id': updated_account.id,
                    'account_holder_name': updated_account.account_holder_name,
                    'account_holder_type': updated_account.account_holder_type,
                    'bank_name': updated_account.bank_name,
                    'last4': updated_account.last4,
                    'status': updated_account.status
                }
            })

        except (PermissionDeniedError, ValidationError, ConfigurationError, PaymentError) as e:
            raise
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error updating bank account: {str(e)}")
            raise PaymentError(f"Failed to update bank account: {str(e)}")
        except Exception as e:
            self.log_error('doctor_bank_account_update', e, request.user.id)
            return self.handle_billing_error(e)

    @EdgeCaseHandler.handle_stripe_api_error
    @action(detail=False, methods=['delete'])
    def remove_bank_account(self, request):
        """Remove bank account"""
        try:
            self._verify_doctor_permission(request)
            EdgeCaseHandler.check_rate_limit(str(request.user.id), 'bank_account_delete', max_per_minute=3)
            
            profile = self.get_doctor_profile()
            if not profile.stripe_account_id:
                raise ConfigurationError("Stripe Connect account not found")

            external_account_id = EdgeCaseHandler.safe_dict_get(
                request.data, 'external_account_id', required=True
            )

            # Delete external account from Stripe
            stripe.api_key = settings.STRIPE_SECRET_KEY
            
            deleted_account = stripe.Account.delete_external_account(
                profile.stripe_account_id,
                external_account_id
            )

            self.log_operation('doctor_bank_account_removed', request.user.id, {
                'external_account_id': external_account_id,
                'deleted': deleted_account.deleted
            })

            return Response({
                'success': True,
                'message': 'Bank account removed successfully',
                'deleted_account_id': external_account_id
            })

        except (PermissionDeniedError, ValidationError, ConfigurationError, PaymentError) as e:
            raise
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error removing bank account: {str(e)}")
            raise PaymentError(f"Failed to remove bank account: {str(e)}")
        except Exception as e:
            self.log_error('doctor_bank_account_removal', e, request.user.id)
            return self.handle_billing_error(e)

    @EdgeCaseHandler.handle_stripe_api_error
    @action(detail=False, methods=['post'])
    def set_default_bank_account(self, request):
        """Set default bank account for payouts"""
        try:
            self._verify_doctor_permission(request)
            EdgeCaseHandler.check_rate_limit(str(request.user.id), 'bank_account_default', max_per_minute=5)
            
            profile = self.get_doctor_profile()
            if not profile.stripe_account_id:
                raise ConfigurationError("Stripe Connect account not found")

            external_account_id = EdgeCaseHandler.safe_dict_get(
                request.data, 'external_account_id', required=True
            )

            # Set default external account in Stripe
            stripe.api_key = settings.STRIPE_SECRET_KEY
            
            updated_account = stripe.Account.modify_external_account(
                profile.stripe_account_id,
                external_account_id,
                default_for_currency=True
            )

            self.log_operation('doctor_default_bank_account_set', request.user.id, {
                'external_account_id': external_account_id
            })

            return Response({
                'success': True,
                'message': 'Default bank account set successfully',
                'default_account': {
                    'id': updated_account.id,
                    'account_holder_name': updated_account.account_holder_name,
                    'bank_name': updated_account.bank_name,
                    'last4': updated_account.last4,
                    'is_default': updated_account.default_for_currency
                }
            })

        except (PermissionDeniedError, ValidationError, ConfigurationError, PaymentError) as e:
            raise
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error setting default bank account: {str(e)}")
            raise PaymentError(f"Failed to set default bank account: {str(e)}")
        except Exception as e:
            self.log_error('doctor_default_bank_account', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['get'])
    def earnings_summary(self, request):
        """Get doctor earnings summary"""
        try:
            self._verify_doctor_permission(request)
            days = int(request.query_params.get('days', 30))
            start_date = timezone.now() - timedelta(days=days)

            # Doctor-specific earnings from appointments & consultations
            earnings = UserTransfer.objects.filter(
                receiver=request.user,
                status='completed',
                created_at__gte=start_date,
                transfer_type__in=['appointment_payment', 'consultation_payment']
            ).aggregate(
                total_gross=Sum('amount'),
                total_fees=Sum('platform_fee_amount'),
                count=Count('id')
            )

            total_gross = earnings['total_gross'] or 0
            total_fees = earnings['total_fees'] or 0
            total_net = total_gross - total_fees

            # Get breakdown by service type
            appointment_earnings = UserTransfer.objects.filter(
                receiver=request.user,
                status='completed',
                created_at__gte=start_date,
                transfer_type='appointment_payment'
            ).aggregate(
                amount=Sum('amount'),
                count=Count('id')
            )

            consultation_earnings = UserTransfer.objects.filter(
                receiver=request.user,
                status='completed',
                created_at__gte=start_date,
                transfer_type='consultation_payment'
            ).aggregate(
                amount=Sum('amount'),
                count=Count('id')
            )

            return Response({
                'period_days': days,
                'total_gross': total_gross,
                'total_fees': total_fees,
                'total_net': total_net,
                'payment_count': earnings['count'] or 0,
                'service_breakdown': {
                    'appointments': {
                        'amount': appointment_earnings['amount'] or 0,
                        'count': appointment_earnings['count'] or 0
                    },
                    'consultations': {
                        'amount': consultation_earnings['amount'] or 0,
                        'count': consultation_earnings['count'] or 0
                    }
                }
            })
            
        except (PermissionDeniedError, ValidationError) as e:
            raise
        except Exception as e:
            self.log_error('doctor_earnings_summary', e, request.user.id)
            return self.handle_billing_error(e)

    @EdgeCaseHandler.handle_stripe_api_error
    @action(detail=False, methods=['post'])
    def update_account_status(self, request):
        """Update doctor's Stripe Connect account status"""
        try:
            self._verify_doctor_permission(request)
            EdgeCaseHandler.check_rate_limit(str(request.user.id), 'account_status_update', max_per_minute=10)
            
            transfer_service = UserTransferService()
            profile = transfer_service.update_connect_account_status(request.user)
            
            self.log_operation('doctor_account_status_updated', request.user.id, {
                'charges_enabled': profile.charges_enabled,
                'payouts_enabled': profile.payouts_enabled
            })
            
            return Response({
                'charges_enabled': profile.charges_enabled,
                'payouts_enabled': profile.payouts_enabled,
                'details_submitted': profile.details_submitted,
                'can_receive_appointment_payments': profile.can_receive_payments(),
                'can_receive_consultation_payments': profile.can_receive_payments(),
                'bank_account_connected': profile.payouts_enabled and profile.details_submitted
            })
            
        except (PermissionDeniedError, ValidationError, PaymentError) as e:
            raise
        except Exception as e:
            self.log_error('doctor_account_status_update', e, request.user.id)
            return self.handle_billing_error(e) 