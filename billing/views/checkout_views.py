"""
Checkout session creation views
"""
import json
import logging
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.shortcuts import get_object_or_404
from django.core.exceptions import ValidationError

from .base import (
    BasePaymentView, CustomerRequiredMixin, RateLimitMixin, 
    ErrorHandlingMixin, CacheKeyMixin, MetadataHelperMixin, LoggingMixin
)
from ..services import PaymentService
from ..models import Price
from ..exceptions import BillingError

logger = logging.getLogger(__name__)


class CreateCheckoutSessionView(
    BasePaymentView, 
    CustomerRequiredMixin, 
    RateLimitMixin, 
    ErrorHandlingMixin,
    CacheKeyMixin,
    MetadataHelperMixin,
    LoggingMixin
):
    """Create a Stripe Checkout session for SaaS platform services"""
    
    @method_decorator(require_http_methods(["POST"]))
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            price_id = data.get('price_id')
            success_url = data.get('success_url')
            cancel_url = data.get('cancel_url')
            
            if not price_id:
                return self.handle_validation_error('Price ID is required')
            
            if not success_url:
                return self.handle_validation_error('success_url is required')
            
            if not cancel_url:
                return self.handle_validation_error('cancel_url is required')
            
            # Rate limiting
            cache_key = self.get_checkout_cache_key(request.user.id)
            try:
                self.check_rate_limit(cache_key)
            except Exception as e:
                return self.handle_billing_error(e)
            
            # Get or create customer
            try:
                customer = self.get_or_create_customer(request.user)
                self.log_operation('customer_created', request.user.id, {'customer_id': customer.id})
            except Exception as e:
                self.log_error('customer_creation', e, request.user.id)
                return self.handle_billing_error(e)
            
            # Get price
            try:
                price = get_object_or_404(Price, stripe_price_id=price_id)
            except Exception as e:
                self.log_error('price_retrieval', e, request.user.id, {'price_id': price_id})
                return self.handle_validation_error(f'Error getting price: {str(e)}')
            
            # Create checkout session
            try:
                payment_service = PaymentService()

                # Determine the correct mode based on price type
                mode = 'subscription' if price.is_recurring else 'payment'

                # Build metadata with additional context
                metadata = {
                    'price_id': str(price.id),
                    'customer_id': str(customer.id),
                    'mode': mode,
                }

                # Add subscription plan metadata if available from request
                if 'metadata' in data and isinstance(data['metadata'], dict):
                    metadata.update(data['metadata'])

                # Use the URLs from the request data, adding session_id placeholder to success_url
                if '?' in success_url:
                    final_success_url = f"{success_url}&session_id={{CHECKOUT_SESSION_ID}}"
                else:
                    final_success_url = f"{success_url}?session_id={{CHECKOUT_SESSION_ID}}"

                session = payment_service.create_checkout_session(
                    customer=customer,
                    price=price,
                    success_url=final_success_url,
                    cancel_url=cancel_url,
                    metadata=metadata,
                    mode=mode
                )
                
                # Set rate limit
                self.set_rate_limit(cache_key)
                
                self.log_operation('checkout_session_created', request.user.id, {
                    'session_id': session.id,
                    'price_id': price_id,
                    'mode': mode,
                    'is_recurring': price.is_recurring,
                    'success_url': success_url,
                    'cancel_url': cancel_url
                })
                
            except Exception as e:
                self.log_error('checkout_session_creation', e, request.user.id, {'price_id': price_id})
                return self.handle_billing_error(e)
            
            return JsonResponse({
                'session_id': session.id,
                'url': session.url
            })
            
        except ValidationError as e:
            self.log_error('validation', e, request.user.id)
            return self.handle_validation_error(str(e))
        except Exception as e:
            self.log_error('unexpected', e, request.user.id)
            return self.handle_billing_error(e)



