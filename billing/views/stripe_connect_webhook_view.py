"""
Stripe Connect Webhook View for handling Connect-specific events
"""

import json
import logging
import stripe
from django.conf import settings
from django.http import HttpResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.views import View
from rest_framework import status

from ..services.webhook_service import WebhookService
from ..exceptions import WebhookError

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class StripeConnectWebhookView(View):
    """
    Webhook endpoint for Stripe Connect events
    
    Handles Connect-specific events like:
    - account.updated
    - account.application.deauthorized
    - transfer.created
    - transfer.updated
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.webhook_service = WebhookService()
        stripe.api_key = settings.STRIPE_SECRET_KEY
    
    def post(self, request):
        """Handle incoming Stripe Connect webhook"""
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE', '')
        
        try:
            # Verify webhook signature
            secret = getattr(settings, 'STRIPE_CONNECT_WEBHOOK_SECRET', '')
            if not secret:
                logger.error("STRIPE_CONNECT_WEBHOOK_SECRET not configured")
                return HttpResponse("Webhook secret not configured", status=500)
            
            # Use Stripe's built-in webhook verification and parsing
            try:
                event = stripe.Webhook.construct_event(
                    payload, sig_header, secret, tolerance=300  # 5 minutes tolerance
                )
            except stripe.error.SignatureVerificationError as e:
                logger.error(f"Invalid signature for Connect webhook: {str(e)}")
                return HttpResponse("Invalid signature", status=400)
            except ValueError as e:
                logger.error(f"Invalid payload for Connect webhook: {e}")
                return HttpResponse("Invalid payload", status=400)
            
            # Log the event
            logger.info(f"Received Connect webhook event: {event.type} - {event.id}")
            
            # Route to appropriate handler
            success = self._handle_event(event)
            
            if success:
                logger.info(f"Successfully processed Connect webhook: {event.type}")
                return HttpResponse("OK", status=200)
            else:
                logger.error(f"Failed to process Connect webhook: {event.type}")
                return HttpResponse("Processing failed", status=500)
                
        except WebhookError as e:
            logger.error(f"Webhook error: {str(e)}")
            return HttpResponse(f"Webhook error: {str(e)}", status=400)
        except Exception as e:
            logger.error(f"Unexpected error processing Connect webhook: {str(e)}")
            return HttpResponse("Internal server error", status=500)
    
    def _handle_event(self, event):
        """Route event to appropriate handler using unified webhook handling"""
        try:
            # Use the unified webhook service for most events
            if event.type in ['account.updated', 'account.application.deauthorized',
                             'payment_intent.succeeded', 'payment_intent.payment_failed',
                             'checkout.session.completed']:
                return self.webhook_service.handle_event(event)

            # Handle Connect-specific transfer events locally
            elif event.type == 'transfer.created':
                return self._handle_transfer_created(event.data.object)
            elif event.type == 'transfer.updated':
                return self._handle_transfer_updated(event.data.object)
            else:
                logger.warning(f"No handler for Connect event type: {event.type}")
                return True  # Return True for unhandled events to avoid retries

        except Exception as e:
            logger.error(f"Error in handler for {event.type}: {str(e)}")
            return False
    
    def _handle_transfer_created(self, transfer):
        """Handle transfer.created event"""
        try:
            logger.info(f"Processing transfer created: {transfer.id}")

            # Extract metadata for business logic
            metadata = getattr(transfer, 'metadata', {})

            # Update UserTransfer status if this is a telemedicine payment
            if metadata.get('transfer_id'):
                from ..models.payment import UserTransfer
                try:
                    user_transfer = UserTransfer.objects.get(id=metadata['transfer_id'])
                    # Transfer created means funds are being moved to doctor's account
                    if user_transfer.status == 'completed':
                        logger.info(f"Transfer {transfer.id} created for UserTransfer {user_transfer.id}")
                    else:
                        logger.warning(f"Transfer {transfer.id} created but UserTransfer {user_transfer.id} status is {user_transfer.status}")
                except UserTransfer.DoesNotExist:
                    logger.warning(f"UserTransfer not found for transfer_id {metadata['transfer_id']}")

            logger.info(
                f"Transfer created: {transfer.id} "
                f"Amount: {transfer.amount} "
                f"Destination: {transfer.destination} "
                f"Metadata: {metadata}"
            )

            return True

        except Exception as e:
            logger.error(f"Error handling transfer created: {str(e)}")
            return False
    
    def _handle_transfer_updated(self, transfer):
        """Handle transfer.updated event"""
        try:
            logger.info(f"Processing transfer updated: {transfer.id}")

            # Extract metadata and status for business logic
            metadata = getattr(transfer, 'metadata', {})
            transfer_status = getattr(transfer, 'status', 'unknown')

            # Update UserTransfer status based on Stripe transfer status
            if metadata.get('transfer_id'):
                from ..models.payment import UserTransfer
                try:
                    user_transfer = UserTransfer.objects.get(id=metadata['transfer_id'])

                    # Map Stripe transfer status to our status
                    if transfer_status == 'paid':
                        # Transfer completed successfully
                        logger.info(f"Transfer {transfer.id} completed for UserTransfer {user_transfer.id}")
                    elif transfer_status == 'failed':
                        # Transfer failed - might need to handle refund
                        logger.error(f"Transfer {transfer.id} failed for UserTransfer {user_transfer.id}")
                    elif transfer_status == 'canceled':
                        # Transfer was cancelled
                        logger.info(f"Transfer {transfer.id} cancelled for UserTransfer {user_transfer.id}")

                except UserTransfer.DoesNotExist:
                    logger.warning(f"UserTransfer not found for transfer_id {metadata['transfer_id']}")

            logger.info(
                f"Transfer updated: {transfer.id} "
                f"Status: {transfer_status} "
                f"Amount: {transfer.amount}"
            )

            return True

        except Exception as e:
            logger.error(f"Error handling transfer updated: {str(e)}")
            return False