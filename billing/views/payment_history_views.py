"""
Payment history and details views
"""
import logging
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.db.models import Q

from ..models import ServicePayment, UserTransfer, Customer

logger = logging.getLogger(__name__)


@login_required
def list_payment_history(request):
    """List all payment history for the authenticated user"""
    try:
        # Get customer
        customer = get_object_or_404(Customer, user=request.user)
        
        # Get service payments
        service_payments = ServicePayment.objects.filter(
            user=request.user
        ).select_related('service').order_by('-created_at')
        
        # Enterprise payments removed - no longer supported
        enterprise_payments = []
        
        # Get user transfers (sent and received)
        user_transfers = UserTransfer.objects.filter(
            Q(sender=request.user) | Q(receiver=request.user)
        ).select_related('sender', 'receiver').order_by('-created_at')
        
        # Format service payments
        service_payments_data = []
        for payment in service_payments:
            service_payments_data.append({
                'id': str(payment.id),
                'type': 'service_payment',
                'service': {
                    'id': str(payment.service.id),
                    'name': payment.service.name,
                    'description': payment.service.description
                },
                'amount': payment.amount / 100,  # Convert cents to dollars
                'currency': payment.currency,
                'status': payment.status,
                'created_at': payment.created_at,
                'stripe_payment_intent_id': payment.stripe_payment_intent_id,
                'metadata': payment.metadata
            })
        
        # Format enterprise payments
        enterprise_payments_data = []
        for payment in enterprise_payments:
            enterprise_payments_data.append({
                'id': str(payment.id),
                'type': 'enterprise_payment',
                'enterprise': {
                    'id': str(payment.enterprise.id),
                    'name': payment.enterprise.enterprise.name if payment.enterprise.enterprise else 'Unknown'
                },
                'service': {
                    'id': str(payment.service.id) if payment.service else None,
                    'name': payment.service.name if payment.service else 'Unknown Service'
                } if payment.service else None,
                'amount': payment.amount / 100,  # Convert cents to dollars
                'platform_fee_amount': payment.platform_fee_amount / 100 if payment.platform_fee_amount else 0,
                'net_amount': payment.net_amount / 100 if payment.net_amount else 0,
                'status': payment.status,
                'created_at': payment.created_at,
                'stripe_payment_intent_id': payment.stripe_payment_intent_id,
                'metadata': payment.metadata
            })
        
        # Format user transfers
        user_transfers_data = []
        for transfer in user_transfers:
            transfer_data = {
                'id': str(transfer.id),
                'type': 'user_transfer',
                'transfer_type': transfer.transfer_type,
                'amount': transfer.amount / 100,  # Convert cents to dollars
                'currency': transfer.currency,
                'status': transfer.status,
                'message': transfer.message,
                'created_at': transfer.created_at,
                'stripe_payment_intent_id': transfer.stripe_payment_intent_id,
                'platform_fee_amount': transfer.platform_fee_amount / 100 if transfer.platform_fee_amount else 0
            }
            
            # Add sender/receiver info based on user's perspective
            if transfer.sender == request.user:
                transfer_data['direction'] = 'sent'
                transfer_data['other_party'] = {
                    'id': transfer.receiver.id,
                    'name': f"{transfer.receiver.first_name} {transfer.receiver.last_name}".strip() or transfer.receiver.email,
                    'email': transfer.receiver.email
                }
            else:
                transfer_data['direction'] = 'received'
                transfer_data['other_party'] = {
                    'id': transfer.sender.id,
                    'name': f"{transfer.sender.first_name} {transfer.sender.last_name}".strip() or transfer.sender.email,
                    'email': transfer.sender.email
                }
            
            user_transfers_data.append(transfer_data)
        
        # Combine all payments and sort by date
        all_payments = service_payments_data + enterprise_payments_data + user_transfers_data
        all_payments.sort(key=lambda x: x['created_at'], reverse=True)
        
        return JsonResponse({
            'payments': all_payments,
            'summary': {
                'total_payments': len(all_payments),
                'service_payments': len(service_payments_data),
                'enterprise_payments': len(enterprise_payments_data),
                'user_transfers': len(user_transfers_data)
            }
        })
        
    except Exception as e:
        logger.error(f"Error listing payment history: {str(e)}")
        return JsonResponse({
            'error': str(e)
        }, status=400)


@login_required
def get_payment_details(request, payment_id):
    """Get detailed information about a specific payment"""
    try:
        payment_type = request.GET.get('type', 'service_payment')
        
        if payment_type == 'service_payment':
            payment = get_object_or_404(
                ServicePayment,
                id=payment_id,
                user=request.user
            )
            
            payment_data = {
                'id': str(payment.id),
                'type': 'service_payment',
                'service': {
                    'id': str(payment.service.id),
                    'name': payment.service.name,
                    'description': payment.service.description,
                    'category': payment.service.category,
                    'features': payment.service.features
                },
                'amount': payment.amount / 100,
                'currency': payment.currency,
                'status': payment.status,
                'created_at': payment.created_at,
                'updated_at': payment.updated_at,
                'stripe_payment_intent_id': payment.stripe_payment_intent_id,
                'metadata': payment.metadata,
                'dna_analysis': {
                    'id': str(payment.dna_analysis.id),
                    'status': payment.dna_analysis.status
                } if payment.dna_analysis else None
            }
            
        elif payment_type == 'enterprise_payment':
            # Enterprise payments no longer supported
            return JsonResponse({
                'error': 'Enterprise payments are no longer supported'
            }, status=400)
            
        elif payment_type == 'user_transfer':
            payment = get_object_or_404(
                UserTransfer,
                id=payment_id
            )
            
            # Verify user is either sender or receiver
            if payment.sender != request.user and payment.receiver != request.user:
                return JsonResponse({
                    'error': 'Access denied'
                }, status=403)
            
            payment_data = {
                'id': str(payment.id),
                'type': 'user_transfer',
                'transfer_type': payment.transfer_type,
                'amount': payment.amount / 100,
                'currency': payment.currency,
                'status': payment.status,
                'message': payment.message,
                'created_at': payment.created_at,
                'updated_at': payment.updated_at,
                'stripe_payment_intent_id': payment.stripe_payment_intent_id,
                'platform_fee_amount': payment.platform_fee_amount / 100 if payment.platform_fee_amount else 0,
                'sender': {
                    'id': payment.sender.id,
                    'name': f"{payment.sender.first_name} {payment.sender.last_name}".strip() or payment.sender.email,
                    'email': payment.sender.email
                },
                'receiver': {
                    'id': payment.receiver.id,
                    'name': f"{payment.receiver.first_name} {payment.receiver.last_name}".strip() or payment.receiver.email,
                    'email': payment.receiver.email
                },
                'platform_fee': {
                    'id': str(payment.platform_fee.id),
                    'percentage': payment.platform_fee.percentage,
                    'fixed_amount': payment.platform_fee.fixed_amount / 100
                } if payment.platform_fee else None
            }
            
        else:
            return JsonResponse({
                'error': 'Invalid payment type'
            }, status=400)
        
        return JsonResponse({
            'payment': payment_data
        })
        
    except Exception as e:
        logger.error(f"Error getting payment details: {str(e)}")
        return JsonResponse({
            'error': str(e)
        }, status=400)
