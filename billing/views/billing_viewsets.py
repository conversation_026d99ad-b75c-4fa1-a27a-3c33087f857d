"""
DRF ViewSets for billing operations
"""
import stripe
import logging
from django.conf import settings
from django.shortcuts import get_object_or_404
from rest_framework import viewsets, serializers, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from .base import BaseViewSet, APIErrorHandlingMixin, LoggingMixin
from ..models import (
    Product, ServicePayment, Subscription,
    ServiceAccess, SubscriptionAccess, SolutionAccess
)
from ..serializers import (
    ProductSerializer, ServicePaymentSerializer, SubscriptionSerializer,
    ServiceAccessSerializer, SubscriptionAccessSerializer, SolutionAccessSerializer
)
from content_management.models import Service, SubscriptionPlan
from ..services import PaymentService

logger = logging.getLogger(__name__)


class BillingServiceSerializer(serializers.ModelSerializer):
    """Serializer for billing services"""
    content_id = serializers.UUIDField(source='content_id')
    type = serializers.CharField(source='product_type')
    price_id = serializers.SerializerMethodField()
    amount = serializers.SerializerMethodField()
    currency = serializers.SerializerMethodField()
    is_recurring = serializers.SerializerMethodField()
    recurring_interval = serializers.SerializerMethodField()
    button_text = serializers.SerializerMethodField()
    features = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id', 'content_id', 'name', 'description', 'type',
            'price_id', 'amount', 'currency', 'is_recurring',
            'recurring_interval', 'button_text', 'features'
        ]

    def get_price_id(self, obj):
        try:
            return obj.prices.first().stripe_price_id
        except:
            return None

    def get_amount(self, obj):
        try:
            return obj.prices.first().unit_amount / 100
        except:
            return 0

    def get_currency(self, obj):
        try:
            return obj.prices.first().currency
        except:
            return 'usd'

    def get_is_recurring(self, obj):
        try:
            return bool(obj.prices.first().recurring)
        except:
            return False

    def get_recurring_interval(self, obj):
        try:
            return obj.prices.first().recurring.get('interval')
        except:
            return None

    def get_button_text(self, obj):
        if obj.service:
            return obj.service.button_text
        elif obj.subscription_plan:
            return obj.subscription_plan.button_text
        elif obj.solution:
            return obj.solution.button_text
        return "Get Started"

    def get_features(self, obj):
        if obj.service:
            return obj.service.features
        elif obj.subscription_plan:
            return obj.subscription_plan.features
        elif obj.solution:
            return obj.solution.features
        return []


class BillingServiceViewSet(
    BaseViewSet,
    APIErrorHandlingMixin,
    LoggingMixin,
    viewsets.ReadOnlyModelViewSet
):
    """ViewSet for billing services"""
    serializer_class = BillingServiceSerializer
    
    def get_queryset(self):
        return Product.objects.filter(active=True)
    
    def get_serializer_class(self):
        return BillingServiceSerializer


class ServicePaymentViewSet(
    BaseViewSet,
    APIErrorHandlingMixin,
    LoggingMixin,
    viewsets.ModelViewSet
):
    """ViewSet for service payments"""
    queryset = ServicePayment.objects.all()
    serializer_class = ServicePaymentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ServicePayment.objects.filter(user=self.request.user)

    @action(detail=False, methods=['post'])
    def create_checkout_session(self, request):
        """Create a checkout session for a service"""
        try:
            service_id = request.data.get('service_id')
            success_url = request.data.get('success_url', f'{settings.SITE_URL}/services/success')
            cancel_url = request.data.get('cancel_url', f'{settings.SITE_URL}/services/cancel')
            
            service = get_object_or_404(Service, id=service_id)
            
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': 'usd',
                        'unit_amount': int(service.price * 100),
                        'product_data': {
                            'name': service.name,
                            'description': service.description,
                        },
                    },
                    'quantity': 1,
                }],
                mode='payment',
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'user_id': request.user.id,
                    'service_id': service.id,
                    'type': 'one_time'
                }
            )
            
            self.log_operation('service_checkout_session_created', request.user.id, {
                'session_id': checkout_session.id,
                'service_id': str(service.id)
            })
            
            return Response({'sessionId': checkout_session.id})
            
        except Exception as e:
            self.log_error('service_checkout_session_creation', e, request.user.id)
            return self.handle_billing_error(e)


class SubscriptionViewSet(
    BaseViewSet,
    APIErrorHandlingMixin,
    LoggingMixin,
    viewsets.ModelViewSet
):
    """ViewSet for subscriptions"""
    queryset = Subscription.objects.all()
    serializer_class = SubscriptionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Subscription.objects.filter(customer__user=self.request.user)

    @action(detail=False, methods=['post'])
    def create_checkout_session(self, request):
        """Create a checkout session for a subscription"""
        try:
            plan_id = request.data.get('plan_id')
            success_url = request.data.get('success_url', f'{settings.SITE_URL}/subscriptions/success')
            cancel_url = request.data.get('cancel_url', f'{settings.SITE_URL}/subscriptions/cancel')
            
            plan = get_object_or_404(SubscriptionPlan, id=plan_id)
            
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price': plan.stripe_price_id,
                    'quantity': 1,
                }],
                mode='subscription',
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'user_id': request.user.id,
                    'plan_id': plan.id,
                    'type': 'subscription'
                }
            )
            
            self.log_operation('subscription_checkout_session_created', request.user.id, {
                'session_id': checkout_session.id,
                'plan_id': str(plan.id)
            })
            
            return Response({'sessionId': checkout_session.id})
            
        except Exception as e:
            self.log_error('subscription_checkout_session_creation', e, request.user.id)
            return self.handle_billing_error(e)





class ServiceAccessViewSet(
    BaseViewSet,
    APIErrorHandlingMixin,
    LoggingMixin,
    viewsets.ModelViewSet
):
    """ViewSet for service access"""
    queryset = ServiceAccess.objects.all()
    serializer_class = ServiceAccessSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ServiceAccess.objects.filter(user=self.request.user)


class SubscriptionAccessViewSet(
    BaseViewSet,
    APIErrorHandlingMixin,
    LoggingMixin,
    viewsets.ModelViewSet
):
    """ViewSet for subscription access"""
    queryset = SubscriptionAccess.objects.all()
    serializer_class = SubscriptionAccessSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return SubscriptionAccess.objects.filter(user=self.request.user)


class SolutionAccessViewSet(
    BaseViewSet,
    APIErrorHandlingMixin,
    LoggingMixin,
    viewsets.ModelViewSet
):
    """ViewSet for solution access"""
    queryset = SolutionAccess.objects.all()
    serializer_class = SolutionAccessSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return SolutionAccess.objects.filter(enterprise__user=self.request.user)
