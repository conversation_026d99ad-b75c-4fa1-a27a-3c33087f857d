"""
Payment processing and management views
"""
import json
import stripe
import logging
from django.conf import settings
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timedelta

from .base import (
    BasePaymentView, CustomerRequiredMixin, ErrorHandlingMixin,
    MetadataHelperMixin, LoggingMixin
)
from ..services import PaymentService, SubscriptionService, CustomerService
from ..models import (
    Customer, Price, Subscription, ServicePayment, ServiceAccess
)
from ..exceptions import BillingError

logger = logging.getLogger(__name__)


class PaymentSuccessView(
    BasePaymentView,
    ErrorHandlingMixin,
    LoggingMixin
):
    """Handle successful payment"""
    
    def get(self, request, *args, **kwargs):
        try:
            session_id = request.GET.get('session_id')
            if not session_id:
                return self.handle_validation_error('Session ID is required')

            # Use PaymentService to handle the business logic
            payment_service = PaymentService()
            result = payment_service.process_checkout_success(session_id)

            self.log_operation('payment_success_processed', result['user_id'], {
                'payment_id': result['payment_id'],
                'service_id': result['service_id'],
                'session_id': session_id
            })

            return JsonResponse({
                'success': True,
                'payment_id': result['payment_id'],
                'service_id': result['service_id']
            })

        except Exception as e:
            self.log_error('payment_success_processing', e, getattr(request.user, 'id', None))
            return self.handle_billing_error(e)


class PaymentCancelView(BasePaymentView):
    """Handle cancelled payment"""
    
    def get(self, request, *args, **kwargs):
        return JsonResponse({
            'success': False,
            'message': 'Payment was cancelled'
        })


@login_required
def create_subscription(request):
    """Create a new subscription for the authenticated user"""
    try:
        data = json.loads(request.body)
        price_id = data.get('price_id')
        payment_method_id = data.get('payment_method_id')
        
        if not price_id:
            return JsonResponse({
                'error': 'Price ID is required'
            }, status=400)
        
        # Get or create customer
        customer_service = CustomerService()
        customer = customer_service.create_or_update_customer(request.user)
        
        # Get price
        price = get_object_or_404(Price, stripe_price_id=price_id)
        
        # Create subscription
        subscription_service = SubscriptionService()
        subscription = subscription_service.create_subscription(
            customer=customer,
            price=price,
            payment_method_id=payment_method_id
        )
        
        # Get the Stripe subscription for client secret
        stripe_subscription = stripe.Subscription.retrieve(subscription.stripe_subscription_id)
        
        return JsonResponse({
            'subscription_id': subscription.stripe_subscription_id,
            'client_secret': stripe_subscription.latest_invoice.payment_intent.client_secret
        })
        
    except Exception as e:
        logger.error(f"Error creating subscription: {str(e)}")
        return JsonResponse({
            'error': str(e)
        }, status=400)


@login_required
def cancel_subscription(request, subscription_id):
    """Cancel a subscription"""
    try:
        subscription = get_object_or_404(
            Subscription,
            stripe_subscription_id=subscription_id,
            customer__user=request.user
        )
        
        subscription_service = SubscriptionService()
        updated_subscription = subscription_service.cancel_subscription(subscription.id)
        
        return JsonResponse({
            'status': 'success',
            'subscription': {
                'id': updated_subscription.stripe_subscription_id,
                'status': updated_subscription.status,
                'cancel_at_period_end': updated_subscription.cancel_at_period_end
            }
        })
        
    except Exception as e:
        logger.error(f"Error canceling subscription: {str(e)}")
        return JsonResponse({
            'error': str(e)
        }, status=400)


@login_required
def list_subscriptions(request):
    """List all subscriptions for the authenticated user"""
    try:
        customer = get_object_or_404(Customer, user=request.user)
        subscriptions = Subscription.objects.filter(customer=customer)
        
        return JsonResponse({
            'subscriptions': [{
                'id': sub.stripe_subscription_id,
                'status': sub.status,
                'current_period_end': sub.current_period_end.isoformat(),
                'cancel_at_period_end': sub.cancel_at_period_end,
                'price': {
                    'id': sub.price.stripe_price_id,
                    'amount': sub.price.unit_amount / 100,
                    'currency': sub.price.currency,
                    'product': {
                        'name': sub.price.product.name,
                        'description': sub.price.product.description
                    }
                }
            } for sub in subscriptions]
        })
        
    except Exception as e:
        logger.error(f"Error listing subscriptions: {str(e)}")
        return JsonResponse({
            'error': str(e)
        }, status=400)


@login_required
def create_payment_method(request):
    """Create a new payment method for the authenticated user"""
    try:
        data = json.loads(request.body)
        payment_method_id = data.get('payment_method_id')
        
        if not payment_method_id:
            return JsonResponse({
                'error': 'Payment method ID is required'
            }, status=400)
        
        # Get or create customer
        customer_service = CustomerService()
        customer = customer_service.create_or_update_customer(request.user)
        
        # Attach payment method to customer
        stripe.PaymentMethod.attach(
            payment_method_id,
            customer=customer.stripe_customer_id
        )
        
        # Set as default payment method
        stripe.Customer.modify(
            customer.stripe_customer_id,
            invoice_settings={
                'default_payment_method': payment_method_id
            }
        )
        
        return JsonResponse({
            'status': 'success'
        })
        
    except Exception as e:
        logger.error(f"Error creating payment method: {str(e)}")
        return JsonResponse({
            'error': str(e)
        }, status=400)


@login_required
def list_payment_methods(request):
    """List all payment methods for the authenticated user"""
    try:
        customer = get_object_or_404(Customer, user=request.user)
        payment_methods = stripe.PaymentMethod.list(
            customer=customer.stripe_customer_id,
            type='card'
        )
        
        return JsonResponse({
            'payment_methods': payment_methods.data
        })
        
    except Exception as e:
        logger.error(f"Error listing payment methods: {str(e)}")
        return JsonResponse({
            'error': str(e)
        }, status=400)


@login_required
def create_setup_intent(request):
    """Create a SetupIntent for saving a payment method"""
    try:
        setup_intent = stripe.SetupIntent.create()
        
        return JsonResponse({
            'client_secret': setup_intent.client_secret
        })
        
    except Exception as e:
        logger.error(f"Error creating setup intent: {str(e)}")
        return JsonResponse({
            'error': str(e)
        }, status=400)



