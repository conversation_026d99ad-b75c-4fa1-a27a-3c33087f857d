"""
Access control and management views
"""
import logging
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404

from .base import APIErrorHandlingMixin, EnterpriseRequiredMixin, LoggingMixin
from ..models import ServiceAccess, SubscriptionAccess, SolutionAccess
from ..services import StripeService  # TODO: Replace with specific services
from ..exceptions import BillingError

logger = logging.getLogger(__name__)


class AccessViewMixin(APIErrorHandlingMixin, LoggingMixin):
    """Mixin for access-related views"""
    pass


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_service_access(request, service_id):
    """Check if a user has access to a specific service"""
    try:
        # Use AccessService to handle the business logic
        from ..services.access_service import AccessService
        access_service = AccessService()

        result = access_service.check_service_access(request.user, service_id)

        # Extract status code and remove it from response data
        status_code = result.pop('status_code', 200)

        return Response(result, status=status_code)

    except Exception as e:
        logger.error(f"Error checking service access: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_subscription_access(request, subscription_id):
    """Check if a user has access to a specific subscription"""
    try:
        # Use AccessService to handle the business logic
        from ..services.access_service import AccessService
        access_service = AccessService()

        result = access_service.check_subscription_access(request.user, subscription_id)

        # Extract status code and remove it from response data
        status_code = result.pop('status_code', 200)

        return Response(result, status=status_code)

    except Exception as e:
        logger.error(f"Error checking subscription access: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_solution_access(request, solution_id):
    """Check if an enterprise has access to a specific solution"""
    try:
        # Check if user is associated with an enterprise
        if not hasattr(request.user, 'enterprise'):
            return Response({
                'has_access': False,
                'message': 'User is not associated with an enterprise'
            }, status=status.HTTP_403_FORBIDDEN)
        
        enterprise = request.user.enterprise
        
        # Get the solution access record
        access = SolutionAccess.objects.filter(
            enterprise=enterprise,
            solution_id=solution_id,
            status='active'
        ).first()
        
        if not access:
            return Response({
                'has_access': False,
                'message': 'No active access found for this solution'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return access data
        return Response({
            'has_access': True,
            'access_granted_at': access.access_granted_at,
            'subscription_status': access.subscription.status,
            'current_period_end': access.subscription.current_period_end
        })
        
    except Exception as e:
        logger.error(f"Error checking solution access: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_user_services(request):
    """List all services that a user has access to"""
    try:
        # Get all active service access records
        service_access = ServiceAccess.objects.filter(
            user=request.user,
            status='active'
        ).select_related('service')
        
        # Get all active subscription access records
        subscription_access = SubscriptionAccess.objects.filter(
            user=request.user,
            status='active'
        ).select_related('subscription', 'subscription__price', 'subscription__price__product')
        
        # Format service access data
        services = []
        for access in service_access:
            if access.access_expires_at and access.access_expires_at < timezone.now():
                access.status = 'expired'
                access.save()
                continue
                
            services.append({
                'id': access.service.id,
                'name': access.service.name,
                'type': 'service',
                'access_granted_at': access.access_granted_at,
                'access_expires_at': access.access_expires_at,
                'analysis_access_data': access.analysis_access_data
            })
        
        # Format subscription access data
        subscriptions = []
        for access in subscription_access:
            subscriptions.append({
                'id': access.subscription.price.product.id,
                'name': access.subscription.price.product.name,
                'type': 'subscription',
                'access_granted_at': access.access_granted_at,
                'subscription_status': access.subscription.status,
                'current_period_end': access.subscription.current_period_end
            })
        
        return Response({
            'services': services,
            'subscriptions': subscriptions
        })
        
    except Exception as e:
        logger.error(f"Error listing user services: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_enterprise_solutions(request):
    """List all solutions that an enterprise has access to"""
    try:
        # Check if user is associated with an enterprise
        if not hasattr(request.user, 'enterprise'):
            return Response({
                'has_access': False,
                'message': 'User is not associated with an enterprise'
            }, status=status.HTTP_403_FORBIDDEN)
        
        enterprise = request.user.enterprise
        
        # Get all active solution access records
        solution_access = SolutionAccess.objects.filter(
            enterprise=enterprise,
            status='active'
        ).select_related('solution', 'subscription')
        
        # Format solution access data
        solutions = []
        for access in solution_access:
            solutions.append({
                'id': access.solution.id,
                'name': access.solution.name,
                'access_granted_at': access.access_granted_at,
                'subscription_status': access.subscription.status,
                'current_period_end': access.subscription.current_period_end
            })
        
        return Response({
            'solutions': solutions
        })
        
    except Exception as e:
        logger.error(f"Error listing enterprise solutions: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def revoke_service_access(request, service_id):
    """Revoke access to a service"""
    try:
        # Get service access
        service_access = get_object_or_404(
            ServiceAccess,
            service_id=service_id,
            user=request.user,
            status='active'
        )

        # Revoke access
        # TODO: Replace with specific service method
        StripeService.revoke_service_access(service_access)

        return Response({
            'status': 'success',
            'message': 'Service access revoked successfully'
        })

    except Exception as e:
        logger.error(f"Error revoking service access: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def revoke_subscription_access(request, subscription_id):
    """Revoke access to a subscription"""
    try:
        # Get subscription access
        subscription_access = get_object_or_404(
            SubscriptionAccess,
            subscription_id=subscription_id,
            user=request.user,
            status='active'
        )

        # Revoke access
        # TODO: Replace with specific service method
        StripeService.revoke_subscription_access(subscription_access)

        return Response({
            'status': 'success',
            'message': 'Subscription access revoked successfully'
        })

    except Exception as e:
        logger.error(f"Error revoking subscription access: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def revoke_solution_access(request, solution_id):
    """Revoke access to a solution"""
    try:
        # Check if user is associated with an enterprise
        if not hasattr(request.user, 'enterprise'):
            return Response({
                'error': 'User is not associated with an enterprise'
            }, status=status.HTTP_403_FORBIDDEN)

        enterprise = request.user.enterprise

        # Get solution access
        solution_access = get_object_or_404(
            SolutionAccess,
            solution_id=solution_id,
            enterprise=enterprise,
            status='active'
        )

        # Revoke access
        # TODO: Replace with specific service method
        StripeService.revoke_solution_access(solution_access)

        return Response({
            'status': 'success',
            'message': 'Solution access revoked successfully'
        })

    except Exception as e:
        logger.error(f"Error revoking solution access: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
