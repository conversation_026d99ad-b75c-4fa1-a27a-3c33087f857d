"""
Payment link generation ViewSet
"""
import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny

from .base import BaseViewSet, APIErrorHandlingMixin, LoggingMixin
from ..services.payment_link_service import PaymentLinkService
from ..services.unified_payment_service import UnifiedPaymentService, PaymentType
from ..utils.payment_response import PaymentResponseFormat

logger = logging.getLogger(__name__)


class PaymentLinkViewSet(
    BaseViewSet,
    APIErrorHandlingMixin,
    LoggingMixin,
    viewsets.ViewSet
):
    """Specialized ViewSet for payment link generation"""
    
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['post'])
    def generate_appointment_payment_link(self, request):
        """Generate payment link for appointment booking"""
        try:
            appointment_id = request.data.get('appointment_id')
            amount = request.data.get('amount')  # Optional, will use service pricing
            
            if not appointment_id:
                return PaymentResponseFormat.validation_error(
                    "appointment_id is required",
                    field_errors={'appointment_id': ['This field is required']}
                )

            unified_service = UnifiedPaymentService()
            result = unified_service.create_checkout_session(
                payment_type=PaymentType.APPOINTMENT,
                appointment_id=appointment_id,
                amount=amount,
                user=request.user
            )
            
            self.log_operation('appointment_payment_link_generated', request.user.id, {
                'appointment_id': appointment_id,
                'session_id': result['session_id']
            })
            
            return PaymentResponseFormat.checkout_session_success(
                session_id=result['session_id'],
                session_url=result['checkout_url'],
                payment_type=result['payment_type'],
                additional_data={
                    'appointment_id': appointment_id,
                    'amount': result.get('amount')
                }
            )
            
        except Exception as e:
            self.log_error('appointment_payment_link_generation', e, request.user.id)
            return PaymentResponseFormat.payment_error(f"Failed to generate appointment payment link: {str(e)}")

    @action(detail=False, methods=['post'])
    def generate_consultation_payment_link(self, request):
        """Generate payment link for consultation booking"""
        try:
            consultation_type = request.data.get('consultation_type', 'general')
            doctor_id = request.data.get('doctor_id')
            amount = request.data.get('amount')  # Optional
            
            if not doctor_id:
                return Response({
                    'error': 'doctor_id is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            payment_link_service = PaymentLinkService()
            link_data = payment_link_service.generate_consultation_payment_link(
                consultation_type=consultation_type,
                doctor_id=doctor_id,
                amount=amount,
                user=request.user
            )
            
            self.log_operation('consultation_payment_link_generated', request.user.id, {
                'consultation_type': consultation_type,
                'doctor_id': doctor_id,
                'session_id': link_data['session_id']
            })
            
            return Response(link_data)
            
        except Exception as e:
            self.log_error('consultation_payment_link_generation', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['get'])
    def get_donation_link_info(self, request):
        """Get donation link information for a user"""
        try:
            recipient_id = request.query_params.get('recipient_id')
            
            if not recipient_id:
                return Response({
                    'error': 'recipient_id is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            payment_link_service = PaymentLinkService()
            donation_info = payment_link_service.get_donation_link_info(
                recipient_id=recipient_id,
                requester=request.user
            )
            
            return Response(donation_info)
            
        except Exception as e:
            self.log_error('donation_link_info', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['post'])
    def create_donation_session(self, request):
        """Create donation payment session"""
        try:
            recipient_id = request.data.get('recipient_id')
            amount = request.data.get('amount')
            message = request.data.get('message', '')
            
            if not recipient_id or not amount:
                return Response({
                    'error': 'recipient_id and amount are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            payment_link_service = PaymentLinkService()
            session_data = payment_link_service.create_donation_session(
                recipient_id=recipient_id,
                amount=amount,
                message=message,
                donor=request.user
            )
            
            self.log_operation('donation_session_created', request.user.id, {
                'recipient_id': recipient_id,
                'amount': amount,
                'session_id': session_data['session_id']
            })
            
            return Response(session_data)
            
        except Exception as e:
            self.log_error('donation_session_creation', e, request.user.id)
            return self.handle_billing_error(e) 