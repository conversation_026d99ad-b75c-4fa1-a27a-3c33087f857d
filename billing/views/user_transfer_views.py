"""
User transfer and payment profile views
"""
import logging
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db.models import Q
from django.utils import timezone
from datetime import timedelta
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.shortcuts import get_object_or_404
from accounts.models import CustomUser

from .base import BaseViewSet, APIErrorHandlingMixin, LoggingMixin, PaginationMixin
from ..services import UserTransferService
from ..services.payment_link_service import PaymentLinkService
from ..models import UserPaymentProfile, UserTransfer
from ..serializers.user_payment import (
    UserPaymentProfileSerializer, 
    UserTransferSerializer,
    DonationSettingsSerializer,
    CreateTransferSerializer,
    ConnectAccountStatusSerializer,
    PublicDonationInfoSerializer
)
from ..exceptions import BillingError

logger = logging.getLogger(__name__)


class UserPaymentProfileViewSet(
    BaseViewSet,
    APIErrorHandlingMixin,
    LoggingMixin,
    viewsets.ModelViewSet
):
    """API endpoint for managing user payment profiles"""
    
    serializer_class = UserPaymentProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserPaymentProfile.objects.filter(user=self.request.user)

    def get_object(self):
        profile, _ = UserPaymentProfile.objects.get_or_create(user=self.request.user)
        return profile

    @action(detail=False, methods=['post'])
    def create_connect_account(self, request):
        """Create a Stripe Connect Express account for the user"""
        try:
            # Check if user already has a payment profile with Stripe account
            profile = UserPaymentProfile.objects.filter(user=request.user).first()
            if profile and profile.stripe_account_id:
                return Response({
                    'error': 'User already has a Stripe Connect account',
                    'account_id': profile.stripe_account_id,
                    'charges_enabled': profile.charges_enabled,
                    'payouts_enabled': profile.payouts_enabled
                }, status=status.HTTP_400_BAD_REQUEST)
            
            transfer_service = UserTransferService()
            account_link = transfer_service.create_stripe_connect_account(request.user)
            
            self.log_operation('connect_account_created', request.user.id, {
                'account_link_created': True,
                'account_type': 'express'
            })
            
            return Response({
                'account_link': account_link,
                'message': 'Stripe Express account created successfully. Please complete onboarding.',
                'setup_instructions': 'You will only need to provide basic personal information and bank details.'
            })
            
        except Exception as e:
            self.log_error('connect_account_creation', e, request.user.id)
            return self.handle_billing_error(e)

    # @action(detail=False, methods=['post'])
    # def create_simplified_doctor_account(self, request):
    #     """Create a super simplified Stripe Connect account specifically for doctors - DEACTIVATED"""
    #     # This endpoint has been deactivated - use create_connect_account instead
    #     # which already provides Express account setup optimized for simplicity
    #     pass

    @action(detail=False, methods=['post'])
    def refresh_connect_account_link(self, request):
        """Refresh Stripe Connect account onboarding link for existing account"""
        try:
            profile = UserPaymentProfile.objects.filter(user=request.user).first()
            if not profile or not profile.stripe_account_id:
                return Response({
                    'error': 'No Stripe Connect account found. Please create an account first.'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Create new account link
            import stripe
            account_link = stripe.AccountLink.create(
                account=profile.stripe_account_id,
                refresh_url=f"{settings.FRONTEND_URL}",
                return_url=f"{settings.FRONTEND_URL}",
                type='account_onboarding',
                # Use collection_options instead of deprecated 'collect' parameter
                collection_options={
                    'fields': 'eventually_due',
                    'future_requirements': 'omit'
                }
            )
            
            self.log_operation('connect_account_link_refreshed', request.user.id, {
                'account_id': profile.stripe_account_id
            })
            
            return Response({
                'account_link': account_link.url,
                'message': 'New onboarding link created successfully.',
                'account_id': profile.stripe_account_id
            })
            
        except Exception as e:
            self.log_error('connect_account_link_refresh', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['post'])
    def update_donation_settings(self, request):
        """Update donation settings for the user's payment profile"""
        try:
            transfer_service = UserTransferService()
            profile = transfer_service.update_donation_settings(
                request.user, 
                request.data
            )
            
            response_serializer = DonationSettingsSerializer(instance={
                'accept_donations': profile.accept_donations,
                'donation_message': profile.donation_message,
                'minimum_donation': profile.minimum_donation,
                'suggested_donation_amounts': profile.suggested_donation_amounts,
            })
            
            self.log_operation('donation_settings_updated', request.user.id, {
                'profile_id': str(profile.id)
            })
            
            return Response(response_serializer.data)
            
        except Exception as e:
            self.log_error('donation_settings_update', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['get'])
    def payment_status(self, request):
        """Get basic payment account status"""
        try:
            profile = UserPaymentProfile.objects.filter(user=request.user).first()
            
            if not profile:
                return Response({
                    'setup_required': True,
                    'message': 'Payment account setup required',
                    'can_receive_payments': False
                })

            return Response({
                'stripe_account_id': profile.stripe_account_id,
                'charges_enabled': profile.charges_enabled,
                'payouts_enabled': profile.payouts_enabled,
                'details_submitted': profile.details_submitted,
                'is_verified': profile.is_verified,
                'can_receive_payments': profile.can_receive_payments(),
                'can_receive_donations': profile.can_receive_donations(),
                'setup_required': False
            })
            
        except Exception as e:
            self.log_error('payment_status_check', e, request.user.id)
            return self.handle_billing_error(e)



    @action(detail=False, methods=['get'])
    def get_donation_settings(self, request):
        """Get donation settings for the user"""
        try:
            transfer_service = UserTransferService()
            settings = transfer_service.get_donation_settings(request.user)
            return Response(settings)
            
        except Exception as e:
            self.log_error('donation_settings_retrieval', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['post'])
    def update_connect_account_status(self, request):
        """Update user's connect account status from Stripe"""
        try:
            transfer_service = UserTransferService()
            profile = transfer_service.update_connect_account_status(request.user)

            self.log_operation('connect_account_status_updated', request.user.id, {
                'charges_enabled': profile.charges_enabled,
                'payouts_enabled': profile.payouts_enabled
            })

            serializer = UserPaymentProfileSerializer(profile)
            return Response(serializer.data)

        except Exception as e:
            self.log_error('connect_account_status_update', e, request.user.id)
            return self.handle_billing_error(e)
    
    @action(detail=False, methods=['get'])
    def check_account_capabilities(self, request):
        """Check detailed account capabilities from Stripe"""
        try:
            profile = UserPaymentProfile.objects.filter(user=request.user).first()
            if not profile or not profile.stripe_account_id:
                return Response({
                    'error': 'No Stripe Connect account found',
                    'setup_required': True
                }, status=status.HTTP_404_NOT_FOUND)
            
            import stripe
            account = stripe.Account.retrieve(profile.stripe_account_id)
            
            capabilities = getattr(account, 'capabilities', {})
            requirements = getattr(account, 'requirements', {})
            
            return Response({
                'account_id': account.id,
                'charges_enabled': account.charges_enabled,
                'payouts_enabled': account.payouts_enabled,
                'details_submitted': account.details_submitted,
                'capabilities': capabilities,
                'requirements': requirements,
                'can_receive_transfers': capabilities.get('transfers') in ['active', 'pending'],
                'transfers_capability_status': capabilities.get('transfers', 'not_requested'),
                'setup_complete': account.charges_enabled and account.details_submitted,
                'needs_action': bool(requirements.get('currently_due', [])),
                'currently_due': requirements.get('currently_due', []),
                'past_due': requirements.get('past_due', []),
            })
            
        except Exception as e:
            self.log_error('account_capabilities_check', e, request.user.id)
            return self.handle_billing_error(e)
    
    @action(detail=False, methods=['post'])
    def request_transfers_capability(self, request):
        """Request transfers capability for the user's Stripe account"""
        try:
            profile = UserPaymentProfile.objects.filter(user=request.user).first()
            if not profile or not profile.stripe_account_id:
                return Response({
                    'error': 'No Stripe Connect account found',
                    'setup_required': True
                }, status=status.HTTP_404_NOT_FOUND)
            
            import stripe
            
            # First check current capabilities
            account = stripe.Account.retrieve(profile.stripe_account_id)
            capabilities = getattr(account, 'capabilities', {})
            current_transfers = capabilities.get('transfers')
            
            if current_transfers == 'active':
                return Response({
                    'message': 'Transfers capability is already active',
                    'transfers_capability_status': 'active'
                })
            
            # Request transfers capability
            updated_account = stripe.Account.modify(
                profile.stripe_account_id,
                capabilities={'transfers': {'requested': True}}
            )
            
            updated_capabilities = getattr(updated_account, 'capabilities', {})
            updated_transfers = updated_capabilities.get('transfers')
            
            self.log_operation('transfers_capability_requested', request.user.id, {
                'account_id': profile.stripe_account_id,
                'previous_status': current_transfers,
                'new_status': updated_transfers
            })
            
            return Response({
                'message': 'Transfers capability requested successfully',
                'transfers_capability_status': updated_transfers,
                'capabilities': updated_capabilities,
                'next_steps': 'Complete account verification if required' if updated_transfers == 'pending' else 'Capability should be active now'
            })
            
        except Exception as e:
            self.log_error('transfers_capability_request', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['get'])
    def check_pending_transfers(self, request):
        """Check user's pending transfers before deactivation"""
        try:
            transfer_service = UserTransferService()
            pending_info = transfer_service.get_user_pending_transfers_info(request.user)

            # Calculate totals
            total_pending_count = (
                pending_info['pending_as_sender']['count'] + 
                pending_info['pending_as_receiver']['count']
            )
            
            total_pending_amount = (
                pending_info['pending_as_sender']['total_amount'] + 
                pending_info['pending_as_receiver']['total_amount']
            )

            self.log_operation('pending_transfers_checked', request.user.id, {
                'total_pending_count': total_pending_count,
                'total_pending_amount': total_pending_amount
            })

            return Response({
                'total_pending_count': total_pending_count,
                'total_pending_amount_cents': total_pending_amount,
                'total_pending_amount_usd': total_pending_amount / 100,
                'can_deactivate_immediately': total_pending_count == 0,
                'pending_details': pending_info,
                'deactivation_info': {
                    'message': (
                        'Your account can be deactivated immediately.' if total_pending_count == 0
                        else f'You have {total_pending_count} pending transfers that will be cancelled.'
                    ),
                    'will_cancel_transfers': total_pending_count > 0,
                    'affected_amount_usd': total_pending_amount / 100
                }
            })

        except Exception as e:
            self.log_error('pending_transfers_check', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['post'])
    def cancel_all_pending_transfers(self, request):
        """Cancel all pending transfers for the user (as both sender and receiver)"""
        try:
            transfer_service = UserTransferService()
            
            # Get all pending transfers
            pending_as_sender = UserTransfer.objects.filter(
                sender=request.user,
                status__in=['pending', 'processing']
            )
            
            pending_as_receiver = UserTransfer.objects.filter(
                receiver=request.user,
                status__in=['pending', 'processing']
            )
            
            all_pending = list(pending_as_sender) + list(pending_as_receiver)
            
            if not all_pending:
                return Response({
                    'message': 'No pending transfers found',
                    'cancelled_count': 0,
                    'failed_count': 0
                })

            cancelled_transfers = []
            failed_cancellations = []

            # Cancel each transfer
            for transfer in all_pending:
                try:
                    success = transfer_service.cancel_pending_transfer(transfer)
                    if success:
                        cancelled_transfers.append({
                            'id': str(transfer.id),
                            'amount': transfer.amount,
                            'status': 'cancelled'
                        })
                    else:
                        failed_cancellations.append({
                            'id': str(transfer.id),
                            'amount': transfer.amount,
                            'reason': 'Could not cancel'
                        })
                except Exception as e:
                    failed_cancellations.append({
                        'id': str(transfer.id),
                        'amount': transfer.amount,
                        'reason': str(e)
                    })

            self.log_operation('all_pending_transfers_cancelled', request.user.id, {
                'total_processed': len(all_pending),
                'cancelled_count': len(cancelled_transfers),
                'failed_count': len(failed_cancellations)
            })

            return Response({
                'message': f'Processed {len(all_pending)} pending transfers',
                'cancelled_count': len(cancelled_transfers),
                'failed_count': len(failed_cancellations),
                'cancelled_transfers': cancelled_transfers,
                'failed_cancellations': failed_cancellations,
                'next_step': 'You can now safely deactivate your Connect account' if len(failed_cancellations) == 0 else 'Some transfers could not be cancelled'
            })

        except Exception as e:
            self.log_error('cancel_all_pending_transfers', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['delete'])
    def deactivate_connect_account(self, request):
        """Deactivate and delete user's Stripe Connect account"""
        try:
            # Check if user has a payment profile with Stripe account
            profile = UserPaymentProfile.objects.filter(user=request.user).first()
            if not profile or not profile.stripe_account_id:
                return Response({
                    'error': 'No Stripe Connect account found to deactivate'
                }, status=status.HTTP_404_NOT_FOUND)

            transfer_service = UserTransferService()
            result = transfer_service.deactivate_connect_account(request.user)

            self.log_operation('connect_account_deactivated', request.user.id, {
                'account_id': profile.stripe_account_id,
                'method': result['method'],
                'transfers_affected': result['transfers_affected'],
                'cancelled_transfers': len(result['cancelled_transfers']),
                'failed_cancellations': len(result['failed_cancellations'])
            })

            return Response({
                'success': True,
                'message': result['message'],
                'details': {
                    'method': result['method'],
                    'profile_updated': result['profile_updated'],
                    'transfers_affected': result['transfers_affected'],
                    'cancelled_transfers': result['cancelled_transfers'],
                    'failed_cancellations': result['failed_cancellations']
                },
                'summary': {
                    'account_deactivated': True,
                    'pending_transfers_handled': result['transfers_affected'] > 0,
                    'total_transfers_cancelled': len(result['cancelled_transfers']),
                    'transfers_marked_as_failed': len(result['failed_cancellations'])
                }
            })

        except Exception as e:
            self.log_error('connect_account_deactivation', e, request.user.id)
            return self.handle_billing_error(e)
    
    @action(detail=False, methods=['get'], permission_classes=[AllowAny])
    def public_donation_info(self, request):
        """Get public donation info for a user (no auth required)"""
        user_id = request.query_params.get('user_id')
        if not user_id:
            return Response({
                'error': 'user_id parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = get_object_or_404(CustomUser, id=user_id)
            profile = get_object_or_404(UserPaymentProfile, user=user)
            
            if not profile.can_receive_donations():
                return Response({
                    'accepts_donations': False,
                    'message': 'This user does not accept donations'
                })
            
            serializer = PublicDonationInfoSerializer(instance={
                'user_id': str(user.id),
                'user_name': user.get_full_name() or user.email.split('@')[0],
                'accepts_donations': profile.accept_donations,
                'donation_message': profile.donation_message,
                'minimum_donation': profile.minimum_donation,
                'suggested_donation_amounts': profile.suggested_donation_amounts,
                'minimum_donation_usd': profile.minimum_donation / 100,
                'suggested_amounts_usd': [amt / 100 for amt in profile.suggested_donation_amounts],
            })
            
            return Response(serializer.data)
            
        except (CustomUser.DoesNotExist, UserPaymentProfile.DoesNotExist):
            return Response({
                'error': 'User or payment profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error getting public donation info: {str(e)}")
            return Response({
                'error': 'Internal server error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)




class UserTransferViewSet(
    BaseViewSet,
    APIErrorHandlingMixin,
    LoggingMixin,
    PaginationMixin,
    viewsets.ModelViewSet
):
    """API endpoint for managing user transfers"""
    
    serializer_class = UserTransferSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserTransfer.objects.filter(
            Q(sender=self.request.user) | Q(receiver=self.request.user)
        ).order_by('-created_at')

    @action(detail=False, methods=['post'])
    def create_transfer(self, request):
        """Create a transfer to another user with enhanced validation"""
        serializer = CreateTransferSerializer(data=request.data)
        if serializer.is_valid():
            try:
                receiver_id = serializer.validated_data['receiver_id']
                amount = serializer.validated_data['amount']
                currency = serializer.validated_data.get('currency', 'usd')
                message = serializer.validated_data.get('message', '')
                transfer_type = serializer.validated_data.get('transfer_type', 'transfer')
                
                receiver = get_object_or_404(CustomUser, id=receiver_id)
                
                transfer_service = UserTransferService()
                transfer, client_secret = transfer_service.create_transfer(
                    sender=request.user,
                    receiver=receiver,
                    amount=amount,
                    currency=currency,
                    message=message,
                    transfer_type=transfer_type
                )
                
                response_serializer = UserTransferSerializer(transfer)
                response_data = response_serializer.data
                response_data['client_secret'] = client_secret
                
                self.log_operation('transfer_created', request.user.id, {
                    'transfer_id': str(transfer.id),
                    'receiver_id': str(receiver.id),
                    'amount': transfer.amount,
                    'transfer_type': transfer.transfer_type
                })
                
                return Response(response_data, status=status.HTTP_201_CREATED)
                
            except Exception as e:
                self.log_error('transfer_creation', e, request.user.id)
                return self.handle_billing_error(e)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def perform_create(self, serializer):
        # Keep original implementation for backward compatibility
        try:
            transfer_service = UserTransferService()
            transfer, client_secret = transfer_service.create_transfer(
                sender=self.request.user,
                receiver=serializer.validated_data['receiver'],
                amount=serializer.validated_data['amount'],
                currency=serializer.validated_data.get('currency', 'usd'),
                message=serializer.validated_data.get('message', ''),
                transfer_type=serializer.validated_data.get('transfer_type', 'transfer')
            )
            
            serializer.instance = transfer
            serializer.validated_data['client_secret'] = client_secret
            
            self.log_operation('transfer_created', self.request.user.id, {
                'transfer_id': str(transfer.id),
                'receiver_id': transfer.receiver.id,
                'amount': transfer.amount,
                'transfer_type': transfer.transfer_type
            })
            
        except Exception as e:
            self.log_error('transfer_creation', e, self.request.user.id)
            raise ValidationError(str(e))

    @action(detail=True, methods=['post'])
    def confirm_payment(self, request, pk=None):
        """Confirm a pending transfer payment"""
        try:
            transfer = self.get_object()
            if transfer.status != 'pending':
                raise BillingError('Transfer is not in pending status')

            transfer_service = UserTransferService()
            updated_transfer = transfer_service.confirm_transfer(transfer)
            
            self.log_operation('transfer_confirmed', request.user.id, {
                'transfer_id': str(transfer.id),
                'new_status': updated_transfer.status
            })
            
            return Response({'status': 'success'})
            
        except Exception as e:
            self.log_error('transfer_confirmation', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['get'])
    def sent_transfers(self, request):
        """Get all transfers sent by the user"""
        try:
            transfer_service = UserTransferService()
            transfers = transfer_service.get_sent_transfers(request.user)
            
            return self.paginate_response(transfers, UserTransferSerializer, request)
            
        except Exception as e:
            self.log_error('sent_transfers_retrieval', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['get'])
    def received_transfers(self, request):
        """Get all transfers received by the user"""
        try:
            transfer_service = UserTransferService()
            transfers = transfer_service.get_received_transfers(request.user)
            
            return self.paginate_response(transfers, UserTransferSerializer, request)
            
        except Exception as e:
            self.log_error('received_transfers_retrieval', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['get'])
    def transfer_history(self, request):
        """Get complete transfer history for the user"""
        try:
            transfer_service = UserTransferService()
            transfers = transfer_service.get_user_transfers(request.user)
            
            return self.paginate_response(transfers, UserTransferSerializer, request)
            
        except Exception as e:
            self.log_error('transfer_history_retrieval', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['post'])
    def create_donation(self, request):
        """Create a donation transfer"""
        try:
            data = request.data
            receiver_id = data.get('receiver_id')
            amount = data.get('amount')
            message = data.get('message', '')
            
            if not receiver_id or not amount:
                return Response({
                    'error': 'Receiver ID and amount are required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            from django.contrib.auth import get_user_model
            User = get_user_model()
            receiver = User.objects.get(id=receiver_id)
            
            transfer_service = UserTransferService()
            transfer, client_secret = transfer_service.create_donation(
                sender=request.user,
                receiver=receiver,
                amount=amount,
                message=message
            )
            
            self.log_operation('donation_created', request.user.id, {
                'transfer_id': str(transfer.id),
                'receiver_id': receiver.id,
                'amount': amount
            })
            
            serializer = UserTransferSerializer(transfer)
            response_data = serializer.data
            response_data['client_secret'] = client_secret
            
            return Response(response_data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            self.log_error('donation_creation', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['get'])
    def donations_sent(self, request):
        """Get all donations sent by the user"""
        try:
            transfer_service = UserTransferService()
            donations = transfer_service.get_user_transfers(request.user, transfer_type='donation')
            donations = donations.filter(sender=request.user)
            
            return self.paginate_response(donations, UserTransferSerializer, request)
            
        except Exception as e:
            self.log_error('donations_sent_retrieval', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['get'])
    def donations_received(self, request):
        """Get all donations received by the user"""
        try:
            transfer_service = UserTransferService()
            donations = transfer_service.get_user_transfers(request.user, transfer_type='donation')
            donations = donations.filter(receiver=request.user)
            
            return self.paginate_response(donations, UserTransferSerializer, request)
            
        except Exception as e:
            self.log_error('donations_received_retrieval', e, request.user.id)
            return self.handle_billing_error(e)
