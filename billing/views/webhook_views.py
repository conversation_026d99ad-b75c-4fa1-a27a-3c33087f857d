"""
Webhook handling views
"""
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST

from ..services import WebhookService
from ..exceptions import WebhookError, WebhookSignatureError

logger = logging.getLogger(__name__)


@csrf_exempt
@require_POST
def stripe_webhook(request):
    """Handle Stripe webhook events"""
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    
    logger.info("Received Stripe webhook request")
    logger.debug(f"Signature header: {sig_header}")
    logger.debug(f"Payload: {payload.decode('utf-8')}")
    
    try:
        webhook_service = WebhookService()

        # Use comprehensive webhook security verification
        from ..services.webhook_security_service import webhook_security

        try:
            # Get client IP for security checks
            client_ip = request.META.get('HTTP_X_FORWARDED_FOR')
            if client_ip:
                client_ip = client_ip.split(',')[0].strip()
            else:
                client_ip = request.META.get('REMOTE_ADDR')

            # Comprehensive security verification
            security_context = webhook_security.verify_webhook_security(
                payload=payload,
                signature=sig_header,
                timestamp=request.META.get('HTTP_X_TIMESTAMP'),
                source_ip=client_ip
            )

            # Parse the verified event
            event = webhook_service.verify_signature_secure(payload, sig_header)
            logger.info(f"Successfully verified webhook signature for event: {event.type}")
            logger.debug(f"Event data: {event.data}")
            logger.info(f"Security context: {security_context}")

        except WebhookSignatureError as e:
            logger.error(f"Webhook signature verification failed: {str(e)}")
            return JsonResponse({
                'error': 'Invalid signature'
            }, status=400)
        except WebhookError as e:
            logger.error(f"Webhook security check failed: {str(e)}")
            return JsonResponse({
                'error': 'Security check failed'
            }, status=400)
        
        # Handle specific events
        if event.type == 'checkout.session.completed':
            session = event.data.object
            
            # Log detailed session information for debugging
            logger.info(f"Processing checkout.session.completed event for session ID: {session.id}")
            logger.info(f"Session customer ID: {session.customer}")
            logger.info(f"Session customer details: {session.customer_details}")
            logger.info(f"Session metadata: {session.metadata}")
            
            # Check if this is a DNA analysis payment
            if session.metadata and session.metadata.get('payment_type') == 'dna_analysis':
                try:
                    logger.info(f"Processing DNA analysis payment for session {session.id}")
                    
                    # Handle DNA analysis payment using the webhook service
                    success = webhook_service.handle_checkout_completed(session)
                    
                    if success:
                        logger.info(f"Successfully processed DNA analysis payment for session {session.id}")
                        return JsonResponse({
                            'status': 'success'
                        })
                    else:
                        logger.error(f"Failed to process DNA analysis payment for session {session.id}")
                        return JsonResponse({
                            'error': 'Failed to process DNA analysis payment'
                        }, status=400)
                        
                except Exception as e:
                    logger.error(f"Error processing DNA analysis payment: {str(e)}")
                    return JsonResponse({
                        'error': f'Error processing DNA analysis payment: {str(e)}'
                    }, status=500)
        
        # Handle the event with the WebhookService
        try:
            success = webhook_service.handle_event(event)
            
            if success:
                logger.info(f"Successfully handled webhook event: {event.type}")
                return JsonResponse({'status': 'success'})
            else:
                logger.error(f"Failed to handle webhook event: {event.type}")
                return JsonResponse({
                    'error': 'Failed to handle webhook event'
                }, status=400)
        except WebhookError as e:
            logger.error(f"Webhook handling error: {str(e)}")
            return JsonResponse({
                'error': str(e)
            }, status=400)
            
    except Exception as e:
        logger.error(f"Webhook error: {str(e)}")
        return JsonResponse({
            'error': 'An unexpected error occurred'
        }, status=500)
