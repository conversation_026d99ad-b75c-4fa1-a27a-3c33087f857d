"""
Billing views package

This package contains all view classes organized by domain.
Views are split into logical groups for better maintainability.
"""

# Import all view classes for backward compatibility
from .checkout_views import (
    CreateCheckoutSessionView,
)

from .webhook_views import stripe_webhook

from .payment_views import (
    PaymentSuccessView,
    PaymentCancelView,
    create_subscription,
    cancel_subscription,
    list_subscriptions,
    create_payment_method,
    list_payment_methods,
    create_setup_intent,
)

from .enterprise_views import (
    list_platform_services,
)

from .dna_analysis_views import (
    DNAAnalysisPaymentHandler,
    DNAAnalysisPaymentHandler,
    check_dna_analysis_status
)

from .user_transfer_views import (
    UserPaymentProfileViewSet,
    UserTransferViewSet
)

from .access_views import (
    check_service_access,
    check_subscription_access,
    check_solution_access,
    list_user_services,
    list_enterprise_solutions,
    revoke_service_access,
    revoke_subscription_access,
    revoke_solution_access
)

from .billing_viewsets import (
    BillingServiceViewSet,
    ServicePaymentViewSet,
    SubscriptionViewSet,
    ServiceAccessViewSet,
    SubscriptionAccessViewSet,
    SolutionAccessViewSet
)

from .payment_history_views import (
    list_payment_history,
    get_payment_details
)

# Base view classes
from .base import BasePaymentView

__all__ = [
    # Base classes
    'BasePaymentView',
    
    # Checkout views
    'CreateCheckoutSessionView',
    
    # Webhook views
    'stripe_webhook',
    
    # Payment views
    'PaymentSuccessView',
    'PaymentCancelView',
    'create_subscription',
    'cancel_subscription',
    'list_subscriptions',
    'create_payment_method',
    'list_payment_methods',
    'create_setup_intent',
    
    # Enterprise views
    'list_platform_services',
    
    # DNA analysis views
    'DNAAnalysisPaymentHandler',
    'DNAAnalysisPaymentHandler',
    'check_dna_analysis_status',
    
    # User transfer views
    'UserPaymentProfileViewSet',
    'UserTransferViewSet',
    
    # Access views
    'check_service_access',
    'check_subscription_access',
    'check_solution_access',
    'list_user_services',
    'list_enterprise_solutions',
    'revoke_service_access',
    'revoke_subscription_access',
    'revoke_solution_access',
    
    # ViewSets
    'BillingServiceViewSet',
    'ServicePaymentViewSet',
    'SubscriptionViewSet',

    'ServiceAccessViewSet',
    'SubscriptionAccessViewSet',
    'SolutionAccessViewSet',
    
    # Payment history views
    'list_payment_history',
    'get_payment_details',
]
