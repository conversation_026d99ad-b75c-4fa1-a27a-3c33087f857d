"""
Enterprise-related views
"""
import logging
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required

from ..models import Product

logger = logging.getLogger(__name__)


@login_required
def list_platform_services(request):
    """List all available platform services (Service, SubscriptionPlan, Solution)"""
    try:
        # Get all active products
        products = Product.objects.filter(active=True).prefetch_related('prices')
        
        services = []
        for product in products:
            # Get the corresponding content model
            if product.product_type == 'service':
                content_model = product.service
            elif product.product_type == 'subscription':
                content_model = product.subscription_plan
            elif product.product_type == 'solution':
                content_model = product.solution
            else:
                continue
                
            if not content_model or not hasattr(content_model, 'is_active') or not content_model.is_active:
                continue
                
            # Get the price
            price = product.prices.filter(active=True).first()
            if not price:
                continue
                
            services.append({
                'id': str(product.id),
                'name': product.name,
                'description': product.description,
                'type': product.product_type,
                'price_id': price.stripe_price_id,
                'amount': price.unit_amount / 100,
                'currency': price.currency,
                'is_recurring': bool(price.recurring),
                'recurring_interval': price.recurring.get('interval') if price.recurring else None,
                'button_text': getattr(content_model, 'button_text', ''),
                'features': getattr(content_model, 'features', []),
            })
        
        return JsonResponse({
            'services': services
        })
        
    except Exception as e:
        logger.error(f"Error listing platform services: {str(e)}")
        return JsonResponse({
            'error': str(e)
        }, status=400)



