"""
DNA analysis payment and management views
"""
import json
import stripe
import logging
from django.conf import settings
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from datetime import timedelta
from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from analysis.services.base import DNAAnalysisService

from .base import BaseViewSet, APIErrorHandlingMixin, LoggingMixin
from ..services import CustomerService, PaymentService
from ..models import ServicePayment, ServiceAccess, Customer
from analysis.models.base import DNAAnalysis
from analysis.models.gs_file_manager import GSFileManager
from content_management.models import Service

logger = logging.getLogger(__name__)


class DNAAnalysisPaymentHandler(BaseViewSet, APIErrorHandlingMixin, LoggingMixin, viewsets.ViewSet):
    """ViewSet for billing operations including DNA analysis"""
    
    @action(detail=False, methods=['get'])
    def list_dna_analyses(self, request):
        """List all DNA analysis records that have been paid for by the user"""
        try:
            # Get all DNA analyses with completed payments for the authenticated user
            dna_analyses = DNAAnalysis.objects.filter(
                user=request.user,
                service_payments__status='completed'
            ).distinct().order_by('-created_at')

            # Format response data
            analyses_data = []
            for analysis in dna_analyses:
                # Get associated service payments
                service_payments = ServicePayment.objects.filter(
                    dna_analysis=analysis,
                    status='completed'
                ).select_related('service')
                
                # Get service access records
                service_access = ServiceAccess.objects.filter(
                    payment__dna_analysis=analysis,
                    status='active'
                ).select_related('service', 'payment')
                
                # Format service data
                services_data = []
                for payment in service_payments:
                    service = payment.service
                    access = next((a for a in service_access if a.service_id == service.id), None)
                    
                    service_data = {
                        'id': str(service.id),
                        'name': service.name,
                        'description': service.description,
                        'payment': {
                            'id': str(payment.id),
                            'amount': payment.amount / 100,  # Convert cents to dollars
                            'currency': payment.currency,
                            'status': payment.status,
                            'created_at': payment.created_at
                        },
                        'access': {
                            'id': str(access.id) if access else None,
                            'status': access.status if access else None,
                            'access_granted_at': access.access_granted_at if access else None,
                            'access_expires_at': access.access_expires_at if access else None,
                            'last_accessed_at': access.last_accessed_at if access else None
                        } if access else None
                    }
                    services_data.append(service_data)
                
                analysis_data = {
                    'id': str(analysis.id),
                    'status': analysis.status,
                    'created_at': analysis.created_at,
                    'updated_at': analysis.updated_at,
                    'parameters': analysis.parameters,
                    'notes': analysis.notes,
                    'is_paid': analysis.is_paid(),
                    'services': services_data,
                    'result_files': analysis.result_files,
                    'stripe_session_id': analysis.stripe_session_id
                }
                analyses_data.append(analysis_data)
            
            return Response({
                'count': len(analyses_data),
                'results': analyses_data
            })
            
        except Exception as e:
            self.log_error('list_dna_analyses', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['post'])
    def checkout_dna_services(self, request):
        """Create a checkout session for DNA services"""
        try:
            data = request.data
            services_data = data.get('services', [])  # List of services with their promo codes
            redirect_url = data.get('redirect_url')
            input_files = data.get('input_files', [])  # List of GSFileManager ids
            
            if not services_data:
                return Response({
                    'error': 'At least one service is required'
                }, status=400)
            if not redirect_url:
                return Response({
                    'error': 'Redirect URL is required'
                }, status=400)
            
            # Validate input_files (must be owned by user)
            gs_files = []
            if input_files:
                gs_files = list(GSFileManager.objects.filter(id__in=input_files, uploaded_file__user=request.user))
                if len(gs_files) != len(input_files):
                    return Response({'error': 'Some files are invalid or do not belong to you.'}, status=400)
            
            # Get or create customer
            customer = self.get_or_create_user_customer()
            
            # Get services and calculate discounted prices
            services = []
            total_amount = 0
            
            # Extract dna_codes and depth if present in any service_item
            dna_codes = None
            depth = None
            
            for service_item in services_data:
                service_id = service_item.get('service_id')
                promo_code = service_item.get('promo_code')
                
                # Check for dna_codes and depth
                if service_item.get('dna_codes') is not None:
                    dna_codes = service_item.get('dna_codes')
                if service_item.get('depth') is not None:
                    depth = service_item.get('depth')
                
                try:
                    service = Service.objects.get(id=service_id)
                    
                    # Calculate discounted price and get Stripe price ID
                    discounted_price, applied_promotion, _ = service.get_best_discounted_price(promo_code)
                    
                    # TODO: Replace with ProductSyncService method
                    from ..services import StripeService
                    price_id = StripeService.get_or_create_discounted_price(service, discounted_price, applied_promotion)
                    
                    services.append({
                        'service': service,
                        'price_id': price_id,
                        'unit_amount': int(discounted_price * 100)
                    })
                    total_amount += int(discounted_price * 100)
                except Service.DoesNotExist:
                    return Response({'error': f'Service {service_id} not found'}, status=404)
            
            # Create base session data
            session_data = {
                'customer': customer.stripe_customer_id,
                'payment_method_types': ['card'],
                'line_items': [{
                    'price': service['price_id'],
                    'quantity': 1
                } for service in services],
                'mode': 'payment',
                'success_url': redirect_url,
                'cancel_url': redirect_url,
                'metadata': {
                    'service_ids': json.dumps([str(s['service'].id) for s in services]),  # Convert UUIDs to strings
                    'input_files': json.dumps(input_files) if input_files else '',
                    'payment_type': 'dna_analysis'
                }
            }
            
            # Create checkout session
            try:
                session = stripe.checkout.Session.create(**session_data)
                
                # Create DNAAnalysis and assign input_files
                dna_analysis_kwargs = {
                    'user': request.user,
                    'parameters': {},
                    'status': 'pending',
                    'stripe_session_id': session.id
                }
                if dna_codes is not None:
                    dna_analysis_kwargs['dna_codes'] = dna_codes
                if depth is not None:
                    dna_analysis_kwargs['depth'] = depth
                
                dna_analysis = DNAAnalysis.objects.create(**dna_analysis_kwargs)
                
                if gs_files:
                    dna_analysis.input_files.set(gs_files)
                dna_analysis.save()
                
                self.log_operation('dna_checkout_session_created', request.user.id, {
                    'session_id': session.id,
                    'dna_analysis_id': str(dna_analysis.id),
                    'service_count': len(services)
                })
                
                return Response({
                    'session_id': session.id,
                    'url': session.url
                })
                
            except stripe.error.StripeError as e:
                self.log_error('stripe_checkout_creation', e, request.user.id)
                return Response({
                    'error': f'Payment processing error: {str(e)}'
                }, status=400)
                
        except Exception as e:
            self.log_error('dna_checkout_creation', e, request.user.id)
            return self.handle_billing_error(e)

    @action(detail=False, methods=['post'])
    def handle_dna_payment_success(self, session, customer, metadata):
        """Handle successful payment for DNA analysis services"""
        try:

            # Get user from customer
            user = Customer.objects.get(id=customer.id).user

            # Get DNA code and service IDs from metadata
            service_ids_json = session.metadata.get('service_ids', '[]')
            
            # Parse service IDs from JSON string
            try:
                service_ids = json.loads(service_ids_json)
                if not service_ids:
                    self.log_error('dna_payment_success_processing', f"No service IDs found in session metadata for session {session.id}", user.id)
                    return Response({
                        'error': 'No service IDs found in session metadata'
                    }, status=400)
            except json.JSONDecodeError:
                self.log_error('dna_payment_success_processing', f"Invalid service IDs JSON in session metadata for session {session.id}", user.id)
                return Response({
                    'error': 'Invalid service IDs format in session metadata'
                }, status=400)
            

            
            # Get all services first to validate and calculate amounts
            services = []
            total_service_price = 0
            for service_id in service_ids:
                try:
                    service = Service.objects.get(id=service_id)
                    services.append(service)
                    total_service_price += service.price
                except Service.DoesNotExist:
                    logger.error(f"Service {service_id} not found")
                    continue
            
            if not services:
                logger.error("No valid services found")
                return Response({
                    'error': 'No valid services found'
                }, status=400)

            # Fetching or creating DNA analysis record
            dna_analysis = None
            auto_start = False
            try:
                dna_analysis = DNAAnalysis.objects.get(stripe_session_id=session.id)
                auto_start = True
            except DNAAnalysis.DoesNotExist:
                # If not found, create a new one (should not normally happen)
                logger.warning(f"DNAAnalysis with stripe_session_id={session.id} not found, creating new record.")
                dna_analysis = DNAAnalysis.objects.create(
                    user=user,
                    status='pending',
                    stripe_session_id=session.id
                )
            
            # Create service payments and access records
            with transaction.atomic():
                # Create a single service payment for all services
                service_payment = ServicePayment.objects.create(
                    user=user,
                    service=services[0],  # Use first service as primary
                    services=','.join(str(service.id) for service in services),  # Concat service IDs
                    amount=session.amount_total,  # Total amount in cents
                    currency=session.currency,
                    status='completed',
                    stripe_payment_intent_id=session.payment_intent,
                    dna_analysis=dna_analysis,
                    metadata={
                        'service_ids': service_ids,  # Store all service IDs in metadata
                        'total_amount': session.amount_total,
                        'amount_per_service': session.amount_total // len(services)
                    }
                )
                self.log_operation('dna_payment_success_processing', user.id, {
                    'service_payment_id': service_payment.id,
                    'service_count': len(services)
                })
                
                # Create or update service access for each service
                for service in services:
                    try:
                        self.log_operation('dna_payment_success_processing', user.id, {
                            'service_id': service.id,
                            'dna_analysis_id': str(dna_analysis.id)
                        })
                        
                        # Check for existing service access
                        existing_access = ServiceAccess.objects.filter(
                            user=user,
                            service=service
                        ).order_by('-access_granted_at').first()
                        
                        if existing_access:
                            # If there's an existing access, update it
                            existing_access.payment = service_payment
                            existing_access.status = 'active'
                            existing_access.analysis_access_data = {
                                'dna_analysis_id': str(dna_analysis.id),
                            }
                            existing_access.save()
                            self.log_operation('dna_payment_success_processing', user.id, {
                                'service_id': service.id,
                                'dna_analysis_id': str(dna_analysis.id)
                            })
                        else:
                            # Create new service access if none exists
                            service_access = ServiceAccess.objects.create(
                                user=user,
                                service=service,
                                payment=service_payment,
                                status='active',
                                access_expires_at=None,  # Set to None for infinite access
                                analysis_access_data={
                                    'dna_analysis_id': str(dna_analysis.id),
                                }
                            )
                            self.log_operation('dna_payment_success_processing', user.id, {
                                'service_id': service.id,
                                'dna_analysis_id': str(dna_analysis.id)
                            })
                        
                    except Exception as e:
                        self.log_error('dna_payment_success_processing', f"Error processing service {service.id} for DNA analysis {dna_analysis.id}: {str(e)}", user.id)
                        raise  # Re-raise the exception to trigger transaction rollback
            
            # TODO: Trigger DNA analysis processing
            # This would be handled by your background task system
            self.log_operation('dna_payment_success_processing', user.id, {
                'dna_analysis_id': str(dna_analysis.id),
                'session_id': session.id
            })
            if auto_start:
                dna_analysis_service = DNAAnalysisService()
                dna_analysis_service.start_gsba_analysis(dna_analysis)
            
            self.log_operation('dna_payment_success_processed', user.id, {
                'dna_analysis_id': str(dna_analysis.id),
                'session_id': session.id
            })

            return Response({
                'status': 'success',
                'dna_analysis_id': str(dna_analysis.id)
            })

        except Exception as e:
            self.log_error('dna_payment_success_processing', e, user.id)
            return self.handle_billing_error(e)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_dna_analysis_status(request, dna_analysis_id):
    """Check the status of a DNA analysis and its associated services"""
    try:
        # Get DNA analysis
        dna_analysis = get_object_or_404(
            DNAAnalysis,
            id=dna_analysis_id,
            user=request.user
        )
        
        # Get associated service payments
        service_payments = ServicePayment.objects.filter(
            dna_analysis=dna_analysis,
            status='completed'
        ).select_related('service')
        
        # Get service access records
        service_access = ServiceAccess.objects.filter(
            payment__dna_analysis=dna_analysis,
            status='active'
        ).select_related('service', 'payment')
        
        # Format service data with payment and access details
        services_data = []
        for payment in service_payments:
            service = payment.service
            access = next((a for a in service_access if a.service_id == service.id), None)
            
            service_data = {
                'id': str(service.id),
                'name': service.name,
                'description': service.description,
                'payment': {
                    'id': str(payment.id),
                    'amount': payment.amount / 100,  # Convert cents to dollars
                    'currency': payment.currency,
                    'status': payment.status,
                    'created_at': payment.created_at
                },
                'access': {
                    'id': str(access.id) if access else None,
                    'status': access.status if access else None,
                    'access_granted_at': access.access_granted_at if access else None,
                    'access_expires_at': access.access_expires_at if access else None,
                    'last_accessed_at': access.last_accessed_at if access else None
                } if access else None
            }
            services_data.append(service_data)
        
        # Return DNA analysis data
        return Response({
            'id': str(dna_analysis.id),
            'status': dna_analysis.status,
            'created_at': dna_analysis.created_at,
            'updated_at': dna_analysis.updated_at,
            'parameters': dna_analysis.parameters,
            'notes': dna_analysis.notes,
            'is_paid': dna_analysis.is_paid(),
            'services': services_data,
            'result_files': dna_analysis.result_files,
            'stripe_session_id': dna_analysis.stripe_session_id
        })
        
    except Exception as e:
        logger.error(f"Error checking DNA analysis status: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
