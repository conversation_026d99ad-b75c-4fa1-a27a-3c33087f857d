"""
Unified payment response format utilities
"""
from rest_framework.response import Response
from rest_framework import status
from django.http import JsonResponse
import logging

logger = logging.getLogger(__name__)


class PaymentResponseFormat:
    """Unified payment response format for all payment operations"""
    
    @staticmethod
    def success(data=None, message="Success", http_status=status.HTTP_200_OK):
        """Standard success response format"""
        response_data = {
            'success': True,
            'message': message,
            'data': data or {}
        }
        return Response(response_data, status=http_status)
    
    @staticmethod
    def error(message="An error occurred", error_code=None, details=None, http_status=status.HTTP_400_BAD_REQUEST):
        """Standard error response format"""
        response_data = {
            'success': False,
            'message': message,
            'error_code': error_code,
            'details': details or {}
        }
        return Response(response_data, status=http_status)
    
    @staticmethod
    def checkout_session_success(session_id, session_url, payment_type="checkout", additional_data=None):
        """Standard checkout session success response"""
        data = {
            'session_id': session_id,
            'checkout_url': session_url,
            'payment_type': payment_type
        }
        if additional_data:
            data.update(additional_data)
            
        return PaymentResponseFormat.success(
            data=data,
            message="Checkout session created successfully"
        )
    
    @staticmethod
    def payment_success(payment_id, service_id=None, amount=None, additional_data=None):
        """Standard payment success response"""
        data = {
            'payment_id': payment_id,
            'status': 'completed'
        }
        if service_id:
            data['service_id'] = service_id
        if amount:
            data['amount'] = amount
        if additional_data:
            data.update(additional_data)
            
        return PaymentResponseFormat.success(
            data=data,
            message="Payment processed successfully"
        )
    
    @staticmethod
    def validation_error(message, field_errors=None):
        """Standard validation error response"""
        return PaymentResponseFormat.error(
            message=message,
            error_code="VALIDATION_ERROR",
            details={'field_errors': field_errors} if field_errors else {},
            http_status=status.HTTP_400_BAD_REQUEST
        )
    
    @staticmethod
    def payment_error(message, stripe_error_code=None):
        """Standard payment processing error response"""
        return PaymentResponseFormat.error(
            message=message,
            error_code="PAYMENT_ERROR",
            details={'stripe_error_code': stripe_error_code} if stripe_error_code else {},
            http_status=status.HTTP_402_PAYMENT_REQUIRED
        )
    
    @staticmethod
    def rate_limit_error():
        """Standard rate limit error response"""
        return PaymentResponseFormat.error(
            message="Too many requests. Please wait before trying again.",
            error_code="RATE_LIMIT_ERROR",
            http_status=status.HTTP_429_TOO_MANY_REQUESTS
        )
    
    @staticmethod
    def not_found_error(resource_type="Resource"):
        """Standard not found error response"""
        return PaymentResponseFormat.error(
            message=f"{resource_type} not found",
            error_code="NOT_FOUND_ERROR",
            http_status=status.HTTP_404_NOT_FOUND
        )
    
    @staticmethod
    def unauthorized_error():
        """Standard unauthorized error response"""
        return PaymentResponseFormat.error(
            message="Authentication required",
            error_code="UNAUTHORIZED_ERROR",
            http_status=status.HTTP_401_UNAUTHORIZED
        )
    
    @staticmethod
    def forbidden_error(message="Permission denied"):
        """Standard forbidden error response"""
        return PaymentResponseFormat.error(
            message=message,
            error_code="FORBIDDEN_ERROR",
            http_status=status.HTTP_403_FORBIDDEN
        )


class LegacyPaymentResponse:
    """Support for legacy JsonResponse format during transition"""
    
    @staticmethod
    def to_json_response(drf_response):
        """Convert DRF Response to JsonResponse for legacy compatibility"""
        if hasattr(drf_response, 'data'):
            return JsonResponse(drf_response.data, status=drf_response.status_code)
        return JsonResponse({'error': 'Invalid response format'}, status=500)


# Convenience functions for common use cases
def checkout_success(session_id, session_url, **kwargs):
    """Quick checkout success response"""
    return PaymentResponseFormat.checkout_session_success(session_id, session_url, **kwargs)

def payment_success(payment_id, **kwargs):
    """Quick payment success response"""
    return PaymentResponseFormat.payment_success(payment_id, **kwargs)

def validation_error(message, **kwargs):
    """Quick validation error response"""
    return PaymentResponseFormat.validation_error(message, **kwargs)

def payment_error(message, **kwargs):
    """Quick payment error response"""
    return PaymentResponseFormat.payment_error(message, **kwargs) 