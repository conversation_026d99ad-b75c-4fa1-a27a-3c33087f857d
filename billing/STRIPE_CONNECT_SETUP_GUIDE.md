# Stripe Connect Setup Guide - RAVID Healthcare AI Platform

## 🚀 Overview

Hướng dẫn hoàn chỉnh để setup Stripe Connect cho nền tảng Healthcare AI, bao gồm:
- User-to-user donations
- Healthcare provider payments  
- Telemedicine consultations
- Platform fee management

## 📋 Prerequisites

1. **Stripe Account** đ<PERSON> verified
2. **Test mode** environment
3. **Django project** đã cấu hình cơ bản Stripe

## 🔧 Step 1: Stripe Dashboard Setup

### 1.1 Kích hoạt Stripe Connect

```bash
1. Login vào Stripe Dashboard: https://dashboard.stripe.com/test
2. <PERSON>y<PERSON><PERSON> sang Test mode (toggle ở góc trái)
3. Sidebar → Connect → Get started
4. Chọn "Express accounts" 
5. Click "Continue"
```

### 1.2 Platform Settings

```
Platform name: RAVID Healthcare AI
Platform URL: https://ravid.cloud (hoặc domain của bạn)
Support email: <EMAIL>
Business type: Technology/Healthcare
Description: AI-powered healthcare platform enabling user donations and telemedicine
```

### 1.3 Express Account Configuration

```
Account capabilities:
✅ Card payments
✅ Transfers
✅ Connect account onboarding

Business model:
✅ Platform or marketplace
✅ Users can receive payments from other users
```

### 1.4 Webhook Endpoints

Thêm 2 webhook endpoints:

**Primary Webhook (existing):**
```
URL: https://yourdomain.com/api/billing/webhook/
Events: checkout.session.completed, payment_intent.succeeded, etc.
```

**Connect Webhook (new):**
```
URL: https://yourdomain.com/api/billing/webhook/connect/
Events: 
- account.updated
- account.application.deauthorized
- payment_intent.succeeded
- payment_intent.payment_failed
- transfer.created
- transfer.updated
```

## ⚙️ Step 2: Environment Variables

Thêm vào `.env` file:

```bash
# Existing Stripe settings
STRIPE_LIVE_MODE=False
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# New Connect settings
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_...connect_webhook_secret_here
STRIPE_APPLICATION_FEE_PERCENT=5.0
FRONTEND_URL=http://localhost:3000
```

## 💻 Step 3: Frontend Integration

### 3.1 Create Connect Account

```javascript
// User tạo Stripe Connect account
const response = await fetch('/api/billing/payment-profiles/create_connect_account/', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${userToken}`,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
if (data.account_link) {
  // Redirect user đến Stripe onboarding
  window.location.href = data.account_link;
}
```

### 3.2 Check Account Status

```javascript
// Kiểm tra trạng thái Connect account
const response = await fetch('/api/billing/payment-profiles/payment_status/', {
  headers: {
    'Authorization': `Bearer ${userToken}`
  }
});

const accountStatus = await response.json();
console.log('Can receive payments:', accountStatus.can_receive_payments);
console.log('Can receive donations:', accountStatus.can_receive_donations);
```

### 3.3 Update Donation Settings

```javascript
// Cập nhật cài đặt nhận donation
const donationSettings = {
  accept_donations: true,
  donation_message: "Support my healthcare research!",
  minimum_donation: 500, // $5.00 in cents
  suggested_donation_amounts: [500, 1000, 2500, 5000] // $5, $10, $25, $50
};

await fetch('/api/billing/payment-profiles/update_donation_settings/', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${userToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(donationSettings)
});
```

### 3.4 Create Transfer/Donation

```javascript
// Tạo transfer/donation cho user khác
const transferData = {
  receiver_id: 'user-uuid-here',
  amount: 1000, // $10.00 in cents
  currency: 'usd',
  message: 'Thank you for your healthcare advice!',
  transfer_type: 'donation'
};

const response = await fetch('/api/billing/transfers/create_transfer/', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${userToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(transferData)
});

const { client_secret } = await response.json();

// Confirm payment với Stripe.js
const stripe = Stripe(publishableKey);
const result = await stripe.confirmCardPayment(client_secret, {
  payment_method: {
    card: cardElement,
    billing_details: {
      name: 'Donor Name'
    }
  }
});
```

### 3.5 Public Donation Info

```javascript
// Lấy thông tin donation công khai của user
const response = await fetch(`/api/billing/payment-profiles/public_donation_info/?user_id=${userId}`);
const donationInfo = await response.json();

if (donationInfo.accepts_donations) {
  console.log('Minimum donation:', donationInfo.minimum_donation_usd);
  console.log('Suggested amounts:', donationInfo.suggested_amounts_usd);
  console.log('Donation message:', donationInfo.donation_message);
}
```

## 🧪 Step 4: Testing

### 4.1 Test Connect Account Creation

```bash
# Test API endpoint
curl -X POST http://localhost:8000/api/billing/payment-profiles/create_connect_account/ \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 4.2 Test Donation Flow

```bash
# 1. Create donation
curl -X POST http://localhost:8000/api/billing/transfers/create_transfer/ \
  -H "Authorization: Bearer SENDER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "receiver_id": "receiver-uuid",
    "amount": 1000,
    "transfer_type": "donation",
    "message": "Test donation"
  }'

# 2. Confirm với client_secret returned
```

### 4.3 Test Webhook

```bash
# Stripe CLI để test webhooks locally
stripe listen --forward-to localhost:8000/api/billing/webhook/connect/

# Trigger test events
stripe trigger account.updated
stripe trigger payment_intent.succeeded
```

## 📊 Step 5: Dashboard & Analytics

### 5.1 User Transfer History

```javascript
// Lấy lịch sử transfers
const transfers = await fetch('/api/billing/transfers/', {
  headers: { 'Authorization': `Bearer ${userToken}` }
});

const sentTransfers = await fetch('/api/billing/transfers/sent_transfers/', {
  headers: { 'Authorization': `Bearer ${userToken}` }
});

const receivedTransfers = await fetch('/api/billing/transfers/received_transfers/', {
  headers: { 'Authorization': `Bearer ${userToken}` }
});
```

### 5.2 Platform Analytics

```python
# Django admin hoặc custom views
from billing.models.customer import UserPaymentProfile
from billing.models.payment import UserTransfer

# Total platform revenue from fees
total_fees = UserTransfer.objects.filter(
    status='completed'
).aggregate(total=models.Sum('platform_fee_amount'))

# Active donation receivers
active_receivers = UserPaymentProfile.objects.filter(
    accept_donations=True,
    is_verified=True
).count()

# Top donation receivers
top_receivers = UserTransfer.objects.filter(
    status='completed',
    transfer_type='donation'
).values('receiver__email').annotate(
    total_received=models.Sum('amount')
).order_by('-total_received')[:10]
```

## 🔐 Step 6: Security & Compliance

### 6.1 Webhook Security

```python
# Verify webhook signatures trong production
STRIPE_CONNECT_WEBHOOK_SECRET = 'whsec_real_secret_here'

# Rate limiting
STRIPE_WEBHOOK_RETRY_ATTEMPTS = 3
```

### 6.2 User Verification

```python
# Require verification cho payment receiving
def can_receive_payments(user):
    profile = user.payment_profile
    return (
        profile.is_verified and 
        profile.charges_enabled and 
        profile.details_submitted
    )
```

### 6.3 Platform Fee Management

```python
# Configure platform fees
from billing.models.payment import PlatformFee

PlatformFee.objects.create(
    percentage=5.0,  # 5% platform fee
    active=True,
    metadata={
        'applies_to': ['donation', 'telemedicine'],
        'description': 'Standard platform fee'
    }
)
```

## 📈 Step 7: Going Live

### 7.1 Switch to Live Mode

```bash
# Update environment variables
STRIPE_LIVE_MODE=True
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_live_...
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_live_connect_...
```

### 7.2 Production Checklist

- [ ] ✅ Platform review approved by Stripe
- [ ] ✅ Live webhook endpoints configured
- [ ] ✅ SSL certificates installed
- [ ] ✅ Terms of Service updated for platform fees
- [ ] ✅ User verification process documented
- [ ] ✅ Customer support procedures established
- [ ] ✅ Monitoring & alerting configured

## 🔗 API Endpoints Summary

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/billing/payment-profiles/create_connect_account/` | Tạo Stripe Connect account |
| GET | `/api/billing/payment-profiles/payment_status/` | Kiểm tra trạng thái account |
| POST | `/api/billing/payment-profiles/update_donation_settings/` | Cập nhật cài đặt donation |
| POST | `/api/billing/transfers/create_transfer/` | Tạo transfer/donation |
| GET | `/api/billing/transfers/` | Lịch sử transfers |
| GET | `/api/billing/transfers/sent_transfers/` | Transfers đã gửi |
| GET | `/api/billing/transfers/received_transfers/` | Transfers đã nhận |
| GET | `/api/billing/payment-profiles/public_donation_info/?user_id=` | Thông tin donation công khai |
| POST | `/api/billing/doctor-payments/setup_payment_account/` | Setup account cho doctors |

## 🆘 Troubleshooting

### Common Issues

1. **Account onboarding fails**
   - Check FRONTEND_URL setting
   - Verify redirect URLs in Stripe dashboard

2. **Webhooks not working**
   - Check webhook secret configuration
   - Verify endpoint URLs
   - Test with Stripe CLI

3. **Transfers failing**
   - Ensure receiver has verified Connect account
   - Check platform fee configuration
   - Verify payment methods

### Debug Commands

```bash
# Check Django logs
docker compose -f docker-compose-dev.yaml logs --tail 50 web

# Test webhook locally
stripe listen --forward-to localhost:8000/api/billing/webhook/connect/

# Verify Stripe API connection
python manage.py shell
>>> import stripe
>>> stripe.api_key = "sk_test_..."
>>> stripe.Account.list(limit=3)
```

## 📞 Support

- **Stripe Documentation**: https://stripe.com/docs/connect
- **Platform Issues**: Tạo GitHub issue
- **Emergency**: Contact platform team

---

**🎉 Congratulations!** Stripe Connect đã được setup hoàn chỉnh cho RAVID Healthcare AI Platform. 