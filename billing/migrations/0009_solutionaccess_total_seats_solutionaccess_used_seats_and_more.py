# Generated by Django 5.0.9 on 2025-05-08 14:28

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('billing', '0008_userpaymentprofile_usertransfer'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='solutionaccess',
            name='total_seats',
            field=models.IntegerField(default=1, help_text='Total number of user seats purchased'),
        ),
        migrations.AddField(
            model_name='solutionaccess',
            name='used_seats',
            field=models.IntegerField(default=0, help_text='Number of seats currently in use'),
        ),
        migrations.CreateModel(
            name='SolutionUsage',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metric_type', models.CharField(choices=[('ai_tokens', 'AI Tokens'), ('storage', 'Storage'), ('api_calls', 'API Calls')], max_length=50)),
                ('usage_amount', models.DecimalField(decimal_places=2, help_text='Amount of usage (tokens, bytes, calls)', max_digits=20)),
                ('usage_date', models.DateField()),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('solution_access', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_metrics', to='billing.solutionaccess')),
            ],
            options={
                'indexes': [models.Index(fields=['solution_access', 'metric_type', 'usage_date'], name='billing_sol_solutio_f8b0cb_idx')],
            },
        ),
        migrations.CreateModel(
            name='SolutionUsageLimit',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metric_type', models.CharField(choices=[('ai_tokens', 'AI Tokens'), ('storage', 'Storage'), ('api_calls', 'API Calls')], max_length=50)),
                ('limit_amount', models.DecimalField(decimal_places=2, help_text='Usage limit amount', max_digits=20)),
                ('reset_period', models.CharField(choices=[('daily', 'Daily'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], max_length=50)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('solution_access', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_limits', to='billing.solutionaccess')),
            ],
            options={
                'indexes': [models.Index(fields=['solution_access', 'metric_type'], name='billing_sol_solutio_74edd6_idx')],
                'unique_together': {('solution_access', 'metric_type')},
            },
        ),
        migrations.CreateModel(
            name='SolutionUserAccess',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('revoked', 'Revoked'), ('expired', 'Expired')], default='active', max_length=50)),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('revoked_at', models.DateTimeField(blank=True, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('solution_access', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_access', to='billing.solutionaccess')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='solution_access', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['solution_access', 'user'], name='billing_sol_solutio_3ecbf5_idx'), models.Index(fields=['status'], name='billing_sol_status_f5cc28_idx')],
                'unique_together': {('solution_access', 'user')},
            },
        ),
    ]
