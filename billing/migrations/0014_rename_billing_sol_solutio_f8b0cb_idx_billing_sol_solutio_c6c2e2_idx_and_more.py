# Generated by Django 5.0.9 on 2025-06-01 04:12

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('analysis', '0009_alter_dnaanalysis_status'),
        ('billing', '0013_servicepayment_services'),
        ('content_management', '0016_alter_servicepromotion_promotion_type'),
        ('enterprise', '0008_rename_unique_identifier_to_euid'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='solutionusage',
            new_name='billing_sol_solutio_c6c2e2_idx',
            old_name='billing_sol_solutio_f8b0cb_idx',
        ),
        migrations.RenameIndex(
            model_name='solutionusagelimit',
            new_name='billing_sol_solutio_3debd7_idx',
            old_name='billing_sol_solutio_74edd6_idx',
        ),
        migrations.RenameIndex(
            model_name='solutionuseraccess',
            new_name='billing_sol_solutio_7ceb98_idx',
            old_name='billing_sol_solutio_3ecbf5_idx',
        ),
        migrations.RenameIndex(
            model_name='solutionuseraccess',
            new_name='billing_sol_status_706aa9_idx',
            old_name='billing_sol_status_f5cc28_idx',
        ),
        migrations.AddField(
            model_name='enterprisepayment',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enterprisepayment',
            name='currency',
            field=models.CharField(default='usd', max_length=3),
        ),
        migrations.AddField(
            model_name='enterprisepayment',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enterprisepayment',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='enterpriseservice',
            name='category',
            field=models.CharField(blank=True, help_text='Service category for organization', max_length=100),
        ),
        migrations.AddField(
            model_name='enterpriseservice',
            name='max_duration_hours',
            field=models.IntegerField(blank=True, help_text='Maximum service duration in hours', null=True),
        ),
        migrations.AddField(
            model_name='enterpriseservice',
            name='requires_approval',
            field=models.BooleanField(default=False, help_text='Whether service requires enterprise approval before purchase'),
        ),
        migrations.AddField(
            model_name='platformfee',
            name='applies_to_donations',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='platformfee',
            name='applies_to_enterprise_payments',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='platformfee',
            name='applies_to_user_transfers',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='platformfee',
            name='maximum_fee',
            field=models.IntegerField(blank=True, help_text='Maximum fee amount in cents (null for no limit)', null=True),
        ),
        migrations.AddField(
            model_name='platformfee',
            name='minimum_fee',
            field=models.IntegerField(default=0, help_text='Minimum fee amount in cents'),
        ),
        migrations.AddField(
            model_name='platformfee',
            name='name',
            field=models.CharField(default='Default Platform Fee', help_text='Descriptive name for this fee structure', max_length=255),
        ),
        migrations.AddField(
            model_name='platformfee',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending'), ('expired', 'Expired'), ('canceled', 'Canceled'), ('failed', 'Failed')], default='pending', max_length=50),
        ),
        migrations.AddField(
            model_name='platformfee',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='price',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending'), ('expired', 'Expired'), ('canceled', 'Canceled'), ('failed', 'Failed')], default='pending', max_length=50),
        ),
        migrations.AddField(
            model_name='price',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='product',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending'), ('expired', 'Expired'), ('canceled', 'Canceled'), ('failed', 'Failed')], default='pending', max_length=50),
        ),
        migrations.AddField(
            model_name='product',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='serviceaccess',
            name='last_usage_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='serviceaccess',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='serviceaccess',
            name='usage_count',
            field=models.IntegerField(default=0, help_text='Number of times used'),
        ),
        migrations.AddField(
            model_name='servicepayment',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='servicepayment',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='servicepayment',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='solutionaccess',
            name='access_expires_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='solutionaccess',
            name='last_accessed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='solutionaccess',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='solutionusage',
            name='source',
            field=models.CharField(blank=True, help_text="Source of the usage (e.g., 'api', 'web_interface', 'batch_job')", max_length=100),
        ),
        migrations.AddField(
            model_name='solutionusage',
            name='user',
            field=models.ForeignKey(blank=True, help_text='User who generated this usage (if applicable)', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='solutionusagelimit',
            name='enforce_limit',
            field=models.BooleanField(default=True, help_text='Whether to enforce this limit (block usage when exceeded)'),
        ),
        migrations.AddField(
            model_name='solutionusagelimit',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending'), ('expired', 'Expired'), ('canceled', 'Canceled'), ('failed', 'Failed')], default='pending', max_length=50),
        ),
        migrations.AddField(
            model_name='solutionusagelimit',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='solutionusagelimit',
            name='warning_threshold',
            field=models.DecimalField(decimal_places=2, default=Decimal('80.00'), help_text='Percentage threshold for usage warnings (e.g., 80.00 for 80%)', max_digits=5),
        ),
        migrations.AddField(
            model_name='solutionuseraccess',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='subscriptionaccess',
            name='access_expires_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='subscriptionaccess',
            name='last_accessed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='subscriptionaccess',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='userpaymentprofile',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userpaymentprofile',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AddField(
            model_name='userpaymentprofile',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userpaymentprofile',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending'), ('expired', 'Expired'), ('canceled', 'Canceled'), ('failed', 'Failed')], default='pending', max_length=50),
        ),
        migrations.AddField(
            model_name='userpaymentprofile',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='usertransfer',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='usertransfer',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='usertransfer',
            name='status_changed_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='customer',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='enterpriseaccount',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='enterpriseaccount',
            name='requirements',
            field=models.JSONField(blank=True, default=dict, help_text='Stripe account requirements and verification status'),
        ),
        migrations.AlterField(
            model_name='enterprisepayment',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='enterprisepayment',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending'), ('expired', 'Expired'), ('canceled', 'Canceled'), ('failed', 'Failed')], default='pending', max_length=50),
        ),
        migrations.AlterField(
            model_name='enterpriseservice',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='platformfee',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='platformfee',
            name='percentage',
            field=models.DecimalField(decimal_places=2, help_text='Platform fee percentage (e.g., 2.50 for 2.5%)', max_digits=5),
        ),
        migrations.AlterField(
            model_name='price',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='price',
            name='recurring',
            field=models.JSONField(blank=True, help_text='Recurring billing configuration', null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='serviceaccess',
            name='last_accessed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='serviceaccess',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='serviceaccess',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('expired', 'Expired'), ('revoked', 'Revoked'), ('suspended', 'Suspended')], default='active', max_length=50),
        ),
        migrations.AlterField(
            model_name='servicepayment',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='servicepayment',
            name='services',
            field=models.JSONField(blank=True, default=list, help_text='List of service IDs for bulk payments', null=True),
        ),
        migrations.AlterField(
            model_name='servicepayment',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded'), ('canceled', 'Canceled')], default='pending', max_length=50),
        ),
        migrations.AlterField(
            model_name='solutionaccess',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='solutionaccess',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('past_due', 'Past Due'), ('canceled', 'Canceled'), ('expired', 'Expired'), ('suspended', 'Suspended')], default='active', max_length=50),
        ),
        migrations.AlterField(
            model_name='solutionusage',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='solutionusage',
            name='metric_type',
            field=models.CharField(choices=[('ai_tokens', 'AI Tokens'), ('storage', 'Storage (bytes)'), ('api_calls', 'API Calls'), ('users', 'Active Users'), ('analyses', 'Analyses Performed'), ('uploads', 'File Uploads'), ('downloads', 'File Downloads')], max_length=50),
        ),
        migrations.AlterField(
            model_name='solutionusage',
            name='usage_amount',
            field=models.DecimalField(decimal_places=2, help_text='Amount of usage (tokens, bytes, calls, etc.)', max_digits=20),
        ),
        migrations.AlterField(
            model_name='solutionusagelimit',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='solutionusagelimit',
            name='metric_type',
            field=models.CharField(choices=[('ai_tokens', 'AI Tokens'), ('storage', 'Storage (bytes)'), ('api_calls', 'API Calls'), ('users', 'Active Users'), ('analyses', 'Analyses Performed'), ('uploads', 'File Uploads'), ('downloads', 'File Downloads')], max_length=50),
        ),
        migrations.AlterField(
            model_name='solutionuseraccess',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='solutionuseraccess',
            name='role',
            field=models.CharField(choices=[('owner', 'Owner'), ('admin', 'Administrator'), ('member', 'Member'), ('viewer', 'Viewer')], default='member', max_length=20),
        ),
        migrations.AlterField(
            model_name='solutionuseraccess',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('revoked', 'Revoked'), ('expired', 'Expired'), ('suspended', 'Suspended')], default='active', max_length=20),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('past_due', 'Past Due'), ('unpaid', 'Unpaid'), ('canceled', 'Canceled'), ('incomplete', 'Incomplete'), ('incomplete_expired', 'Incomplete Expired'), ('trialing', 'Trialing'), ('paused', 'Paused')], default='incomplete', max_length=50),
        ),
        migrations.AlterField(
            model_name='subscriptionaccess',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='subscriptionaccess',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('past_due', 'Past Due'), ('canceled', 'Canceled'), ('expired', 'Expired'), ('suspended', 'Suspended')], default='active', max_length=50),
        ),
        migrations.AlterField(
            model_name='usertransfer',
            name='metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional metadata for this record'),
        ),
        migrations.AlterField(
            model_name='usertransfer',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded'), ('canceled', 'Canceled')], default='pending', max_length=50),
        ),
        migrations.AlterUniqueTogether(
            name='serviceaccess',
            unique_together={('user', 'service', 'payment')},
        ),
        migrations.AlterUniqueTogether(
            name='solutionaccess',
            unique_together={('enterprise', 'solution')},
        ),
        migrations.AlterUniqueTogether(
            name='solutionusage',
            unique_together={('solution_access', 'metric_type', 'usage_date', 'source', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='subscriptionaccess',
            unique_together={('user', 'subscription')},
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['stripe_customer_id'], name='billing_cus_stripe__a3af1c_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['email'], name='billing_cus_email_61ea81_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['user'], name='billing_cus_user_id_b846e5_idx'),
        ),
        migrations.AddIndex(
            model_name='enterpriseaccount',
            index=models.Index(fields=['stripe_account_id'], name='billing_ent_stripe__8ad175_idx'),
        ),
        migrations.AddIndex(
            model_name='enterpriseaccount',
            index=models.Index(fields=['enterprise'], name='billing_ent_enterpr_dab2ae_idx'),
        ),
        migrations.AddIndex(
            model_name='enterpriseaccount',
            index=models.Index(fields=['charges_enabled'], name='billing_ent_charges_d2e7df_idx'),
        ),
        migrations.AddIndex(
            model_name='enterpriseaccount',
            index=models.Index(fields=['payouts_enabled'], name='billing_ent_payouts_4a1265_idx'),
        ),
        migrations.AddIndex(
            model_name='enterpriseaccount',
            index=models.Index(fields=['details_submitted'], name='billing_ent_details_af5fee_idx'),
        ),
        migrations.AddIndex(
            model_name='enterprisepayment',
            index=models.Index(fields=['stripe_payment_intent_id'], name='billing_ent_stripe__cc4cda_idx'),
        ),
        migrations.AddIndex(
            model_name='enterprisepayment',
            index=models.Index(fields=['customer'], name='billing_ent_custome_a860f7_idx'),
        ),
        migrations.AddIndex(
            model_name='enterprisepayment',
            index=models.Index(fields=['enterprise'], name='billing_ent_enterpr_e9d6ab_idx'),
        ),
        migrations.AddIndex(
            model_name='enterprisepayment',
            index=models.Index(fields=['status'], name='billing_ent_status_723667_idx'),
        ),
        migrations.AddIndex(
            model_name='enterprisepayment',
            index=models.Index(fields=['created_at'], name='billing_ent_created_546795_idx'),
        ),
        migrations.AddIndex(
            model_name='enterpriseservice',
            index=models.Index(fields=['stripe_price_id'], name='billing_ent_stripe__1e1567_idx'),
        ),
        migrations.AddIndex(
            model_name='enterpriseservice',
            index=models.Index(fields=['enterprise'], name='billing_ent_enterpr_59ffbf_idx'),
        ),
        migrations.AddIndex(
            model_name='enterpriseservice',
            index=models.Index(fields=['active'], name='billing_ent_active_a74fc0_idx'),
        ),
        migrations.AddIndex(
            model_name='enterpriseservice',
            index=models.Index(fields=['category'], name='billing_ent_categor_ec2542_idx'),
        ),
        migrations.AddIndex(
            model_name='enterpriseservice',
            index=models.Index(fields=['requires_approval'], name='billing_ent_require_41eae6_idx'),
        ),
        migrations.AddIndex(
            model_name='platformfee',
            index=models.Index(fields=['active'], name='billing_pla_active_9f9021_idx'),
        ),
        migrations.AddIndex(
            model_name='platformfee',
            index=models.Index(fields=['percentage'], name='billing_pla_percent_277507_idx'),
        ),
        migrations.AddIndex(
            model_name='price',
            index=models.Index(fields=['stripe_price_id'], name='billing_pri_stripe__2507a7_idx'),
        ),
        migrations.AddIndex(
            model_name='price',
            index=models.Index(fields=['product'], name='billing_pri_product_39d91e_idx'),
        ),
        migrations.AddIndex(
            model_name='price',
            index=models.Index(fields=['active'], name='billing_pri_active_8d1ffb_idx'),
        ),
        migrations.AddIndex(
            model_name='price',
            index=models.Index(fields=['currency'], name='billing_pri_currenc_8116ba_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['stripe_product_id'], name='billing_pro_stripe__6717be_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['product_type'], name='billing_pro_product_81954c_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['active'], name='billing_pro_active_747cf6_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['content_id'], name='billing_pro_content_343037_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceaccess',
            index=models.Index(fields=['user'], name='billing_ser_user_id_ea37c8_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceaccess',
            index=models.Index(fields=['service'], name='billing_ser_service_c2f522_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceaccess',
            index=models.Index(fields=['payment'], name='billing_ser_payment_e34111_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceaccess',
            index=models.Index(fields=['status'], name='billing_ser_status_066c19_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceaccess',
            index=models.Index(fields=['access_granted_at'], name='billing_ser_access__13e331_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceaccess',
            index=models.Index(fields=['access_expires_at'], name='billing_ser_access__a7c34b_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceaccess',
            index=models.Index(fields=['last_accessed_at'], name='billing_ser_last_ac_4d1d8f_idx'),
        ),
        migrations.AddIndex(
            model_name='servicepayment',
            index=models.Index(fields=['stripe_payment_intent_id'], name='billing_ser_stripe__ee9707_idx'),
        ),
        migrations.AddIndex(
            model_name='servicepayment',
            index=models.Index(fields=['user'], name='billing_ser_user_id_5be446_idx'),
        ),
        migrations.AddIndex(
            model_name='servicepayment',
            index=models.Index(fields=['service'], name='billing_ser_service_ddbb7c_idx'),
        ),
        migrations.AddIndex(
            model_name='servicepayment',
            index=models.Index(fields=['status'], name='billing_ser_status_182f4e_idx'),
        ),
        migrations.AddIndex(
            model_name='servicepayment',
            index=models.Index(fields=['created_at'], name='billing_ser_created_27cb18_idx'),
        ),
        migrations.AddIndex(
            model_name='servicepayment',
            index=models.Index(fields=['dna_analysis'], name='billing_ser_dna_ana_121eb2_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionaccess',
            index=models.Index(fields=['enterprise'], name='billing_sol_enterpr_8a9a51_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionaccess',
            index=models.Index(fields=['solution'], name='billing_sol_solutio_669d4f_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionaccess',
            index=models.Index(fields=['subscription'], name='billing_sol_subscri_cff9db_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionaccess',
            index=models.Index(fields=['status'], name='billing_sol_status_d4d60d_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionaccess',
            index=models.Index(fields=['total_seats'], name='billing_sol_total_s_63fe01_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionaccess',
            index=models.Index(fields=['used_seats'], name='billing_sol_used_se_569cba_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionusage',
            index=models.Index(fields=['usage_date'], name='billing_sol_usage_d_20fb42_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionusage',
            index=models.Index(fields=['metric_type'], name='billing_sol_metric__52a98f_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionusage',
            index=models.Index(fields=['user'], name='billing_sol_user_id_e38462_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionusagelimit',
            index=models.Index(fields=['enforce_limit'], name='billing_sol_enforce_e67647_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionuseraccess',
            index=models.Index(fields=['role'], name='billing_sol_role_fb04d6_idx'),
        ),
        migrations.AddIndex(
            model_name='solutionuseraccess',
            index=models.Index(fields=['assigned_at'], name='billing_sol_assigne_4de0f0_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['stripe_subscription_id'], name='billing_sub_stripe__abc269_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['customer'], name='billing_sub_custome_42f265_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['price'], name='billing_sub_price_i_8d5232_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['status'], name='billing_sub_status_948046_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['current_period_end'], name='billing_sub_current_c59a21_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['cancel_at_period_end'], name='billing_sub_cancel__08dab5_idx'),
        ),
        migrations.AddIndex(
            model_name='subscriptionaccess',
            index=models.Index(fields=['user'], name='billing_sub_user_id_8ef472_idx'),
        ),
        migrations.AddIndex(
            model_name='subscriptionaccess',
            index=models.Index(fields=['subscription'], name='billing_sub_subscri_079646_idx'),
        ),
        migrations.AddIndex(
            model_name='subscriptionaccess',
            index=models.Index(fields=['status'], name='billing_sub_status_f77bb7_idx'),
        ),
        migrations.AddIndex(
            model_name='subscriptionaccess',
            index=models.Index(fields=['access_granted_at'], name='billing_sub_access__3b53d3_idx'),
        ),
        migrations.AddIndex(
            model_name='subscriptionaccess',
            index=models.Index(fields=['access_expires_at'], name='billing_sub_access__1f6068_idx'),
        ),
        migrations.AddIndex(
            model_name='userpaymentprofile',
            index=models.Index(fields=['stripe_account_id'], name='billing_use_stripe__5c908f_idx'),
        ),
        migrations.AddIndex(
            model_name='userpaymentprofile',
            index=models.Index(fields=['user'], name='billing_use_user_id_16f8b4_idx'),
        ),
        migrations.AddIndex(
            model_name='userpaymentprofile',
            index=models.Index(fields=['is_verified'], name='billing_use_is_veri_bcbf7c_idx'),
        ),
        migrations.AddIndex(
            model_name='userpaymentprofile',
            index=models.Index(fields=['accept_donations'], name='billing_use_accept__d208a5_idx'),
        ),
        migrations.AddIndex(
            model_name='usertransfer',
            index=models.Index(fields=['stripe_payment_intent_id'], name='billing_use_stripe__a11652_idx'),
        ),
        migrations.AddIndex(
            model_name='usertransfer',
            index=models.Index(fields=['sender'], name='billing_use_sender__ea962c_idx'),
        ),
        migrations.AddIndex(
            model_name='usertransfer',
            index=models.Index(fields=['receiver'], name='billing_use_receive_809267_idx'),
        ),
        migrations.AddIndex(
            model_name='usertransfer',
            index=models.Index(fields=['transfer_type'], name='billing_use_transfe_69d57b_idx'),
        ),
        migrations.AddIndex(
            model_name='usertransfer',
            index=models.Index(fields=['status'], name='billing_use_status_7b5c58_idx'),
        ),
        migrations.AddIndex(
            model_name='usertransfer',
            index=models.Index(fields=['created_at'], name='billing_use_created_a8c0b8_idx'),
        ),
        migrations.AlterModelTable(
            name='customer',
            table='billing_customer',
        ),
        migrations.AlterModelTable(
            name='enterpriseaccount',
            table='billing_enterprise_account',
        ),
        migrations.AlterModelTable(
            name='enterprisepayment',
            table='billing_enterprise_payment',
        ),
        migrations.AlterModelTable(
            name='enterpriseservice',
            table='billing_enterprise_service',
        ),
        migrations.AlterModelTable(
            name='platformfee',
            table='billing_platform_fee',
        ),
        migrations.AlterModelTable(
            name='price',
            table='billing_price',
        ),
        migrations.AlterModelTable(
            name='product',
            table='billing_product',
        ),
        migrations.AlterModelTable(
            name='serviceaccess',
            table='billing_service_access',
        ),
        migrations.AlterModelTable(
            name='servicepayment',
            table='billing_service_payment',
        ),
        migrations.AlterModelTable(
            name='solutionaccess',
            table='billing_solution_access',
        ),
        migrations.AlterModelTable(
            name='solutionusage',
            table='billing_solution_usage',
        ),
        migrations.AlterModelTable(
            name='solutionusagelimit',
            table='billing_solution_usage_limit',
        ),
        migrations.AlterModelTable(
            name='solutionuseraccess',
            table='billing_solution_user_access',
        ),
        migrations.AlterModelTable(
            name='subscription',
            table='billing_subscription',
        ),
        migrations.AlterModelTable(
            name='subscriptionaccess',
            table='billing_subscription_access',
        ),
        migrations.AlterModelTable(
            name='userpaymentprofile',
            table='billing_user_payment_profile',
        ),
        migrations.AlterModelTable(
            name='usertransfer',
            table='billing_user_transfer',
        ),
    ]
