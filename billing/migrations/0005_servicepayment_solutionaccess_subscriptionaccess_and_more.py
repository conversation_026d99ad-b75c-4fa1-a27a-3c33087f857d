# Generated by Django 5.0.9 on 2025-04-13 09:24

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('billing', '0004_product_content_id'),
        ('content_management', '0005_solution_button_text_subscriptionplan_button_text'),
        ('enterprise', '0005_alter_enterprise_user'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ServicePayment',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('amount', models.IntegerField(help_text='Amount in cents')),
                ('currency', models.CharField(default='usd', max_length=3)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded')], max_length=50)),
                ('stripe_payment_intent_id', models.CharField(max_length=255, unique=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='content_management.service')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_payments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SolutionAccess',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('past_due', 'Past Due'), ('canceled', 'Canceled'), ('expired', 'Expired')], max_length=50)),
                ('access_granted_at', models.DateTimeField(auto_now_add=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('enterprise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='solution_access', to='enterprise.enterprise')),
                ('solution', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enterprise_access', to='content_management.solution')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='solution_access', to='billing.subscription')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SubscriptionAccess',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('past_due', 'Past Due'), ('canceled', 'Canceled'), ('expired', 'Expired')], max_length=50)),
                ('access_granted_at', models.DateTimeField(auto_now_add=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='access_records', to='billing.subscription')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscription_access', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ServiceAccess',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('expired', 'Expired'), ('revoked', 'Revoked')], max_length=50)),
                ('access_granted_at', models.DateTimeField(auto_now_add=True)),
                ('access_expires_at', models.DateTimeField(blank=True, null=True)),
                ('analysis_access_data', models.JSONField(blank=True, default=dict, help_text='Specific access data for analysis app')),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_access', to='content_management.service')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_access', to=settings.AUTH_USER_MODEL)),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='access_records', to='billing.servicepayment')),
            ],
            options={
                'unique_together': {('user', 'service')},
            },
        ),
    ]
