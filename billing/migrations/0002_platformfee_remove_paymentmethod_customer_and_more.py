# Generated by Django 5.0.9 on 2025-04-11 15:25

import django.db.models.deletion
import uuid6
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('billing', '0001_initial'),
        ('content_management', '0005_solution_button_text_subscriptionplan_button_text'),
        ('enterprise', '0005_alter_enterprise_user'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlatformFee',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('percentage', models.DecimalField(decimal_places=2, help_text='Platform fee percentage', max_digits=5)),
                ('fixed_amount', models.IntegerField(default=0, help_text='Fixed platform fee in cents')),
                ('active', models.BooleanField(default=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='paymentmethod',
            name='customer',
        ),
        migrations.RemoveField(
            model_name='usagerecord',
            name='subscription',
        ),
        migrations.RemoveField(
            model_name='product',
            name='content_id',
        ),
        migrations.AddField(
            model_name='product',
            name='service',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stripe_products', to='content_management.service'),
        ),
        migrations.AddField(
            model_name='product',
            name='solution',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stripe_products', to='content_management.solution'),
        ),
        migrations.AddField(
            model_name='product',
            name='subscription_plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stripe_products', to='content_management.subscriptionplan'),
        ),
        migrations.CreateModel(
            name='EnterpriseAccount',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('stripe_account_id', models.CharField(max_length=255, unique=True)),
                ('charges_enabled', models.BooleanField(default=False)),
                ('payouts_enabled', models.BooleanField(default=False)),
                ('details_submitted', models.BooleanField(default=False)),
                ('requirements', models.JSONField(blank=True, default=dict)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('enterprise', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='stripe_account', to='enterprise.enterprise')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='EnterpriseService',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('price', models.DecimalField(decimal_places=2, help_text='Price in dollars', max_digits=10)),
                ('stripe_price_id', models.CharField(max_length=255, unique=True)),
                ('active', models.BooleanField(default=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('enterprise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='billing_services', to='enterprise.enterprise')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='EnterprisePayment',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('amount', models.IntegerField(help_text='Amount in cents')),
                ('platform_fee_amount', models.IntegerField(help_text='Platform fee amount in cents')),
                ('status', models.CharField(max_length=50)),
                ('stripe_payment_intent_id', models.CharField(max_length=255, unique=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enterprise_payments', to='billing.customer')),
                ('enterprise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='enterprise.enterprise')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='billing.enterpriseservice')),
                ('platform_fee', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='billing.platformfee')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.DeleteModel(
            name='Invoice',
        ),
        migrations.DeleteModel(
            name='PaymentMethod',
        ),
        migrations.DeleteModel(
            name='UsageRecord',
        ),
    ]
