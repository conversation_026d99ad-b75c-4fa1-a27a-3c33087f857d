# Generated by Django 5.0.9 on 2025-04-24 14:32

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('billing', '0007_alter_serviceaccess_unique_together_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserPaymentProfile',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('stripe_account_id', models.CharField(blank=True, max_length=255, null=True, unique=True)),
                ('charges_enabled', models.BooleanField(default=False)),
                ('payouts_enabled', models.BooleanField(default=False)),
                ('details_submitted', models.BooleanField(default=False)),
                ('accept_donations', models.BooleanField(default=False)),
                ('donation_message', models.TextField(blank=True)),
                ('minimum_donation', models.IntegerField(default=100, help_text='Minimum donation amount in cents')),
                ('suggested_donation_amounts', models.JSONField(default=list, help_text='List of suggested donation amounts in cents')),
                ('is_verified', models.BooleanField(default=False)),
                ('verification_date', models.DateTimeField(blank=True, null=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='payment_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='UserTransfer',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('amount', models.IntegerField(help_text='Amount in cents')),
                ('currency', models.CharField(default='usd', max_length=3)),
                ('message', models.TextField(blank=True)),
                ('transfer_type', models.CharField(choices=[('donation', 'Donation'), ('payment', 'Payment'), ('transfer', 'Transfer')], max_length=50)),
                ('platform_fee_amount', models.IntegerField(help_text='Platform fee amount in cents')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded')], max_length=50)),
                ('stripe_payment_intent_id', models.CharField(max_length=255, unique=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('platform_fee', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='billing.platformfee')),
                ('receiver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_transfers', to=settings.AUTH_USER_MODEL)),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_transfers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
