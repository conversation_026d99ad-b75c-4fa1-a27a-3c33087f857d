# Generated by Django 5.0.9 on 2025-05-08 16:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('billing', '0010_alter_solutionaccess_subscription'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='solutionuseraccess',
            name='role',
            field=models.CharField(choices=[('owner', 'Owner'), ('admin', 'Administrator'), ('member', 'Member')], default='member', max_length=20),
        ),
        migrations.AlterField(
            model_name='solutionuseraccess',
            name='solution_access',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users', to='billing.solutionaccess'),
        ),
        migrations.AlterField(
            model_name='solutionuseraccess',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('revoked', 'Revoked'), ('expired', 'Expired')], default='active', max_length=20),
        ),
        migrations.AlterField(
            model_name='solutionuseraccess',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
