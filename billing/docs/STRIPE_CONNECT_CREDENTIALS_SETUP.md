# Stripe Connect Credentials Setup Guide
## Ravid Healthcare Platform

This guide explains how to configure the necessary Stripe Connect credentials for the Ravid Healthcare Platform's peer-to-peer payment functionality.

## 🎯 Overview

The Ravid Healthcare Platform uses two Stripe integration approaches:

1. **Stripe Checkout** - Platform services (DNA analysis, AI features, subscriptions) ✅ *Already Configured*
2. **Stripe Connect** - User-to-user payments (donations, telemedicine) ⚠️ *Requires Additional Setup*

## 📋 Required Credentials Checklist

### ✅ Already Configured (Stripe Checkout)
- `STRIPE_SECRET_KEY` - Your Stripe secret API key
- `STRIPE_PUBLISHABLE_KEY` - Your Stripe publishable API key  
- `STRIPE_WEBHOOK_SECRET` - Webhook secret for standard Stripe events
- `STRIPE_LIVE_MODE` - Set to False for testing

### ⚠️ Missing (Stripe Connect - Required)
- `STRIPE_CONNECT_CLIENT_ID` - OAuth client ID for Connect applications
- `STRIPE_CONNECT_WEBHOOK_SECRET` - Webhook secret for Connect-specific events

### ⚙️ Optional Configuration
- `STRIPE_APPLICATION_FEE_PERCENT` - Platform fee percentage (default: 2.9%)
- `FRONTEND_URL` - Frontend URL for OAuth redirects
- `BACKEND_URL` - Backend URL for webhook endpoints

## 🔧 Step-by-Step Setup

### Step 1: Enable Stripe Connect in Dashboard

1. **Login to Stripe Dashboard**: https://dashboard.stripe.com/
2. **Navigate to Connect**: Click "Connect" in the left sidebar
3. **Enable Connect**: If not already enabled, click "Get started" and follow the setup wizard
4. **Choose Platform Type**: Select "Platform or marketplace" 

### Step 2: Get Connect Client ID

1. **Go to Connect Settings**: Dashboard > Connect > Settings
2. **Find OAuth Settings**: Look for "OAuth settings" section
3. **Copy Client ID**: Copy the value that starts with `ca_`
4. **Update .env**: Replace `STRIPE_CONNECT_CLIENT_ID=ca_PLACEHOLDER_REPLACE_WITH_YOUR_CONNECT_CLIENT_ID`

**Example:**
```bash
STRIPE_CONNECT_CLIENT_ID=ca_1234567890abcdef1234567890abcdef
```

### Step 3: Create Connect Webhook Endpoint

1. **Go to Webhooks**: Dashboard > Developers > Webhooks
2. **Add Endpoint**: Click "Add endpoint"
3. **Set URL**: `https://yourdomain.com/api/billing/webhook/connect/`
   - For local development: `https://your-ngrok-url.ngrok.io/api/billing/webhook/connect/`
4. **Select Events**: Add these specific Connect events:
   - `account.updated`
   - `account.application.deauthorized`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `transfer.created`
   - `transfer.updated`
5. **Save and Copy Secret**: Copy the webhook secret (starts with `whsec_`)
6. **Update .env**: Replace `STRIPE_CONNECT_WEBHOOK_SECRET=whsec_PLACEHOLDER_REPLACE_WITH_YOUR_CONNECT_WEBHOOK_SECRET`

**Example:**
```bash
STRIPE_CONNECT_WEBHOOK_SECRET=whsec_1234567890abcdef1234567890abcdef1234567890abcdef
```

### Step 4: Configure Platform Settings

Update these optional settings in your `.env` file:

```bash
# Platform fee for user-to-user transfers (2.9% is healthcare industry standard)
STRIPE_APPLICATION_FEE_PERCENT=2.9

# URLs for OAuth redirects (update for production)
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000
```

## 🏥 Healthcare-Specific Configuration

### Platform Fee Structure
The current configuration uses a 2.9% platform fee, which is standard for healthcare platforms:

- **Donations**: 2.9% platform fee
- **Telemedicine**: 2.9% platform fee  
- **General Transfers**: 2.9% platform fee

### Compliance Considerations
- **HIPAA Compliance**: Stripe is HIPAA-compliant when properly configured
- **Healthcare Regulations**: Platform fees must comply with local healthcare payment regulations
- **User Verification**: Healthcare providers may require additional verification

## 🔍 Verification Steps

After configuration, verify your setup:

### 1. Test Stripe Configuration
```bash
# Run inside Docker container
docker compose exec web python manage.py shell
```

```python
from billing.tests.test_stripe_config import test_stripe_configuration
test_stripe_configuration()
```

### 2. Test Connect Account Creation
```bash
# Test Connect account creation endpoint
curl -X POST http://localhost:8000/api/billing/payment-profiles/create_connect_account/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Check Webhook Endpoints
- Standard webhooks: `http://localhost:8000/api/billing/webhooks/stripe/`
- Connect webhooks: `http://localhost:8000/api/billing/webhook/connect/`

## 🚨 Security Notes

### Environment Variables Security
- **Never commit real credentials** to version control
- **Use different credentials** for development/staging/production
- **Rotate webhook secrets** regularly
- **Monitor webhook delivery** in Stripe Dashboard

### Production Checklist
- [ ] Switch to live Stripe keys (`sk_live_`, `pk_live_`)
- [ ] Update webhook URLs to production domains
- [ ] Enable HTTPS for all webhook endpoints
- [ ] Configure proper CORS settings
- [ ] Set up monitoring and alerting
- [ ] Test Connect account onboarding flow
- [ ] Verify platform fee collection

## 🔗 Related Documentation

- [Stripe Connect Setup Guide](./billing/STRIPE_CONNECT_SETUP_GUIDE.md)
- [Stripe Integration Overview](./billing/stripe_integration_overview.md)
- [Billing Refactoring Strategy](./billing_refactoring_strategy.md)

## 📞 Support

If you encounter issues:
1. Check Stripe Dashboard logs for webhook delivery failures
2. Review Django logs for authentication errors
3. Verify all environment variables are properly set
4. Test with Stripe's test mode first

## 🎯 Next Steps

After completing this setup:
1. **Test Connect account creation** for healthcare providers
2. **Configure donation settings** for user profiles  
3. **Test telemedicine payment flows**
4. **Set up monitoring** for Connect events
5. **Plan production deployment** with live credentials
