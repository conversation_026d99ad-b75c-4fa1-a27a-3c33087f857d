# Stripe Connect Simplified Setup for Doctors

## Tổng quan

Hệ thống hiện tại đã được tối ưu hóa để tạo Stripe Connect account dạng **Express** với setup đơn giản nhất có thể cho bác sĩ nhận thanh toán từ bệnh nhân.

## <PERSON><PERSON><PERSON> lo<PERSON>i setup hiện có

### 1. Setup thông thường (`create_connect_account`)
- **Endpoint**: `POST /api/billing/user-payment-profiles/create_connect_account/`
- **Loại account**: Express
- **Country**: Vietnam (VN)
- **Business type**: Individual
- **Yêu cầu**: Thông tin cá nhân + thông tin ngân hàng

### 2. Setup đơn giản cho bác sĩ (`create_simplified_doctor_account`) - MỚI
- **Endpoint**: `POST /api/billing/user-payment-profiles/create_simplified_doctor_account/`
- **Loại account**: Express (simplified)
- **Country**: Vietnam (VN)
- **Business type**: Individual
- **Yêu cầu**: CHỈ thông tin ngân hàng
- **Đặc điểm**:
  - Manual payout (bác sĩ tự rút tiền khi muốn)
  - Chỉ cần capabilities 'transfers' (không cần card_payments ban đầu)
  - Tối thiểu thông tin cần thiết

## Cách sử dụng

### Frontend Integration

```javascript
// Cho bác sĩ setup đơn giản
const setupDoctorPayment = async () => {
  try {
    const response = await fetch('/api/billing/user-payment-profiles/create_simplified_doctor_account/', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    if (response.ok) {
      // Redirect đến Stripe onboarding
      window.location.href = data.account_link;
    } else {
      console.error('Error:', data.error);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};
```

### Response Format

```json
{
  "account_link": "https://connect.stripe.com/express/onboarding/...",
  "message": "Simplified doctor account created successfully.",
  "setup_instructions": "You only need to provide your bank account details to receive payments.",
  "benefits": [
    "Minimal information required",
    "Only bank details needed", 
    "Optimized for Vietnamese doctors",
    "Manual payout control"
  ]
}
```

## Stripe Express vs Standard vs Custom

| Feature | Express (Simplified) | Express (Normal) | Standard | Custom |
|---------|---------------------|------------------|----------|---------|
| Setup complexity | ⭐ Rất đơn giản | ⭐⭐ Đơn giản | ⭐⭐⭐ Phức tạp | ⭐⭐⭐⭐⭐ Rất phức tạp |
| Thông tin cần thiết | Chỉ ngân hàng | Cá nhân + ngân hàng | Đầy đủ business info | Custom implementation |
| Thời gian setup | 2-3 phút | 5-10 phút | 15-30 phút | Tuần/tháng |
| Phù hợp cho | Bác sĩ cá nhân | Freelancer | Doanh nghiệp nhỏ | Enterprise |

## Lợi ích của Express Simplified

1. **Tối thiểu thông tin**: Chỉ cần thông tin ngân hàng
2. **Nhanh chóng**: Setup trong 2-3 phút
3. **Phù hợp Việt Nam**: Tối ưu cho thị trường VN
4. **Manual payout**: Bác sĩ tự kiểm soát việc rút tiền
5. **Peer-to-peer**: Hoàn hảo cho thanh toán tư vấn y tế

## Workflow

```mermaid
graph TD
    A[Bác sĩ click Setup Payment] --> B[Call create_simplified_doctor_account API]
    B --> C[Tạo Stripe Express Account]
    C --> D[Redirect đến Stripe Onboarding]
    D --> E[Bác sĩ nhập thông tin ngân hàng]
    E --> F[Stripe verify thông tin]
    F --> G[Account active - Có thể nhận tiền]
    G --> H[Bệnh nhân có thể thanh toán]
```

## Troubleshooting

### Lỗi thường gặp

1. **"User already has a Stripe Connect account"**
   - Sử dụng `refresh_connect_account_link` để tiếp tục setup

2. **"Invalid country"**
   - Đảm bảo country='VN' cho Việt Nam

3. **"Capabilities not available"**
   - Express account có thể cần thời gian để activate capabilities

### Kiểm tra trạng thái account

```javascript
const checkPaymentStatus = async () => {
  const response = await fetch('/api/billing/user-payment-profiles/payment_status/');
  const data = await response.json();
  
  console.log('Can receive payments:', data.can_receive_payments);
  console.log('Charges enabled:', data.charges_enabled);
  console.log('Payouts enabled:', data.payouts_enabled);
};
```

## Migration từ setup cũ

Nếu đã có account với setup phức tạp, có thể:
1. Giữ nguyên account hiện tại
2. Hoặc tạo account mới với simplified setup (cần xóa account cũ trước)

## Kết luận

Setup simplified này giúp bác sĩ có thể nhận thanh toán từ bệnh nhân một cách nhanh chóng và đơn giản nhất, chỉ cần cung cấp thông tin ngân hàng mà không cần điền nhiều form phức tạp như website, loại doanh nghiệp, v.v.
