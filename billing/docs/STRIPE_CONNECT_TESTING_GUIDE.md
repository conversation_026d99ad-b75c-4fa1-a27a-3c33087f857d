# 🧪 Stripe Connect Testing Guide
## Ravid Healthcare Platform

This guide provides step-by-step instructions to test your Stripe Connect configuration and verify all functionality is working properly.

## 🚀 Quick Start Testing

### Step 1: Basic Configuration Test
```bash
# Run in Docker development environment
docker compose -f docker-compose-dev.yaml exec web python billing/tests/test_stripe_connect_config.py
```

### Step 2: API Endpoints Test
```bash
# Test all Connect API endpoints
docker compose -f docker-compose-dev.yaml exec web python billing/tests/test_stripe_connect_api.py
```

## 📋 Comprehensive Testing Checklist

### ✅ **Phase 1: Configuration Validation**

#### Test 1: Basic Stripe Connectivity
```bash
# Test basic Stripe API connection
docker compose -f docker-compose-dev.yaml exec web python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

import stripe
from django.conf import settings
from billing.config import BillingConfig

stripe.api_key = settings.STRIPE_SECRET_KEY
customers = stripe.Customer.list(limit=1)
print('✅ Stripe API connectivity successful')
print(f'API Version: {BillingConfig.STRIPE_API_VERSION}')
"
```

#### Test 2: Connect Client ID Validation
```bash
# Verify Connect Client ID is properly configured
docker compose -f docker-compose-dev.yaml exec web python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.conf import settings
client_id = getattr(settings, 'STRIPE_CONNECT_CLIENT_ID', '')
if client_id and client_id.startswith('ca_'):
    print(f'✅ Connect Client ID configured: {client_id[:15]}...')
else:
    print('❌ Connect Client ID not properly configured')
"
```

#### Test 3: Environment Variables Check
```bash
# Check all required environment variables
docker compose -f docker-compose-dev.yaml exec web python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.conf import settings

required_vars = [
    'STRIPE_SECRET_KEY',
    'STRIPE_PUBLISHABLE_KEY', 
    'STRIPE_CONNECT_CLIENT_ID',
    'FRONTEND_URL',
    'BACKEND_URL'
]

for var in required_vars:
    value = getattr(settings, var, '')
    status = '✅' if value else '❌'
    print(f'{status} {var}: {\"Configured\" if value else \"Missing\"}')
"
```

### ✅ **Phase 2: Service Layer Testing**

#### Test 4: UserTransferService Initialization
```bash
# Test Connect service initialization
docker compose -f docker-compose-dev.yaml exec web python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from billing.services.user_transfer_service import UserTransferService

try:
    service = UserTransferService()
    print('✅ UserTransferService initialized successfully')
except Exception as e:
    print(f'❌ Service initialization failed: {e}')
"
```

#### Test 5: User Payment Profile Model
```bash
# Test UserPaymentProfile model
docker compose -f docker-compose-dev.yaml exec web python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from billing.models.customer import UserPaymentProfile
from django.contrib.auth import get_user_model

User = get_user_model()
print(f'✅ UserPaymentProfile model accessible')
print(f'✅ User model: {User.__name__}')
"
```

### ✅ **Phase 3: API Endpoint Testing**

#### Test 6: Payment Profile Endpoints
```bash
# Test payment profile API endpoints
docker compose -f docker-compose-dev.yaml exec web python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()
user, _ = User.objects.get_or_create(
    email='<EMAIL>',
    defaults={'username': 'testuser', 'is_active': True}
)

refresh = RefreshToken.for_user(user)
token = str(refresh.access_token)

client = APIClient()
client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

response = client.get('/api/billing/payment-profiles/')
print(f'✅ Payment profiles endpoint: {response.status_code}')
"
```

#### Test 7: Connect Account Creation Endpoint
```bash
# Test Connect account creation endpoint (dry run)
docker compose -f docker-compose-dev.yaml exec web python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.urls import reverse
try:
    url = reverse('billing:payment-profile-create-connect-account')
    print(f'✅ Connect account creation URL: {url}')
except Exception as e:
    print(f'❌ URL routing error: {e}')
"
```

### ✅ **Phase 4: Webhook Testing**

#### Test 8: Webhook Endpoint Accessibility
```bash
# Test webhook endpoints are accessible
docker compose -f docker-compose-dev.yaml exec web python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.test import Client

client = Client()

# Test standard webhook (should reject GET)
response = client.get('/api/billing/webhook/')
print(f'✅ Standard webhook endpoint: {response.status_code} (405 expected)')

# Test Connect webhook (should reject GET)  
response = client.get('/api/billing/webhook/connect/')
print(f'✅ Connect webhook endpoint: {response.status_code} (405 expected)')
"
```

#### Test 9: Webhook URL Configuration
```bash
# Check webhook URL configuration
docker compose -f docker-compose-dev.yaml exec web python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.conf import settings

backend_url = getattr(settings, 'BACKEND_URL', 'http://localhost:8000')
print(f'Standard webhook: {backend_url}/api/billing/webhook/')
print(f'Connect webhook: {backend_url}/api/billing/webhook/connect/')
print('ℹ️  Configure these URLs in your Stripe Dashboard')
"
```

### ✅ **Phase 5: End-to-End Testing**

#### Test 10: Complete Configuration Test
```bash
# Run comprehensive configuration test
docker compose -f docker-compose-dev.yaml exec web python billing/tests/test_stripe_connect_config.py
```

#### Test 11: Complete API Test
```bash
# Run comprehensive API test
docker compose -f docker-compose-dev.yaml exec web python billing/tests/test_stripe_connect_api.py
```

## 🔧 **Manual Testing Steps**

### Step 1: Test Connect Account Creation
```bash
# Create a test user and Connect account
curl -X POST http://localhost:8000/api/billing/payment-profiles/create_connect_account/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Step 2: Test Account Status Update
```bash
# Update Connect account status
curl -X POST http://localhost:8000/api/billing/payment-profiles/update_connect_account_status/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Step 3: Test Transfer Creation
```bash
# Create a test transfer (requires two users with Connect accounts)
curl -X POST http://localhost:8000/api/billing/transfers/create_transfer/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "receiver_id": "USER_ID",
    "amount": 1000,
    "message": "Test donation",
    "transfer_type": "donation"
  }'
```

## 🚨 **Troubleshooting Common Issues**

### Issue 1: "STRIPE_CONNECT_CLIENT_ID not configured"
```bash
# Check your .env file
docker compose -f docker-compose-dev.yaml exec web cat .env | grep STRIPE_CONNECT_CLIENT_ID
```

### Issue 2: "Invalid Connect Client ID format"
- Ensure Client ID starts with `ca_`
- Get correct Client ID from Stripe Dashboard > Connect > Onboarding options

### Issue 3: "Webhook secret not configured"
- Set up webhook endpoint in Stripe Dashboard
- Add `STRIPE_CONNECT_WEBHOOK_SECRET` to .env file

### Issue 4: API endpoints return 404
```bash
# Check URL routing
docker compose -f docker-compose-dev.yaml exec web python manage.py show_urls | grep billing
```

## 📊 **Expected Test Results**

### ✅ **All Tests Passing**
```
Tests Passed: 7/7
✅ All tests passed! Stripe Connect is properly configured.
```

### ⚠️ **Partial Success**
```
Tests Passed: 6/7
⚠️ Most tests passed. Check warnings above.
```

### ❌ **Configuration Issues**
```
Tests Passed: 3/7
❌ Multiple tests failed. Review configuration.
```

## 🎯 **Next Steps After Testing**

1. **If all tests pass:**
   - Set up webhook endpoints in Stripe Dashboard
   - Test with real Connect account creation
   - Test end-to-end payment flow

2. **If tests fail:**
   - Review error messages
   - Check environment variables
   - Verify Stripe Dashboard configuration

3. **Production readiness:**
   - Switch to live Stripe keys
   - Update webhook URLs to production domains
   - Test with real bank accounts (small amounts)

## 📞 **Support**

If tests fail or you encounter issues:
1. Check Docker container logs: `docker compose -f docker-compose-dev.yaml logs web`
2. Verify environment variables are loaded correctly
3. Check Stripe Dashboard for any account issues
4. Review Django settings configuration
