# Peer-to-Peer Payments API Documentation
## Ravid Healthcare Platform

### **Overview**

The Ravid Healthcare Platform provides comprehensive peer-to-peer payment capabilities through Stripe Connect integration. This API enables users to:

- Set up payment profiles for receiving payments
- Send and receive donations, payments, and transfers
- Manage telemedicine consultation payments
- Track payment history and status

### **Base URL**
```
https://api.ravid.cloud/billing/
```

### **Authentication**
All endpoints require authentication via Bearer token:
```
Authorization: Bearer <your_access_token>
```

---

## **1. Payment Profile Management**

### **1.1 Create Stripe Connect Account**
Create a Stripe Connect account for receiving payments.

**Endpoint:** `POST /payment-profiles/create_connect_account/`

**Request Body:**
```json
{
  "account_type": "express",
  "country": "US",
  "business_type": "individual"
}
```

**Response:**
```json
{
  "onboarding_url": "https://connect.stripe.com/express/onboarding/...",
  "account_id": "acct_1234567890",
  "status": "pending"
}
```

### **1.2 Update Donation Settings**
Configure donation preferences and settings.

**Endpoint:** `POST /payment-profiles/update_donation_settings/`

**Request Body:**
```json
{
  "accept_donations": true,
  "donation_message": "Support my healthcare practice",
  "minimum_donation": 500,
  "suggested_donation_amounts": [1000, 2500, 5000, 10000]
}
```

**Response:**
```json
{
  "accept_donations": true,
  "donation_message": "Support my healthcare practice",
  "minimum_donation": 500,
  "suggested_donation_amounts": [1000, 2500, 5000, 10000]
}
```

### **1.3 Get Payment Profile**
Retrieve current user's payment profile.

**Endpoint:** `GET /payment-profiles/`

**Response:**
```json
{
  "id": "uuid-here",
  "user": "user-id",
  "stripe_account_id": "acct_1234567890",
  "charges_enabled": true,
  "payouts_enabled": true,
  "details_submitted": true,
  "accept_donations": true,
  "donation_message": "Support my healthcare practice",
  "minimum_donation": 500,
  "suggested_donation_amounts": [1000, 2500, 5000, 10000],
  "is_verified": true,
  "verification_date": "2024-01-15T10:30:00Z",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **1.4 Get Public Donation Info**
Get public donation information for any user.

**Endpoint:** `GET /payment-profiles/donation_info/{user_id}/`

**Response:**
```json
{
  "user_id": "user-uuid",
  "user_name": "Dr. John Smith",
  "accepts_donations": true,
  "donation_message": "Support my healthcare practice",
  "minimum_donation": 500,
  "suggested_donation_amounts": [1000, 2500, 5000, 10000],
  "minimum_donation_usd": 5.00,
  "suggested_amounts_usd": [10.00, 25.00, 50.00, 100.00]
}
```

### **1.5 Update Connect Account Status**
Sync Stripe Connect account status.

**Endpoint:** `POST /payment-profiles/update_connect_account_status/`

**Response:**
```json
{
  "charges_enabled": true,
  "payouts_enabled": true,
  "details_submitted": true,
  "is_verified": true,
  "can_receive_payments": true
}
```

---

## **2. Transfer Operations**

### **2.1 Create Transfer**
Create a peer-to-peer transfer between users.

**Endpoint:** `POST /transfers/create_transfer/`

**Request Body:**
```json
{
  "receiver_id": "receiver-user-uuid",
  "amount": 5000,
  "currency": "usd",
  "message": "Payment for consultation",
  "transfer_type": "payment"
}
```

**Response:**
```json
{
  "id": "transfer-uuid",
  "sender": "sender-user-id",
  "receiver": "receiver-user-id",
  "sender_name": "Jane Doe",
  "sender_email": "<EMAIL>",
  "receiver_name": "Dr. John Smith",
  "receiver_email": "<EMAIL>",
  "amount": 5000,
  "amount_usd": 50.00,
  "currency": "usd",
  "message": "Payment for consultation",
  "transfer_type": "payment",
  "platform_fee_amount": 250,
  "platform_fee_usd": 2.50,
  "net_amount_usd": 47.50,
  "status": "pending",
  "client_secret": "pi_1234567890_secret_xyz",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **2.2 Create Donation**
Create a donation transfer.

**Endpoint:** `POST /transfers/create_donation/`

**Request Body:**
```json
{
  "receiver_id": "receiver-user-uuid",
  "amount": 2500,
  "message": "Thank you for your excellent care"
}
```

**Response:**
```json
{
  "id": "donation-uuid",
  "sender": "sender-user-id",
  "receiver": "receiver-user-id",
  "sender_name": "Patient Name",
  "receiver_name": "Dr. John Smith",
  "amount": 2500,
  "amount_usd": 25.00,
  "transfer_type": "donation",
  "platform_fee_amount": 125,
  "platform_fee_usd": 1.25,
  "net_amount_usd": 23.75,
  "status": "pending",
  "client_secret": "pi_donation_secret_xyz",
  "created_at": "2024-01-15T10:30:00Z"
}
```

### **2.3 Confirm Transfer Payment**
Confirm a pending transfer payment.

**Endpoint:** `POST /transfers/{transfer_id}/confirm_payment/`

**Response:**
```json
{
  "status": "success",
  "message": "Transfer confirmed successfully"
}
```

### **2.4 Get Transfer History**
Get complete transfer history for the authenticated user.

**Endpoint:** `GET /transfers/transfer_history/`

**Query Parameters:**
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20)
- `transfer_type`: Filter by type (`donation`, `payment`, `transfer`)
- `status`: Filter by status (`pending`, `completed`, `failed`)

**Response:**
```json
{
  "count": 50,
  "next": "https://api.ravid.cloud/billing/transfers/transfer_history/?page=2",
  "previous": null,
  "results": [
    {
      "id": "transfer-uuid",
      "sender": "sender-user-id",
      "receiver": "receiver-user-id",
      "sender_name": "Jane Doe",
      "receiver_name": "Dr. John Smith",
      "amount": 5000,
      "amount_usd": 50.00,
      "transfer_type": "payment",
      "status": "completed",
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### **2.5 Get Sent Transfers**
Get transfers sent by the authenticated user.

**Endpoint:** `GET /transfers/sent_transfers/`

**Response:**
```json
{
  "count": 25,
  "results": [
    {
      "id": "transfer-uuid",
      "receiver": "receiver-user-id",
      "receiver_name": "Dr. John Smith",
      "receiver_email": "<EMAIL>",
      "amount": 5000,
      "amount_usd": 50.00,
      "transfer_type": "payment",
      "status": "completed",
      "message": "Payment for consultation",
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### **2.6 Get Received Transfers**
Get transfers received by the authenticated user.

**Endpoint:** `GET /transfers/received_transfers/`

**Response:**
```json
{
  "count": 30,
  "results": [
    {
      "id": "transfer-uuid",
      "sender": "sender-user-id",
      "sender_name": "Jane Doe",
      "sender_email": "<EMAIL>",
      "amount": 2500,
      "amount_usd": 25.00,
      "transfer_type": "donation",
      "status": "completed",
      "message": "Thank you for your excellent care",
      "platform_fee_amount": 125,
      "net_amount_usd": 23.75,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

## **3. Healthcare-Specific Features**

### **3.1 Telemedicine Payments**
Create payments for telemedicine consultations.

**Endpoint:** `POST /transfers/create_transfer/`

**Request Body:**
```json
{
  "receiver_id": "doctor-user-uuid",
  "amount": 15000,
  "currency": "usd",
  "message": "Telemedicine consultation - 30 minutes",
  "transfer_type": "payment",
  "metadata": {
    "consultation_type": "telemedicine",
    "duration_minutes": 30,
    "appointment_id": "appointment-uuid"
  }
}
```

### **3.2 Healthcare Provider Donations**
Support donations between healthcare providers.

**Endpoint:** `POST /transfers/create_donation/`

**Request Body:**
```json
{
  "receiver_id": "provider-user-uuid",
  "amount": 10000,
  "message": "Supporting your medical mission work"
}
```

---

## **4. Error Handling**

### **Common Error Responses**

**400 Bad Request:**
```json
{
  "error": "Invalid request data",
  "details": {
    "amount": ["Amount must be at least 50 cents"]
  }
}
```

**403 Forbidden:**
```json
{
  "error": "Receiver does not accept donations",
  "code": "DONATIONS_DISABLED"
}
```

**404 Not Found:**
```json
{
  "error": "User not found",
  "code": "USER_NOT_FOUND"
}
```

**422 Unprocessable Entity:**
```json
{
  "error": "Payment profile not set up",
  "code": "PAYMENT_PROFILE_REQUIRED",
  "action": "create_connect_account"
}
```

### **Error Codes**
- `PAYMENT_PROFILE_REQUIRED`: User needs to set up payment profile
- `STRIPE_ACCOUNT_REQUIRED`: Receiver needs Stripe Connect account
- `DONATIONS_DISABLED`: Receiver doesn't accept donations
- `INSUFFICIENT_FUNDS`: Payment method declined
- `TRANSFER_FAILED`: Stripe payment processing failed
- `USER_NOT_FOUND`: Specified user doesn't exist
- `INVALID_AMOUNT`: Amount below minimum or invalid format

---

## **5. Platform Fees**

### **Fee Structure**
- **Donations**: 5% platform fee
- **Telemedicine Payments**: 3% platform fee
- **General Transfers**: 2.5% platform fee

### **Fee Calculation**
Platform fees are automatically calculated and deducted from transfers:
- **Gross Amount**: Amount specified by sender
- **Platform Fee**: Calculated percentage of gross amount
- **Net Amount**: Amount received by recipient (gross - platform fee)

---

## **6. Webhook Events**

### **Transfer Status Updates**
Your application can receive webhook notifications for transfer status changes:

**Event Types:**
- `transfer.pending`: Transfer created and pending payment
- `transfer.completed`: Transfer successfully completed
- `transfer.failed`: Transfer failed to process
- `transfer.refunded`: Transfer was refunded

**Webhook Payload:**
```json
{
  "event_type": "transfer.completed",
  "transfer_id": "transfer-uuid",
  "sender_id": "sender-user-id",
  "receiver_id": "receiver-user-id",
  "amount": 5000,
  "transfer_type": "payment",
  "status": "completed",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

## **7. Testing**

### **Test Mode**
Use Stripe test keys for development and testing:
- Test card numbers: `****************`
- Test Connect accounts are automatically created
- No real money is processed in test mode

### **Docker Testing**
Run tests within Docker containers:
```bash
make dev
docker-compose exec web python manage.py test billing.tests.test_user_transfers
```

---

## **8. Rate Limits**

- **Transfer Creation**: 10 requests per minute per user
- **Profile Updates**: 5 requests per minute per user
- **History Queries**: 100 requests per minute per user

---

## **9. Security Considerations**

- All payment data is processed through Stripe's secure infrastructure
- PCI compliance handled by Stripe
- User authentication required for all operations
- Transfer amounts validated on server side
- Webhook signatures verified for authenticity
```
