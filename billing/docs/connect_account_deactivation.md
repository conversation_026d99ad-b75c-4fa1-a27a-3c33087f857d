# Stripe Connect Account Deactivation API

## Overview

This document describes the API endpoint for deactivating and deleting Stripe Connect accounts in the Ravid Healthcare Platform.

## Endpoint

```
DELETE /api/billing/payment-profiles/deactivate_connect_account/
```

## Authentication

Requires JWT authentication. The user must be authenticated and can only deactivate their own Connect account.

## Request

### Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Body
No request body required.

## Response

### Success Response (200 OK)

```json
{
    "success": true,
    "message": "Stripe Connect account has been deleted successfully",
    "details": {
        "method": "deleted",
        "profile_updated": true,
        "transfers_affected": 0
    }
}
```

### Error Responses

#### No Connect Account (404 Not Found)
```json
{
    "error": "No Stripe Connect account found to deactivate"
}
```

#### Pending Transfers (400 Bad Request)
```json
{
    "error": "Cannot deactivate account with pending transfers"
}
```

#### Validation Error (400 Bad Request)
```json
{
    "error": "User does not have a payment profile"
}
```

## Behavior

### What happens when deactivating:

1. **Validation Checks**:
   - Verifies user has a payment profile with Stripe Connect account
   - Checks for pending transfers (blocks deactivation if found)

2. **Stripe Account Deletion**:
   - Attempts to delete the Stripe Express account
   - Falls back to deactivation for Standard accounts (if deletion fails)

3. **Database Updates**:
   - Clears `stripe_account_id` from user's payment profile
   - Sets `charges_enabled`, `payouts_enabled`, `details_submitted`, `is_verified` to `false`
   - Disables donation acceptance (`accept_donations = false`)

4. **Transfer Cleanup**:
   - Marks any incomplete transfers as 'failed'
   - Returns count of affected transfers

### Account Types

- **Express Accounts**: Can be fully deleted from Stripe
- **Standard Accounts**: Can only be deactivated (not deleted)

The API handles both cases automatically and reports the method used in the response.

## Use Cases

1. **User Account Cleanup**: When users want to remove their payment capabilities
2. **Privacy Compliance**: For GDPR/data deletion requests
3. **Account Management**: Administrative cleanup of inactive accounts
4. **Testing**: Cleanup test accounts during development

## Security Considerations

- Users can only deactivate their own accounts
- Prevents deactivation when pending transfers exist
- Logs all deactivation attempts for audit purposes
- Maintains data integrity by properly cleaning up related records

## Example Usage

### JavaScript/Frontend
```javascript
const response = await fetch('/api/billing/payment-profiles/deactivate_connect_account/', {
    method: 'DELETE',
    headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
    }
});

const result = await response.json();

if (response.ok) {
    console.log('Account deactivated:', result.message);
    console.log('Method used:', result.details.method);
} else {
    console.error('Deactivation failed:', result.error);
}
```

### Python/Backend
```python
from rest_framework.test import APIClient

client = APIClient()
client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

response = client.delete('/api/billing/payment-profiles/deactivate_connect_account/')

if response.status_code == 200:
    data = response.json()
    print(f"Success: {data['message']}")
    print(f"Method: {data['details']['method']}")
else:
    print(f"Error: {response.json()['error']}")
```

## Testing

Run the test suite to verify functionality:

```bash
python billing/tests/test_stripe_connect_api.py
```

The test includes:
- Testing deactivation when no account exists (404 response)
- Testing successful deactivation when account exists
- Verifying proper cleanup of database records

## Related Endpoints

- `POST /api/billing/payment-profiles/create_connect_account/` - Create Connect account
- `POST /api/billing/payment-profiles/refresh_connect_account_link/` - Refresh onboarding link
- `GET /api/billing/payment-profiles/payment_status/` - Check account status

## Logging

All deactivation attempts are logged with:
- User ID
- Account ID (if exists)
- Method used (deleted/deactivated)
- Number of transfers affected
- Timestamp and operation details
