# Telemedicine Payments Implementation Guide
## Ravid Healthcare Platform

### **Overview**

This guide provides comprehensive instructions for implementing telemedicine payment functionality using the existing peer-to-peer payment infrastructure.

### **Current Implementation Status**

✅ **Fully Functional Components:**
- Stripe Connect integration for healthcare providers
- UserTransfer model supporting payment types
- Payment profile management for doctors
- Platform fee collection system
- Transfer history and status tracking

🏗️ **Ready for Enhancement:**
- Appointment-specific payment flows
- Automated consultation fee calculation
- Integration with appointment booking system

---

## **1. Architecture Overview**

### **Payment Flow**
```
Patient → Appointment Booking → Payment Processing → Doctor Receives Payment
    ↓              ↓                    ↓                      ↓
CustomUser    Appointment        UserTransfer         UserPaymentProfile
              Model              (payment type)       (Stripe Connect)
```

### **Key Components**
- **UserPaymentProfile**: Doctor's Stripe Connect account
- **UserTransfer**: Payment transaction record
- **Appointment**: Consultation booking (integration point)
- **Platform Fee**: Configurable fee structure

---

## **2. Doctor Setup Process**

### **2.1 Create Payment Profile**
Doctors must set up their payment profile to receive consultation payments.

**API Call:**
```javascript
// Create Stripe Connect account for doctor
const response = await fetch('/billing/payment-profiles/create_connect_account/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    account_type: 'express',
    country: 'US',
    business_type: 'individual'
  })
});

const { onboarding_url } = await response.json();
// Redirect doctor to Stripe onboarding
window.location.href = onboarding_url;
```

### **2.2 Set Consultation Rates**
Doctors can configure their consultation fees and availability.

**Recommended Implementation:**
```python
# Add to accounts/models.py or create new model
class DoctorConsultationProfile(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    consultation_fee = models.IntegerField(help_text="Fee in cents")
    consultation_duration = models.IntegerField(default=30)  # minutes
    accepts_telemedicine = models.BooleanField(default=False)
    available_hours = models.JSONField(default=dict)
    
    def get_consultation_fee_usd(self):
        return self.consultation_fee / 100
```

---

## **3. Patient Payment Process**

### **3.1 Consultation Booking with Payment**
When a patient books a telemedicine appointment, payment is processed immediately.

**Frontend Implementation:**
```javascript
async function bookTelemedicineAppointment(doctorId, appointmentData) {
  try {
    // 1. Create appointment
    const appointment = await createAppointment(doctorId, appointmentData);
    
    // 2. Process payment
    const paymentResponse = await fetch('/billing/transfers/create_transfer/', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        receiver_id: doctorId,
        amount: appointmentData.consultation_fee,
        currency: 'usd',
        message: `Telemedicine consultation - ${appointmentData.duration} minutes`,
        transfer_type: 'payment',
        metadata: {
          consultation_type: 'telemedicine',
          appointment_id: appointment.id,
          duration_minutes: appointmentData.duration
        }
      })
    });
    
    const payment = await paymentResponse.json();
    
    // 3. Handle Stripe payment confirmation
    if (payment.client_secret) {
      const stripe = Stripe(process.env.STRIPE_PUBLISHABLE_KEY);
      const result = await stripe.confirmCardPayment(payment.client_secret);
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        appointment,
        payment,
        status: 'confirmed'
      };
    }
    
  } catch (error) {
    console.error('Booking failed:', error);
    throw error;
  }
}
```

### **3.2 Payment Confirmation**
After successful payment, confirm the transfer and update appointment status.

**Backend Integration:**
```python
# In appointments/services.py or similar
class TelemedicinePaymentService:
    def __init__(self):
        self.transfer_service = UserTransferService()
    
    def process_consultation_payment(self, patient, doctor, appointment):
        """Process payment for telemedicine consultation"""
        try:
            # Get doctor's consultation fee
            doctor_profile = getattr(doctor, 'consultation_profile', None)
            if not doctor_profile:
                raise ValidationError("Doctor consultation profile not found")
            
            # Create transfer
            transfer, client_secret = self.transfer_service.create_transfer(
                sender=patient,
                receiver=doctor,
                amount=doctor_profile.consultation_fee,
                message=f"Telemedicine consultation - {appointment.id}",
                transfer_type=TransferType.PAYMENT
            )
            
            # Link transfer to appointment
            appointment.payment_transfer = transfer
            appointment.payment_status = 'pending'
            appointment.save()
            
            return transfer, client_secret
            
        except Exception as e:
            logger.error(f"Consultation payment failed: {str(e)}")
            raise
    
    def confirm_consultation_payment(self, appointment):
        """Confirm consultation payment and activate appointment"""
        try:
            if not appointment.payment_transfer:
                raise ValidationError("No payment transfer found")
            
            # Confirm transfer
            updated_transfer = self.transfer_service.confirm_transfer(
                appointment.payment_transfer
            )
            
            # Update appointment status
            if updated_transfer.status == 'completed':
                appointment.payment_status = 'paid'
                appointment.status = 'confirmed'
            else:
                appointment.payment_status = 'failed'
                appointment.status = 'cancelled'
            
            appointment.save()
            return appointment
            
        except Exception as e:
            logger.error(f"Payment confirmation failed: {str(e)}")
            raise
```

---

## **4. Integration with Appointment System**

### **4.1 Appointment Model Enhancement**
Extend the existing appointment model to include payment information.

**Recommended Changes:**
```python
# In appointments/models.py
class Appointment(BaseModel):
    # ... existing fields ...
    
    # Payment integration fields
    payment_transfer = models.ForeignKey(
        'billing.UserTransfer',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='appointments'
    )
    payment_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('paid', 'Paid'),
            ('failed', 'Failed'),
            ('refunded', 'Refunded'),
        ],
        default='pending'
    )
    consultation_fee = models.IntegerField(
        null=True,
        blank=True,
        help_text="Consultation fee in cents"
    )
    
    def get_consultation_fee_usd(self):
        return self.consultation_fee / 100 if self.consultation_fee else 0
```

### **4.2 Appointment ViewSet Updates**
Update appointment creation to handle payments.

**API Enhancement:**
```python
# In appointments/api/views.py
class AppointmentViewSet(viewsets.ModelViewSet):
    
    @action(detail=False, methods=['post'])
    def create_telemedicine_appointment(self, request):
        """Create telemedicine appointment with payment processing"""
        try:
            data = request.data
            doctor_id = data.get('doctor_id')
            appointment_data = data.get('appointment_data')
            
            # Validate doctor
            doctor = get_object_or_404(CustomUser, id=doctor_id)
            
            # Create appointment
            appointment = Appointment.objects.create(
                patient=request.user,
                doctor=doctor,
                appointment_type='telemedicine',
                **appointment_data
            )
            
            # Process payment
            payment_service = TelemedicinePaymentService()
            transfer, client_secret = payment_service.process_consultation_payment(
                patient=request.user,
                doctor=doctor,
                appointment=appointment
            )
            
            return Response({
                'appointment': AppointmentSerializer(appointment).data,
                'payment': {
                    'transfer_id': str(transfer.id),
                    'client_secret': client_secret,
                    'amount': transfer.amount,
                    'amount_usd': transfer.amount / 100
                }
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
```

---

## **5. Frontend Integration Examples**

### **5.1 React Component for Telemedicine Booking**
```jsx
import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY);

function TelemedicineBooking({ doctor, onSuccess }) {
  const [loading, setLoading] = useState(false);
  const [appointmentData, setAppointmentData] = useState({
    date: '',
    time: '',
    duration: 30,
    notes: ''
  });

  const handleBooking = async () => {
    setLoading(true);
    try {
      const response = await bookTelemedicineAppointment(doctor.id, {
        ...appointmentData,
        consultation_fee: doctor.consultation_fee
      });
      
      onSuccess(response);
    } catch (error) {
      console.error('Booking failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="telemedicine-booking">
      <h3>Book Telemedicine Consultation</h3>
      <p>Dr. {doctor.name}</p>
      <p>Fee: ${doctor.consultation_fee / 100}</p>
      
      {/* Appointment form fields */}
      <input
        type="date"
        value={appointmentData.date}
        onChange={(e) => setAppointmentData({...appointmentData, date: e.target.value})}
      />
      
      <button onClick={handleBooking} disabled={loading}>
        {loading ? 'Processing...' : 'Book & Pay'}
      </button>
    </div>
  );
}

export default function TelemedicineBookingWrapper(props) {
  return (
    <Elements stripe={stripePromise}>
      <TelemedicineBooking {...props} />
    </Elements>
  );
}
```

### **5.2 Payment Status Component**
```jsx
function PaymentStatus({ appointment }) {
  const getStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'green';
      case 'pending': return 'orange';
      case 'failed': return 'red';
      default: return 'gray';
    }
  };

  return (
    <div className="payment-status">
      <span 
        className={`status-badge ${getStatusColor(appointment.payment_status)}`}
      >
        {appointment.payment_status.toUpperCase()}
      </span>
      {appointment.consultation_fee && (
        <span className="fee">
          ${appointment.consultation_fee / 100}
        </span>
      )}
    </div>
  );
}
```

---

## **6. Testing Telemedicine Payments**

### **6.1 Docker Test Setup**
```bash
# Run tests in Docker environment
make dev
docker-compose exec web python manage.py test billing.tests.test_telemedicine_payments
```

### **6.2 Test Cases**
```python
# In billing/tests/test_telemedicine_payments.py
class TelemedicinePaymentTests(TestCase):
    def setUp(self):
        self.patient = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass'
        )
        self.doctor = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass'
        )
        
        # Set up doctor payment profile
        self.doctor_profile = UserPaymentProfile.objects.create(
            user=self.doctor,
            stripe_account_id='acct_test_123',
            charges_enabled=True,
            payouts_enabled=True
        )
    
    def test_consultation_payment_creation(self):
        """Test creating consultation payment"""
        service = TelemedicinePaymentService()
        
        appointment = Appointment.objects.create(
            patient=self.patient,
            doctor=self.doctor,
            appointment_type='telemedicine'
        )
        
        transfer, client_secret = service.process_consultation_payment(
            patient=self.patient,
            doctor=self.doctor,
            appointment=appointment
        )
        
        self.assertIsNotNone(transfer)
        self.assertIsNotNone(client_secret)
        self.assertEqual(transfer.transfer_type, 'payment')
```

---

## **7. Deployment Considerations**

### **7.1 Environment Variables**
```bash
# Add to Docker environment
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
FRONTEND_URL=https://app.ravid.cloud
```

### **7.2 Database Migrations**
```bash
# Generate and apply migrations
docker-compose exec web python manage.py makemigrations
docker-compose exec web python manage.py migrate
```

### **7.3 Monitoring and Logging**
- Monitor transfer success rates
- Track payment failures and reasons
- Log consultation booking patterns
- Alert on unusual payment activity

---

## **8. Next Steps**

1. **Implement consultation profile model** for doctors
2. **Extend appointment model** with payment fields
3. **Create telemedicine-specific API endpoints**
4. **Build frontend payment components**
5. **Add comprehensive testing**
6. **Deploy with proper monitoring**

The existing peer-to-peer payment infrastructure provides a solid foundation for telemedicine payments. The main work involves integrating it with the appointment system and creating user-friendly interfaces.
