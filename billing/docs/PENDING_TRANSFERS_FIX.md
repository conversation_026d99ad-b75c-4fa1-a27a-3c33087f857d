# Pending Transfers Fix Guide

## Overview

Tài liệu này hướng dẫn cách xử lý lỗi "Cannot deactivate account with pending transfers" khi user muốn deactivate Stripe Connect account.

## Lỗi gặp phải

```
ERROR: Cannot deactivate account with pending transfers
Bad Request: /api/billing/payment-profiles/deactivate_connect_account/
```

## Giải pháp

### 1. API Endpoints mới

#### Check Pending Transfers
```http
GET /api/billing/payment-profiles/check_pending_transfers/
Authorization: Bearer <token>
```

**Response:**
```json
{
    "total_pending_count": 2,
    "total_pending_amount_cents": 5000,
    "total_pending_amount_usd": 50.0,
    "can_deactivate_immediately": false,
    "pending_details": {
        "pending_as_sender": {
            "count": 1,
            "total_amount": 2500,
            "transfers": [...]
        },
        "pending_as_receiver": {
            "count": 1,
            "total_amount": 2500,
            "transfers": [...]
        }
    },
    "deactivation_info": {
        "message": "You have 2 pending transfers that will be cancelled.",
        "will_cancel_transfers": true,
        "affected_amount_usd": 50.0
    }
}
```

#### Cancel All Pending Transfers
```http
POST /api/billing/payment-profiles/cancel_all_pending_transfers/
Authorization: Bearer <token>
```

**Response:**
```json
{
    "message": "Processed 2 pending transfers",
    "cancelled_count": 2,
    "failed_count": 0,
    "cancelled_transfers": [...],
    "failed_cancellations": [],
    "next_step": "You can now safely deactivate your Connect account"
}
```

#### Deactivate Connect Account (Updated)
```http
DELETE /api/billing/payment-profiles/deactivate_connect_account/
Authorization: Bearer <token>
```

**Response (với pending transfers):**
```json
{
    "success": true,
    "message": "Stripe Connect account has been deleted successfully",
    "details": {
        "method": "deleted",
        "profile_updated": true,
        "transfers_affected": 2,
        "cancelled_transfers": ["transfer_id_1", "transfer_id_2"],
        "failed_cancellations": []
    },
    "summary": {
        "account_deactivated": true,
        "pending_transfers_handled": true,
        "total_transfers_cancelled": 2,
        "transfers_marked_as_failed": 0
    }
}
```

### 2. Frontend Flow

```javascript
// Step 1: Check pending transfers
const checkPending = async () => {
    try {
        const response = await fetch('/api/billing/payment-profiles/check_pending_transfers/', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        const data = await response.json();
        
        if (data.can_deactivate_immediately) {
            // Proceed to deactivate directly
            return deactivateAccount();
        } else {
            // Show warning and ask user confirmation
            return showPendingTransfersWarning(data);
        }
    } catch (error) {
        console.error('Error checking pending transfers:', error);
    }
};

// Step 2: Show warning and handle user choice
const showPendingTransfersWarning = (data) => {
    const message = `
        You have ${data.total_pending_count} pending transfers totaling $${data.total_pending_amount_usd}.
        These will be cancelled if you proceed with deactivation.
        
        Do you want to continue?
    `;
    
    if (confirm(message)) {
        return deactivateAccount();
    }
};

// Step 3: Deactivate account (now handles pending transfers automatically)
const deactivateAccount = async () => {
    try {
        const response = await fetch('/api/billing/payment-profiles/deactivate_connect_account/', {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('Account deactivated successfully');
            console.log(`Cancelled ${data.summary.total_transfers_cancelled} transfers`);
        }
    } catch (error) {
        console.error('Error deactivating account:', error);
    }
};
```

### 3. Management Command

Sử dụng management command để xử lý hàng loạt:

```bash
# Kiểm tra pending transfers
python manage.py fix_pending_transfers --check-only

# Kiểm tra cho user cụ thể
python manage.py fix_pending_transfers --user-id=USER_ID --check-only

# Cancel tất cả pending transfers cũ hơn 24h
python manage.py fix_pending_transfers --older-than-hours=24

# Force cancel tất cả (nguy hiểm!)
python manage.py fix_pending_transfers --force-cancel
```

## Cách thức hoạt động

### 1. Auto-cancellation Logic

Khi user gọi `deactivate_connect_account`, system sẽ:

1. **Tìm pending transfers**: Query tất cả transfers có status `pending` hoặc `processing`
2. **Cancel từng transfer**: Gọi `stripe.PaymentIntent.cancel()` cho mỗi payment intent
3. **Update status**: Đánh dấu transfers là `canceled` hoặc `failed`
4. **Deactivate account**: Sau khi xử lý xong transfers, tiến hành deactivate

### 2. Error Handling

- **Payment intent không thể cancel**: Đánh dấu transfer là `failed`
- **Stripe API error**: Log error và đánh dấu transfer là `failed`
- **Network error**: Retry logic trong BaseStripeService

### 3. Status Mapping

| Stripe PaymentIntent Status | Transfer Status | Action |
|----------------------------|----------------|---------|
| `canceled` | `canceled` | Success |
| `succeeded` | `completed` | Skip (đã complete) |
| `processing` | `failed` | Cannot cancel |
| `requires_action` | `failed` | Cannot cancel |

## Testing

### 1. Unit Tests

```python
def test_cancel_pending_transfer():
    # Create pending transfer
    transfer = UserTransfer.objects.create(...)
    
    # Cancel it
    service = UserTransferService()
    success = service.cancel_pending_transfer(transfer)
    
    assert success
    assert transfer.status == 'canceled'

def test_deactivate_with_pending_transfers():
    # Create user with pending transfers
    # Call deactivate
    # Verify transfers are cancelled and account is deactivated
```

### 2. API Tests

```python
def test_deactivate_endpoint_with_pending():
    # Create pending transfers
    # Call deactivate API
    # Verify response includes cancellation details
```

## Monitoring và Logging

### Logs to watch:

```
INFO: Found 2 pending transfers for user {user_id}, attempting to cancel them
INFO: Successfully cancelled transfer {transfer_id}
WARNING: Failed to cancel transfer {transfer_id}
INFO: Deactivated Stripe Connect account for user {user_id} - method: deleted
```

### Metrics to monitor:

- Number of pending transfers cancelled per day
- Success rate of transfer cancellations
- Time taken to process deactivation requests

## Troubleshooting

### Common Issues:

1. **Transfer không thể cancel**
   - Reason: Payment intent đã chuyển sang processing
   - Solution: Đánh dấu là failed, user cần chờ complete

2. **Stripe API rate limit**
   - Reason: Quá nhiều requests
   - Solution: Retry với exponential backoff

3. **User có quá nhiều pending transfers**
   - Reason: System issue hoặc abuse
   - Solution: Investigate và có thể cần manual intervention

### Debug Commands:

```bash
# Check specific user's transfers
python manage.py fix_pending_transfers --user-id=USER_ID --check-only

# Check all old pending transfers
python manage.py fix_pending_transfers --check-only --older-than-hours=1

# Force clean specific user
python manage.py fix_pending_transfers --user-id=USER_ID --force-cancel
```

## Security Considerations

1. **Authorization**: Chỉ user có thể cancel transfers của mình
2. **Rate limiting**: Prevent abuse của cancel operations
3. **Audit logging**: Log tất cả cancellation activities
4. **Refund policy**: Cancelled transfers không thể refund

## Related Documentation

- [Stripe Connect Account Deactivation](./connect_account_deactivation.md)
- [User Transfer Service](../services/user_transfer_service.py)
- [Error Handling Guide](./error_handling.md) 