# Donation System Implementation Guide
## Ravid Healthcare Platform

### **Overview**

The Ravid Healthcare Platform includes a comprehensive donation system that enables users to support healthcare providers, medical research, and community health initiatives through secure peer-to-peer payments.

### **Key Features**

✅ **Fully Implemented:**
- Stripe Connect integration for receiving donations
- Customizable donation settings and messages
- Suggested donation amounts
- Minimum donation thresholds
- Platform fee collection
- Donation history tracking
- Public donation profiles

---

## **1. Donation System Architecture**

### **Core Components**
```
Donor → Donation Settings → Payment Processing → Healthcare Provider
  ↓           ↓                    ↓                    ↓
CustomUser  UserPaymentProfile  UserTransfer    Stripe Connect Account
```

### **Models Involved**
- **UserPaymentProfile**: Donation settings and Stripe account
- **UserTransfer**: Donation transaction records
- **PlatformFee**: Fee structure for donations
- **CustomUser**: Donor and recipient information

---

## **2. Healthcare Provider Setup**

### **2.1 Enable Donation Acceptance**
Healthcare providers must configure their donation settings.

**API Call:**
```javascript
const response = await fetch('/billing/payment-profiles/update_donation_settings/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + providerToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    accept_donations: true,
    donation_message: "Support my medical mission work in underserved communities",
    minimum_donation: 500,  // $5.00 minimum
    suggested_donation_amounts: [1000, 2500, 5000, 10000]  // $10, $25, $50, $100
  })
});
```

### **2.2 Create Stripe Connect Account**
Providers need a Stripe Connect account to receive donations.

**Setup Process:**
```javascript
// 1. Create Connect account
const accountResponse = await fetch('/billing/payment-profiles/create_connect_account/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + providerToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    account_type: 'express',
    country: 'US',
    business_type: 'individual'
  })
});

const { onboarding_url } = await accountResponse.json();

// 2. Redirect to Stripe onboarding
window.location.href = onboarding_url;

// 3. After onboarding completion, update account status
await fetch('/billing/payment-profiles/update_connect_account_status/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + providerToken
  }
});
```

---

## **3. Donation Process**

### **3.1 Discover Healthcare Providers**
Donors can discover providers accepting donations.

**Get Public Donation Info:**
```javascript
async function getProviderDonationInfo(providerId) {
  const response = await fetch(`/billing/payment-profiles/donation_info/${providerId}/`);
  const data = await response.json();
  
  return {
    providerId: data.user_id,
    providerName: data.user_name,
    acceptsDonations: data.accepts_donations,
    message: data.donation_message,
    minimumUSD: data.minimum_donation_usd,
    suggestedAmountsUSD: data.suggested_amounts_usd
  };
}
```

**Example Response:**
```json
{
  "user_id": "provider-uuid",
  "user_name": "Dr. Sarah Johnson",
  "accepts_donations": true,
  "donation_message": "Support my medical mission work in underserved communities",
  "minimum_donation": 500,
  "suggested_donation_amounts": [1000, 2500, 5000, 10000],
  "minimum_donation_usd": 5.00,
  "suggested_amounts_usd": [10.00, 25.00, 50.00, 100.00]
}
```

### **3.2 Make a Donation**
Donors can send donations to healthcare providers.

**Create Donation:**
```javascript
async function makeDonation(providerId, amount, message) {
  try {
    const response = await fetch('/billing/transfers/create_donation/', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + donorToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        receiver_id: providerId,
        amount: amount * 100,  // Convert to cents
        message: message
      })
    });
    
    const donation = await response.json();
    
    // Handle Stripe payment confirmation
    if (donation.client_secret) {
      const stripe = Stripe(process.env.STRIPE_PUBLISHABLE_KEY);
      const result = await stripe.confirmCardPayment(donation.client_secret);
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      return {
        donationId: donation.id,
        amount: donation.amount_usd,
        platformFee: donation.platform_fee_usd,
        netAmount: donation.net_amount_usd,
        status: 'completed'
      };
    }
    
  } catch (error) {
    console.error('Donation failed:', error);
    throw error;
  }
}
```

---

## **4. Frontend Implementation Examples**

### **4.1 Provider Donation Profile Component**
```jsx
import React, { useState, useEffect } from 'react';

function ProviderDonationProfile({ providerId }) {
  const [provider, setProvider] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadProvider() {
      try {
        const data = await getProviderDonationInfo(providerId);
        setProvider(data);
      } catch (error) {
        console.error('Failed to load provider:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadProvider();
  }, [providerId]);

  if (loading) return <div>Loading...</div>;
  if (!provider?.acceptsDonations) return <div>This provider doesn't accept donations</div>;

  return (
    <div className="provider-donation-profile">
      <h2>{provider.providerName}</h2>
      <p className="donation-message">{provider.message}</p>
      
      <div className="donation-info">
        <p>Minimum donation: ${provider.minimumUSD}</p>
        <div className="suggested-amounts">
          <h4>Suggested amounts:</h4>
          {provider.suggestedAmountsUSD.map(amount => (
            <span key={amount} className="amount-badge">
              ${amount}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}
```

### **4.2 Donation Form Component**
```jsx
import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY);

function DonationForm({ provider, onSuccess }) {
  const [amount, setAmount] = useState(provider.suggestedAmountsUSD[0] || 10);
  const [message, setMessage] = useState('');
  const [customAmount, setCustomAmount] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleDonation = async () => {
    if (amount < provider.minimumUSD) {
      alert(`Minimum donation is $${provider.minimumUSD}`);
      return;
    }

    setLoading(true);
    try {
      const result = await makeDonation(provider.providerId, amount, message);
      onSuccess(result);
    } catch (error) {
      alert('Donation failed: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="donation-form">
      <h3>Make a Donation</h3>
      
      {/* Suggested amounts */}
      <div className="amount-selection">
        <h4>Select amount:</h4>
        {provider.suggestedAmountsUSD.map(suggestedAmount => (
          <button
            key={suggestedAmount}
            className={`amount-btn ${amount === suggestedAmount ? 'selected' : ''}`}
            onClick={() => {
              setAmount(suggestedAmount);
              setCustomAmount(false);
            }}
          >
            ${suggestedAmount}
          </button>
        ))}
        <button
          className={`amount-btn ${customAmount ? 'selected' : ''}`}
          onClick={() => setCustomAmount(true)}
        >
          Custom
        </button>
      </div>

      {/* Custom amount input */}
      {customAmount && (
        <div className="custom-amount">
          <label>Custom amount ($):</label>
          <input
            type="number"
            min={provider.minimumUSD}
            step="0.01"
            value={amount}
            onChange={(e) => setAmount(parseFloat(e.target.value))}
          />
        </div>
      )}

      {/* Message */}
      <div className="donation-message">
        <label>Message (optional):</label>
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Add a personal message..."
          maxLength={500}
        />
      </div>

      {/* Donation summary */}
      <div className="donation-summary">
        <p>Donation amount: ${amount.toFixed(2)}</p>
        <p>Platform fee (5%): ${(amount * 0.05).toFixed(2)}</p>
        <p><strong>Provider receives: ${(amount * 0.95).toFixed(2)}</strong></p>
      </div>

      <button 
        className="donate-btn"
        onClick={handleDonation}
        disabled={loading || amount < provider.minimumUSD}
      >
        {loading ? 'Processing...' : `Donate $${amount.toFixed(2)}`}
      </button>
    </div>
  );
}
```

### **4.3 Donation History Component**
```jsx
function DonationHistory() {
  const [donations, setDonations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadDonations() {
      try {
        const response = await fetch('/billing/transfers/sent_transfers/?transfer_type=donation', {
          headers: {
            'Authorization': 'Bearer ' + token
          }
        });
        const data = await response.json();
        setDonations(data.results);
      } catch (error) {
        console.error('Failed to load donations:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadDonations();
  }, []);

  if (loading) return <div>Loading donation history...</div>;

  return (
    <div className="donation-history">
      <h3>Your Donations</h3>
      {donations.length === 0 ? (
        <p>No donations yet</p>
      ) : (
        <div className="donations-list">
          {donations.map(donation => (
            <div key={donation.id} className="donation-item">
              <div className="donation-header">
                <span className="recipient">{donation.receiver_name}</span>
                <span className="amount">${donation.amount_usd}</span>
                <span className={`status ${donation.status}`}>
                  {donation.status}
                </span>
              </div>
              {donation.message && (
                <p className="donation-message">"{donation.message}"</p>
              )}
              <p className="donation-date">
                {new Date(donation.created_at).toLocaleDateString()}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

---

## **5. Healthcare Provider Dashboard**

### **5.1 Received Donations Component**
```jsx
function ReceivedDonations() {
  const [donations, setDonations] = useState([]);
  const [stats, setStats] = useState({
    totalReceived: 0,
    donationCount: 0,
    averageDonation: 0
  });

  useEffect(() => {
    async function loadReceivedDonations() {
      try {
        const response = await fetch('/billing/transfers/received_transfers/?transfer_type=donation', {
          headers: {
            'Authorization': 'Bearer ' + token
          }
        });
        const data = await response.json();
        
        setDonations(data.results);
        
        // Calculate stats
        const total = data.results.reduce((sum, d) => sum + d.net_amount_usd, 0);
        setStats({
          totalReceived: total,
          donationCount: data.results.length,
          averageDonation: data.results.length > 0 ? total / data.results.length : 0
        });
        
      } catch (error) {
        console.error('Failed to load received donations:', error);
      }
    }
    
    loadReceivedDonations();
  }, []);

  return (
    <div className="received-donations">
      <h3>Donations Received</h3>
      
      {/* Stats */}
      <div className="donation-stats">
        <div className="stat">
          <h4>${stats.totalReceived.toFixed(2)}</h4>
          <p>Total Received</p>
        </div>
        <div className="stat">
          <h4>{stats.donationCount}</h4>
          <p>Donations</p>
        </div>
        <div className="stat">
          <h4>${stats.averageDonation.toFixed(2)}</h4>
          <p>Average</p>
        </div>
      </div>

      {/* Donations list */}
      <div className="donations-list">
        {donations.map(donation => (
          <div key={donation.id} className="donation-item">
            <div className="donation-header">
              <span className="donor">From: {donation.sender_name}</span>
              <span className="amount">${donation.net_amount_usd}</span>
            </div>
            {donation.message && (
              <p className="donation-message">"{donation.message}"</p>
            )}
            <p className="donation-date">
              {new Date(donation.created_at).toLocaleDateString()}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
```

---

## **6. Use Cases for Healthcare Donations**

### **6.1 Medical Mission Support**
Healthcare providers working in underserved areas can receive donations to support their mission work.

**Example Implementation:**
```javascript
// Provider sets up mission-specific donation campaign
await updateDonationSettings({
  accept_donations: true,
  donation_message: "Supporting free medical care in rural Guatemala. Your donation helps provide essential medications and medical supplies.",
  minimum_donation: 1000,  // $10 minimum
  suggested_donation_amounts: [2500, 5000, 10000, 25000]  // $25, $50, $100, $250
});
```

### **6.2 Research Funding**
Medical researchers can receive donations to fund their research projects.

### **6.3 Community Health Programs**
Healthcare providers can raise funds for community health initiatives.

### **6.4 Emergency Medical Relief**
Quick donation collection for emergency medical situations.

---

## **7. Platform Fee Structure**

### **7.1 Donation Fees**
- **Platform Fee**: 5% of donation amount
- **Stripe Processing Fee**: ~2.9% + $0.30 (handled by Stripe)
- **Net to Provider**: ~92% of donation amount

### **7.2 Fee Transparency**
The donation form clearly shows fee breakdown to donors:
```
Donation: $50.00
Platform fee (5%): $2.50
Provider receives: $47.50
```

---

## **8. Testing and Validation**

### **8.1 Test Donation Flow**
```python
# In billing/tests/test_donations.py
class DonationSystemTests(TestCase):
    def test_donation_creation(self):
        """Test creating a donation"""
        donor = CustomUser.objects.create_user(email='<EMAIL>')
        provider = CustomUser.objects.create_user(email='<EMAIL>')
        
        # Set up provider profile
        profile = UserPaymentProfile.objects.create(
            user=provider,
            accept_donations=True,
            minimum_donation=500
        )
        
        service = UserTransferService()
        transfer, client_secret = service.create_donation(
            sender=donor,
            receiver=provider,
            amount=2500,
            message="Thank you for your service"
        )
        
        self.assertEqual(transfer.transfer_type, 'donation')
        self.assertEqual(transfer.amount, 2500)
        self.assertIsNotNone(client_secret)
```

---

## **9. Security and Compliance**

### **9.1 Data Protection**
- All payment data processed through Stripe's secure infrastructure
- PCI compliance handled by Stripe
- Donor information protected according to HIPAA requirements

### **9.2 Fraud Prevention**
- Minimum donation amounts prevent micro-fraud
- Transfer limits and monitoring
- Stripe's built-in fraud detection

---

## **10. Monitoring and Analytics**

### **10.1 Donation Metrics**
Track key donation metrics:
- Total donations processed
- Average donation amount
- Provider participation rates
- Donor retention rates

### **10.2 Provider Success Metrics**
Help providers understand their donation performance:
- Monthly donation totals
- Donor engagement rates
- Message effectiveness
- Suggested amount optimization

The donation system is fully functional and ready for healthcare providers to start accepting donations immediately. The main implementation work involves creating user-friendly interfaces and integrating with provider profiles.
