"""
Abstraction interfaces to decouple billing from content_management
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from decimal import Decimal


class ServiceProviderInterface(ABC):
    """
    Abstract interface for service providers
    Decouples billing from content_management
    """
    
    @abstractmethod
    def get_service_by_type(self, service_type: str, service_code: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get service by type and optional code
        
        Returns:
            Dict with keys: id, name, price, currency, service_type, is_active, stripe_price_id
        """
        pass
    
    @abstractmethod
    def get_service_pricing(self, service_id: str) -> Dict[str, Any]:
        """
        Get pricing information for a service
        
        Returns:
            Dict with keys: base_price, discounted_price, currency, stripe_price_id
        """
        pass
    
    @abstractmethod
    def validate_service_access(self, service_id: str, user_id: str) -> bool:
        """Check if user has access to the service"""
        pass
    
    @abstractmethod
    def get_subscription_plan(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """
        Get subscription plan details
        
        Returns:
            Dict with keys: id, name, price, currency, interval, stripe_price_id
        """
        pass
    
    @abstractmethod
    def get_solution(self, solution_id: str) -> Optional[Dict[str, Any]]:
        """
        Get enterprise solution details
        
        Returns:
            Dict with keys: id, name, price, currency, features, stripe_price_id
        """
        pass


class PricingProviderInterface(ABC):
    """Abstract interface for pricing calculations"""
    
    @abstractmethod
    def calculate_service_price(self, service_id: str, user_id: Optional[str] = None) -> Decimal:
        """Calculate final price for service including discounts"""
        pass
    
    @abstractmethod
    def calculate_platform_fee(self, amount: Decimal, service_type: str) -> Decimal:
        """Calculate platform fee for a transaction"""
        pass
    
    @abstractmethod
    def get_available_promotions(self, service_id: str, user_id: str) -> List[Dict[str, Any]]:
        """Get available promotions for a service and user"""
        pass


class PaymentMetadataProviderInterface(ABC):
    """Abstract interface for payment metadata"""
    
    @abstractmethod
    def build_payment_metadata(self, payment_type: str, **kwargs) -> Dict[str, str]:
        """Build standardized payment metadata"""
        pass
    
    @abstractmethod
    def extract_payment_info(self, metadata: Dict[str, str]) -> Dict[str, Any]:
        """Extract payment information from metadata"""
        pass


class ServiceDiscoveryInterface(ABC):
    """Abstract interface for service discovery"""
    
    @abstractmethod
    def find_services_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Find services by category"""
        pass
    
    @abstractmethod
    def search_services(self, query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search services with filters"""
        pass 