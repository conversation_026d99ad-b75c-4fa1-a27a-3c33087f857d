"""
Service provider registry for dependency injection
"""
import logging
from typing import Dict, Type, Optional, Any
from ..interfaces.service_provider import (
    ServiceProviderInterface,
    PricingProviderInterface,
    PaymentMetadataProviderInterface,
    ServiceDiscoveryInterface
)

logger = logging.getLogger(__name__)


class ServiceProviderRegistry:
    """
    Registry for service providers using dependency injection
    Allows switching between different implementations without tight coupling
    """
    
    _instance = None
    _providers: Dict[str, Any] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._providers = {}
            self._initialized = True
    
    def register(self, interface_name: str, provider_instance: Any) -> None:
        """Register a provider instance for an interface"""
        self._providers[interface_name] = provider_instance
        logger.info(f"Registered provider for {interface_name}: {type(provider_instance).__name__}")
    
    def get_service_provider(self) -> Optional[ServiceProviderInterface]:
        """Get the registered service provider"""
        return self._providers.get('service_provider')
    
    def get_pricing_provider(self) -> Optional[PricingProviderInterface]:
        """Get the registered pricing provider"""
        return self._providers.get('pricing_provider')
    
    def get_metadata_provider(self) -> Optional[PaymentMetadataProviderInterface]:
        """Get the registered metadata provider"""
        return self._providers.get('metadata_provider')
    
    def get_discovery_provider(self) -> Optional[ServiceDiscoveryInterface]:
        """Get the registered discovery provider"""
        return self._providers.get('discovery_provider')
    
    def is_registered(self, interface_name: str) -> bool:
        """Check if a provider is registered for an interface"""
        return interface_name in self._providers
    
    def unregister(self, interface_name: str) -> None:
        """Unregister a provider"""
        if interface_name in self._providers:
            del self._providers[interface_name]
            logger.info(f"Unregistered provider for {interface_name}")
    
    def clear_all(self) -> None:
        """Clear all registered providers"""
        self._providers.clear()
        logger.info("Cleared all registered providers")


# Global registry instance
registry = ServiceProviderRegistry()


def get_service_provider() -> Optional[ServiceProviderInterface]:
    """Get the global service provider with null safety"""
    try:
        if not registry:
            logger.error("Service provider registry not available")
            return None
        return registry.get_service_provider()
    except Exception as e:
        logger.error(f"Error getting service provider: {str(e)}")
        return None


def get_pricing_provider() -> Optional[PricingProviderInterface]:
    """Get the global pricing provider with null safety"""
    try:
        if not registry:
            logger.error("Service provider registry not available")
            return None
        return registry.get_pricing_provider()
    except Exception as e:
        logger.error(f"Error getting pricing provider: {str(e)}")
        return None


def get_metadata_provider() -> Optional[PaymentMetadataProviderInterface]:
    """Get the global metadata provider with null safety"""
    try:
        if not registry:
            logger.error("Service provider registry not available")
            return None
        return registry.get_metadata_provider()
    except Exception as e:
        logger.error(f"Error getting metadata provider: {str(e)}")
        return None


def get_discovery_provider() -> Optional[ServiceDiscoveryInterface]:
    """Get the global discovery provider with null safety"""
    try:
        if not registry:
            logger.error("Service provider registry not available")
            return None
        return registry.get_discovery_provider()
    except Exception as e:
        logger.error(f"Error getting discovery provider: {str(e)}")
        return None


def setup_default_providers():
    """Setup default providers using content_management adapter"""
    try:
        from ..adapters.content_management_adapter import ContentManagementAdapter
        
        adapter = ContentManagementAdapter()
        
        # Register adapter for all interfaces it implements
        registry.register('service_provider', adapter)
        registry.register('pricing_provider', adapter) 
        registry.register('metadata_provider', adapter)
        registry.register('discovery_provider', adapter)
        
        logger.info("Default providers setup successfully")
        
    except Exception as e:
        logger.error(f"Error setting up default providers: {str(e)}")


# Auto-setup providers on import
setup_default_providers() 