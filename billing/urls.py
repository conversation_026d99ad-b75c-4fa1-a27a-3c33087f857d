from django.urls import path, include
from rest_framework.routers import <PERSON>fa<PERSON><PERSON>out<PERSON>
from billing.views import (
    CreateCheckoutSessionView,
    list_platform_services,
    stripe_webhook,
    check_service_access,
    check_subscription_access,
    check_solution_access,
    list_user_services,
    list_enterprise_solutions,
    list_payment_history,
    get_payment_details,
    PaymentSuccessView,
    PaymentCancelView,
    revoke_service_access,
    revoke_subscription_access,
    revoke_solution_access,
    DNAAnalysisPaymentHandler,
    check_dna_analysis_status,
    UserPaymentProfileViewSet,
    UserTransferViewSet
)
from billing.views.doctor_viewset import DoctorPaymentViewSet
from billing.views.payment_link_viewset import PaymentLinkViewSet
from billing.views.stripe_connect_webhook_view import StripeConnectWebhookView


app_name = 'billing'

router = DefaultRouter()
router.register(r'billing', DNAAnalysisPaymentHandler, basename='billing')
router.register(r'payment-profiles', UserPaymentProfileViewSet, basename='payment-profile')
router.register(r'transfers', UserTransferViewSet, basename='transfer')
router.register(r'doctor-payments', DoctorPaymentViewSet, basename='doctor-payment')
router.register(r'payment-links', PaymentLinkViewSet, basename='payment-link')

# Note: Stripe Connect endpoints are handled by existing viewsets:
# - UserPaymentProfileViewSet for individual users  
# - DoctorPaymentViewSet for healthcare providers


urlpatterns = [
    # SaaS platform checkout
    path('checkout/', CreateCheckoutSessionView.as_view(), name='create_checkout_session'),
    # DNA analysis endpoints
    path('checkout_dna_services/', DNAAnalysisPaymentHandler.as_view({'post': 'checkout_dna_services'}), name='checkout_dna_services'),
    path('handle_dna_payment_success/', DNAAnalysisPaymentHandler.as_view({'post': 'handle_dna_payment_success'}), name='handle_dna_payment_success'),
    path('dna_analysis/<uuid:dna_analysis_id>/status/', check_dna_analysis_status, name='check_dna_analysis_status'),
    path('dna_analyses/', DNAAnalysisPaymentHandler.as_view({'get': 'list_dna_analyses'}), name='list_dna_analyses'),
    path('services/', list_platform_services, name='list_platform_services'),
    

    
    # Webhook endpoints
    path('webhook/', stripe_webhook, name='stripe_webhook'),
    path('webhook/connect/', StripeConnectWebhookView.as_view(), name='stripe_connect_webhook'),
    
    # Access management
    path('access/service/<uuid:service_id>/check/', check_service_access, name='check_service_access'),
    path('access/subscription/<uuid:subscription_id>/check/', check_subscription_access, name='check_subscription_access'),
    path('access/solution/<uuid:solution_id>/check/', check_solution_access, name='check_solution_access'),
    path('access/user/services/', list_user_services, name='list_user_services'),
    path('access/enterprise/solutions/', list_enterprise_solutions, name='list_enterprise_solutions'),
    
    # Access revocation
    path('access/service/<uuid:service_id>/revoke/', revoke_service_access, name='revoke_service_access'),
    path('access/subscription/<uuid:subscription_id>/revoke/', revoke_subscription_access, name='revoke_subscription_access'),
    path('access/solution/<uuid:solution_id>/revoke/', revoke_solution_access, name='revoke_solution_access'),
    
    # Payment history endpoints
    path('payments/', list_payment_history, name='list_payment_history'),
    path('payments/<uuid:payment_id>/<str:payment_type>/', get_payment_details, name='get_payment_details'),
    
    # Payment success/cancel endpoints
    path('payment/success/', PaymentSuccessView.as_view(), name='payment_success'),
    path('payment/cancel/', PaymentCancelView.as_view(), name='payment_cancel'),

    path('', include(router.urls)),
] 