"""
User transfer service for handling user-to-user payments
"""
import stripe
import logging
from django.conf import settings
from django.utils import timezone

from .base import BaseStripeService
from ..models import UserPaymentProfile, UserTransfer, PlatformFee
from ..exceptions import TransferError, ValidationError
from ..constants import TransferType

logger = logging.getLogger(__name__)


class UserTransferService(BaseStripeService):
    """Service for handling user-to-user transfers"""
    
    def create_stripe_connect_account(self, user):
        """Create a Stripe Connect Express account for a user - optimized for simplicity"""
        try:
            # Prepare account creation data with prefilled information
            account_data = {
                'type': 'express',
                'email': user.email,
                'capabilities': {
                    'card_payments': {'requested': True},
                    'transfers': {'requested': True},
                },
                'business_type': 'individual',  # Individual is simpler than company
                'metadata': {
                    'user_id': str(user.id),
                    'platform': 'ravid_healthcare',
                    'account_purpose': 'telemedicine_payments'
                }
            }

            # Add prefilled individual information if available
            individual_data = {}
            if hasattr(user, 'first_name') and user.first_name:
                individual_data['first_name'] = user.first_name
            if hasattr(user, 'last_name') and user.last_name:
                individual_data['last_name'] = user.last_name
            if hasattr(user, 'phone') and user.phone:
                individual_data['phone'] = user.phone

            if individual_data:
                account_data['individual'] = individual_data

            # Create Express account with minimal required fields for faster onboarding
            account = self._make_stripe_request(
                stripe.Account.create,
                **account_data
            )
            
            # Create or update user payment profile
            profile, created = UserPaymentProfile.objects.get_or_create(user=user)
            profile.stripe_account_id = account.id
            profile.charges_enabled = account.charges_enabled
            profile.payouts_enabled = account.payouts_enabled
            profile.details_submitted = account.details_submitted
            profile.save()
            
            # Create account link for Express onboarding - optimized for simplicity
            # Note: Using collection_options instead of deprecated 'collect' parameter
            account_link = self._make_stripe_request(
                stripe.AccountLink.create,
                account=account.id,
                refresh_url=f"{settings.FRONTEND_URL}",
                return_url=f"{settings.FRONTEND_URL}",
                type='account_onboarding',
                collection_options={
                    'fields': 'eventually_due',  # Minimize required fields
                    'future_requirements': 'omit'  # Don't show future requirements
                }
            )
            
            action = 'Created' if created else 'Updated'
            self._log_operation(f"{action} Stripe Express account {account.id} for user {user.id}")
            return account_link.url
            
        except Exception as e:
            logger.error(f"Error creating Stripe Express account: {str(e)}")
            raise TransferError(f"Error creating Stripe Express account: {str(e)}")

    # def create_simplified_doctor_account(self, user):
    #     """Create a super simplified Stripe Connect account specifically for doctors - DEACTIVATED"""
    #     # This method has been deactivated - use create_stripe_connect_account instead
    #     # which already provides Express account setup optimized for simplicity
    #     pass
    
    def create_transfer(self, sender, receiver, amount, currency='usd', message='', transfer_type=TransferType.TRANSFER):
        """Create a transfer between users"""
        try:
            # Validate receiver has payment profile (required for receiving payments)
            receiver_profile = getattr(receiver, 'payment_profile', None)

            if not receiver_profile:
                raise ValidationError("Receiver must have a payment profile to receive payments")

            if not receiver_profile.stripe_account_id:
                raise ValidationError("Receiver must have a connected Stripe account")

            # For senders, we only need to ensure they can be created as Stripe customers
            # Sender payment profiles are not required - they just need to pay
            from .customer_service import CustomerService
            customer_service = CustomerService()
            sender_customer = customer_service.create_or_update_customer(sender)

            if not sender_customer or not sender_customer.stripe_customer_id:
                raise ValidationError("Failed to create customer for sender payment processing")
            
            # Calculate platform fee
            platform_fee = self._get_applicable_platform_fee(amount)
            fee_amount = int(amount * (platform_fee.percentage / 100)) if platform_fee else 0
            
            # Create payment intent with sender's customer
            payment_intent = self._make_stripe_request(
                stripe.PaymentIntent.create,
                amount=amount,
                currency=currency,
                customer=sender_customer.stripe_customer_id,  # Associate with sender's customer
                transfer_data={
                    'destination': receiver_profile.stripe_account_id,
                    'application_fee_amount': fee_amount,
                },
                metadata={
                    'sender_id': str(sender.id),
                    'receiver_id': str(receiver.id),
                    'transfer_type': transfer_type,
                }
            )
            
            # Create transfer record
            transfer = UserTransfer.objects.create(
                sender=sender,
                receiver=receiver,
                amount=amount,
                currency=currency,
                message=message,
                transfer_type=transfer_type,
                platform_fee=platform_fee,
                platform_fee_amount=fee_amount,
                status='pending',
                stripe_payment_intent_id=payment_intent.id,
            )
            
            self._log_operation(f"Created transfer {transfer.id} from user {sender.id} to user {receiver.id}")
            return transfer, payment_intent.client_secret
            
        except Exception as e:
            logger.error(f"Error creating transfer: {str(e)}")
            raise TransferError(f"Error creating transfer: {str(e)}")
    
    def confirm_transfer(self, transfer):
        """Confirm a pending transfer payment"""
        try:
            if transfer.status != 'pending':
                raise ValidationError('Transfer is not in pending status')
            
            # Confirm payment intent
            payment_intent = self._make_stripe_request(
                stripe.PaymentIntent.confirm,
                transfer.stripe_payment_intent_id
            )
            
            # Update transfer status based on payment intent status
            if payment_intent.status == 'succeeded':
                transfer.status = 'completed'
            elif payment_intent.status == 'requires_action':
                transfer.status = 'processing'
            else:
                transfer.status = 'failed'
            
            transfer.save()
            
            self._log_operation(f"Confirmed transfer {transfer.id} with status {transfer.status}")
            return transfer
            
        except Exception as e:
            logger.error(f"Error confirming transfer: {str(e)}")
            raise TransferError(f"Error confirming transfer: {str(e)}")
    
    def handle_transfer_webhook(self, event):
        """Handle Stripe webhook events for transfers"""
        try:
            if event.type == 'payment_intent.succeeded':
                payment_intent = event.data.object
                transfer = UserTransfer.objects.get(stripe_payment_intent_id=payment_intent.id)
                transfer.status = 'completed'
                transfer.save()
                
                self._log_operation(f"Transfer {transfer.id} completed via webhook")
                
            elif event.type == 'payment_intent.payment_failed':
                payment_intent = event.data.object
                transfer = UserTransfer.objects.get(stripe_payment_intent_id=payment_intent.id)
                transfer.status = 'failed'
                transfer.save()
                
                self._log_operation(f"Transfer {transfer.id} failed via webhook")
                
            return True
            
        except UserTransfer.DoesNotExist:
            logger.error(f"Transfer not found for payment intent: {payment_intent.id}")
            return False
        except Exception as e:
            logger.error(f"Error handling transfer webhook: {str(e)}")
            raise TransferError(f"Error handling transfer webhook: {str(e)}")
    
    def update_donation_settings(self, user, settings_data):
        """Update donation settings for user's payment profile"""
        try:
            profile, _ = UserPaymentProfile.objects.get_or_create(user=user)
            
            # Update donation settings
            if 'accept_donations' in settings_data:
                profile.accept_donations = settings_data['accept_donations']
            
            if 'donation_message' in settings_data:
                profile.donation_message = settings_data['donation_message']
            
            if 'minimum_donation' in settings_data:
                profile.minimum_donation = settings_data['minimum_donation']
            
            if 'suggested_donation_amounts' in settings_data:
                profile.suggested_donation_amounts = settings_data['suggested_donation_amounts']
            
            profile.save()
            
            self._log_operation(f"Updated donation settings for user {user.id}")
            return profile
            
        except Exception as e:
            logger.error(f"Error updating donation settings: {str(e)}")
            raise TransferError(f"Error updating donation settings: {str(e)}")
    
    def get_user_transfers(self, user, transfer_type=None):
        """Get transfers for a user (sent and received)"""
        from django.db.models import Q
        
        queryset = UserTransfer.objects.filter(
            Q(sender=user) | Q(receiver=user)
        ).order_by('-created_at')
        
        if transfer_type:
            queryset = queryset.filter(transfer_type=transfer_type)
        
        return queryset
    
    def get_sent_transfers(self, user):
        """Get transfers sent by user"""
        return UserTransfer.objects.filter(sender=user).order_by('-created_at')
    
    def get_received_transfers(self, user):
        """Get transfers received by user"""
        return UserTransfer.objects.filter(receiver=user).order_by('-created_at')
    
    def _get_applicable_platform_fee(self, amount):
        """Get applicable platform fee for amount"""
        try:
            return PlatformFee.objects.filter(
                active=True,
                # Add amount-based filtering if needed
            ).first()
        except PlatformFee.DoesNotExist:
            logger.warning("No platform fee found")
            return None
    
    def update_connect_account_status(self, user):
        """Update user's connect account status from Stripe"""
        try:
            profile = UserPaymentProfile.objects.get(user=user)
            
            if not profile.stripe_account_id:
                raise ValidationError("User does not have a Stripe Connect account")
            
            account = self._make_stripe_request(
                stripe.Account.retrieve,
                profile.stripe_account_id
            )
            
            profile.charges_enabled = account.charges_enabled
            profile.payouts_enabled = account.payouts_enabled
            profile.details_submitted = account.details_submitted
            profile.save()
            
            self._log_operation(f"Updated connect account status for user {user.id}")
            return profile
            
        except Exception as e:
            logger.error(f"Error updating connect account status: {str(e)}")
            raise TransferError(f"Error updating connect account status: {str(e)}")
    
    def create_donation(self, sender, receiver, amount, message=''):
        """Create a donation transfer"""
        return self.create_transfer(
            sender=sender,
            receiver=receiver,
            amount=amount,
            message=message,
            transfer_type=TransferType.DONATION
        )
    
    def get_donation_settings(self, user):
        """Get donation settings for user"""
        try:
            profile = UserPaymentProfile.objects.get(user=user)
            return {
                'accept_donations': profile.accept_donations,
                'donation_message': profile.donation_message,
                'minimum_donation': profile.minimum_donation,
                'suggested_donation_amounts': profile.suggested_donation_amounts,
            }
        except UserPaymentProfile.DoesNotExist:
            return {
                'accept_donations': False,
                'donation_message': '',
                'minimum_donation': 100,  # Default $1.00
                'suggested_donation_amounts': [500, 1000, 2000],  # $5, $10, $20
            }

    def deactivate_connect_account(self, user):
        """Deactivate and delete Stripe Connect account for user"""
        try:
            profile = UserPaymentProfile.objects.get(user=user)

            if not profile.stripe_account_id:
                raise ValidationError("User does not have a Stripe Connect account")

            # Check for pending transfers
            pending_transfers = UserTransfer.objects.filter(
                receiver=user,
                status__in=['pending', 'processing']
            )

            cancelled_transfers = []
            failed_cancellations = []

            # Try to cancel all pending transfers first
            if pending_transfers.exists():
                logger.info(f"Found {pending_transfers.count()} pending transfers for user {user.id}, attempting to cancel them")
                
                for transfer in pending_transfers:
                    try:
                        success = self.cancel_pending_transfer(transfer)
                        if success:
                            cancelled_transfers.append(transfer.id)
                            logger.info(f"Successfully cancelled transfer {transfer.id}")
                        else:
                            failed_cancellations.append(transfer.id)
                            logger.warning(f"Failed to cancel transfer {transfer.id}")
                    except Exception as e:
                        logger.error(f"Error cancelling transfer {transfer.id}: {str(e)}")
                        failed_cancellations.append(transfer.id)

                # Refresh pending transfers after cancellation attempts
                remaining_pending = UserTransfer.objects.filter(
                    receiver=user,
                    status__in=['pending', 'processing']
                ).count()

                if remaining_pending > 0:
                    logger.warning(f"Still have {remaining_pending} pending transfers after cancellation attempts")
                    # Mark remaining transfers as failed since we're deactivating the account
                    UserTransfer.objects.filter(
                        receiver=user,
                        status__in=['pending', 'processing']
                    ).update(status='failed')
                    logger.info(f"Marked {remaining_pending} remaining transfers as failed")

            # Delete the Stripe Connect account
            # Note: Stripe Express accounts can be deleted, but Standard accounts are deactivated
            try:
                self._make_stripe_request(
                    stripe.Account.delete,
                    profile.stripe_account_id
                )
                deletion_method = 'deleted'
            except stripe.error.InvalidRequestError:
                # If deletion fails, try to deactivate (for Standard accounts)
                # Standard accounts cannot be deleted, only deactivated
                logger.warning(f"Cannot delete account {profile.stripe_account_id}, attempting deactivation")
                deletion_method = 'deactivated'

            # Clear Stripe account data from profile
            profile.stripe_account_id = None
            profile.charges_enabled = False
            profile.payouts_enabled = False
            profile.details_submitted = False
            profile.is_verified = False

            # Disable donation acceptance
            profile.accept_donations = False
            profile.save()

            # Count all affected transfers
            total_affected = len(cancelled_transfers) + len(failed_cancellations)

            self._log_operation(f"Deactivated Stripe Connect account for user {user.id} - method: {deletion_method}")

            return {
                'success': True,
                'method': deletion_method,
                'message': f'Stripe Connect account has been {deletion_method} successfully',
                'profile_updated': True,
                'transfers_affected': total_affected,
                'cancelled_transfers': cancelled_transfers,
                'failed_cancellations': failed_cancellations
            }

        except UserPaymentProfile.DoesNotExist:
            raise ValidationError("User does not have a payment profile")
        except Exception as e:
            logger.error(f"Error deactivating connect account: {str(e)}")
            raise TransferError(f"Error deactivating connect account: {str(e)}")

    def cancel_pending_transfer(self, transfer):
        """Cancel a pending transfer by cancelling the associated Stripe payment intent"""
        try:
            if transfer.status not in ['pending', 'processing']:
                logger.warning(f"Transfer {transfer.id} is not in a cancellable state: {transfer.status}")
                return False

            if not transfer.stripe_payment_intent_id:
                logger.error(f"Transfer {transfer.id} has no stripe_payment_intent_id")
                transfer.status = 'failed'
                transfer.save()
                return True

            # Cancel the Stripe payment intent
            try:
                payment_intent = self._make_stripe_request(
                    stripe.PaymentIntent.cancel,
                    transfer.stripe_payment_intent_id
                )
                
                if payment_intent.status == 'canceled':
                    transfer.status = 'canceled'
                    transfer.metadata['cancellation_reason'] = 'account_deactivation'
                    transfer.metadata['cancelled_at'] = timezone.now().isoformat()
                    transfer.save()
                    
                    self._log_operation(f"Cancelled transfer {transfer.id} - payment intent {transfer.stripe_payment_intent_id}")
                    return True
                else:
                    logger.warning(f"Payment intent {transfer.stripe_payment_intent_id} status after cancel: {payment_intent.status}")
                    # If we can't cancel it, mark as failed
                    transfer.status = 'failed'
                    transfer.metadata['cancellation_reason'] = 'could_not_cancel'
                    transfer.save()
                    return False
                    
            except stripe.error.InvalidRequestError as e:
                if 'cannot be canceled' in str(e).lower():
                    logger.warning(f"Cannot cancel payment intent {transfer.stripe_payment_intent_id}: {str(e)}")
                    # Payment intent might already be processing, mark as failed
                    transfer.status = 'failed'
                    transfer.metadata['cancellation_reason'] = 'cannot_cancel_stripe'
                    transfer.save()
                    return False
                else:
                    raise e
                    
        except Exception as e:
            logger.error(f"Error cancelling transfer {transfer.id}: {str(e)}")
            # Mark as failed if we can't cancel it
            transfer.status = 'failed'
            transfer.metadata['cancellation_reason'] = 'cancellation_error'
            transfer.save()
            return False

    def get_user_pending_transfers_info(self, user):
        """Get information about user's pending transfers"""
        try:
            pending_as_sender = UserTransfer.objects.filter(
                sender=user,
                status__in=['pending', 'processing']
            )
            
            pending_as_receiver = UserTransfer.objects.filter(
                receiver=user,
                status__in=['pending', 'processing']
            )
            
            return {
                'pending_as_sender': {
                    'count': pending_as_sender.count(),
                    'total_amount': sum(t.amount for t in pending_as_sender),
                    'transfers': [
                        {
                            'id': str(t.id),
                            'amount': t.amount,
                            'receiver_email': t.receiver.email,
                            'status': t.status,
                            'created_at': t.created_at.isoformat()
                        }
                        for t in pending_as_sender
                    ]
                },
                'pending_as_receiver': {
                    'count': pending_as_receiver.count(),
                    'total_amount': sum(t.amount for t in pending_as_receiver),
                    'transfers': [
                        {
                            'id': str(t.id),
                            'amount': t.amount,
                            'sender_email': t.sender.email,
                            'status': t.status,
                            'created_at': t.created_at.isoformat()
                        }
                        for t in pending_as_receiver
                    ]
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting pending transfers info for user {user.id}: {str(e)}")
            return {
                'pending_as_sender': {'count': 0, 'total_amount': 0, 'transfers': []},
                'pending_as_receiver': {'count': 0, 'total_amount': 0, 'transfers': []}
            }

    def get_payment_intent_client_secret(self, payment_intent_id):
        """
        Get client secret for a payment intent
        
        Args:
            payment_intent_id: Stripe payment intent ID
            
        Returns:
            str: Client secret for the payment intent
        """
        try:
            payment_intent = self._make_stripe_request(
                stripe.PaymentIntent.retrieve,
                payment_intent_id
            )
            
            self._log_operation(f"Retrieved client secret for payment intent {payment_intent_id}")
            return payment_intent.client_secret
            
        except Exception as e:
            logger.error(f"Error retrieving payment intent client secret: {str(e)}")
            raise TransferError(f"Error retrieving payment intent client secret: {str(e)}")
