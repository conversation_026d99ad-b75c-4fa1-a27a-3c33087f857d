"""
Distributed lock service for preventing race conditions in billing operations.
"""
import time
import uuid
import logging
from typing import Optional, Any
from django.core.cache import cache
from django.conf import settings
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class DistributedLockError(Exception):
    """Raised when distributed lock operations fail"""
    pass


class DistributedLockService:
    """
    Redis-based distributed lock service for preventing race conditions.
    
    Uses Redis SET with NX and EX options for atomic lock acquisition.
    """
    
    def __init__(self, default_timeout: int = 30, retry_delay: float = 0.1):
        self.default_timeout = default_timeout
        self.retry_delay = retry_delay
        
    def _get_lock_key(self, resource_id: str) -> str:
        """Generate lock key for resource"""
        return f"billing:lock:{resource_id}"
    
    def _get_lock_value(self) -> str:
        """Generate unique lock value"""
        return f"{uuid.uuid4().hex}:{time.time()}"
    
    def acquire_lock(self, resource_id: str, timeout: Optional[int] = None) -> Optional[str]:
        """
        Acquire distributed lock for resource.
        
        Args:
            resource_id: Unique identifier for the resource
            timeout: Lock timeout in seconds
            
        Returns:
            Lock value if acquired, None if failed
        """
        timeout = timeout or self.default_timeout
        lock_key = self._get_lock_key(resource_id)
        lock_value = self._get_lock_value()
        
        try:
            # Use Redis SET with NX (only if not exists) and EX (expiration)
            acquired = cache.set(lock_key, lock_value, timeout=timeout, nx=True)
            
            if acquired:
                logger.debug(f"Acquired lock for resource: {resource_id}")
                return lock_value
            else:
                logger.debug(f"Failed to acquire lock for resource: {resource_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error acquiring lock for {resource_id}: {str(e)}")
            raise DistributedLockError(f"Failed to acquire lock: {str(e)}")
    
    def release_lock(self, resource_id: str, lock_value: str) -> bool:
        """
        Release distributed lock.
        
        Args:
            resource_id: Resource identifier
            lock_value: Lock value returned by acquire_lock
            
        Returns:
            True if released, False if lock was already released or expired
        """
        lock_key = self._get_lock_key(resource_id)
        
        try:
            # Use Lua script for atomic compare-and-delete
            lua_script = """
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("DEL", KEYS[1])
            else
                return 0
            end
            """
            
            # Execute Lua script (this would need Redis client, using cache for now)
            current_value = cache.get(lock_key)
            if current_value == lock_value:
                cache.delete(lock_key)
                logger.debug(f"Released lock for resource: {resource_id}")
                return True
            else:
                logger.debug(f"Lock already released or expired for resource: {resource_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error releasing lock for {resource_id}: {str(e)}")
            return False
    
    @contextmanager
    def lock(self, resource_id: str, timeout: Optional[int] = None, 
             max_retries: int = 3, retry_delay: Optional[float] = None):
        """
        Context manager for distributed lock.
        
        Args:
            resource_id: Resource identifier
            timeout: Lock timeout in seconds
            max_retries: Maximum retry attempts
            retry_delay: Delay between retries
            
        Raises:
            DistributedLockError: If lock cannot be acquired
        """
        retry_delay = retry_delay or self.retry_delay
        lock_value = None
        
        # Try to acquire lock with retries
        for attempt in range(max_retries + 1):
            lock_value = self.acquire_lock(resource_id, timeout)
            if lock_value:
                break
                
            if attempt < max_retries:
                logger.debug(f"Lock acquisition attempt {attempt + 1} failed, retrying...")
                time.sleep(retry_delay)
            else:
                raise DistributedLockError(
                    f"Failed to acquire lock for {resource_id} after {max_retries + 1} attempts"
                )
        
        try:
            yield lock_value
        finally:
            if lock_value:
                self.release_lock(resource_id, lock_value)


# Global instance
distributed_lock = DistributedLockService()


# Usage example for customer service
def create_customer_with_lock(user):
    """Example of using distributed lock for customer creation"""
    resource_id = f"customer_creation:{user.id}"
    
    with distributed_lock.lock(resource_id, timeout=30):
        # Critical section - only one process can execute this
        customer, created = Customer.objects.get_or_create(user=user)
        if created:
            # Create Stripe customer
            stripe_customer = stripe.Customer.create(
                email=user.email,
                metadata={'user_id': str(user.id)}
            )
            customer.stripe_customer_id = stripe_customer.id
            customer.save()
        
        return customer
