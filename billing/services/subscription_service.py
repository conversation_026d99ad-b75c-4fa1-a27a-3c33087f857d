"""
Subscription management service for billing operations
"""
import stripe
import logging
from django.utils import timezone

from .base import BaseStripeService, SubscriptionManagerInterface
from ..models import Subscription, SubscriptionAccess, Customer
from ..exceptions import SubscriptionError, SubscriptionNotFoundError
from ..constants import SubscriptionStatus

logger = logging.getLogger(__name__)


class SubscriptionService(BaseStripeService, SubscriptionManagerInterface):
    """Service for managing subscriptions"""
    
    def create_subscription(self, customer, price, quantity=1, trial_period_days=None):
        """Create a subscription"""
        try:
            subscription_data = {
                'customer': customer.stripe_customer_id,
                'items': [{
                    'price': price.stripe_price_id,
                    'quantity': quantity
                }],
                'payment_behavior': 'default_incomplete',
                'expand': ['latest_invoice.payment_intent']
            }
            
            if trial_period_days:
                subscription_data['trial_period_days'] = trial_period_days
            
            stripe_subscription = self._make_stripe_request(
                stripe.Subscription.create,
                **subscription_data
            )
            
            # Create local subscription record
            subscription = Subscription.objects.create(
                customer=customer,
                stripe_subscription_id=stripe_subscription.id,
                price=price,
                status=stripe_subscription.status,
                current_period_start=timezone.datetime.fromtimestamp(stripe_subscription.current_period_start),
                current_period_end=timezone.datetime.fromtimestamp(stripe_subscription.current_period_end),
                trial_end=timezone.datetime.fromtimestamp(stripe_subscription.trial_end) if stripe_subscription.trial_end else None,
            )
            
            self._log_operation(f"Created subscription {subscription.id}")
            return subscription
            
        except Exception as e:
            logger.error(f"Error creating subscription: {str(e)}")
            raise SubscriptionError(f"Error creating subscription: {str(e)}")
    
    def update_subscription(self, subscription_id, **kwargs):
        """Update a subscription"""
        try:
            subscription = self.get_subscription_by_id(subscription_id)
            
            # Update in Stripe
            stripe_subscription = self._make_stripe_request(
                stripe.Subscription.modify,
                subscription.stripe_subscription_id,
                **kwargs
            )
            
            # Update local record
            subscription.status = stripe_subscription.status
            subscription.current_period_start = timezone.datetime.fromtimestamp(stripe_subscription.current_period_start)
            subscription.current_period_end = timezone.datetime.fromtimestamp(stripe_subscription.current_period_end)
            subscription.save()
            
            self._log_operation(f"Updated subscription {subscription.id}")
            return subscription
            
        except Exception as e:
            logger.error(f"Error updating subscription: {str(e)}")
            raise SubscriptionError(f"Error updating subscription: {str(e)}")
    
    def cancel_subscription(self, subscription_id, at_period_end=True):
        """Cancel a subscription"""
        try:
            subscription = self.get_subscription_by_id(subscription_id)
            
            if at_period_end:
                stripe_subscription = self._make_stripe_request(
                    stripe.Subscription.modify,
                    subscription.stripe_subscription_id,
                    cancel_at_period_end=True
                )
                subscription.cancel_at_period_end = True
            else:
                stripe_subscription = self._make_stripe_request(
                    stripe.Subscription.delete,
                    subscription.stripe_subscription_id
                )
                subscription.status = SubscriptionStatus.CANCELED
                subscription.canceled_at = timezone.now()
            
            subscription.save()
            
            self._log_operation(f"Canceled subscription {subscription.id}")
            return subscription
            
        except Exception as e:
            logger.error(f"Error canceling subscription: {str(e)}")
            raise SubscriptionError(f"Error canceling subscription: {str(e)}")
    
    def get_subscription_by_id(self, subscription_id):
        """Get subscription by ID"""
        try:
            return Subscription.objects.get(id=subscription_id)
        except Subscription.DoesNotExist:
            raise SubscriptionNotFoundError(f"Subscription not found: {subscription_id}")
    
    def get_subscription_by_stripe_id(self, stripe_subscription_id):
        """Get subscription by Stripe ID"""
        try:
            return Subscription.objects.get(stripe_subscription_id=stripe_subscription_id)
        except Subscription.DoesNotExist:
            raise SubscriptionNotFoundError(f"Subscription not found for Stripe ID: {stripe_subscription_id}")
    
    def handle_subscription_created(self, stripe_subscription):
        """Handle subscription created webhook"""
        try:
            customer = Customer.objects.get(stripe_customer_id=stripe_subscription.customer)

            # Get price from subscription items
            price = None
            if hasattr(stripe_subscription, 'items') and stripe_subscription.items.data:
                stripe_price_id = stripe_subscription.items.data[0].price.id

                # Get the Price object from our database
                from ..models.product import Price
                try:
                    price = Price.objects.get(stripe_price_id=stripe_price_id)
                except Price.DoesNotExist:
                    logger.error(f"Price not found for stripe_price_id {stripe_price_id}")
                    return False

            if not price:
                logger.error(f"No price found in subscription items for subscription {stripe_subscription.id}")
                return False

            subscription = Subscription.objects.create(
                customer=customer,
                stripe_subscription_id=stripe_subscription.id,
                price=price,
                status=stripe_subscription.status,
                current_period_start=timezone.datetime.fromtimestamp(stripe_subscription.current_period_start),
                current_period_end=timezone.datetime.fromtimestamp(stripe_subscription.current_period_end),
                cancel_at_period_end=stripe_subscription.cancel_at_period_end,
                canceled_at=timezone.datetime.fromtimestamp(stripe_subscription.canceled_at) if stripe_subscription.canceled_at else None,
                trial_end=timezone.datetime.fromtimestamp(stripe_subscription.trial_end) if stripe_subscription.trial_end else None,
            )

            self._log_operation(f"Created subscription record for Stripe subscription {stripe_subscription.id}")
            return True

        except Exception as e:
            logger.error(f"Error handling subscription created: {str(e)}")
            return False
    
    def handle_subscription_updated(self, stripe_subscription):
        """Handle subscription updated webhook"""
        try:
            Subscription.objects.filter(
                stripe_subscription_id=stripe_subscription.id
            ).update(
                status=stripe_subscription.status,
                current_period_start=timezone.datetime.fromtimestamp(stripe_subscription.current_period_start),
                current_period_end=timezone.datetime.fromtimestamp(stripe_subscription.current_period_end),
                cancel_at_period_end=stripe_subscription.cancel_at_period_end,
                canceled_at=timezone.datetime.fromtimestamp(stripe_subscription.canceled_at) if stripe_subscription.canceled_at else None,
                trial_end=timezone.datetime.fromtimestamp(stripe_subscription.trial_end) if stripe_subscription.trial_end else None,
            )
            
            self._log_operation(f"Updated subscription record for Stripe subscription {stripe_subscription.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling subscription updated: {str(e)}")
            return False
    
    def handle_subscription_deleted(self, stripe_subscription):
        """Handle subscription deleted webhook"""
        try:
            Subscription.objects.filter(
                stripe_subscription_id=stripe_subscription.id
            ).update(
                status=SubscriptionStatus.CANCELED,
                canceled_at=timezone.now()
            )
            
            self._log_operation(f"Updated subscription record for deleted Stripe subscription {stripe_subscription.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling subscription deleted: {str(e)}")
            return False
    
    def handle_subscription_quantity_updated(self, stripe_subscription):
        """Handle subscription quantity updates"""
        # This will be implemented when we add the SolutionAccess handling
        logger.info(f"Subscription quantity updated for {stripe_subscription.id}")
        return True
    
    def handle_invoice_payment_succeeded(self, invoice):
        """Handle successful invoice payments"""
        # This will be implemented when we add the invoice handling
        logger.info(f"Invoice payment succeeded for {invoice.id}")
        return True
    
    def handle_invoice_payment_failed(self, invoice):
        """Handle failed invoice payments"""
        # This will be implemented when we add the invoice handling
        logger.info(f"Invoice payment failed for {invoice.id}")
        return True
