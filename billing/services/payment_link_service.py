"""
Payment link service that integrates with existing billing system
"""
import logging
import stripe
from django.conf import settings
from django.shortcuts import get_object_or_404
from django.utils import timezone

from .base import StripeService
from ..models import UserPaymentProfile, Customer
from accounts.models import CustomUser
from appointments.models import Appointment
from ..providers.service_provider_registry import get_service_provider, get_metadata_provider
from ..services.error_handling_service import EdgeCaseHandler
from ..exceptions import (
    ValidationError, PaymentError, ServiceNotFoundError,
    PermissionDeniedError, ConfigurationError
)

logger = logging.getLogger(__name__)


class PaymentLinkService(StripeService):
    """Service for generating payment links using existing billing infrastructure"""

    @EdgeCaseHandler.handle_stripe_api_error
    @EdgeCaseHandler.handle_database_error
    @EdgeCaseHandler.retry_on_failure(max_retries=2)
    def generate_appointment_payment_link(self, appointment_id, amount=None, user=None):
        """Generate payment link for appointment using existing Service model"""
        # Input validation
        EdgeCaseHandler.validate_user_id(appointment_id)
        if user:
            EdgeCaseHandler.validate_user_id(user.id)
        if amount is not None:
            EdgeCaseHandler.validate_amount(amount)
        
        # Check configuration
        EdgeCaseHandler.check_configuration()
        
        # Rate limiting
        if user:
            EdgeCaseHandler.check_rate_limit(str(user.id), 'payment_link_generation', max_per_minute=5)
        
        # Concurrent operation check
        operation_key = f"appointment_payment_{appointment_id}"
        EdgeCaseHandler.validate_concurrent_operation(str(user.id) if user else 'anonymous', operation_key)
        
        try:
            appointment = get_object_or_404(Appointment, id=appointment_id)
            
            # Enhanced permission verification
            if user:
                if appointment.patient != user and appointment.doctor != user:
                    raise PermissionDeniedError("Permission denied for this appointment")
                
                # Additional checks for patient vs doctor access
                if appointment.patient == user and appointment.status == 'cancelled':
                    raise ValidationError("Cannot create payment link for cancelled appointment")
            
            # Enhanced doctor payment profile validation
            doctor_profile = UserPaymentProfile.objects.filter(user=appointment.doctor).first()
            if not doctor_profile:
                raise ServiceNotFoundError("Doctor payment account not found")
            
            if not doctor_profile.can_receive_payments():
                if not doctor_profile.charges_enabled:
                    raise ConfigurationError("Doctor payment account not enabled for receiving payments")
                elif not doctor_profile.details_submitted:
                    raise ConfigurationError("Doctor payment account setup incomplete")
                elif not doctor_profile.is_verified:
                    raise ConfigurationError("Doctor payment account not verified")
                else:
                    raise ConfigurationError("Doctor payment account not setup or not verified")
            
            # Get consultation service through service provider interface with validation
            service_provider = get_service_provider()
            if not service_provider:
                raise ConfigurationError("Service provider not configured")
                
            consultation_service = service_provider.get_service_by_type('consultation')
            if not consultation_service:
                raise ServiceNotFoundError("Consultation service not configured")
            
            # Validate service data structure
            EdgeCaseHandler.validate_service_data(consultation_service)
            
            # Enhanced pricing calculation with validation
            if not amount:
                pricing = service_provider.get_service_pricing(consultation_service['id'])
                if not pricing:
                    raise ServiceNotFoundError("Service pricing not available")
                
                discounted_price = EdgeCaseHandler.safe_dict_get(
                    pricing, 'discounted_price', 
                    default=EdgeCaseHandler.safe_dict_get(pricing, 'base_price', default=0),
                    required=True
                )
                
                if discounted_price <= 0:
                    raise ValidationError("Service pricing must be greater than zero")
                
                amount = int(float(discounted_price) * 100)  # Convert to cents
            else:
                # Validate provided amount
                amount = int(float(EdgeCaseHandler.validate_amount(amount / 100)) * 100)
            
            # Enhanced customer creation with validation
            if not appointment.patient:
                raise ValidationError("Appointment patient not found")
            
            customer = StripeService.create_or_update_customer(appointment.patient)
            if not customer or not getattr(customer, 'stripe_customer_id', None):
                raise PaymentError("Failed to create or retrieve customer")
            
            # Enhanced Stripe price ID validation
            stripe_price_id = EdgeCaseHandler.safe_dict_get(
                consultation_service, 'stripe_price_id', required=True
            )
            EdgeCaseHandler.validate_stripe_price_id(stripe_price_id)
            
            # Enhanced metadata building with validation
            metadata_provider = get_metadata_provider()
            
            base_metadata = {
                'appointment_id': str(appointment_id),
                'doctor_id': str(appointment.doctor.id),
                'patient_id': str(appointment.patient.id),
                'payment_type': 'appointment',
                'service_id': str(consultation_service['id']),
                'amount_cents': str(amount),
                'created_at': str(timezone.now().isoformat())
            }
            
            if metadata_provider:
                try:
                    provider_metadata = metadata_provider.build_payment_metadata(
                        'appointment',
                        appointment_id=appointment_id,
                        doctor_id=appointment.doctor.id,
                        patient_id=appointment.patient.id,
                        service_id=consultation_service['id']
                    )
                    # Merge with base metadata
                    metadata = {**base_metadata, **provider_metadata}
                except Exception as e:
                    logger.warning(f"Metadata provider failed, using base metadata: {str(e)}")
                    metadata = base_metadata
            else:
                metadata = base_metadata
            
            # Validate and sanitize metadata
            metadata = EdgeCaseHandler.validate_metadata(metadata)
            
            # Enhanced checkout session creation with validation
            try:
                session = stripe.checkout.Session.create(
                    customer=customer.stripe_customer_id,
                    payment_method_types=['card'],
                    line_items=[{
                        'price': stripe_price_id,
                        'quantity': 1,
                    }],
                    mode='payment',
                    success_url=f"{settings.FRONTEND_URL}/payment/success?session_id={{CHECKOUT_SESSION_ID}}",
                    cancel_url=f"{settings.FRONTEND_URL}/payment/cancel",
                    metadata=metadata,
                    expires_at=int(timezone.now().timestamp()) + 3600  # 1 hour expiry
                )
            except Exception as e:
                logger.error(f"Failed to create Stripe checkout session: {str(e)}")
                raise PaymentError(f"Failed to create payment session: {str(e)}")
            
            # Validate session creation
            if not session or not session.url:
                raise PaymentError("Invalid payment session created")
            
            self._log_operation(f"Generated appointment payment link for appointment {appointment_id}")
            
            # Enhanced response with validation
            doctor_name = EdgeCaseHandler.safe_get_attribute(
                appointment.doctor, 'get_full_name', 
                default=lambda: f"Doctor {appointment.doctor.id}"
            )
            
            service_name = EdgeCaseHandler.safe_dict_get(
                consultation_service, 'name', 
                default="Medical Consultation"
            )
            
            appointment_date = EdgeCaseHandler.safe_get_attribute(
                appointment, 'start_time',
                default=timezone.now()
            )
            
            return {
                'payment_url': session.url,
                'session_id': session.id,
                'amount': amount,
                'service_name': service_name,
                'doctor_name': doctor_name() if callable(doctor_name) else doctor_name,
                'appointment_date': appointment_date.strftime('%Y-%m-%d %H:%M'),
                'expires_at': session.expires_at
            }
            
        except (ValidationError, PaymentError, ServiceNotFoundError, 
                PermissionDeniedError, ConfigurationError) as e:
            # Re-raise known exceptions
            raise
        except Exception as e:
            logger.error(f"Unexpected error generating appointment payment link: {str(e)}")
            raise PaymentError(f"Failed to generate payment link: {str(e)}")
        finally:
            # Always release the operation lock
            if user:
                EdgeCaseHandler.release_operation_lock(str(user.id), operation_key)

    def generate_consultation_payment_link(self, doctor_id, consultation_type='general', amount=None, user=None):
        """Generate payment link for doctor consultation"""
        try:
            doctor = get_object_or_404(CustomUser, id=doctor_id)
            
            # Check if doctor can receive payments
            doctor_profile = UserPaymentProfile.objects.filter(user=doctor).first()
            if not doctor_profile or not doctor_profile.can_receive_payments():
                raise ValueError("Doctor payment account not setup or not verified")
            
            # Get consultation service based on type
            service_filter = {'service_type': 'CONSULTATION', 'is_active': True}
            if consultation_type == 'telemedicine':
                service_filter['service_code'] = 'telemedicine'
            elif consultation_type == 'basic_monitoring':
                service_filter['service_code'] = 'basic_monitoring'
            elif consultation_type == 'monthly_care':
                service_filter['service_code'] = 'monthly_care'
            elif consultation_type == 'premium_care':
                service_filter['service_code'] = 'premium_care'
            elif consultation_type == 'emergency':
                service_filter['service_code'] = 'emergency'
            
            consultation_service = Service.objects.filter(**service_filter).first()
            
            if not consultation_service:
                # Fallback to any consultation service
                consultation_service = Service.objects.filter(
                    service_type='CONSULTATION',
                    is_active=True
                ).first()
            
            if not consultation_service:
                raise ValueError("Consultation service not configured")
            
            # Use service pricing if amount not specified
            if not amount:
                amount = int(consultation_service.get_discounted_price() * 100)
            
            # Get or create customer
            customer = StripeService.create_or_update_customer(user)
            
            # Get Stripe info
            stripe_info = StripeService.get_service_stripe_info(consultation_service)
            
            if not stripe_info or not stripe_info.get('stripe_price_id'):
                raise ValueError("Service not properly configured in Stripe")
            
            # Create checkout session
            session = StripeService.create_checkout_session(
                customer=customer,
                price=stripe_info['stripe_price_id'],
                success_url=f"{settings.FRONTEND_URL}/payment/success?session_id={{CHECKOUT_SESSION_ID}}",
                cancel_url=f"{settings.FRONTEND_URL}/payment/cancel",
                metadata={
                    'doctor_id': str(doctor_id),
                    'patient_id': str(user.id),
                    'consultation_type': consultation_type,
                    'payment_type': 'consultation',
                    'service_id': str(consultation_service.id)
                },
                mode='payment'
            )
            
            self._log_operation(f"Generated consultation payment link for doctor {doctor_id}")
            
            return {
                'payment_url': session.url,
                'session_id': session.id,
                'amount': amount,
                'service_name': consultation_service.name,
                'doctor_name': doctor.get_full_name(),
                'consultation_type': consultation_type
            }
            
        except Exception as e:
            logger.error(f"Error generating consultation payment link: {str(e)}")
            raise

    def get_donation_link_info(self, user_id):
        """Get donation link information for a user"""
        try:
            user = get_object_or_404(CustomUser, id=user_id)
            profile = UserPaymentProfile.objects.filter(user=user).first()
            
            if not profile or not profile.accept_donations:
                raise ValueError("User does not accept donations")
            
            if not profile.can_receive_donations():
                raise ValueError("User donation account not verified")
            
            return {
                'donation_url': f"{settings.FRONTEND_URL}/donate/{user_id}",
                'user_name': user.get_full_name(),
                'donation_message': profile.donation_message,
                'minimum_amount': profile.minimum_donation,
                'suggested_amounts': profile.suggested_donation_amounts,
                'can_receive_donations': True,
                'user_id': str(user_id)
            }
            
        except Exception as e:
            logger.error(f"Error getting donation link info: {str(e)}")
            raise

    def create_donation_checkout_session(self, receiver_id, amount, sender_user):
        """Create checkout session for donation using existing transfer system"""
        try:
            receiver = get_object_or_404(CustomUser, id=receiver_id)
            profile = UserPaymentProfile.objects.filter(user=receiver).first()
            
            if not profile or not profile.can_receive_donations():
                raise ValueError("User cannot receive donations")
            
            if amount < profile.minimum_donation:
                raise ValueError(f"Amount must be at least ${profile.minimum_donation/100:.2f}")
            
            # Create customer
            customer = StripeService.create_or_update_customer(sender_user)
            
            # Create Stripe checkout session for donation
            session = stripe.checkout.Session.create(
                customer=customer.stripe_customer_id,
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': 'usd',
                        'product_data': {
                            'name': f'Donation to {receiver.get_full_name()}',
                            'description': profile.donation_message or 'Support this user',
                        },
                        'unit_amount': amount,
                    },
                    'quantity': 1,
                }],
                mode='payment',
                success_url=f"{settings.FRONTEND_URL}/donation/success?session_id={{CHECKOUT_SESSION_ID}}",
                cancel_url=f"{settings.FRONTEND_URL}/donation/cancel",
                metadata={
                    'sender_id': str(sender_user.id),
                    'receiver_id': str(receiver_id),
                    'payment_type': 'donation',
                    'transfer_type': 'donation'
                }
            )
            
            self._log_operation(f"Created donation session for {receiver_id}")
            
            return {
                'payment_url': session.url,
                'session_id': session.id,
                'amount': amount,
                'receiver_name': receiver.get_full_name()
            }
            
        except Exception as e:
            logger.error(f"Error creating donation session: {str(e)}")
            raise 