"""
Unified payment service for handling all types of payments in a single interface.
"""
import logging
from typing import Dict, Any, Optional, Union
from django.conf import settings
from django.shortcuts import get_object_or_404

from .base import StripeService
from .customer_service import CustomerService
from .payment_service import PaymentService
from .subscription_service import SubscriptionService
from .user_transfer_service import UserTransferService
from .payment_link_service import PaymentLinkService
from ..models import Product, Price, PlatformFee, EnterpriseAccount
from ..constants import PaymentType
from ..exceptions import BillingError

logger = logging.getLogger(__name__)


class UnifiedPaymentService(StripeService):
    """
    Unified service for handling all payment types through a single interface.
    
    This service provides a consistent API for creating payment sessions
    regardless of the underlying payment type (subscriptions, one-time payments,
    transfers, etc.).
    """
    
    def __init__(self):
        super().__init__()
        self.customer_service = CustomerService()
        self.payment_service = PaymentService()
        self.subscription_service = SubscriptionService()
        self.user_transfer_service = UserTransferService()
        self.payment_link_service = PaymentLinkService()

    def create_payment_session(self, payment_type: str, **kwargs) -> Dict[str, Any]:
        """
        Create a payment session based on payment type.
        
        Args:
            payment_type: Type of payment (subscription, one_time, transfer, etc.)
            **kwargs: Payment-specific parameters
            
        Returns:
            Dictionary containing session details
        """
        try:
            payment_type_enum = PaymentType(payment_type)
            
            if payment_type_enum == PaymentType.SUBSCRIPTION:
                return self._create_subscription_session(**kwargs)
            elif payment_type_enum == PaymentType.ONE_TIME:
                return self._create_one_time_session(**kwargs)
            elif payment_type_enum == PaymentType.USER_TRANSFER:
                return self._create_user_transfer_session(**kwargs)
            elif payment_type_enum == PaymentType.APPOINTMENT:
                return self._create_appointment_session(**kwargs)
            else:
                raise BillingError(f"Unsupported payment type: {payment_type}")
                
        except ValueError:
            raise BillingError(f"Invalid payment type: {payment_type}")
        except Exception as e:
            logger.error(f"Error creating payment session: {str(e)}")
            raise BillingError(f"Failed to create payment session: {str(e)}")

    def _create_subscription_session(self, customer, price, success_url, cancel_url, metadata=None):
        """Create session for subscription payments"""
        session = self.subscription_service.create_subscription_session(
            customer=customer,
            price=price,
            success_url=success_url,
            cancel_url=cancel_url,
            metadata=metadata or {}
        )
        
        return {
            'session_id': session.id,
            'checkout_url': session.url,
            'payment_type': PaymentType.SUBSCRIPTION.value,
            'subscription_id': None,  # Will be set after successful payment
            'amount': price.unit_amount,
            'currency': price.currency,
            'interval': price.recurring.get('interval') if price.recurring else None
        }
    
    def _create_one_time_session(self, customer, price, success_url, cancel_url, metadata=None):
        """Create session for one-time payments"""
        session = self.payment_service.create_checkout_session(
            customer=customer,
            price=price,
            success_url=success_url,
            cancel_url=cancel_url,
            metadata=metadata or {},
            mode='payment'
        )
        
        return {
            'session_id': session.id,
            'checkout_url': session.url,
            'payment_type': PaymentType.ONE_TIME.value,
            'amount': price.unit_amount,
            'currency': price.currency
        }
    
    def _create_user_transfer_session(self, from_user, to_user, amount, success_url, cancel_url, metadata=None):
        """Create session for user-to-user transfers"""
        result = self.user_transfer_service.create_transfer_payment(
            from_user=from_user,
            to_user=to_user,
            amount=amount,
            success_url=success_url,
            cancel_url=cancel_url,
            metadata=metadata or {}
        )
        
        return {
            'session_id': result['session_id'],
            'checkout_url': result['checkout_url'],
            'payment_type': PaymentType.USER_TRANSFER.value,
            'transfer_id': result.get('transfer_id'),
            'amount': amount,
            'currency': 'usd',
            'from_user_id': str(from_user.id),
            'to_user_id': str(to_user.id)
        }
    
    def _create_appointment_session(self, appointment_id, amount=None, user=None):
        """Create session for appointment payments"""
        result = self.payment_link_service.generate_appointment_payment_link(
            appointment_id=appointment_id,
            amount=amount,
            user=user
        )
        
        return {
            'session_id': result['session_id'],
            'checkout_url': result['checkout_url'],
            'payment_type': PaymentType.APPOINTMENT.value,
            'appointment_id': appointment_id,
            'amount': result.get('amount'),
            'currency': 'usd'
        }

    def process_webhook_event(self, event_type: str, event_data: Dict[str, Any]) -> bool:
        """
        Process webhook events for all payment types.
        
        Args:
            event_type: Stripe event type
            event_data: Event data from Stripe
            
        Returns:
            True if event was processed successfully
        """
        try:
            # Delegate to appropriate service based on event type
            if event_type.startswith('customer.subscription'):
                return self.subscription_service.handle_webhook_event(event_type, event_data)
            elif event_type.startswith('payment_intent'):
                return self.payment_service.handle_webhook_event(event_type, event_data)
            elif event_type.startswith('checkout.session'):
                # Determine payment type from session metadata
                session = event_data.get('data', {}).get('object', {})
                metadata = session.get('metadata', {})
                payment_type = metadata.get('payment_type')
                
                if payment_type == PaymentType.USER_TRANSFER.value:
                    return self.user_transfer_service.handle_webhook_event(event_type, event_data)
                elif payment_type == PaymentType.APPOINTMENT.value:
                    return self.payment_link_service.handle_webhook_event(event_type, event_data)
                else:
                    return self.payment_service.handle_webhook_event(event_type, event_data)
            else:
                logger.warning(f"Unhandled webhook event type: {event_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error processing webhook event {event_type}: {str(e)}")
            return False

    def get_payment_status(self, session_id: str) -> Dict[str, Any]:
        """
        Get payment status for any payment type.
        
        Args:
            session_id: Stripe checkout session ID
            
        Returns:
            Payment status information
        """
        try:
            # Retrieve session from Stripe
            import stripe
            stripe.api_key = settings.STRIPE_SECRET_KEY
            session = stripe.checkout.Session.retrieve(session_id)
            
            metadata = session.get('metadata', {})
            payment_type = metadata.get('payment_type', PaymentType.ONE_TIME.value)
            
            base_status = {
                'session_id': session_id,
                'payment_status': session.payment_status,
                'payment_type': payment_type,
                'amount_total': session.amount_total,
                'currency': session.currency
            }
            
            # Add payment-type specific information
            if payment_type == PaymentType.SUBSCRIPTION.value:
                subscription_id = session.subscription
                if subscription_id:
                    base_status['subscription_id'] = subscription_id
            elif payment_type == PaymentType.USER_TRANSFER.value:
                base_status.update({
                    'from_user_id': metadata.get('from_user_id'),
                    'to_user_id': metadata.get('to_user_id'),
                    'transfer_id': metadata.get('transfer_id')
                })
            elif payment_type == PaymentType.APPOINTMENT.value:
                base_status['appointment_id'] = metadata.get('appointment_id')
            
            return base_status
            
        except Exception as e:
            logger.error(f"Error getting payment status for session {session_id}: {str(e)}")
            raise BillingError(f"Failed to get payment status: {str(e)}")

    def cancel_payment(self, session_id: str) -> bool:
        """
        Cancel a payment session if possible.
        
        Args:
            session_id: Stripe checkout session ID
            
        Returns:
            True if cancellation was successful
        """
        try:
            # Note: Stripe checkout sessions cannot be directly cancelled
            # This method would handle any cleanup needed
            logger.info(f"Payment cancellation requested for session {session_id}")
            
            # Check if session is still pending
            status = self.get_payment_status(session_id)
            if status['payment_status'] == 'unpaid':
                # Session is still pending, no action needed
                return True
            else:
                logger.warning(f"Cannot cancel session {session_id} with status {status['payment_status']}")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling payment session {session_id}: {str(e)}")
            return False 