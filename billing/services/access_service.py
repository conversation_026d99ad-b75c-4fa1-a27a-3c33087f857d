"""
Access control service for billing operations.

This service handles all access-related business logic including checking permissions,
granting access, revoking access, and managing access lifecycles.
"""
import logging
from django.utils import timezone
from typing import Dict, List, Any, Optional

from .base import BaseStripeService
from ..models import ServiceAccess, SubscriptionAccess, SolutionAccess, SolutionUserAccess
from ..exceptions import AccessError, ValidationError
from ..constants import AccessStatus

logger = logging.getLogger(__name__)


class AccessService(BaseStripeService):
    """Service for handling access control operations"""
    
    def check_service_access(self, user, service_id) -> Dict[str, Any]:
        """Check if a user has access to a specific service"""
        try:
            self._log_operation(f"Checking service access for user {user.id} and service {service_id}")
            
            # Get the service access record
            access = ServiceAccess.objects.filter(
                user=user,
                service_id=service_id,
                status=AccessStatus.ACTIVE
            ).first()
            
            if not access:
                return {
                    'has_access': False,
                    'message': 'No active access found for this service',
                    'status_code': 404
                }
            
            # Check if access has expired
            if self._is_access_expired(access):
                access.status = AccessStatus.EXPIRED
                access.save()
                return {
                    'has_access': False,
                    'message': 'Access has expired',
                    'expired_at': access.access_expires_at,
                    'status_code': 403
                }
            
            # Update last accessed timestamp
            access.last_accessed_at = timezone.now()
            access.save(update_fields=['last_accessed_at'])
            
            return {
                'has_access': True,
                'access_granted_at': access.access_granted_at,
                'access_expires_at': access.access_expires_at,
                'analysis_access_data': access.analysis_access_data,
                'status_code': 200
            }
            
        except Exception as e:
            logger.error(f"Error checking service access: {str(e)}")
            raise AccessError(f"Error checking service access: {str(e)}")
    
    def check_subscription_access(self, user, subscription_id) -> Dict[str, Any]:
        """Check if a user has access to a specific subscription"""
        try:
            self._log_operation(f"Checking subscription access for user {user.id} and subscription {subscription_id}")
            
            # Get the subscription access record
            access = SubscriptionAccess.objects.filter(
                user=user,
                subscription_id=subscription_id,
                status=AccessStatus.ACTIVE
            ).select_related('subscription').first()
            
            if not access:
                return {
                    'has_access': False,
                    'message': 'No active access found for this subscription',
                    'status_code': 404
                }
            
            # Sync with subscription status
            access.sync_with_subscription()
            
            return {
                'has_access': True,
                'access_granted_at': access.access_granted_at,
                'subscription_status': access.subscription.status,
                'current_period_end': access.subscription.current_period_end,
                'status_code': 200
            }
            
        except Exception as e:
            logger.error(f"Error checking subscription access: {str(e)}")
            raise AccessError(f"Error checking subscription access: {str(e)}")
    
    def check_solution_access(self, user, solution_id) -> Dict[str, Any]:
        """Check if an enterprise has access to a specific solution"""
        try:
            self._log_operation(f"Checking solution access for user {user.id} and solution {solution_id}")
            
            # Check if user is associated with an enterprise
            if not hasattr(user, 'enterprise') or not user.enterprise:
                return {
                    'has_access': False,
                    'message': 'User is not associated with an enterprise',
                    'status_code': 403
                }
            
            enterprise = user.enterprise
            
            # Get the solution access record
            access = SolutionAccess.objects.filter(
                enterprise=enterprise,
                solution_id=solution_id,
                status=AccessStatus.ACTIVE
            ).select_related('subscription').first()
            
            if not access:
                return {
                    'has_access': False,
                    'message': 'No active access found for this solution',
                    'status_code': 404
                }
            
            # Check user's individual access within the solution
            user_access = SolutionUserAccess.objects.filter(
                solution_access=access,
                user=user,
                status=AccessStatus.ACTIVE
            ).first()
            
            if not user_access:
                return {
                    'has_access': False,
                    'message': 'User does not have individual access to this solution',
                    'status_code': 403
                }
            
            # Sync with subscription status
            access.sync_with_subscription()
            
            return {
                'has_access': True,
                'access_granted_at': access.access_granted_at,
                'subscription_status': access.subscription.status if access.subscription else None,
                'current_period_end': access.subscription.current_period_end if access.subscription else None,
                'user_role': user_access.role,
                'status_code': 200
            }
            
        except Exception as e:
            logger.error(f"Error checking solution access: {str(e)}")
            raise AccessError(f"Error checking solution access: {str(e)}")
    
    def list_user_services(self, user) -> Dict[str, List[Dict[str, Any]]]:
        """List all services that a user has access to"""
        try:
            self._log_operation(f"Listing services for user {user.id}")
            
            # Get all active service access records
            service_access = ServiceAccess.objects.filter(
                user=user,
                status=AccessStatus.ACTIVE
            ).select_related('service')
            
            # Get all active subscription access records
            subscription_access = SubscriptionAccess.objects.filter(
                user=user,
                status=AccessStatus.ACTIVE
            ).select_related('subscription', 'subscription__price', 'subscription__price__product')
            
            # Format service access data
            services = []
            for access in service_access:
                if self._is_access_expired(access):
                    access.status = AccessStatus.EXPIRED
                    access.save()
                    continue
                    
                services.append({
                    'id': access.service.id,
                    'name': access.service.name,
                    'type': 'service',
                    'access_granted_at': access.access_granted_at,
                    'access_expires_at': access.access_expires_at,
                    'analysis_access_data': access.analysis_access_data
                })
            
            # Format subscription access data
            subscriptions = []
            for access in subscription_access:
                # Sync with subscription status
                access.sync_with_subscription()
                
                subscriptions.append({
                    'id': access.subscription.price.product.id,
                    'name': access.subscription.price.product.name,
                    'type': 'subscription',
                    'access_granted_at': access.access_granted_at,
                    'subscription_status': access.subscription.status,
                    'current_period_end': access.subscription.current_period_end
                })
            
            return {
                'services': services,
                'subscriptions': subscriptions
            }
            
        except Exception as e:
            logger.error(f"Error listing user services: {str(e)}")
            raise AccessError(f"Error listing user services: {str(e)}")
    
    def list_enterprise_solutions(self, user) -> Dict[str, List[Dict[str, Any]]]:
        """List all solutions that an enterprise has access to"""
        try:
            self._log_operation(f"Listing enterprise solutions for user {user.id}")
            
            # Check if user is associated with an enterprise
            if not hasattr(user, 'enterprise') or not user.enterprise:
                raise AccessError('User is not associated with an enterprise')
            
            enterprise = user.enterprise
            
            # Get all active solution access records
            solution_access = SolutionAccess.objects.filter(
                enterprise=enterprise,
                status=AccessStatus.ACTIVE
            ).select_related('solution', 'subscription')
            
            # Format solution access data
            solutions = []
            for access in solution_access:
                # Sync with subscription status
                access.sync_with_subscription()
                
                solutions.append({
                    'id': access.solution.id,
                    'name': access.solution.name,
                    'access_granted_at': access.access_granted_at,
                    'subscription_status': access.subscription.status if access.subscription else None,
                    'current_period_end': access.subscription.current_period_end if access.subscription else None,
                    'total_seats': access.total_seats,
                    'used_seats': access.used_seats,
                    'available_seats': access.available_seats
                })
            
            return {
                'solutions': solutions
            }
            
        except Exception as e:
            logger.error(f"Error listing enterprise solutions: {str(e)}")
            raise AccessError(f"Error listing enterprise solutions: {str(e)}")
    
    def revoke_service_access(self, user, service_id, reason: str = '') -> bool:
        """Revoke access to a service"""
        try:
            self._log_operation(f"Revoking service access for user {user.id} and service {service_id}")
            
            # Get service access
            access = ServiceAccess.objects.filter(
                service_id=service_id,
                user=user,
                status=AccessStatus.ACTIVE
            ).first()
            
            if not access:
                raise AccessError('No active service access found')
            
            # Revoke access
            access.revoke_access(reason)
            
            self._log_operation(f"Successfully revoked service access {access.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error revoking service access: {str(e)}")
            raise AccessError(f"Error revoking service access: {str(e)}")
    
    def revoke_subscription_access(self, user, subscription_id, reason: str = '') -> bool:
        """Revoke access to a subscription"""
        try:
            self._log_operation(f"Revoking subscription access for user {user.id} and subscription {subscription_id}")
            
            # Get subscription access
            access = SubscriptionAccess.objects.filter(
                subscription_id=subscription_id,
                user=user,
                status=AccessStatus.ACTIVE
            ).first()
            
            if not access:
                raise AccessError('No active subscription access found')
            
            # Revoke access
            access.revoke_access(reason)
            
            self._log_operation(f"Successfully revoked subscription access {access.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error revoking subscription access: {str(e)}")
            raise AccessError(f"Error revoking subscription access: {str(e)}")
    
    def revoke_solution_access(self, user, solution_id, reason: str = '') -> bool:
        """Revoke access to a solution"""
        try:
            self._log_operation(f"Revoking solution access for user {user.id} and solution {solution_id}")
            
            # Check if user is associated with an enterprise
            if not hasattr(user, 'enterprise') or not user.enterprise:
                raise AccessError('User is not associated with an enterprise')
            
            enterprise = user.enterprise
            
            # Get solution access
            access = SolutionAccess.objects.filter(
                solution_id=solution_id,
                enterprise=enterprise,
                status=AccessStatus.ACTIVE
            ).first()
            
            if not access:
                raise AccessError('No active solution access found')
            
            # Revoke access (this will revoke all user seats)
            access.update_status('revoked')
            access.set_metadata_value('revocation_reason', reason)
            access.save()
            
            # Revoke all user seats
            access.users.filter(status=AccessStatus.ACTIVE).update(
                status='revoked',
                revoked_at=timezone.now()
            )
            access.used_seats = 0
            access.save()
            
            self._log_operation(f"Successfully revoked solution access {access.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error revoking solution access: {str(e)}")
            raise AccessError(f"Error revoking solution access: {str(e)}")
    
    def _is_access_expired(self, access) -> bool:
        """Check if access has expired"""
        if not access.access_expires_at:
            return False
        return timezone.now() > access.access_expires_at
