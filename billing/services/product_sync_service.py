"""
Product synchronization service for billing operations
"""
import stripe
import logging
from django.utils import timezone

from .base import BaseStripeService
from ..models import Product, Price
from ..exceptions import ProductSyncError
from ..constants import ProductType
from content_management.models import Service, SubscriptionPlan, Solution

logger = logging.getLogger(__name__)


class ProductSyncService(BaseStripeService):
    """Service for synchronizing products with Stripe"""
    
    def sync_products(self):
        """Sync all products from content_management models to Stripe"""
        try:
            self._log_operation("Starting product sync")
            
            # Sync Services
            services_synced = self._sync_services()
            
            # Sync Subscription Plans
            plans_synced = self._sync_subscription_plans()
            
            # Sync Solutions
            solutions_synced = self._sync_solutions()
            
            self._log_operation(f"Product sync completed: {services_synced} services, {plans_synced} plans, {solutions_synced} solutions")
            
            return {
                'services': services_synced,
                'subscription_plans': plans_synced,
                'solutions': solutions_synced
            }
            
        except Exception as e:
            logger.error(f"Error syncing products: {str(e)}")
            raise ProductSyncError(f"Error syncing products: {str(e)}")
    
    def _sync_services(self):
        """Sync services with Stripe"""
        synced_count = 0
        
        for service in Service.objects.filter(is_active=True):
            try:
                self._sync_service(service)
                synced_count += 1
            except Exception as e:
                logger.error(f"Error syncing service {service.id}: {str(e)}")
                continue
        
        return synced_count
    
    def _sync_subscription_plans(self):
        """Sync subscription plans with Stripe"""
        synced_count = 0
        
        for plan in SubscriptionPlan.objects.filter(is_active=True):
            try:
                self._sync_subscription_plan(plan)
                synced_count += 1
            except Exception as e:
                logger.error(f"Error syncing subscription plan {plan.id}: {str(e)}")
                continue
        
        return synced_count
    
    def _sync_solutions(self):
        """Sync solutions with Stripe"""
        synced_count = 0
        
        for solution in Solution.objects.filter(is_active=True):
            try:
                self._sync_solution(solution)
                synced_count += 1
            except Exception as e:
                logger.error(f"Error syncing solution {solution.id}: {str(e)}")
                continue
        
        return synced_count
    
    def _sync_service(self, service):
        """Sync a service with Stripe"""
        try:
            # Create or update Stripe product
            product_data = {
                'name': service.name,
                'description': service.description,
                'metadata': {
                    'type': ProductType.SERVICE,
                    'content_id': str(service.id)
                }
            }
            
            # Try to get existing product
            try:
                product = Product.objects.get(content_id=service.id, product_type=ProductType.SERVICE)
                stripe_product = self._make_stripe_request(
                    stripe.Product.modify,
                    product.stripe_product_id,
                    **product_data
                )
                
                # Update local product
                product.name = service.name
                product.description = service.description
                product.active = service.is_active
                product.save()
                
            except Product.DoesNotExist:
                stripe_product = self._make_stripe_request(
                    stripe.Product.create,
                    **product_data
                )
                
                product = Product.objects.create(
                    stripe_product_id=stripe_product.id,
                    content_id=service.id,
                    name=service.name,
                    description=service.description,
                    product_type=ProductType.SERVICE,
                    service=service,
                    active=service.is_active
                )
            
            # Sync prices
            self._sync_product_prices(product, service)
            
            self._log_operation(f"Synced service {service.id} with Stripe product {stripe_product.id}")
            
        except Exception as e:
            logger.error(f"Error syncing service {service.id}: {str(e)}")
            raise
    
    def _sync_subscription_plan(self, plan):
        """Sync a subscription plan with Stripe"""
        try:
            product_data = {
                'name': plan.name,
                'description': plan.description,
                'metadata': {
                    'type': ProductType.SUBSCRIPTION,
                    'content_id': str(plan.id)
                }
            }
            
            try:
                product = Product.objects.get(content_id=plan.id, product_type=ProductType.SUBSCRIPTION)
                stripe_product = self._make_stripe_request(
                    stripe.Product.modify,
                    product.stripe_product_id,
                    **product_data
                )
                
                product.name = plan.name
                product.description = plan.description
                product.active = plan.is_active
                product.save()
                
            except Product.DoesNotExist:
                stripe_product = self._make_stripe_request(
                    stripe.Product.create,
                    **product_data
                )
                
                product = Product.objects.create(
                    stripe_product_id=stripe_product.id,
                    content_id=plan.id,
                    name=plan.name,
                    description=plan.description,
                    product_type=ProductType.SUBSCRIPTION,
                    subscription_plan=plan,
                    active=plan.is_active
                )
            
            # Sync prices
            self._sync_product_prices(product, plan)
            
            self._log_operation(f"Synced subscription plan {plan.id} with Stripe product {stripe_product.id}")
            
        except Exception as e:
            logger.error(f"Error syncing subscription plan {plan.id}: {str(e)}")
            raise
    
    def _sync_solution(self, solution):
        """Sync a solution with Stripe"""
        try:
            product_data = {
                'name': solution.name,
                'description': solution.description,
                'metadata': {
                    'type': ProductType.SOLUTION,
                    'solution_type': solution.solution_type,
                    'content_id': str(solution.id)
                }
            }
            
            try:
                product = Product.objects.get(content_id=solution.id, product_type=ProductType.SOLUTION)
                stripe_product = self._make_stripe_request(
                    stripe.Product.modify,
                    product.stripe_product_id,
                    **product_data
                )
                
                product.name = solution.name
                product.description = solution.description
                product.active = solution.is_active
                product.save()
                
            except Product.DoesNotExist:
                stripe_product = self._make_stripe_request(
                    stripe.Product.create,
                    **product_data
                )
                
                product = Product.objects.create(
                    stripe_product_id=stripe_product.id,
                    content_id=solution.id,
                    name=solution.name,
                    description=solution.description,
                    product_type=ProductType.SOLUTION,
                    solution=solution,
                    active=solution.is_active
                )
            
            # Sync prices
            self._sync_product_prices(product, solution)
            
            self._log_operation(f"Synced solution {solution.id} with Stripe product {stripe_product.id}")
            
        except Exception as e:
            logger.error(f"Error syncing solution {solution.id}: {str(e)}")
            raise
    
    def _sync_product_prices(self, product, content_model):
        """Sync product prices with Stripe"""
        try:
            # Get price amount
            price_amount = content_model.price
            unit_amount = int(price_amount * 100)  # Convert to cents
            
            # Determine if this is a subscription price
            is_subscription = product.product_type in [ProductType.SUBSCRIPTION, ProductType.SOLUTION]
            recurring_config = {
                'interval': 'month',
                'interval_count': 1
            } if is_subscription else None
            
            # Check for existing active price
            existing_prices = self._make_stripe_request(
                stripe.Price.list,
                product=product.stripe_product_id,
                active=True
            ).data
            
            matching_price = next(
                (p for p in existing_prices 
                 if p.unit_amount == unit_amount and p.currency.lower() == 'usd'),
                None
            )
            
            if matching_price:
                # Use existing price
                stripe_price = matching_price
                self._log_operation(f"Using existing price {stripe_price.id} for product {product.stripe_product_id}")
            else:
                # Archive old prices and create new one
                for price in existing_prices:
                    self._make_stripe_request(stripe.Price.modify, price.id, active=False)
                
                # Create new price
                price_params = {
                    'product': product.stripe_product_id,
                    'unit_amount': unit_amount,
                    'currency': 'usd',
                    'metadata': {
                        'content_id': str(content_model.id),
                    }
                }
                
                if is_subscription:
                    price_params['recurring'] = recurring_config
                
                stripe_price = self._make_stripe_request(stripe.Price.create, **price_params)
                self._log_operation(f"Created new price {stripe_price.id} for product {product.stripe_product_id}")
            
            # Update or create price in database
            Price.objects.update_or_create(
                stripe_price_id=stripe_price.id,
                defaults={
                    'product': product,
                    'active': True,
                    'unit_amount': unit_amount,
                    'currency': 'usd',
                    'recurring': recurring_config
                }
            )
            
        except Exception as e:
            logger.error(f"Error syncing product prices: {str(e)}")
            raise
