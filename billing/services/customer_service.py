"""
Customer management service for billing operations
"""
import stripe
import logging
from django.core.cache import cache
from django.contrib.auth import get_user_model

from .base import BaseStripeService
from ..models import Customer
from ..exceptions import CustomerNotFoundError, StripeAPIError
from ..config import BillingConfig
from ..constants import CacheKeys

logger = logging.getLogger(__name__)
User = get_user_model()


class CustomerService(BaseStripeService):
    """Service for managing Stripe customers"""
    
    def create_or_update_customer(self, user):
        """Create or update a Stripe customer for a user"""
        try:
            # Validate Stripe configuration first
            self._validate_stripe_config()

            # Validate user data
            self._validate_user_data(user)

            # Check cache first
            cache_key = CacheKeys.CUSTOMER_DATA.format(user_id=user.id)
            cached_customer = cache.get(cache_key)
            if cached_customer:
                return cached_customer

            # Use select_for_update to prevent race conditions
            from django.db import transaction

            with transaction.atomic():
                try:
                    # Try to get existing customer with lock
                    customer = Customer.objects.select_for_update().filter(user=user).first()

                    if customer and customer.stripe_customer_id:
                        result = self._update_existing_customer(customer, user)
                    else:
                        # Use get_or_create for atomic operation
                        customer, created = Customer.objects.get_or_create(
                            user=user,
                            defaults={'stripe_customer_id': None}
                        )

                        if created or not customer.stripe_customer_id:
                            result = self._create_new_customer(user, customer)
                        else:
                            result = self._update_existing_customer(customer, user)

                except Exception as e:
                    logger.error(f"Database error in create_or_update_customer: {str(e)}")
                    raise

            # Cache the result
            cache.set(cache_key, result, BillingConfig.get_cache_timeouts()['customer_data'])
            return result

        except stripe.error.StripeError as e:
            logger.error(f"Stripe API error creating/updating customer for user {user.id}: {str(e)}")
            raise StripeAPIError(f"Stripe API error: {str(e)}")
        except Exception as e:
            logger.error(f"Error creating/updating customer for user {user.id}: {str(e)}")
            raise StripeAPIError(f"Error creating customer: {str(e)}")
    
    def _update_existing_customer(self, customer, user):
        """Update existing customer in Stripe"""
        self._log_operation(f"Updating existing Stripe customer {customer.stripe_customer_id} for user {user.id}")
        
        stripe_customer = self._make_stripe_request(
            stripe.Customer.modify,
            customer.stripe_customer_id,
            email=user.email,
            name=self._get_user_display_name(user),
            metadata={'user_id': str(user.id)}
        )
        
        # Update local customer record
        customer.email = user.email
        customer.name = self._get_user_display_name(user)
        customer.save()
        
        self._invalidate_customer_cache(user.id)
        return customer
    
    def _create_new_customer(self, user, existing_customer=None):
        """Create new customer in Stripe"""
        self._log_operation(f"Creating new Stripe customer for user {user.id}")

        try:
            # Prepare customer data for Stripe
            customer_data = {
                'email': user.email,
                'name': self._get_user_display_name(user),
                'metadata': {'user_id': str(user.id)}
            }
            logger.info(f"Creating Stripe customer with data: {customer_data}")

            # Use the retry mechanism directly - no double-try approach
            stripe_customer = self._make_stripe_request(
                stripe.Customer.create,
                **customer_data
            )

        except Exception as e:
            # Log the exact error details
            logger.error(f"Failed to create Stripe customer for user {user.id}")
            logger.error(f"User data: email={user.email}, name={self._get_user_display_name(user)}")
            logger.error(f"Error type: {type(e).__name__}")
            logger.error(f"Error message: {str(e)}")
            raise
        
        # Create or update customer record
        if existing_customer:
            existing_customer.stripe_customer_id = stripe_customer.id
            existing_customer.email = user.email
            existing_customer.name = self._get_user_display_name(user)
            existing_customer.save()
            customer = existing_customer
        else:
            customer = Customer.objects.create(
                user=user,
                stripe_customer_id=stripe_customer.id,
                email=user.email,
                name=self._get_user_display_name(user)
            )
        
        self._log_operation(f"Created Stripe customer {stripe_customer.id} for user {user.id}")
        return customer
    
    def get_customer_by_user(self, user):
        """Get customer by user, raise exception if not found"""
        try:
            return Customer.objects.get(user=user)
        except Customer.DoesNotExist:
            raise CustomerNotFoundError(f"Customer not found for user {user.id}")
    
    def get_customer_by_stripe_id(self, stripe_customer_id):
        """Get customer by Stripe ID, raise exception if not found"""
        try:
            return Customer.objects.get(stripe_customer_id=stripe_customer_id)
        except Customer.DoesNotExist:
            raise CustomerNotFoundError(f"Customer not found for Stripe ID {stripe_customer_id}")
    
    def get_customer_by_email(self, email):
        """Get customer by email, create if not found"""
        try:
            user = User.objects.get(email=email)
            return self.get_customer_by_user(user)
        except User.DoesNotExist:
            raise CustomerNotFoundError(f"User not found for email {email}")
    
    def update_customer_metadata(self, customer, metadata):
        """Update customer metadata in Stripe"""
        try:
            self._make_stripe_request(
                stripe.Customer.modify,
                customer.stripe_customer_id,
                metadata=metadata
            )
            
            # Update local metadata if needed
            customer.metadata.update(metadata)
            customer.save()
            
            self._invalidate_customer_cache(customer.user.id)
            return customer
            
        except Exception as e:
            logger.error(f"Error updating customer metadata: {str(e)}")
            raise StripeAPIError(f"Error updating customer metadata: {str(e)}")
    
    def delete_customer(self, customer):
        """Delete customer from Stripe (soft delete locally)"""
        try:
            self._make_stripe_request(
                stripe.Customer.delete,
                customer.stripe_customer_id
            )
            
            # Soft delete locally - don't actually delete the record
            customer.active = False
            customer.save()
            
            self._invalidate_customer_cache(customer.user.id)
            return True
            
        except Exception as e:
            logger.error(f"Error deleting customer: {str(e)}")
            raise StripeAPIError(f"Error deleting customer: {str(e)}")
    
    def list_customers_for_enterprise(self, enterprise):
        """List all customers associated with an enterprise"""
        from enterprise.models import EnterpriseMember
        
        enterprise_members = EnterpriseMember.objects.filter(
            enterprise=enterprise,
            is_active=True
        ).select_related('user')
        
        customers = []
        for member in enterprise_members:
            try:
                customer = self.get_customer_by_user(member.user)
                customers.append(customer)
            except CustomerNotFoundError:
                # Skip users without customers
                continue
        
        return customers
    
    def _get_user_display_name(self, user):
        """Get display name for user, truncated to Stripe's 256 character limit"""
        if hasattr(user, 'first_name') and hasattr(user, 'last_name'):
            full_name = f"{user.first_name} {user.last_name}".strip()
            display_name = full_name or user.email
        else:
            display_name = user.email

        # Truncate to Stripe's 256 character limit
        if len(display_name) > 256:
            return display_name[:256]
        return display_name

    def _validate_stripe_config(self):
        """Validate Stripe configuration"""
        from django.conf import settings

        if not hasattr(settings, 'STRIPE_SECRET_KEY') or not settings.STRIPE_SECRET_KEY:
            raise StripeAPIError("Stripe secret key is not configured")

        if not settings.STRIPE_SECRET_KEY.startswith(('sk_test_', 'sk_live_')):
            raise StripeAPIError("Invalid Stripe secret key format")

    def _validate_user_data(self, user):
        """Validate user data for Stripe customer creation"""
        if not user:
            raise ValueError("User is required")

        if not hasattr(user, 'email') or not user.email:
            raise ValueError("User email is required")

        if not hasattr(user, 'id') or not user.id:
            raise ValueError("User ID is required")

        # Validate email format (basic check)
        if '@' not in user.email or '.' not in user.email:
            raise ValueError("Invalid email format")

        # Ensure name is not too long for Stripe (max 256 characters)
        display_name = self._get_user_display_name(user)
        if len(display_name) > 256:
            logger.warning(f"User display name too long, will be truncated: {display_name[:50]}...")
    
    def _invalidate_customer_cache(self, user_id):
        """Invalidate customer cache"""
        cache_key = CacheKeys.CUSTOMER_DATA.format(user_id=user_id)
        cache.delete(cache_key)
    
    def get_or_create_customer_for_session(self, session):
        """Get or create customer from Stripe session data"""
        try:
            if session.customer:
                # Session has customer ID
                return self.get_customer_by_stripe_id(session.customer)
            elif hasattr(session, 'customer_details') and session.customer_details.get('email'):
                # Session has customer email
                email = session.customer_details['email']
                return self.get_customer_by_email(email)
            else:
                raise CustomerNotFoundError("No customer information found in session")
                
        except CustomerNotFoundError:
            # If customer not found and we have email, try to create
            if hasattr(session, 'customer_details') and session.customer_details.get('email'):
                email = session.customer_details['email']
                try:
                    user = User.objects.get(email=email)
                    return self.create_or_update_customer(user)
                except User.DoesNotExist:
                    raise CustomerNotFoundError(f"User not found for email {email}")
            raise
