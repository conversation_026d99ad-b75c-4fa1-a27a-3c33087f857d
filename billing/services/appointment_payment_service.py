"""
Appointment payment service for handling telemedicine payments
"""
import logging
from django.db import transaction
from .base import BaseStripeService
from ..exceptions import PaymentError
import uuid

logger = logging.getLogger(__name__)


class AppointmentPaymentService(BaseStripeService):
    """Service for handling appointment payment operations"""
    
    def handle_appointment_payment_success(self, session, metadata):
        """
        Handle successful appointment payment from Stripe webhook
        
        Args:
            session: Stripe checkout session object
            metadata: Payment metadata containing appointment_id
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self._log_operation(f"Processing appointment payment success for session {session.id}")
            
            # Get appointment ID from metadata
            appointment_id = metadata.get('appointment_id')
            if not appointment_id:
                logger.error(f"No appointment_id found in metadata for session {session.id}")
                return False
            
            # Import here to avoid circular imports
            from appointments.models import Appointment
            
            try:
                appointment = Appointment.objects.select_related(
                    'patient', 'doctor', 'payment_transfer'
                ).get(id=appointment_id)
                
                # Validate that the session belongs to this appointment
                if appointment.payment_transfer and hasattr(appointment.payment_transfer, 'stripe_checkout_session_id'):
                    if appointment.payment_transfer.stripe_checkout_session_id != session.id:
                        logger.warning(f"Session {session.id} does not match appointment {appointment_id} transfer session")
                
                # Process payment confirmation
                return self._process_appointment_payment_confirmation(appointment, session)
                
            except Appointment.DoesNotExist:
                logger.error(f"Appointment not found for appointment_id {appointment_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error handling appointment payment success: {str(e)}")
            return False
    
    def _process_appointment_payment_confirmation(self, appointment, session):
        """
        Process appointment payment confirmation after successful payment
        Only updates payment status - does NOT auto-confirm appointment
        Doctor must manually confirm appointment to create Google Meet link

        Args:
            appointment: Appointment instance
            session: Stripe checkout session

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with transaction.atomic():
                # Update payment transfer status if exists
                if hasattr(appointment, 'payment_transfer') and appointment.payment_transfer:
                    transfer = appointment.payment_transfer
                    transfer.status = 'completed'
                    
                    # Update with payment intent ID from the session
                    if hasattr(session, 'payment_intent') and session.payment_intent:
                        transfer.stripe_payment_intent_id = session.payment_intent
                        # Clear temporary payment intent flag
                        if 'temp_payment_intent' in transfer.metadata:
                            del transfer.metadata['temp_payment_intent']
                    
                    # Ensure checkout session ID is stored
                    transfer.stripe_checkout_session_id = session.id
                    transfer.save()

                    self._log_operation(f"Updated transfer status to completed for transfer {transfer.id}")

                # Update payment status only - do NOT auto-confirm appointment
                # Doctor must manually confirm appointment according to business logic
                old_payment_status = appointment.payment_status
                appointment.payment_status = 'paid'
                appointment.save()

                self._log_operation(f"Updated appointment {appointment.id} payment status from {old_payment_status} to paid")

                # Send payment confirmation notification (not appointment confirmation)
                self._send_payment_confirmation_notifications(appointment)

                self._log_operation(f"Successfully processed appointment payment confirmation for appointment {appointment.id}")
                return True

        except Exception as e:
            logger.error(f"Error processing appointment payment confirmation: {str(e)}")
            return False
    
    def _create_google_meet_link(self, appointment):
        """
        Create Google Meet link for video call appointment
        
        Args:
            appointment: Appointment instance
        """
        try:
            from appointments.services.google_meet import create_meet_link
            
            meet_data = create_meet_link(appointment)
            if meet_data:
                appointment.meeting_link = meet_data['meet_link']
                appointment.google_event_id = meet_data['event_id']
                appointment.save()
                
                self._log_operation(f"Created Meet link for appointment {appointment.id}")
                logger.info(f"Google Meet link created for appointment {appointment.id}: {meet_data['meet_link']}")
            else:
                logger.warning(f"Failed to create Meet link for appointment {appointment.id}")
                
        except Exception as e:
            logger.error(f"Error creating Meet link for appointment {appointment.id}: {str(e)}")
            # Don't raise here as the main payment was successful

    def _send_payment_confirmation_notifications(self, appointment):
        """
        Send payment confirmation notifications to patient and doctor
        This is different from appointment confirmation - just notifies payment success

        Args:
            appointment: Appointment instance
        """
        try:
            # Import here to avoid circular imports
            from appointments.tasks import send_payment_confirmation_email

            # Send payment confirmation email to patient and doctor
            if appointment.patient and appointment.patient.email and appointment.doctor:
                # Get payment amount from appointment if available
                payment_amount = getattr(appointment, 'consultation_fee', None)
                if payment_amount:
                    payment_amount = payment_amount / 100  # Convert cents to dollars
                
                send_payment_confirmation_email.delay(
                    appointment.id,
                    payment_amount,
                    'both'  # Send to both patient and doctor
                )
                self._log_operation(f"Queued payment confirmation email for appointment {appointment.id}")

        except Exception as e:
            logger.error(f"Error sending payment confirmation notifications: {str(e)}")
            # Don't raise here as the main payment was successful

    def _send_appointment_confirmation_notifications(self, appointment):
        """
        Send confirmation notifications to patient and doctor
        
        Args:
            appointment: Appointment instance
        """
        try:
            # Import here to avoid circular imports
            from appointments.tasks import send_appointment_confirmation_email
            
            # Send confirmation email to patient
            if appointment.patient and appointment.patient.email:
                send_appointment_confirmation_email.delay(
                    appointment.id,
                    appointment.patient.email,
                    'patient'
                )
                self._log_operation(f"Queued confirmation email for patient {appointment.patient.id}")
            
            # Send confirmation email to doctor
            if appointment.doctor and appointment.doctor.email:
                send_appointment_confirmation_email.delay(
                    appointment.id,
                    appointment.doctor.email,
                    'doctor'
                )
                self._log_operation(f"Queued confirmation email for doctor {appointment.doctor.id}")
                
        except Exception as e:
            logger.error(f"Error sending appointment confirmation notifications: {str(e)}")
            # Don't raise here as the main payment was successful
    
    def create_appointment_checkout_session(self, patient, doctor, appointment, consultation_fee):
        """
        Create Stripe checkout session for appointment payment
        
        Args:
            patient: Patient user instance
            doctor: Doctor user instance  
            appointment: Appointment instance
            consultation_fee: Fee amount in cents
            
        Returns:
            tuple: (transfer, checkout_url)
        """
        try:
            import stripe
            from django.conf import settings
            from .user_transfer_service import UserTransferService
            from ..models import PlatformFee
            
            # Validate doctor has payment profile (required for receiving payments)
            doctor_profile = getattr(doctor, 'payment_profile', None)

            if not doctor_profile:
                raise PaymentError("Doctor must have a payment profile to receive payments")

            if not doctor_profile.stripe_account_id:
                raise PaymentError("Doctor must have a connected Stripe account")

            # For patients, we only need to ensure they can be created as Stripe customers
            # Patient payment profiles are not required - they just need to pay via checkout

            # Create or get Stripe customer for patient (for checkout session)
            from .customer_service import CustomerService
            customer_service = CustomerService()
            patient_customer = customer_service.create_or_update_customer(patient)

            if not patient_customer or not patient_customer.stripe_customer_id:
                raise PaymentError("Failed to create customer for patient payment processing")

            # Validate and sync doctor's account capabilities
            self._validate_and_sync_doctor_account(doctor_profile)
            
            # Calculate platform fee (3% for appointments)
            platform_fee = self._get_applicable_platform_fee(consultation_fee)
            if platform_fee:
                fee_amount = int(consultation_fee * (platform_fee.percentage / 100))
            else:
                logger.warning("No platform fee found, proceeding without platform fee")
                fee_amount = 0
            
            # Create UserTransfer record first
            transfer = self._create_user_transfer_record(
                patient, doctor, consultation_fee, appointment, platform_fee, fee_amount
            )
            
            # Create Stripe checkout session for Connect account
            success_url = f"{settings.FRONTEND_URL}/appointments/{appointment.id}/payment-success"
            cancel_url = f"{settings.FRONTEND_URL}/appointments/{appointment.id}/payment-cancel"
            
            checkout_session = self._make_stripe_request(
                stripe.checkout.Session.create,
                customer=patient_customer.stripe_customer_id,  # Associate with patient's customer
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': 'usd',
                        'product_data': {
                            'name': f'Telemedicine Consultation with {doctor.get_full_name()}',
                            'description': f'Video consultation appointment - {appointment.start_time.strftime("%B %d, %Y at %I:%M %p")}',
                        },
                        'unit_amount': consultation_fee,
                    },
                    'quantity': 1,
                }],
                mode='payment',
                success_url=success_url,
                cancel_url=cancel_url,
                payment_intent_data={
                    'application_fee_amount': fee_amount,
                    'transfer_data': {
                        'destination': doctor_profile.stripe_account_id,
                    },
                    'metadata': {
                        'transfer_id': str(transfer.id),
                        'appointment_id': str(appointment.id),
                        'patient_id': str(patient.id),
                        'doctor_id': str(doctor.id),
                        'payment_type': 'appointment'
                    }
                },
                metadata={
                    'transfer_id': str(transfer.id),
                    'appointment_id': str(appointment.id),
                    'patient_id': str(patient.id),
                    'doctor_id': str(doctor.id),
                    'payment_type': 'appointment'
                }
            )
            
            # Update transfer with checkout session ID
            transfer.stripe_checkout_session_id = checkout_session.id
            transfer.save()
            
            # Link transfer to appointment
            appointment.payment_transfer = transfer
            appointment.consultation_fee = consultation_fee
            appointment.payment_status = 'pending'
            appointment.save()

            self._log_operation(f"Created checkout session {checkout_session.id} for appointment {appointment.id} with fee ${consultation_fee/100}")

            return transfer, checkout_session.url
            
        except Exception as e:
            logger.error(f"Error creating appointment checkout session: {str(e)}")
            raise PaymentError(f"Failed to create appointment checkout: {str(e)}")
    
    def _create_user_transfer_record(self, patient, doctor, consultation_fee, appointment, platform_fee, fee_amount):
        """Create UserTransfer record for appointment"""
        from ..models import UserTransfer
        from ..constants import TransferType
        
        # Handle None platform_fee case
        if platform_fee is None:
            logger.warning("No platform fee found, setting fee_amount to 0")
            fee_amount = 0
        
        # Create temporary unique ID to avoid constraint violation
        # This will be updated with actual payment intent ID after Stripe session creation
        temp_payment_intent_id = f"temp_pi_{uuid.uuid4().hex[:16]}"
        
        transfer = UserTransfer.objects.create(
            sender=patient,
            receiver=doctor,
            amount=consultation_fee,
            currency='usd',
            message=f"Telemedicine consultation - Appointment {appointment.id}",
            transfer_type=TransferType.PAYMENT,
            platform_fee=platform_fee,  # Can be None
            platform_fee_amount=fee_amount,
            status='pending',
            stripe_payment_intent_id=temp_payment_intent_id,  # Temporary ID
            metadata={
                'appointment_id': str(appointment.id),
                'consultation_type': 'telemedicine',
                'payment_type': 'appointment',
                'temp_payment_intent': True  # Flag to indicate this is temporary
            }
        )
        
        return transfer
    
    def create_appointment_payment_transfer(self, patient, doctor, appointment, consultation_fee):
        """
        Create peer-to-peer payment transfer for appointment
        THIS METHOD IS DEPRECATED - Use create_appointment_checkout_session instead
        
        Args:
            patient: Patient user instance
            doctor: Doctor user instance  
            appointment: Appointment instance
            consultation_fee: Fee amount in cents
            
        Returns:
            tuple: (transfer, checkout_url)
        """
        # Redirect to new checkout session method
        return self.create_appointment_checkout_session(patient, doctor, appointment, consultation_fee)
    
    def _get_applicable_platform_fee(self, amount):
        """Get applicable platform fee for appointment payments"""
        try:
            from ..models import PlatformFee
            
            # For appointments, use 3% platform fee
            platform_fee = PlatformFee.objects.filter(
                active=True,
                name__icontains="appointment"
            ).first()
            
            # If no platform fee found, create default 3% fee for appointments
            if not platform_fee:
                try:
                    platform_fee = PlatformFee.objects.create(
                        name="Appointment Platform Fee",
                        percentage=3.0,
                        fixed_amount=0,
                        active=True,
                        applies_to_user_transfers=True  # Appointments use transfers
                    )
                    logger.info(f"Created new platform fee for appointments: {platform_fee.id}")
                except Exception as create_error:
                    logger.error(f"Failed to create platform fee: {str(create_error)}")
                    # Try to get any active platform fee as fallback
                    platform_fee = PlatformFee.objects.filter(active=True).first()
                    
            return platform_fee
            
        except Exception as e:
            logger.error(f"Error getting platform fee: {str(e)}")
            # Return None instead of a fake object
            return None
    
    def _validate_and_sync_doctor_account(self, doctor_profile):
        """
        Validate and sync doctor's Stripe account status and capabilities
        
        Args:
            doctor_profile: UserPaymentProfile instance
            
        Raises:
            PaymentError: If account doesn't have necessary capabilities
        """
        try:
            import stripe
            
            # Retrieve account from Stripe to check capabilities
            account = self._make_stripe_request(
                stripe.Account.retrieve,
                doctor_profile.stripe_account_id
            )
            
            # Sync account status with local profile
            doctor_profile.charges_enabled = account.charges_enabled
            doctor_profile.payouts_enabled = account.payouts_enabled
            doctor_profile.details_submitted = account.details_submitted
            doctor_profile.save()
            
            # Check if account has transfers capability
            capabilities = getattr(account, 'capabilities', {})
            transfers_capability = capabilities.get('transfers')
            
            logger.info(f"Account {doctor_profile.stripe_account_id} capabilities: {capabilities}")
            
            if not transfers_capability or transfers_capability not in ['active', 'pending']:
                # Try to request transfers capability if not active
                logger.warning(f"Account {doctor_profile.stripe_account_id} missing transfers capability, attempting to request it")
                
                try:
                    # Update account to request transfers capability
                    self._make_stripe_request(
                        stripe.Account.modify,
                        doctor_profile.stripe_account_id,
                        capabilities={'transfers': {'requested': True}}
                    )
                    
                    # Re-check after update
                    updated_account = self._make_stripe_request(
                        stripe.Account.retrieve,
                        doctor_profile.stripe_account_id
                    )
                    
                    updated_capabilities = getattr(updated_account, 'capabilities', {})
                    updated_transfers = updated_capabilities.get('transfers')
                    
                    logger.info(f"Updated account capabilities: {updated_capabilities}")
                    
                    if updated_transfers in ['active', 'pending']:
                        logger.info(f"Successfully requested transfers capability for account {doctor_profile.stripe_account_id}")
                        # If pending, continue but warn user
                        if updated_transfers == 'pending':
                            logger.warning(f"Transfers capability is pending for account {doctor_profile.stripe_account_id}")
                        return
                    
                except Exception as update_error:
                    logger.error(f"Failed to update account capabilities: {str(update_error)}")
                
                # If we can't enable transfers, provide helpful error message
                raise PaymentError(
                    "Doctor's payment account needs to complete setup to receive payments. "
                    f"Account capabilities: {capabilities}. "
                    "Please complete Stripe Connect onboarding including bank account verification, "
                    "or contact support for assistance."
                )
            
            # Also check if account is ready to receive payments
            if not account.charges_enabled:
                raise PaymentError(
                    "Doctor's payment account is not activated for receiving payments. "
                    "Please complete the account verification process."
                )
            
            # For transfers, payouts_enabled is also important but not strictly required for initial payment
            if not account.payouts_enabled:
                logger.warning(f"Account {doctor_profile.stripe_account_id} does not have payouts enabled yet")
                
            logger.info(f"Doctor account {doctor_profile.stripe_account_id} validated successfully for transfers")
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error validating doctor account: {str(e)}")
            raise PaymentError(f"Unable to validate doctor's payment account: {str(e)}")
        except Exception as e:
            logger.error(f"Error validating doctor account capabilities: {str(e)}")
            raise PaymentError(f"Error validating doctor's payment account: {str(e)}")
    
    def get_doctor_consultation_fee(self, doctor):
        """
        Get doctor's consultation fee
        
        Args:
            doctor: Doctor user instance
            
        Returns:
            int: Consultation fee in cents
        """
        try:
            if hasattr(doctor, 'consultation_profile'):
                return doctor.consultation_profile.consultation_fee
            else:
                # Default consultation fee if no profile set
                return 5000  # $50.00 default
                
        except Exception as e:
            logger.error(f"Error getting doctor consultation fee: {str(e)}")
            return 5000  # Default fallback
