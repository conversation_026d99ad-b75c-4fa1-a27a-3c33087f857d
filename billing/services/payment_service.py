"""
Payment processing service for billing operations
"""
import stripe
import logging
from django.core.cache import cache
from django.utils import timezone

from .base import BaseStripeService, PaymentProcessorInterface
from ..models import Price, ServicePayment, ServiceAccess
from ..exceptions import PaymentError, StripeAPIError, ValidationError
from ..config import BillingConfig
from ..constants import CacheKeys, PaymentStatus, AccessStatus

logger = logging.getLogger(__name__)


class PaymentService(BaseStripeService, PaymentProcessorInterface):
    """Service for handling payment operations"""
    
    def create_customer(self, user):
        """Create a customer for payment processing"""
        from .customer_service import CustomerService
        customer_service = CustomerService()
        return customer_service.create_or_update_customer(user)
    
    def create_payment_intent(self, amount, customer, currency='usd', metadata=None):
        """Create a payment intent"""
        try:
            self._validate_payment_amount(amount)
            
            payment_intent = self._make_stripe_request(
                stripe.PaymentIntent.create,
                amount=amount,
                currency=currency,
                customer=customer.stripe_customer_id,
                metadata=metadata or {},
                automatic_payment_methods={'enabled': True}
            )
            
            self._log_operation(f"Created payment intent {payment_intent.id} for customer {customer.id}")
            return payment_intent
            
        except Exception as e:
            logger.error(f"Error creating payment intent: {str(e)}")
            raise PaymentError(f"Error creating payment intent: {str(e)}")
    
    def create_checkout_session(self, customer, price, success_url, cancel_url, metadata=None, mode='subscription'):
        """Create a Stripe Checkout session"""
        try:
            # Validate inputs
            self._validate_required_params(
                {'customer': customer, 'price': price, 'success_url': success_url, 'cancel_url': cancel_url},
                ['customer', 'price', 'success_url', 'cancel_url']
            )

            # Validate price compatibility with mode
            self._validate_price_mode_compatibility(price, mode)

            # Check rate limiting
            self._check_rate_limit(customer.user.id)

            session_data = {
                'customer': customer.stripe_customer_id,
                'payment_method_types': ['card'],
                'line_items': [{
                    'price': price.stripe_price_id,
                    'quantity': 1,
                }],
                'mode': mode,
                'success_url': success_url,
                'cancel_url': cancel_url,
                'metadata': metadata or {}
            }

            # Add subscription-specific configuration
            if mode == 'subscription':
                session_data.update({
                    'subscription_data': {
                        'metadata': metadata or {}
                    }
                })
            
            session = self._make_stripe_request(
                stripe.checkout.Session.create,
                **session_data
            )
            
            self._log_operation(f"Created checkout session {session.id} for customer {customer.id}")
            
            # Set rate limit
            self._set_rate_limit(customer.user.id)
            
            return session
            
        except Exception as e:
            logger.error(f"Error creating checkout session: {str(e)}")
            raise PaymentError(f"Error creating checkout session: {str(e)}")
    
    def create_service_payment(self, user, service, amount, currency='usd', stripe_payment_intent_id=None, metadata=None):
        """Create a service payment record"""
        try:
            payment = ServicePayment.objects.create(
                user=user,
                service=service,
                amount=amount,
                currency=currency,
                status=PaymentStatus.PENDING,
                stripe_payment_intent_id=stripe_payment_intent_id,
                metadata=metadata or {}
            )
            
            self._log_operation(f"Created service payment {payment.id} for user {user.id}")
            return payment
            
        except Exception as e:
            logger.error(f"Error creating service payment: {str(e)}")
            raise PaymentError(f"Error creating service payment: {str(e)}")
    
    def update_payment_status(self, payment, status, metadata=None):
        """Update payment status"""
        try:
            payment.status = status
            if metadata:
                payment.metadata.update(metadata)
            payment.save()
            
            self._log_operation(f"Updated payment {payment.id} status to {status}")
            
            # Handle status-specific actions
            if status == PaymentStatus.COMPLETED:
                self._handle_payment_completed(payment)
            elif status == PaymentStatus.FAILED:
                self._handle_payment_failed(payment)
            
            return payment
            
        except Exception as e:
            logger.error(f"Error updating payment status: {str(e)}")
            raise PaymentError(f"Error updating payment status: {str(e)}")
    
    def process_refund(self, payment, amount=None, reason=None):
        """Process a refund for a payment"""
        try:
            if not payment.stripe_payment_intent_id:
                raise PaymentError("No Stripe payment intent ID found for refund")
            
            refund_amount = amount or payment.amount
            
            refund = self._make_stripe_request(
                stripe.Refund.create,
                payment_intent=payment.stripe_payment_intent_id,
                amount=refund_amount,
                reason=reason
            )
            
            # Update payment status
            payment.status = PaymentStatus.REFUNDED
            payment.metadata['refund_id'] = refund.id
            payment.metadata['refund_amount'] = refund_amount
            if reason:
                payment.metadata['refund_reason'] = reason
            payment.save()
            
            # Revoke access if applicable
            self._revoke_payment_access(payment)
            
            self._log_operation(f"Processed refund {refund.id} for payment {payment.id}")
            return refund
            
        except Exception as e:
            logger.error(f"Error processing refund: {str(e)}")
            raise PaymentError(f"Error processing refund: {str(e)}")
    
    def get_payment_by_stripe_id(self, stripe_payment_intent_id):
        """Get payment by Stripe payment intent ID"""
        try:
            return ServicePayment.objects.get(stripe_payment_intent_id=stripe_payment_intent_id)
        except ServicePayment.DoesNotExist:
            raise PaymentError(f"Payment not found for Stripe ID {stripe_payment_intent_id}")
    
    def _validate_payment_amount(self, amount):
        """Validate payment amount"""
        if not isinstance(amount, int) or amount <= 0:
            raise ValidationError("Payment amount must be a positive integer (in cents)")
        
        # Minimum amount check (50 cents)
        if amount < 50:
            raise ValidationError("Payment amount must be at least 50 cents")
    
    def _check_rate_limit(self, user_id):
        """Check if user is rate limited for checkout sessions"""
        cache_key = CacheKeys.CHECKOUT_SESSION.format(user_id=user_id)
        if cache.get(cache_key):
            raise PaymentError("Please wait before creating another checkout session")
    
    def _set_rate_limit(self, user_id):
        """Set rate limit for user"""
        cache_key = CacheKeys.CHECKOUT_SESSION.format(user_id=user_id)
        cache.set(cache_key, True, BillingConfig.RATE_LIMIT_TIMEOUT)

    def _validate_price_mode_compatibility(self, price, mode):
        """Validate that the price is compatible with the checkout session mode"""
        if mode == 'subscription':
            if not price.is_recurring:
                raise ValidationError(
                    f"Price {price.stripe_price_id} is not a recurring price. "
                    f"Subscription mode requires a recurring price."
                )

            # Validate that the price has proper recurring configuration
            if not price.recurring or not isinstance(price.recurring, dict):
                raise ValidationError(
                    f"Price {price.stripe_price_id} has invalid recurring configuration. "
                    f"Expected a dictionary with interval information."
                )

            # Check required recurring fields
            required_fields = ['interval']
            missing_fields = [field for field in required_fields if field not in price.recurring]
            if missing_fields:
                raise ValidationError(
                    f"Price {price.stripe_price_id} recurring configuration missing required fields: {missing_fields}"
                )

            self._log_operation(f"Validated recurring price {price.stripe_price_id} for subscription mode")

        elif mode == 'payment':
            if price.is_recurring:
                logger.warning(f"Using recurring price {price.stripe_price_id} in payment mode")

            self._log_operation(f"Validated price {price.stripe_price_id} for payment mode")
    
    def _handle_payment_completed(self, payment):
        """Handle completed payment"""
        try:
            # Create or update service access
            access, created = ServiceAccess.objects.get_or_create(
                user=payment.user,
                service=payment.service,
                payment=payment,
                defaults={
                    'status': AccessStatus.ACTIVE,
                    'access_granted_at': timezone.now(),
                }
            )
            
            if not created and access.status != AccessStatus.ACTIVE:
                access.status = AccessStatus.ACTIVE
                access.save()
            
            self._log_operation(f"Granted service access {access.id} for payment {payment.id}")
            
        except Exception as e:
            logger.error(f"Error handling completed payment: {str(e)}")
            # Don't raise here as payment is already completed
    
    def _handle_payment_failed(self, payment):
        """Handle failed payment"""
        try:
            # Revoke any existing access
            self._revoke_payment_access(payment)
            
            self._log_operation(f"Handled failed payment {payment.id}")
            
        except Exception as e:
            logger.error(f"Error handling failed payment: {str(e)}")
            # Don't raise here as we're already handling a failure
    
    def _revoke_payment_access(self, payment):
        """Revoke access associated with a payment"""
        try:
            access_records = ServiceAccess.objects.filter(
                payment=payment,
                status=AccessStatus.ACTIVE
            )
            
            for access in access_records:
                access.status = AccessStatus.REVOKED
                access.save()
                self._log_operation(f"Revoked service access {access.id}")
                
        except Exception as e:
            logger.error(f"Error revoking payment access: {str(e)}")
    
    def get_user_payment_history(self, user, limit=None):
        """Get payment history for a user"""
        queryset = ServicePayment.objects.filter(user=user).order_by('-created_at')
        if limit:
            queryset = queryset[:limit]
        return queryset

    def process_checkout_success(self, session_id):
        """Process successful checkout session"""
        try:
            # Retrieve the session
            session = self._make_stripe_request(
                stripe.checkout.Session.retrieve,
                session_id
            )

            # Get the payment details
            payment_intent = session.payment_intent
            customer_id = session.customer
            price_id = session.metadata.get('price_id')

            # Get customer and price
            from ..models import Customer, Price
            customer = Customer.objects.get(stripe_customer_id=customer_id)
            price = Price.objects.get(stripe_price_id=price_id)

            # Create payment record
            payment = ServicePayment.objects.create(
                user=customer.user,
                service=price.product.service,
                amount=session.amount_total,  # Already in cents
                currency=session.currency,
                status=PaymentStatus.COMPLETED,
                stripe_payment_intent_id=payment_intent,
                metadata={'stripe_session_id': session_id}
            )

            # Grant service access
            self._handle_payment_completed(payment)

            self._log_operation(f"Processed checkout success for session {session_id}")

            return {
                'payment_id': str(payment.id),
                'service_id': str(price.product.service.id),
                'user_id': customer.user.id
            }

        except Exception as e:
            logger.error(f"Error processing checkout success: {str(e)}")
            raise PaymentError(f"Error processing checkout success: {str(e)}")
