"""
Comprehensive error handling service for edge cases
"""
import logging
import time
import stripe
from typing import Callable, Any, Optional, Dict
from functools import wraps
from decimal import Decimal, InvalidOperation
from django.conf import settings
from django.core.cache import cache
from django.db import transaction, DatabaseError

from ..exceptions import (
    PaymentError, ValidationError, ConfigurationError, 
    RateLimitError, NetworkError, ServiceUnavailableError
)

logger = logging.getLogger(__name__)


class EdgeCaseHandler:
    """Comprehensive edge case handler"""
    
    @staticmethod
    def validate_amount(amount: Any) -> Decimal:
        """Validate and convert amount to Decimal with edge case handling"""
        if amount is None:
            raise ValidationError("Amount cannot be None")
        
        if amount == "":
            raise ValidationError("Amount cannot be empty string")
        
        try:
            decimal_amount = Decimal(str(amount))
        except (InvalidOperation, ValueError, TypeError) as e:
            raise ValidationError(f"Invalid amount format: {amount}. Error: {str(e)}")
        
        if decimal_amount < 0:
            raise ValidationError("Amount cannot be negative")
        
        if decimal_amount == 0:
            raise ValidationError("Amount cannot be zero")
        
        # Check for extremely large amounts (potential overflow)
        if decimal_amount > Decimal('999999999.99'):
            raise ValidationError("Amount exceeds maximum allowed value")
        
        # Check for too many decimal places
        if decimal_amount.as_tuple().exponent < -2:
            raise ValidationError("Amount cannot have more than 2 decimal places")
        
        return decimal_amount
    
    @staticmethod
    def validate_stripe_price_id(price_id: Optional[str]) -> str:
        """Validate Stripe price ID"""
        if not price_id:
            raise ValidationError("Stripe price ID is required")
        
        if not isinstance(price_id, str):
            raise ValidationError("Stripe price ID must be a string")
        
        if not price_id.strip():
            raise ValidationError("Stripe price ID cannot be empty")
        
        # Stripe price IDs have specific format
        if not (price_id.startswith('price_') or price_id.startswith('plan_')):
            raise ValidationError(f"Invalid Stripe price ID format: {price_id}")
        
        return price_id.strip()
    
    @staticmethod
    def validate_user_id(user_id: Any) -> str:
        """Validate user ID"""
        if not user_id:
            raise ValidationError("User ID is required")
        
        user_id_str = str(user_id).strip()
        if not user_id_str:
            raise ValidationError("User ID cannot be empty")
        
        # Basic UUID validation (adjust based on your ID format)
        if len(user_id_str) < 1:
            raise ValidationError("User ID too short")
        
        return user_id_str
    
    @staticmethod
    def validate_service_data(service_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate service data from provider"""
        if not service_data:
            raise ValidationError("Service data is required")
        
        if not isinstance(service_data, dict):
            raise ValidationError("Service data must be a dictionary")
        
        required_fields = ['id', 'name', 'price']
        for field in required_fields:
            if field not in service_data:
                raise ValidationError(f"Service data missing required field: {field}")
            
            if service_data[field] is None:
                raise ValidationError(f"Service {field} cannot be None")
        
        # Validate price in service data
        try:
            EdgeCaseHandler.validate_amount(service_data['price'])
        except ValidationError as e:
            raise ValidationError(f"Invalid service price: {str(e)}")
        
        return service_data
    
    @staticmethod
    def safe_get_attribute(obj: Any, attr: str, default: Any = None, required: bool = False) -> Any:
        """Safely get attribute with null checking"""
        if obj is None:
            if required:
                raise ValidationError(f"Object is None, cannot get attribute {attr}")
            return default
        
        value = getattr(obj, attr, default)
        
        if required and value is None:
            raise ValidationError(f"Required attribute {attr} is None")
        
        return value
    
    @staticmethod
    def handle_stripe_api_error(func: Callable) -> Callable:
        """Decorator to handle Stripe API errors"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except stripe.error.CardError as e:
                logger.warning(f"Stripe card error: {str(e)}")
                raise PaymentError(f"Card error: {e.user_message}", stripe_error_code=e.code)
            except stripe.error.RateLimitError as e:
                logger.warning(f"Stripe rate limit error: {str(e)}")
                raise RateLimitError("Too many requests to payment processor")
            except stripe.error.InvalidRequestError as e:
                logger.error(f"Stripe invalid request: {str(e)}")
                raise ValidationError(f"Invalid payment request: {str(e)}")
            except stripe.error.AuthenticationError as e:
                logger.error(f"Stripe authentication error: {str(e)}")
                raise ConfigurationError("Payment system authentication failed")
            except stripe.error.APIConnectionError as e:
                logger.error(f"Stripe connection error: {str(e)}")
                raise NetworkError("Payment system connection failed")
            except stripe.error.StripeError as e:
                logger.error(f"Generic Stripe error: {str(e)}")
                raise PaymentError(f"Payment system error: {str(e)}")
        return wrapper
    
    @staticmethod
    def handle_database_error(func: Callable) -> Callable:
        """Decorator to handle database errors"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                with transaction.atomic():
                    return func(*args, **kwargs)
            except DatabaseError as e:
                logger.error(f"Database error: {str(e)}")
                raise ServiceUnavailableError("Database service temporarily unavailable")
            except Exception as e:
                logger.error(f"Unexpected database error: {str(e)}")
                raise ServiceUnavailableError("Service temporarily unavailable")
        return wrapper
    
    @staticmethod
    def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
        """Decorator to retry function on failure with exponential backoff"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                current_delay = delay
                last_exception = None
                
                for attempt in range(max_retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except (NetworkError, ServiceUnavailableError) as e:
                        last_exception = e
                        if attempt < max_retries:
                            logger.warning(f"Attempt {attempt + 1} failed: {str(e)}. Retrying in {current_delay}s")
                            time.sleep(current_delay)
                            current_delay *= backoff
                        else:
                            logger.error(f"All {max_retries + 1} attempts failed")
                    except Exception as e:
                        # Don't retry on non-retryable errors
                        logger.error(f"Non-retryable error: {str(e)}")
                        raise
                
                raise last_exception
            return wrapper
        return decorator
    
    @staticmethod
    def check_configuration() -> None:
        """Check system configuration for edge cases"""
        required_settings = [
            'STRIPE_SECRET_KEY',
            'STRIPE_PUBLISHABLE_KEY',
            'FRONTEND_URL'
        ]
        
        missing_settings = []
        for setting in required_settings:
            if not hasattr(settings, setting) or not getattr(settings, setting):
                missing_settings.append(setting)
        
        if missing_settings:
            raise ConfigurationError(f"Missing required settings: {', '.join(missing_settings)}")
        
        # Validate Stripe keys format
        secret_key = getattr(settings, 'STRIPE_SECRET_KEY', '')
        if not secret_key.startswith('sk_'):
            raise ConfigurationError("Invalid Stripe secret key format")
        
        pub_key = getattr(settings, 'STRIPE_PUBLISHABLE_KEY', '')
        if not pub_key.startswith('pk_'):
            raise ConfigurationError("Invalid Stripe publishable key format")
    
    @staticmethod
    def check_rate_limit(user_id: str, operation: str, max_per_minute: int = 10) -> None:
        """Check rate limiting for operations"""
        cache_key = f"rate_limit_{operation}_{user_id}"
        current_count = cache.get(cache_key, 0)
        
        if current_count >= max_per_minute:
            raise RateLimitError(f"Rate limit exceeded for {operation}. Max {max_per_minute} per minute.")
        
        # Increment counter with 60 second expiry
        cache.set(cache_key, current_count + 1, 60)
    
    @staticmethod
    def validate_concurrent_operation(user_id: str, operation: str) -> bool:
        """Prevent concurrent operations that could cause conflicts"""
        lock_key = f"lock_{operation}_{user_id}"
        
        # Try to acquire lock
        if cache.get(lock_key):
            raise ValidationError(f"Another {operation} operation is in progress. Please wait.")
        
        # Set lock for 30 seconds
        cache.set(lock_key, True, 30)
        return True
    
    @staticmethod
    def release_operation_lock(user_id: str, operation: str) -> None:
        """Release operation lock"""
        lock_key = f"lock_{operation}_{user_id}"
        cache.delete(lock_key)
    
    @staticmethod
    def safe_dict_get(data: Dict[str, Any], key: str, default: Any = None, required: bool = False) -> Any:
        """Safely get value from dictionary with validation"""
        if not isinstance(data, dict):
            if required:
                raise ValidationError("Data must be a dictionary")
            return default
        
        value = data.get(key, default)
        
        if required and value is None:
            raise ValidationError(f"Required key '{key}' not found in data")
        
        return value
    
    @staticmethod
    def validate_metadata(metadata: Dict[str, Any]) -> Dict[str, str]:
        """Validate and sanitize metadata for Stripe"""
        if not metadata:
            return {}
        
        sanitized = {}
        for key, value in metadata.items():
            # Convert key to string and validate
            str_key = str(key).strip()
            if not str_key:
                continue
            
            # Convert value to string and validate
            if value is None:
                str_value = ""
            else:
                str_value = str(value).strip()
            
            # Stripe metadata has limits
            if len(str_key) > 40:
                logger.warning(f"Metadata key too long, truncating: {str_key}")
                str_key = str_key[:40]
            
            if len(str_value) > 500:
                logger.warning(f"Metadata value too long, truncating: {str_value}")
                str_value = str_value[:500]
            
            sanitized[str_key] = str_value
        
        return sanitized 