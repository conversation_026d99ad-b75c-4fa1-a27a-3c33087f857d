"""
Billing services package

This package contains all the service classes for handling billing operations.
Services are organized by domain to maintain separation of concerns.
"""

# Import all service classes
from .customer_service import CustomerService
from .payment_service import PaymentService
from .subscription_service import SubscriptionService
from .webhook_service import WebhookService
from .product_sync_service import ProductSyncService
from .user_transfer_service import UserTransferService
from .payment_link_service import PaymentLinkService
from .unified_payment_service import UnifiedPaymentService
from .enterprise_service import EnterpriseService

# Backward compatibility - maintain existing imports
from .base import StripeService

__all__ = [
    'CustomerService',
    'PaymentService', 
    'SubscriptionService',
    'WebhookService',
    'ProductSyncService',
    'UserTransferService',
    'PaymentLinkService',
    'UnifiedPaymentService',
    'EnterpriseService',
    'StripeService',  # For backward compatibility
]

# Version info
__version__ = '2.0.0'
