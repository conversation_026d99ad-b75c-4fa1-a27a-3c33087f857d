import logging
import json
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone

from .base import BaseStripeService
from ..models import SolutionAccess, Customer
from ..constants import AccessStatus

logger = logging.getLogger(__name__)
User = get_user_model()


class EnterpriseService(BaseStripeService):
    """Service for handling enterprise-related payment operations"""

    def handle_solution_subscription(self, session, customer, metadata):
        """
        Handle enterprise solution subscription from webhook
        This replaces the deprecated logic with proper enterprise creation
        """
        try:
            self._log_operation(f"Processing enterprise solution subscription for session {getattr(session, 'id', None)}")
            
            # 1. Create or update enterprise
            enterprise = self._create_or_update_enterprise_from_metadata(metadata)
            
            # 2. Create solution access
            solution_access = self._create_solution_access(enterprise, metadata['solution_id'])
            
            # 3. Check if admin should be included as member
            include_admin = metadata.get('include_admin_as_member', 'True').lower() == 'true'
            admin_wants_subscription = metadata.get('admin_wants_subscription', 'False').lower() == 'true'
            
            if include_admin:
                # 4a. Add owner as first user (SolutionUserAccess) if they want to be included
                self._add_owner_to_solution_access(solution_access, enterprise.owner)
                
                # 4b. Add owner as EnterpriseMember (role=owner)
                self._add_owner_as_enterprise_member(enterprise)
                
                logger.info(f"Admin included as member and owner of enterprise {enterprise.id}")
            else:
                logger.info(f"Admin chose not to be included as member of the enterprise solution")
            
            # 5. Handle admin personal subscription if requested
            if admin_wants_subscription:
                logger.info(f"Admin requested personal subscription - this should be handled separately")
                # TODO: This would need to be implemented as a separate subscription flow
            
            logger.info(f"Successfully processed enterprise solution subscription for enterprise {enterprise.id}")
            return solution_access
            
        except Exception as e:
            logger.error(f"Error processing enterprise solution subscription: {str(e)}")
            raise ValidationError(f"Error processing enterprise subscription: {str(e)}")

    def handle_service_payment(self, session, customer, metadata):
        """Handle enterprise service payment"""
        # Implementation for enterprise service payments
        logger.info(f"Processing enterprise service payment for session {getattr(session, 'id', None)}")
        # TODO: Implement if needed
        return True

    def _create_or_update_enterprise_from_metadata(self, metadata):
        """Create or update an enterprise from session metadata."""
        from enterprise.models import Enterprise, Location
        
        try:
            user = User.objects.get(id=metadata['user_id'])
        except Exception as e:
            logger.error(f"User not found for ID {metadata['user_id']}: {str(e)}")
            raise ValidationError("User not found")
        
        # Use the validated enterprise_name from frontend, not user name
        enterprise_name = metadata.get('enterprise_name', f"{user.first_name} {user.last_name}".strip())
        
        # Create or update enterprise
        enterprise, created = Enterprise.objects.get_or_create(
            owner=user,
            defaults={
                'name': enterprise_name,  # Use proper enterprise name
                'additional_email': metadata.get('enterprise_email', user.email),
                'contact_number': metadata.get('enterprise_phone', user.phone_number or ''),
                'status': 'active'
            }
        )
        if not created:
            # Update enterprise info with provided data
            enterprise.name = enterprise_name
            enterprise.additional_email = metadata.get('enterprise_email', user.email)
            enterprise.contact_number = metadata.get('enterprise_phone', user.phone_number or '')
            enterprise.save()
        
        # Update user enterprise reference
        user.enterprise = enterprise
        user.save()
        
        # Parse and create location if needed
        try:
            address_str = metadata.get('enterprise_address', '{}')
            if isinstance(address_str, str):
                address = json.loads(address_str)
            else:
                address = address_str
            
            # Only create location if we have meaningful data
            if address and any(v for k, v in address.items() if k != 'default' and v != 'default'):
                Location.objects.get_or_create(
                    enterprise=enterprise,
                    defaults={
                        'address': address.get('street', ''),
                        'city': address.get('city', ''),
                        'state': address.get('state', ''),
                        'country': address.get('country', ''),
                        'zip_code': address.get('zip_code', '')
                    }
                )
        except Exception as e:
            logger.warning(f"Could not create location for enterprise {enterprise.id}: {str(e)}")
        
        return enterprise

    def _create_solution_access(self, enterprise, solution_id):
        """Create solution access for enterprise"""
        from content_management.models import Solution
        
        try:
            solution = Solution.objects.get(id=solution_id)
        except Exception as e:
            logger.error(f"Solution not found for ID {solution_id}: {str(e)}")
            raise ValidationError("Solution not found")
        
        solution_access = SolutionAccess.objects.create(
            enterprise=enterprise,
            solution=solution,
            total_seats=20,
            used_seats=1,
            status=AccessStatus.ACTIVE
        )
        return solution_access

    def _add_owner_to_solution_access(self, solution_access, user):
        """Add owner to solution user access"""
        from billing.models import SolutionUserAccess
        
        try:
            SolutionUserAccess.objects.create(
                solution_access=solution_access,
                user=user,
                status=AccessStatus.ACTIVE,
                role='owner'
            )
        except Exception as e:
            logger.warning(f"Could not add owner {user.id} to solution access {solution_access.id}: {str(e)}")

    def _add_owner_as_enterprise_member(self, enterprise):
        """Add owner as enterprise member with proper permissions"""
        from enterprise.models import EnterpriseMember
        
        member, created = EnterpriseMember.objects.get_or_create(
            enterprise=enterprise,
            user=enterprise.owner,
            defaults={
                "role": "owner", 
                "is_active": True,
                "can_manage_members": True,
                "can_manage_billing": True
            }
        )
        if not created:
            # Update existing member to owner if needed
            member.role = "owner"
            member.can_manage_members = True
            member.can_manage_billing = True
            member.save()
        
        logger.info(f"EnterpriseMember for owner created: {created}, id: {member.id}")
        return member 