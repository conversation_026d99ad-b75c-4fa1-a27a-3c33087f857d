"""
Usage tracking and platform fee models for billing system.

This module contains models for tracking usage metrics, limits, and platform fees
for enterprise solutions and services.
"""
from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from .base import BillingBaseModel, StatusMixin, CacheableMixin
from .managers import UsageManager
import logging
from typing import Optional, Dict, Any, List
from decimal import Decimal
from datetime import timedelta, date

logger = logging.getLogger(__name__)


class PlatformFee(BillingBaseModel, StatusMixin):
    """
    Represents platform fees for enterprise payments and transfers.
    
    This model manages the fee structure for payments processed through the platform,
    including percentage-based and fixed fees.
    """
    name = models.CharField(
        max_length=255,
        default="Default Platform Fee",
        help_text="Descriptive name for this fee structure"
    )
    percentage = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        help_text="Platform fee percentage (e.g., 2.50 for 2.5%)"
    )
    fixed_amount = models.IntegerField(
        default=0, 
        help_text="Fixed platform fee in cents"
    )
    active = models.BooleanField(default=True)
    
    # Fee application rules
    applies_to_enterprise_payments = models.BooleanField(default=True)
    applies_to_user_transfers = models.BooleanField(default=True)
    applies_to_donations = models.BooleanField(default=True)
    
    # Minimum and maximum fee limits
    minimum_fee = models.IntegerField(
        default=0,
        help_text="Minimum fee amount in cents"
    )
    maximum_fee = models.IntegerField(
        null=True,
        blank=True,
        help_text="Maximum fee amount in cents (null for no limit)"
    )
    
    class Meta:
        db_table = 'billing_platform_fee'
        indexes = [
            models.Index(fields=['active']),
            models.Index(fields=['percentage']),
        ]
    
    def __str__(self):
        fixed_part = f" + ${self.fixed_amount/100:.2f}" if self.fixed_amount > 0 else ""
        return f"{self.name}: {self.percentage}%{fixed_part}"
    
    def clean(self):
        """Validate platform fee data."""
        super().clean()
        
        # Validate percentage is non-negative
        if self.percentage < 0:
            raise ValidationError("Percentage cannot be negative")
        
        # Validate percentage is reasonable (less than 100%)
        if self.percentage >= 100:
            raise ValidationError("Percentage must be less than 100%")
        
        # Validate fixed amount is non-negative
        if self.fixed_amount < 0:
            raise ValidationError("Fixed amount cannot be negative")
        
        # Validate minimum fee
        if self.minimum_fee < 0:
            raise ValidationError("Minimum fee cannot be negative")
        
        # Validate maximum fee if set
        if self.maximum_fee is not None:
            if self.maximum_fee < self.minimum_fee:
                raise ValidationError("Maximum fee cannot be less than minimum fee")
    
    def calculate_fee(self, amount: int) -> int:
        """
        Calculate platform fee for a given amount.
        
        Args:
            amount: Amount in cents
            
        Returns:
            Fee amount in cents
        """
        if amount <= 0:
            return 0
        
        # Calculate percentage fee
        percentage_fee = int(amount * self.percentage / 100)
        
        # Add fixed fee
        total_fee = percentage_fee + self.fixed_amount
        
        # Apply minimum fee
        if total_fee < self.minimum_fee:
            total_fee = self.minimum_fee
        
        # Apply maximum fee if set
        if self.maximum_fee is not None and total_fee > self.maximum_fee:
            total_fee = self.maximum_fee
        
        return total_fee
    
    def get_net_amount(self, gross_amount: int) -> int:
        """Get net amount after deducting platform fee."""
        fee = self.calculate_fee(gross_amount)
        return gross_amount - fee
    
    @classmethod
    def get_active_fee(cls, payment_type: str = 'enterprise') -> Optional['PlatformFee']:
        """Get the active platform fee for a specific payment type."""
        filters = {'active': True}
        
        if payment_type == 'enterprise':
            filters['applies_to_enterprise_payments'] = True
        elif payment_type == 'transfer':
            filters['applies_to_user_transfers'] = True
        elif payment_type == 'donation':
            filters['applies_to_donations'] = True
        
        return cls.objects.filter(**filters).first()


class SolutionUsage(BillingBaseModel, CacheableMixin):
    """
    Tracks usage metrics for enterprise solutions.
    
    This model records daily usage of various metrics like AI tokens, storage,
    and API calls for enterprise solution access.
    """
    METRIC_TYPE_CHOICES = [
        ('ai_tokens', 'AI Tokens'),
        ('storage', 'Storage (bytes)'),
        ('api_calls', 'API Calls'),
        ('users', 'Active Users'),
        ('analyses', 'Analyses Performed'),
        ('uploads', 'File Uploads'),
        ('downloads', 'File Downloads'),
    ]
    
    solution_access = models.ForeignKey(
        'billing.SolutionAccess',
        on_delete=models.CASCADE,
        related_name='usage_metrics'
    )
    metric_type = models.CharField(
        max_length=50,
        choices=METRIC_TYPE_CHOICES
    )
    usage_amount = models.DecimalField(
        max_digits=20, 
        decimal_places=2, 
        help_text="Amount of usage (tokens, bytes, calls, etc.)"
    )
    usage_date = models.DateField()
    
    # Additional context
    source = models.CharField(
        max_length=100,
        blank=True,
        help_text="Source of the usage (e.g., 'api', 'web_interface', 'batch_job')"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="User who generated this usage (if applicable)"
    )
    
    objects = UsageManager()
    
    class Meta:
        db_table = 'billing_solution_usage'
        indexes = [
            models.Index(fields=['solution_access', 'metric_type', 'usage_date']),
            models.Index(fields=['usage_date']),
            models.Index(fields=['metric_type']),
            models.Index(fields=['user']),
        ]
        unique_together = ('solution_access', 'metric_type', 'usage_date', 'source', 'user')
    
    def __str__(self):
        return f"{self.solution_access.enterprise.name} - {self.metric_type} - {self.usage_date}"
    
    def clean(self):
        """Validate usage data."""
        super().clean()
        
        # Validate usage amount is non-negative
        if self.usage_amount < 0:
            raise ValidationError("Usage amount cannot be negative")
        
        # Validate usage date is not in the future
        if self.usage_date > date.today():
            raise ValidationError("Usage date cannot be in the future")
    
    @classmethod
    def record_usage(
        cls, 
        solution_access, 
        metric_type: str, 
        amount: Decimal, 
        usage_date: date = None,
        source: str = '',
        user = None
    ) -> 'SolutionUsage':
        """
        Record usage for a solution access.
        
        This method creates or updates a usage record for the given parameters.
        """
        if usage_date is None:
            usage_date = date.today()
        
        usage, created = cls.objects.get_or_create(
            solution_access=solution_access,
            metric_type=metric_type,
            usage_date=usage_date,
            source=source or '',
            user=user,
            defaults={'usage_amount': amount}
        )
        
        if not created:
            usage.usage_amount += amount
            usage.save(update_fields=['usage_amount'])
        
        logger.info(
            f"Recorded {amount} {metric_type} usage for solution access {solution_access.pk}"
        )
        
        return usage
    
    @classmethod
    def get_monthly_usage(
        cls, 
        solution_access, 
        metric_type: str, 
        year: int, 
        month: int
    ) -> Decimal:
        """Get total usage for a specific month."""
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(year, month + 1, 1) - timedelta(days=1)
        
        total = cls.objects.filter(
            solution_access=solution_access,
            metric_type=metric_type,
            usage_date__range=[start_date, end_date]
        ).aggregate(total=models.Sum('usage_amount'))['total']
        
        return total or Decimal('0')
    
    def check_against_limits(self) -> Dict[str, Any]:
        """Check if this usage exceeds any defined limits."""
        try:
            limit = SolutionUsageLimit.objects.get(
                solution_access=self.solution_access,
                metric_type=self.metric_type
            )
            
            # Get current period usage
            if limit.reset_period == 'daily':
                current_usage = self.usage_amount
            elif limit.reset_period == 'monthly':
                current_usage = self.get_monthly_usage(
                    self.solution_access,
                    self.metric_type,
                    self.usage_date.year,
                    self.usage_date.month
                )
            else:  # yearly
                year_start = date(self.usage_date.year, 1, 1)
                year_end = date(self.usage_date.year, 12, 31)
                current_usage = SolutionUsage.objects.filter(
                    solution_access=self.solution_access,
                    metric_type=self.metric_type,
                    usage_date__range=[year_start, year_end]
                ).aggregate(total=models.Sum('usage_amount'))['total'] or Decimal('0')
            
            exceeds_limit = current_usage > limit.limit_amount
            usage_percentage = (current_usage / limit.limit_amount * 100) if limit.limit_amount > 0 else 0
            
            return {
                'has_limit': True,
                'limit_amount': limit.limit_amount,
                'current_usage': current_usage,
                'exceeds_limit': exceeds_limit,
                'usage_percentage': float(usage_percentage),
                'reset_period': limit.reset_period
            }
            
        except SolutionUsageLimit.DoesNotExist:
            return {'has_limit': False}


class SolutionUsageLimit(BillingBaseModel):
    """
    Defines usage limits for enterprise solutions.
    
    This model sets limits on various usage metrics and defines reset periods
    for tracking usage against those limits.
    """
    METRIC_TYPE_CHOICES = SolutionUsage.METRIC_TYPE_CHOICES
    
    RESET_PERIOD_CHOICES = [
        ('daily', 'Daily'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
    ]
    
    solution_access = models.ForeignKey(
        'billing.SolutionAccess',
        on_delete=models.CASCADE,
        related_name='usage_limits'
    )
    metric_type = models.CharField(
        max_length=50,
        choices=METRIC_TYPE_CHOICES
    )
    limit_amount = models.DecimalField(
        max_digits=20, 
        decimal_places=2, 
        help_text="Usage limit amount"
    )
    reset_period = models.CharField(
        max_length=50,
        choices=RESET_PERIOD_CHOICES
    )
    
    # Limit enforcement
    enforce_limit = models.BooleanField(
        default=True,
        help_text="Whether to enforce this limit (block usage when exceeded)"
    )
    warning_threshold = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('80.00'),
        help_text="Percentage threshold for usage warnings (e.g., 80.00 for 80%)"
    )
    
    class Meta:
        db_table = 'billing_solution_usage_limit'
        unique_together = ('solution_access', 'metric_type')
        indexes = [
            models.Index(fields=['solution_access', 'metric_type']),
            models.Index(fields=['enforce_limit']),
        ]
    
    def __str__(self):
        return f"{self.solution_access.enterprise.name} - {self.metric_type} Limit"
    
    def clean(self):
        """Validate usage limit data."""
        super().clean()
        
        # Validate limit amount is positive
        if self.limit_amount <= 0:
            raise ValidationError("Limit amount must be positive")
        
        # Validate warning threshold
        if self.warning_threshold < 0 or self.warning_threshold > 100:
            raise ValidationError("Warning threshold must be between 0 and 100")
    
    def get_current_usage(self) -> Decimal:
        """Get current usage for this limit's reset period."""
        today = date.today()
        
        if self.reset_period == 'daily':
            usage_date = today
            return SolutionUsage.objects.filter(
                solution_access=self.solution_access,
                metric_type=self.metric_type,
                usage_date=usage_date
            ).aggregate(total=models.Sum('usage_amount'))['total'] or Decimal('0')
        
        elif self.reset_period == 'monthly':
            return SolutionUsage.get_monthly_usage(
                self.solution_access,
                self.metric_type,
                today.year,
                today.month
            )
        
        else:  # yearly
            year_start = date(today.year, 1, 1)
            year_end = date(today.year, 12, 31)
            return SolutionUsage.objects.filter(
                solution_access=self.solution_access,
                metric_type=self.metric_type,
                usage_date__range=[year_start, year_end]
            ).aggregate(total=models.Sum('usage_amount'))['total'] or Decimal('0')
    
    def get_usage_percentage(self) -> float:
        """Get current usage as percentage of limit."""
        current_usage = self.get_current_usage()
        if self.limit_amount <= 0:
            return 0.0
        return float(current_usage / self.limit_amount * 100)
    
    def is_limit_exceeded(self) -> bool:
        """Check if current usage exceeds the limit."""
        return self.get_current_usage() > self.limit_amount
    
    def is_warning_threshold_reached(self) -> bool:
        """Check if usage has reached the warning threshold."""
        return self.get_usage_percentage() >= float(self.warning_threshold)
    
    def get_remaining_usage(self) -> Decimal:
        """Get remaining usage before hitting the limit."""
        current_usage = self.get_current_usage()
        remaining = self.limit_amount - current_usage
        return max(Decimal('0'), remaining)
    
    def can_consume(self, amount: Decimal) -> bool:
        """Check if a specific amount can be consumed without exceeding limit."""
        if not self.enforce_limit:
            return True
        
        current_usage = self.get_current_usage()
        return (current_usage + amount) <= self.limit_amount
