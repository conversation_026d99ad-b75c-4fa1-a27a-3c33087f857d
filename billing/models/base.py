"""
Base model classes and mixins for billing models.

This module provides common functionality and base classes that can be
shared across different billing model types.
"""
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.conf import settings
from config.models import BaseModel
from decimal import Decimal
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class BillingBaseModel(BaseModel):
    """
    Abstract base model for all billing-related models.
    
    Provides common fields and functionality for billing models including
    metadata storage, audit fields, and common validation.
    """
    metadata = models.JSONField(default=dict, blank=True, help_text="Additional metadata for this record")
    
    class Meta:
        abstract = True
    
    def clean(self):
        """Perform model validation."""
        super().clean()
        self.validate_metadata()
    
    def validate_metadata(self):
        """Validate metadata field."""
        if not isinstance(self.metadata, dict):
            raise ValidationError("Metadata must be a dictionary")
    
    def get_metadata_value(self, key: str, default: Any = None) -> Any:
        """Get a value from metadata with optional default."""
        return self.metadata.get(key, default)
    
    def set_metadata_value(self, key: str, value: Any) -> None:
        """Set a value in metadata."""
        self.metadata[key] = value
    
    def update_metadata(self, data: Dict[str, Any]) -> None:
        """Update metadata with new data."""
        self.metadata.update(data)


class StripeIntegrationMixin(models.Model):
    """
    Mixin for models that integrate with Stripe.
    
    Provides common fields and methods for Stripe-integrated models.
    """
    stripe_id = models.CharField(
        max_length=255, 
        unique=True, 
        help_text="Stripe object ID"
    )
    
    class Meta:
        abstract = True
    
    def sync_from_stripe(self, stripe_object: Dict[str, Any]) -> None:
        """
        Sync model data from Stripe object.
        
        This method should be overridden by subclasses to implement
        specific sync logic.
        """
        raise NotImplementedError("Subclasses must implement sync_from_stripe")
    
    def get_stripe_object(self):
        """
        Retrieve the corresponding Stripe object.
        
        This method should be overridden by subclasses to implement
        specific retrieval logic.
        """
        raise NotImplementedError("Subclasses must implement get_stripe_object")


class AmountMixin(models.Model):
    """
    Mixin for models that handle monetary amounts.
    
    Provides common fields and methods for amount handling.
    """
    amount = models.IntegerField(help_text="Amount in cents")
    currency = models.CharField(max_length=3, default='usd')
    
    class Meta:
        abstract = True
    
    @property
    def amount_decimal(self) -> Decimal:
        """Get amount as decimal value in major currency units."""
        return Decimal(self.amount) / Decimal('100')
    
    @property
    def formatted_amount(self) -> str:
        """Get formatted amount string."""
        return f"{self.amount_decimal:.2f} {self.currency.upper()}"
    
    def set_amount_from_decimal(self, amount: Decimal) -> None:
        """Set amount from decimal value."""
        self.amount = int(amount * 100)
    
    def validate_amount(self):
        """Validate amount is positive."""
        if self.amount < 0:
            raise ValidationError("Amount cannot be negative")


class StatusMixin(models.Model):
    """
    Mixin for models with status tracking.
    
    Provides common status field and methods.
    """
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('pending', 'Pending'),
        ('expired', 'Expired'),
        ('canceled', 'Canceled'),
        ('failed', 'Failed'),
    ]
    
    status = models.CharField(
        max_length=50,
        choices=STATUS_CHOICES,
        default='pending'
    )
    status_changed_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        abstract = True
    
    def update_status(self, new_status: str, save: bool = True) -> None:
        """Update status and timestamp."""
        if new_status != self.status:
            old_status = self.status
            self.status = new_status
            self.status_changed_at = timezone.now()
            
            if save:
                self.save(update_fields=['status', 'status_changed_at'])
            
            # Log status change
            logger.info(
                f"{self.__class__.__name__} {self.pk} status changed from "
                f"{old_status} to {new_status}"
            )
    
    @property
    def is_active(self) -> bool:
        """Check if status is active."""
        return self.status == 'active'
    
    @property
    def is_pending(self) -> bool:
        """Check if status is pending."""
        return self.status == 'pending'
    
    @property
    def is_expired(self) -> bool:
        """Check if status is expired."""
        return self.status == 'expired'


class AccessMixin(models.Model):
    """
    Mixin for models that track access permissions.
    
    Provides common access-related fields and methods.
    """
    access_granted_at = models.DateTimeField(auto_now_add=True)
    access_expires_at = models.DateTimeField(null=True, blank=True)
    last_accessed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        abstract = True
    
    def grant_access(self, expires_at: Optional[timezone.datetime] = None) -> None:
        """Grant access with optional expiration."""
        self.access_granted_at = timezone.now()
        self.access_expires_at = expires_at
        self.update_status('active')
    
    def revoke_access(self) -> None:
        """Revoke access."""
        self.update_status('revoked')
    
    def update_last_accessed(self) -> None:
        """Update last accessed timestamp."""
        self.last_accessed_at = timezone.now()
        self.save(update_fields=['last_accessed_at'])
    
    @property
    def is_access_expired(self) -> bool:
        """Check if access has expired."""
        if not self.access_expires_at:
            return False
        return timezone.now() > self.access_expires_at
    
    @property
    def has_valid_access(self) -> bool:
        """Check if access is valid (active and not expired)."""
        return self.is_active and not self.is_access_expired
    
    def get_access_duration(self) -> Optional[timezone.timedelta]:
        """Get duration of access if it has an expiration."""
        if not self.access_expires_at:
            return None
        return self.access_expires_at - self.access_granted_at


class UsageMixin(models.Model):
    """
    Mixin for models that track usage metrics.
    
    Provides common usage tracking fields and methods.
    """
    usage_count = models.IntegerField(default=0, help_text="Number of times used")
    last_usage_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        abstract = True
    
    def increment_usage(self) -> None:
        """Increment usage count and update timestamp."""
        self.usage_count += 1
        self.last_usage_at = timezone.now()
        self.save(update_fields=['usage_count', 'last_usage_at'])
    
    def reset_usage(self) -> None:
        """Reset usage count."""
        self.usage_count = 0
        self.last_usage_at = None
        self.save(update_fields=['usage_count', 'last_usage_at'])
    
    @property
    def has_been_used(self) -> bool:
        """Check if the resource has been used."""
        return self.usage_count > 0


class AuditMixin(models.Model):
    """
    Mixin for models that need audit trail functionality.
    
    Provides fields to track who created/modified records and when.
    """
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_created'
    )
    modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_modified'
    )
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """Override save to update audit fields."""
        user = kwargs.pop('user', None)
        
        if user:
            if not self.pk:  # New record
                self.created_by = user
            self.modified_by = user
        
        super().save(*args, **kwargs)


class CacheableMixin(models.Model):
    """
    Mixin for models that support caching.
    
    Provides methods for cache management.
    """
    
    class Meta:
        abstract = True
    
    def get_cache_key(self, suffix: str = '') -> str:
        """Generate cache key for this model instance."""
        base_key = f"{self._meta.app_label}:{self._meta.model_name}:{self.pk}"
        return f"{base_key}:{suffix}" if suffix else base_key
    
    def invalidate_cache(self, suffix: str = '') -> None:
        """Invalidate cache for this model instance."""
        from django.core.cache import cache
        cache_key = self.get_cache_key(suffix)
        cache.delete(cache_key)
    
    def get_from_cache(self, suffix: str = '', default: Any = None) -> Any:
        """Get value from cache."""
        from django.core.cache import cache
        cache_key = self.get_cache_key(suffix)
        return cache.get(cache_key, default)
    
    def set_cache(self, value: Any, suffix: str = '', timeout: int = 3600) -> None:
        """Set value in cache."""
        from django.core.cache import cache
        cache_key = self.get_cache_key(suffix)
        cache.set(cache_key, value, timeout)
