"""
Product and pricing models for billing system.

This module contains models for managing products and their pricing structures
that integrate with Stripe products and prices.
"""
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from content_management.models import Service, SubscriptionPlan, Solution
from .base import BillingBaseModel, StripeIntegrationMixin, StatusMixin, AmountMixin, CacheableMixin
import stripe
import logging
from typing import Optional, Dict, Any, List
from decimal import Decimal

logger = logging.getLogger(__name__)


class Product(BillingBaseModel, StripeIntegrationMixin, StatusMixin, CacheableMixin):
    """
    Represents a Stripe Product for SaaS platform services.
    
    Maps to Service, SubscriptionPlan, and Solution models from content_management
    and provides integration with Stripe products.
    """
    stripe_product_id = models.CharField(max_length=255, unique=True)
    name = models.CharField(max_length=255)
    content_id = models.UUIDField(unique=True, null=True, blank=True)
    description = models.TextField(blank=True)
    active = models.BooleanField(default=True)
    
    PRODUCT_TYPE_CHOICES = [
        ('service', 'Service'),
        ('subscription', 'Subscription'),
        ('solution', 'Solution')
    ]
    
    product_type = models.CharField(
        max_length=50,
        choices=PRODUCT_TYPE_CHOICES
    )
    
    # Foreign key relationships to content models
    service = models.ForeignKey(
        Service, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='stripe_products'
    )
    subscription_plan = models.ForeignKey(
        SubscriptionPlan, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='stripe_products'
    )
    solution = models.ForeignKey(
        Solution, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='stripe_products'
    )
    
    # Override the stripe_id field from mixin to use stripe_product_id
    stripe_id = None
    
    class Meta:
        db_table = 'billing_product'
        indexes = [
            models.Index(fields=['stripe_product_id']),
            models.Index(fields=['product_type']),
            models.Index(fields=['active']),
            models.Index(fields=['content_id']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.product_type})"
    
    def clean(self):
        """Validate product data and relationships."""
        super().clean()
        
        # Validate that the correct content model is set based on product_type
        if self.product_type == 'service' and not self.service:
            raise ValidationError('Service must be set for service product type')
        elif self.product_type == 'subscription' and not self.subscription_plan:
            raise ValidationError('Subscription plan must be set for subscription product type')
        elif self.product_type == 'solution' and not self.solution:
            raise ValidationError('Solution must be set for solution product type')
        
        # Validate that only the correct content model is set
        if self.product_type != 'service' and self.service:
            raise ValidationError('Service should not be set for non-service product type')
        if self.product_type != 'subscription' and self.subscription_plan:
            raise ValidationError('Subscription plan should not be set for non-subscription product type')
        if self.product_type != 'solution' and self.solution:
            raise ValidationError('Solution should not be set for non-solution product type')
    
    def save(self, *args, **kwargs):
        """Override save to ensure data consistency."""
        self.clean()
        
        # Set content_id from the related content model
        if self.product_type == 'service' and self.service:
            self.content_id = self.service.id
        elif self.product_type == 'subscription' and self.subscription_plan:
            self.content_id = self.subscription_plan.id
        elif self.product_type == 'solution' and self.solution:
            self.content_id = self.solution.id
        
        super().save(*args, **kwargs)
        
        # Invalidate related caches
        self.invalidate_cache('prices')
    
    def sync_from_stripe(self, stripe_product: Dict[str, Any]) -> None:
        """Sync product data from Stripe product object."""
        self.name = stripe_product.get('name', self.name)
        self.description = stripe_product.get('description', self.description)
        self.active = stripe_product.get('active', self.active)
        self.metadata.update(stripe_product.get('metadata', {}))
        self.save()
        
        logger.info(f"Synced product {self.pk} from Stripe product {self.stripe_product_id}")
    
    def get_stripe_object(self):
        """Retrieve the Stripe product object."""
        try:
            return stripe.Product.retrieve(self.stripe_product_id)
        except stripe.error.StripeError as e:
            logger.error(f"Failed to retrieve Stripe product {self.stripe_product_id}: {e}")
            return None
    
    def get_content_model(self):
        """Get the related content model based on product type."""
        if self.product_type == 'service':
            return self.service
        elif self.product_type == 'subscription':
            return self.subscription_plan
        elif self.product_type == 'solution':
            return self.solution
        return None
    
    def get_active_prices(self):
        """Get all active prices for this product."""
        return self.prices.filter(active=True)
    
    def get_default_price(self):
        """Get the default price for this product."""
        return self.prices.filter(active=True).first()
    
    def update_stripe_product(self, **kwargs) -> bool:
        """Update the Stripe product with new data."""
        try:
            stripe_product = stripe.Product.modify(
                self.stripe_product_id,
                **kwargs
            )
            self.sync_from_stripe(stripe_product)
            return True
        except stripe.error.StripeError as e:
            logger.error(f"Failed to update Stripe product {self.stripe_product_id}: {e}")
            return False
    
    def deactivate(self) -> bool:
        """Deactivate the product both locally and in Stripe."""
        self.active = False
        self.update_status('inactive')
        return self.update_stripe_product(active=False)


class Price(BillingBaseModel, StripeIntegrationMixin, StatusMixin, CacheableMixin):
    """
    Represents a Stripe Price for SaaS platform services.

    Defines pricing information for products including one-time and recurring prices.
    """
    stripe_price_id = models.CharField(max_length=255, unique=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='prices')
    active = models.BooleanField(default=True)
    currency = models.CharField(max_length=3, default='usd')
    unit_amount = models.IntegerField(help_text="Amount in cents")
    recurring = models.JSONField(null=True, blank=True, help_text="Recurring billing configuration")
    
    # Override the stripe_id field from mixin to use stripe_price_id
    stripe_id = None
    
    class Meta:
        db_table = 'billing_price'
        indexes = [
            models.Index(fields=['stripe_price_id']),
            models.Index(fields=['product']),
            models.Index(fields=['active']),
            models.Index(fields=['currency']),
        ]
    
    def __str__(self):
        return f"{self.product.name} - {self.formatted_amount}"

    # Backward compatibility properties
    @property
    def amount(self):
        """Alias for unit_amount for backward compatibility."""
        return self.unit_amount

    @amount.setter
    def amount(self, value):
        """Setter for amount property."""
        self.unit_amount = value

    @property
    def amount_decimal(self):
        """Get amount as decimal value in major currency units."""
        from decimal import Decimal
        return Decimal(self.unit_amount) / Decimal('100')

    @property
    def formatted_amount(self):
        """Get formatted amount string."""
        return f"{self.amount_decimal:.2f} {self.currency.upper()}"

    def validate_unit_amount(self):
        """Validate unit_amount is positive."""
        if self.unit_amount < 0:
            raise ValidationError("Amount cannot be negative")
    
    def clean(self):
        """Validate price data."""
        super().clean()
        self.validate_unit_amount()
        self.validate_recurring()
    
    def validate_recurring(self):
        """Validate recurring billing configuration."""
        if self.recurring:
            if not isinstance(self.recurring, dict):
                raise ValidationError("Recurring must be a dictionary")
            
            required_fields = ['interval']
            for field in required_fields:
                if field not in self.recurring:
                    raise ValidationError(f"Recurring configuration missing required field: {field}")
            
            valid_intervals = ['day', 'week', 'month', 'year']
            if self.recurring['interval'] not in valid_intervals:
                raise ValidationError(f"Invalid recurring interval. Must be one of: {valid_intervals}")
    
    def save(self, *args, **kwargs):
        """Override save to ensure data consistency."""
        self.clean()
        super().save(*args, **kwargs)
        
        # Invalidate related caches
        self.invalidate_cache('subscriptions')
        self.product.invalidate_cache('prices')
    
    def sync_from_stripe(self, stripe_price: Dict[str, Any]) -> None:
        """Sync price data from Stripe price object."""
        self.unit_amount = stripe_price.get('unit_amount', self.unit_amount)
        self.currency = stripe_price.get('currency', self.currency)
        self.active = stripe_price.get('active', self.active)
        self.recurring = stripe_price.get('recurring', self.recurring)
        self.metadata.update(stripe_price.get('metadata', {}))
        self.save()

        logger.info(f"Synced price {self.pk} from Stripe price {self.stripe_price_id}")
    
    def get_stripe_object(self):
        """Retrieve the Stripe price object."""
        try:
            return stripe.Price.retrieve(self.stripe_price_id)
        except stripe.error.StripeError as e:
            logger.error(f"Failed to retrieve Stripe price {self.stripe_price_id}: {e}")
            return None
    
    @property
    def is_recurring(self) -> bool:
        """Check if this is a recurring price."""
        return self.recurring is not None
    
    @property
    def is_one_time(self) -> bool:
        """Check if this is a one-time price."""
        return self.recurring is None
    
    def get_billing_interval(self) -> Optional[str]:
        """Get the billing interval for recurring prices."""
        if self.recurring:
            return self.recurring.get('interval')
        return None
    
    def get_interval_count(self) -> Optional[int]:
        """Get the interval count for recurring prices."""
        if self.recurring:
            return self.recurring.get('interval_count', 1)
        return None
    
    def get_formatted_interval(self) -> str:
        """Get a human-readable billing interval description."""
        if not self.is_recurring:
            return "One-time"
        
        interval = self.get_billing_interval()
        count = self.get_interval_count()
        
        if count == 1:
            return f"Every {interval}"
        else:
            return f"Every {count} {interval}s"
    
    def calculate_annual_amount(self) -> Optional[int]:
        """Calculate the annual amount for recurring prices."""
        if not self.is_recurring:
            return None
        
        interval = self.get_billing_interval()
        count = self.get_interval_count()
        
        # Calculate how many billing cycles per year
        cycles_per_year = {
            'day': 365 / count,
            'week': 52 / count,
            'month': 12 / count,
            'year': 1 / count
        }
        
        if interval in cycles_per_year:
            return int(self.unit_amount * cycles_per_year[interval])
        
        return None
    
    def deactivate(self) -> bool:
        """Deactivate the price both locally and in Stripe."""
        self.active = False
        self.update_status('inactive')
        
        # Note: Stripe doesn't allow modifying prices, so we just update locally
        self.save()
        return True
