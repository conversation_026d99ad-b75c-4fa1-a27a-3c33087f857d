"""
Access control models for billing system.

This module contains models for managing user access to services, solutions,
and other platform resources based on payments and subscriptions.
"""
from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from content_management.models import Service, Solution
from .base import BillingBaseModel, StatusMixin, AccessMixin, UsageMixin, CacheableMixin
from .managers import AccessManager
import logging
from typing import Optional, Dict, Any, List
from datetime import timedelta

logger = logging.getLogger(__name__)


class ServiceAccess(BillingBaseModel, StatusMixin, AccessMixin, UsageMixin, CacheableMixin):
    """
    Tracks which users have access to which services and their status.
    
    This model manages access to one-time services based on completed payments.
    """
    ACCESS_STATUS_CHOICES = [
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('revoked', 'Revoked'),
        ('suspended', 'Suspended'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='service_access'
    )
    service = models.ForeignKey(
        Service, 
        on_delete=models.CASCADE, 
        related_name='user_access'
    )
    payment = models.ForeignKey(
        'billing.ServicePayment',
        on_delete=models.CASCADE,
        related_name='access_records'
    )
    analysis_access_data = models.JSONField(
        default=dict, 
        blank=True, 
        help_text="Specific access data for analysis app"
    )
    
    status = models.CharField(
        max_length=50,
        choices=ACCESS_STATUS_CHOICES,
        default='active'
    )
    
    objects = AccessManager()
    
    class Meta:
        db_table = 'billing_service_access'
        unique_together = ('user', 'service', 'payment')
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['service']),
            models.Index(fields=['payment']),
            models.Index(fields=['status']),
            models.Index(fields=['access_granted_at']),
            models.Index(fields=['access_expires_at']),
            models.Index(fields=['last_accessed_at']),
        ]
    
    def __str__(self):
        return f"Access for {self.user.email} to {self.service.name} - {self.status}"
    
    def clean(self):
        """Validate service access data."""
        super().clean()
        
        # Ensure payment user matches access user
        if self.payment and self.payment.user != self.user:
            raise ValidationError("Payment user must match access user")
        
        # Ensure payment service matches access service
        if self.payment and self.payment.service != self.service:
            raise ValidationError("Payment service must match access service")
        
        # Validate payment is completed
        if self.payment and self.payment.status != 'completed':
            raise ValidationError("Payment must be completed to grant access")
    
    def save(self, *args, **kwargs):
        """Override save to ensure data consistency."""
        self.clean()
        super().save(*args, **kwargs)
        
        # Invalidate related caches
        self.invalidate_cache('permissions')
    
    def grant_access(self, expires_at: Optional[timezone.datetime] = None) -> None:
        """Grant access with optional expiration based on service settings."""
        if not expires_at and self.service:
            # Check if service has default access duration
            service_metadata = getattr(self.service, 'metadata', {})
            access_duration_days = service_metadata.get('access_duration_days')
            
            if access_duration_days:
                expires_at = timezone.now() + timedelta(days=access_duration_days)
        
        super().grant_access(expires_at)
        
        # Set up analysis access data if this is an analysis service
        if self.service and hasattr(self.service, 'service_type'):
            if self.service.service_type == 'analysis':
                self.setup_analysis_access()
        
        logger.info(f"Granted service access {self.pk} to user {self.user.email}")
    
    def setup_analysis_access(self) -> None:
        """Set up specific access data for analysis services."""
        if not self.analysis_access_data:
            self.analysis_access_data = {}
        
        # Set default analysis permissions
        self.analysis_access_data.update({
            'can_upload_files': True,
            'can_view_results': True,
            'can_download_results': True,
            'max_file_size_mb': 100,
            'max_files_per_analysis': 10,
            'analysis_types_allowed': ['basic', 'standard'],
            'setup_at': timezone.now().isoformat()
        })
        
        # Add service-specific permissions
        if self.service and self.service.metadata:
            service_permissions = self.service.metadata.get('analysis_permissions', {})
            self.analysis_access_data.update(service_permissions)
        
        self.save(update_fields=['analysis_access_data'])
    
    def get_analysis_permission(self, permission: str) -> Any:
        """Get a specific analysis permission value."""
        return self.analysis_access_data.get(permission)
    
    def has_analysis_permission(self, permission: str) -> bool:
        """Check if user has a specific analysis permission."""
        return bool(self.analysis_access_data.get(permission, False))
    
    def update_analysis_permission(self, permission: str, value: Any) -> None:
        """Update a specific analysis permission."""
        if not self.analysis_access_data:
            self.analysis_access_data = {}
        
        self.analysis_access_data[permission] = value
        self.save(update_fields=['analysis_access_data'])
    
    def extend_access(self, additional_days: int) -> None:
        """Extend access by additional days."""
        if self.access_expires_at:
            self.access_expires_at += timedelta(days=additional_days)
        else:
            self.access_expires_at = timezone.now() + timedelta(days=additional_days)
        
        self.save(update_fields=['access_expires_at'])
        logger.info(f"Extended service access {self.pk} by {additional_days} days")
    
    def revoke_access(self, reason: str = '') -> None:
        """Revoke service access."""
        super().revoke_access()
        self.set_metadata_value('revocation_reason', reason)
        self.save()
        
        logger.info(f"Revoked service access {self.pk} for user {self.user.email}")
    
    @property
    def is_analysis_service(self) -> bool:
        """Check if this is access to an analysis service."""
        return bool(self.analysis_access_data)
    
    def get_remaining_usage(self) -> Optional[int]:
        """Get remaining usage count if service has usage limits."""
        if self.service and self.service.metadata:
            max_usage = self.service.metadata.get('max_usage_count')
            if max_usage:
                return max(0, max_usage - self.usage_count)
        return None


class SolutionAccess(BillingBaseModel, StatusMixin, AccessMixin, CacheableMixin):
    """
    Tracks which enterprises have access to which solutions and their status.
    
    This model manages enterprise access to solution packages with seat management.
    """
    ACCESS_STATUS_CHOICES = [
        ('active', 'Active'),
        ('past_due', 'Past Due'),
        ('canceled', 'Canceled'),
        ('expired', 'Expired'),
        ('suspended', 'Suspended'),
    ]
    
    enterprise = models.ForeignKey(
        'enterprise.Enterprise', 
        on_delete=models.CASCADE, 
        related_name='solution_access'
    )
    solution = models.ForeignKey(
        Solution, 
        on_delete=models.CASCADE, 
        related_name='enterprise_access'
    )
    subscription = models.ForeignKey(
        'billing.Subscription',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='solution_access'
    )
    total_seats = models.IntegerField(
        default=20, 
        help_text="Total number of user seats purchased"
    )
    used_seats = models.IntegerField(
        default=0, 
        help_text="Number of seats currently in use"
    )
    
    status = models.CharField(
        max_length=50,
        choices=ACCESS_STATUS_CHOICES,
        default='active'
    )
    
    objects = AccessManager()
    
    class Meta:
        db_table = 'billing_solution_access'
        unique_together = ('enterprise', 'solution')
        indexes = [
            models.Index(fields=['enterprise']),
            models.Index(fields=['solution']),
            models.Index(fields=['subscription']),
            models.Index(fields=['status']),
            models.Index(fields=['total_seats']),
            models.Index(fields=['used_seats']),
        ]
    
    def __str__(self):
        return f"{self.enterprise.name} - {self.solution.name} ({self.status})"
    
    def clean(self):
        """Validate solution access data."""
        super().clean()
        
        # Validate seat counts
        if self.used_seats > self.total_seats:
            raise ValidationError("Used seats cannot exceed total seats")
        
        if self.total_seats < 1:
            raise ValidationError("Total seats must be at least 1")
    
    @property
    def available_seats(self) -> int:
        """Get number of available seats."""
        return self.total_seats - self.used_seats
    
    @property
    def seat_utilization_percentage(self) -> float:
        """Get seat utilization as percentage."""
        if self.total_seats == 0:
            return 0.0
        return (self.used_seats / self.total_seats) * 100
    
    def add_seats(self, count: int) -> None:
        """Add additional seats to the solution access."""
        if count > 0:
            self.total_seats += count
            self.save(update_fields=['total_seats'])
            logger.info(f"Added {count} seats to solution access {self.pk}")
    
    def remove_seats(self, count: int) -> None:
        """Remove seats from the solution access."""
        if count > 0:
            new_total = max(self.used_seats, self.total_seats - count)
            if new_total != self.total_seats:
                self.total_seats = new_total
                self.save(update_fields=['total_seats'])
                logger.info(f"Removed {count} seats from solution access {self.pk}")
    
    def assign_user_seat(self, user) -> bool:
        """Assign a seat to a user."""
        if self.available_seats <= 0:
            return False
        
        # Check if user already has access
        existing_access = self.users.filter(user=user, status='active').first()
        if existing_access:
            return True  # User already has access
        
        # Create or reactivate user access
        user_access, created = SolutionUserAccess.objects.get_or_create(
            solution_access=self,
            user=user,
            defaults={'status': 'active', 'role': 'member'}
        )
        
        if not created and user_access.status != 'active':
            user_access.status = 'active'
            user_access.assigned_at = timezone.now()
            user_access.revoked_at = None
            user_access.save()
        
        # Update used seats count
        self.used_seats = self.users.filter(status='active').count()
        self.save(update_fields=['used_seats'])
        
        logger.info(f"Assigned seat to user {user.email} for solution access {self.pk}")
        return True
    
    def revoke_user_seat(self, user) -> bool:
        """Revoke a user's seat."""
        try:
            user_access = self.users.get(user=user, status='active')
            user_access.status = 'revoked'
            user_access.revoked_at = timezone.now()
            user_access.save()
            
            # Update used seats count
            self.used_seats = self.users.filter(status='active').count()
            self.save(update_fields=['used_seats'])
            
            logger.info(f"Revoked seat for user {user.email} from solution access {self.pk}")
            return True
            
        except SolutionUserAccess.DoesNotExist:
            return False
    
    def get_active_users(self):
        """Get all users with active access to this solution."""
        return self.users.filter(status='active')
    
    def sync_with_subscription(self) -> None:
        """Sync access status with subscription status."""
        if not self.subscription:
            return
        
        # Map subscription status to access status
        status_mapping = {
            'active': 'active',
            'trialing': 'active',
            'past_due': 'past_due',
            'canceled': 'canceled',
            'incomplete': 'suspended',
            'incomplete_expired': 'expired',
            'unpaid': 'suspended',
            'paused': 'suspended',
        }
        
        new_status = status_mapping.get(self.subscription.status, 'suspended')
        if new_status != self.status:
            self.update_status(new_status)
            
            # If access is being revoked, revoke all user seats
            if new_status in ['canceled', 'expired', 'suspended']:
                self.users.filter(status='active').update(
                    status='revoked',
                    revoked_at=timezone.now()
                )
                self.used_seats = 0
            
            self.save()


class SolutionUserAccess(BillingBaseModel, StatusMixin):
    """
    Tracks individual user access to enterprise solutions.
    
    This model manages individual user permissions within enterprise solution access.
    """
    USER_ACCESS_STATUS_CHOICES = [
        ('active', 'Active'),
        ('revoked', 'Revoked'),
        ('expired', 'Expired'),
        ('suspended', 'Suspended'),
    ]
    
    ROLE_CHOICES = [
        ('owner', 'Owner'),
        ('admin', 'Administrator'),
        ('member', 'Member'),
        ('viewer', 'Viewer'),
    ]
    
    solution_access = models.ForeignKey(
        SolutionAccess, 
        on_delete=models.CASCADE, 
        related_name='users'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE
    )
    assigned_at = models.DateTimeField(auto_now_add=True)
    revoked_at = models.DateTimeField(null=True, blank=True)
    role = models.CharField(
        max_length=20, 
        choices=ROLE_CHOICES, 
        default='member'
    )
    
    status = models.CharField(
        max_length=20,
        choices=USER_ACCESS_STATUS_CHOICES,
        default='active'
    )
    
    class Meta:
        db_table = 'billing_solution_user_access'
        unique_together = ('solution_access', 'user')
        indexes = [
            models.Index(fields=['solution_access', 'user']),
            models.Index(fields=['status']),
            models.Index(fields=['role']),
            models.Index(fields=['assigned_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.solution_access.solution.name}"
    
    def clean(self):
        """Validate user access data."""
        super().clean()
        
        # Validate that solution access is active
        if self.solution_access and not self.solution_access.is_active:
            raise ValidationError("Cannot assign user to inactive solution access")
    
    def revoke_access(self, reason: str = '') -> None:
        """Revoke user access to the solution."""
        self.update_status('revoked')
        self.revoked_at = timezone.now()
        self.set_metadata_value('revocation_reason', reason)
        self.save()
        
        # Update parent solution access used seats count
        self.solution_access.used_seats = self.solution_access.users.filter(status='active').count()
        self.solution_access.save(update_fields=['used_seats'])
        
        logger.info(f"Revoked solution user access {self.pk} for user {self.user.email}")
    
    def change_role(self, new_role: str) -> None:
        """Change user's role in the solution."""
        if new_role in dict(self.ROLE_CHOICES):
            old_role = self.role
            self.role = new_role
            self.save(update_fields=['role'])
            
            logger.info(f"Changed role for user {self.user.email} from {old_role} to {new_role}")
    
    @property
    def is_admin_or_owner(self) -> bool:
        """Check if user has admin or owner role."""
        return self.role in ['admin', 'owner']
    
    @property
    def can_manage_users(self) -> bool:
        """Check if user can manage other users."""
        return self.role in ['admin', 'owner']
    
    @property
    def can_view_analytics(self) -> bool:
        """Check if user can view solution analytics."""
        return self.role in ['admin', 'owner']
