"""
Billing models package.

This package contains all billing-related models organized by domain.
All models are imported here for backward compatibility with existing code.
"""

# Import all models for backward compatibility
from .customer import Customer, UserPaymentProfile
from .product import Product, Price
from .payment import ServicePayment, UserTransfer
from .subscription import Subscription, SubscriptionAccess
from .access import ServiceAccess, SolutionAccess, SolutionUserAccess
from .enterprise import EnterpriseAccount
from .usage import PlatformFee, SolutionUsage, SolutionUsageLimit

# Import base classes and managers for advanced usage
from .base import (
    BillingBaseModel,
    StripeIntegrationMixin,
    AmountMixin,
    StatusMixin,
    AccessMixin,
    UsageMixin,
    AuditMixin,
    CacheableMixin
)

from .managers import (
    PaymentManager,
    PaymentQuerySet,
    SubscriptionManager,
    SubscriptionQuerySet,
    AccessManager,
    AccessQuerySet,
    EnterpriseManager,
    EnterpriseQuerySet,
    UsageManager,
    UsageQuerySet,
    CustomerManager,
    CustomerQuerySet
)

# Define what gets imported with "from billing.models import *"
__all__ = [
    # Core models
    'Customer',
    'UserPaymentProfile',
    'Product',
    'Price',
    'ServicePayment',

    'UserTransfer',
    'Subscription',
    'SubscriptionAccess',
    'ServiceAccess',
    'SolutionAccess',
    'SolutionUserAccess',
    'EnterpriseAccount',

    'PlatformFee',
    'SolutionUsage',
    'SolutionUsageLimit',
    
    # Base classes
    'BillingBaseModel',
    'StripeIntegrationMixin',
    'AmountMixin',
    'StatusMixin',
    'AccessMixin',
    'UsageMixin',
    'AuditMixin',
    'CacheableMixin',
    
    # Managers and QuerySets
    'PaymentManager',
    'PaymentQuerySet',
    'SubscriptionManager',
    'SubscriptionQuerySet',
    'AccessManager',
    'AccessQuerySet',
    'EnterpriseManager',
    'EnterpriseQuerySet',
    'UsageManager',
    'UsageQuerySet',
    'CustomerManager',
    'CustomerQuerySet',
]

# Model registry for dynamic access
MODEL_REGISTRY = {
    'customer': Customer,
    'user_payment_profile': UserPaymentProfile,
    'product': Product,
    'price': Price,
    'service_payment': ServicePayment,

    'user_transfer': UserTransfer,
    'subscription': Subscription,
    'subscription_access': SubscriptionAccess,
    'service_access': ServiceAccess,
    'solution_access': SolutionAccess,
    'solution_user_access': SolutionUserAccess,
    'enterprise_account': EnterpriseAccount,

    'platform_fee': PlatformFee,
    'solution_usage': SolutionUsage,
    'solution_usage_limit': SolutionUsageLimit,
}

def get_model(model_name: str):
    """
    Get a model class by name.
    
    Args:
        model_name: Name of the model (e.g., 'customer', 'service_payment')
        
    Returns:
        Model class or None if not found
    """
    return MODEL_REGISTRY.get(model_name.lower())

def get_all_models():
    """Get all billing model classes."""
    return list(MODEL_REGISTRY.values())

def get_model_names():
    """Get all billing model names."""
    return list(MODEL_REGISTRY.keys())

# Validation helpers
def validate_model_relationships():
    """
    Validate that all model relationships are properly configured.
    This can be called during application startup to catch configuration issues.
    """
    issues = []
    
    # Check that all foreign key relationships point to valid models
    for model_name, model_class in MODEL_REGISTRY.items():
        for field in model_class._meta.get_fields():
            if hasattr(field, 'related_model') and field.related_model:
                related_model = field.related_model
                if hasattr(related_model, '_meta'):
                    # Check if related model exists in our registry or is external
                    related_app = related_model._meta.app_label
                    if related_app == 'billing':
                        related_name = related_model._meta.model_name
                        if related_name not in MODEL_REGISTRY:
                            issues.append(
                                f"{model_name}.{field.name} references unknown model: {related_name}"
                            )
    
    return issues

# Migration helpers
def get_migration_dependencies():
    """
    Get migration dependencies for billing models.
    This helps ensure proper migration order.
    """
    return [
        ('contenttypes', '0002_remove_content_type_name'),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('accounts', '0001_initial'),  # Assuming accounts app exists
        ('content_management', '0001_initial'),  # Assuming content_management app exists
        ('enterprise', '0001_initial'),  # Assuming enterprise app exists
        ('analysis', '0001_initial'),  # Assuming analysis app exists
    ]

# Cache management
def invalidate_all_caches():
    """Invalidate all billing-related caches."""
    from django.core.cache import cache
    
    # Get all cache keys for billing models
    cache_keys = []
    for model_class in get_all_models():
        if hasattr(model_class, 'invalidate_cache'):
            # This would need to be implemented to get all cache keys for a model
            pass
    
    # Clear all billing-related cache keys
    if cache_keys:
        cache.delete_many(cache_keys)

# Model utilities
class ModelUtils:
    """Utility class for common model operations."""
    
    @staticmethod
    def get_active_records(model_name: str):
        """Get all active records for a model."""
        model_class = get_model(model_name)
        if model_class and hasattr(model_class, 'objects'):
            if hasattr(model_class.objects, 'active'):
                return model_class.objects.active()
            elif hasattr(model_class, 'active'):
                return model_class.objects.filter(active=True)
        return None
    
    @staticmethod
    def get_recent_records(model_name: str, days: int = 30):
        """Get recent records for a model."""
        model_class = get_model(model_name)
        if model_class and hasattr(model_class, 'objects'):
            if hasattr(model_class.objects, 'recent'):
                return model_class.objects.recent(days)
        return None
    
    @staticmethod
    def bulk_update_status(model_name: str, record_ids: list, new_status: str):
        """Bulk update status for multiple records."""
        model_class = get_model(model_name)
        if model_class and hasattr(model_class, 'objects'):
            return model_class.objects.filter(
                id__in=record_ids
            ).update(status=new_status)
        return 0

# Export utility instance
model_utils = ModelUtils()
