"""
Enterprise-related models for billing system.

This module contains models for managing enterprise accounts, services,
and Stripe Connect integration for enterprise billing.
"""
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from .base import BillingBaseModel, StripeIntegrationMixin, StatusMixin, AmountMixin, AuditMixin
from .managers import EnterpriseManager
import stripe
import logging
from typing import Optional, Dict, Any, List
from decimal import Decimal

logger = logging.getLogger(__name__)


class EnterpriseAccount(BillingBaseModel, StripeIntegrationMixin):
    """
    Represents a Stripe Connect account for enterprises.
    
    This model manages the integration between enterprise accounts and Stripe Connect
    for handling payments and payouts to enterprise service providers.
    """
    enterprise = models.OneToOneField(
        'enterprise.Enterprise', 
        on_delete=models.CASCADE, 
        related_name='stripe_account'
    )
    stripe_account_id = models.CharField(max_length=255, unique=True)
    charges_enabled = models.BooleanField(default=False)
    payouts_enabled = models.BooleanField(default=False)
    details_submitted = models.BooleanField(default=False)
    requirements = models.J<PERSON><PERSON><PERSON>(
        default=dict, 
        blank=True,
        help_text="Stripe account requirements and verification status"
    )
    
    # Override the stripe_id field from mixin to use stripe_account_id
    stripe_id = None
    
    objects = EnterpriseManager()
    
    class Meta:
        db_table = 'billing_enterprise_account'
        indexes = [
            models.Index(fields=['stripe_account_id']),
            models.Index(fields=['enterprise']),
            models.Index(fields=['charges_enabled']),
            models.Index(fields=['payouts_enabled']),
            models.Index(fields=['details_submitted']),
        ]
    
    def __str__(self):
        return f"{self.enterprise.name} - {self.stripe_account_id}"
    
    def clean(self):
        """Validate enterprise account data."""
        super().clean()
        
        # Validate requirements structure
        if self.requirements and not isinstance(self.requirements, dict):
            raise ValidationError("Requirements must be a dictionary")
    
    def save(self, *args, **kwargs):
        """Override save to ensure data consistency."""
        super().save(*args, **kwargs)
    
    def sync_from_stripe(self, stripe_account: Dict[str, Any]) -> None:
        """Sync account data from Stripe Connect account object."""
        self.charges_enabled = stripe_account.get('charges_enabled', False)
        self.payouts_enabled = stripe_account.get('payouts_enabled', False)
        self.details_submitted = stripe_account.get('details_submitted', False)
        
        # Update requirements
        requirements = stripe_account.get('requirements', {})
        self.requirements = {
            'currently_due': requirements.get('currently_due', []),
            'eventually_due': requirements.get('eventually_due', []),
            'past_due': requirements.get('past_due', []),
            'pending_verification': requirements.get('pending_verification', []),
            'disabled_reason': stripe_account.get('disabled_reason'),
        }
        
        self.metadata.update(stripe_account.get('metadata', {}))
        self.save()
        
        logger.info(f"Synced enterprise account {self.pk} from Stripe account {self.stripe_account_id}")
    
    def get_stripe_object(self):
        """Retrieve the Stripe Connect account object."""
        try:
            return stripe.Account.retrieve(self.stripe_account_id)
        except stripe.error.StripeError as e:
            logger.error(f"Failed to retrieve Stripe account {self.stripe_account_id}: {e}")
            return None
    
    @property
    def is_fully_verified(self) -> bool:
        """Check if account is fully verified and can process payments."""
        return (
            self.charges_enabled and
            self.payouts_enabled and
            self.details_submitted
        )
    
    @property
    def has_pending_requirements(self) -> bool:
        """Check if account has pending verification requirements."""
        if not self.requirements:
            return False
        
        return bool(
            self.requirements.get('currently_due') or 
            self.requirements.get('past_due')
        )
    
    def get_verification_url(self) -> Optional[str]:
        """Get Stripe account verification URL."""
        try:
            account_link = stripe.AccountLink.create(
                account=self.stripe_account_id,
                refresh_url=f"https://your-domain.com/enterprise/stripe/refresh",
                return_url=f"https://your-domain.com/enterprise/stripe/return",
                type="account_onboarding",
                # Use collection_options instead of deprecated 'collect' parameter
                collection_options={
                    'fields': 'eventually_due',
                    'future_requirements': 'omit'
                }
            )
            return account_link.url
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create account link for {self.stripe_account_id}: {e}")
            return None
    
    def get_dashboard_url(self) -> Optional[str]:
        """Get Stripe Express dashboard URL."""
        try:
            login_link = stripe.Account.create_login_link(self.stripe_account_id)
            return login_link.url
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create dashboard link for {self.stripe_account_id}: {e}")
            return None
    
    def get_balance(self) -> Optional[Dict[str, Any]]:
        """Get account balance from Stripe."""
        try:
            return stripe.Balance.retrieve(stripe_account=self.stripe_account_id)
        except stripe.error.StripeError as e:
            logger.error(f"Failed to retrieve balance for {self.stripe_account_id}: {e}")
            return None
    
    def get_total_earnings(self) -> int:
        """Calculate total earnings from completed payments (in cents)."""
        return self.enterprise.payments.filter(
            status='completed'
        ).aggregate(
            total=models.Sum('amount')
        )['total'] or 0
    
    def get_total_fees_paid(self) -> int:
        """Calculate total platform fees paid (in cents)."""
        return self.enterprise.payments.filter(
            status='completed'
        ).aggregate(
            total=models.Sum('platform_fee_amount')
        )['total'] or 0



