"""
Payment-related models for billing system.

This module contains models for managing different types of payments including
service payments, enterprise payments, and user transfers.
"""
from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from content_management.models import Service
from .base import BillingBaseModel, StripeIntegrationMixin, AmountMixin, StatusMixin, AuditMixin
from .managers import PaymentManager
import stripe
import logging
from typing import Dict, Any, List
from decimal import Decimal

logger = logging.getLogger(__name__)


class ServicePayment(BillingBaseModel, StripeIntegrationMixin, AmountMixin, StatusMixin, AuditMixin):
    """
    Tracks payments for one-time services.
    
    This model manages payments for individual services including DNA analysis
    and other platform services.
    """
    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
        ('canceled', 'Canceled'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='service_payments'
    )
    service = models.ForeignKey(
        Service, 
        on_delete=models.CASCADE, 
        related_name='payments'
    )
    services = models.JSONField(
        default=list, 
        blank=True, 
        null=True, 
        help_text="List of service IDs for bulk payments"
    )
    stripe_payment_intent_id = models.CharField(max_length=255, unique=True)
    dna_analysis = models.ForeignKey(
        'analysis.DNAAnalysis',  # Changed to string reference
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='service_payments'
    )
    
    status = models.CharField(
        max_length=50,
        choices=PAYMENT_STATUS_CHOICES,
        default='pending'
    )
    
    # Override the stripe_id field from mixin to use stripe_payment_intent_id
    stripe_id = None
    
    objects = PaymentManager()
    
    class Meta:
        db_table = 'billing_service_payment'
        indexes = [
            models.Index(fields=['stripe_payment_intent_id']),
            models.Index(fields=['user']),
            models.Index(fields=['service']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['dna_analysis']),
        ]
    
    def __str__(self):
        return f"Payment {self.stripe_payment_intent_id} - {self.user.email} for {self.service.name}"
    
    def clean(self):
        """Validate payment data."""
        super().clean()
        
        # Validate services list if provided
        if self.services:
            if not isinstance(self.services, list):
                raise ValidationError("Services must be a list")
            
            # Ensure all service IDs are valid UUIDs
            from uuid import UUID
            for service_id in self.services:
                try:
                    UUID(str(service_id))
                except ValueError:
                    raise ValidationError(f"Invalid service ID: {service_id}")
    
    def sync_from_stripe(self, stripe_payment_intent: Dict[str, Any]) -> None:
        """Sync payment data from Stripe payment intent."""
        self.amount = stripe_payment_intent.get('amount', self.amount)
        self.currency = stripe_payment_intent.get('currency', self.currency)
        
        # Map Stripe status to our status
        stripe_status = stripe_payment_intent.get('status')
        status_mapping = {
            'succeeded': 'completed',
            'processing': 'pending',
            'requires_payment_method': 'pending',
            'requires_confirmation': 'pending',
            'requires_action': 'pending',
            'canceled': 'canceled',
            'failed': 'failed'
        }
        
        if stripe_status in status_mapping:
            self.update_status(status_mapping[stripe_status], save=False)
        
        self.metadata.update(stripe_payment_intent.get('metadata', {}))
        self.save()
        
        logger.info(f"Synced service payment {self.pk} from Stripe payment intent {self.stripe_payment_intent_id}")
    
    def get_stripe_object(self):
        """Retrieve the Stripe payment intent object."""
        try:
            return stripe.PaymentIntent.retrieve(self.stripe_payment_intent_id)
        except stripe.error.StripeError as e:
            logger.error(f"Failed to retrieve Stripe payment intent {self.stripe_payment_intent_id}: {e}")
            return None
    
    def complete_payment(self) -> None:
        """Mark payment as completed and grant service access."""
        if self.status != 'completed':
            self.update_status('completed')
            
            # Create service access record
            from .access import ServiceAccess
            ServiceAccess.objects.get_or_create(
                user=self.user,
                service=self.service,
                payment=self,
                defaults={
                    'status': 'active',
                    'metadata': {'payment_id': str(self.id)}
                }
            )
            
            logger.info(f"Completed service payment {self.pk} and granted access")
    
    def refund_payment(self, reason: str = '') -> bool:
        """Refund the payment in Stripe and update status."""
        try:
            stripe.Refund.create(
                payment_intent=self.stripe_payment_intent_id,
                reason=reason or 'requested_by_customer'
            )
            
            self.update_status('refunded')
            self.set_metadata_value('refund_reason', reason)
            self.save()
            
            # Revoke service access
            from .access import ServiceAccess
            ServiceAccess.objects.filter(
                user=self.user,
                service=self.service,
                payment=self
            ).update(status='revoked')
            
            logger.info(f"Refunded service payment {self.pk}")
            return True
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to refund payment {self.stripe_payment_intent_id}: {e}")
            return False
    
    def get_services_list(self) -> List[Service]:
        """Get list of Service objects from services field."""
        if not self.services:
            return [self.service] if self.service else []
        
        service_ids = [str(sid) for sid in self.services]
        return list(Service.objects.filter(id__in=service_ids))
    
    @property
    def is_bulk_payment(self) -> bool:
        """Check if this is a payment for multiple services."""
        return bool(self.services and len(self.services) > 1)





class UserTransfer(BillingBaseModel, StripeIntegrationMixin, AmountMixin, StatusMixin, AuditMixin):
    """
    Represents money transfers between users.
    
    This model manages peer-to-peer transfers including donations and payments
    between platform users.
    """
    TRANSFER_TYPE_CHOICES = [
        ('donation', 'Donation'),
        ('payment', 'Payment'),
        ('transfer', 'Transfer'),
    ]
    
    TRANSFER_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
        ('canceled', 'Canceled'),
    ]
    
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='sent_transfers'
    )
    receiver = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='received_transfers'
    )
    message = models.TextField(blank=True)
    transfer_type = models.CharField(
        max_length=50,
        choices=TRANSFER_TYPE_CHOICES
    )
    platform_fee = models.ForeignKey(
        'billing.PlatformFee',
        on_delete=models.SET_NULL,
        null=True
    )
    platform_fee_amount = models.IntegerField(help_text="Platform fee amount in cents")
    stripe_payment_intent_id = models.CharField(max_length=255, unique=True)
    stripe_checkout_session_id = models.CharField(
        max_length=255, 
        blank=True, 
        null=True,
        help_text="Stripe checkout session ID for session-based payments"
    )
    
    status = models.CharField(
        max_length=50,
        choices=TRANSFER_STATUS_CHOICES,
        default='pending'
    )
    
    # Override the stripe_id field from mixin to use stripe_payment_intent_id
    stripe_id = None
    
    objects = PaymentManager()
    
    class Meta:
        db_table = 'billing_user_transfer'
        indexes = [
            models.Index(fields=['stripe_payment_intent_id']),
            models.Index(fields=['sender']),
            models.Index(fields=['receiver']),
            models.Index(fields=['transfer_type']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Transfer {self.stripe_payment_intent_id} - {self.sender.email} to {self.receiver.email}"
    
    def clean(self):
        """Validate transfer data."""
        super().clean()
        
        # Ensure sender and receiver are different
        if self.sender == self.receiver:
            raise ValidationError("Sender and receiver cannot be the same user")
        
        # Validate receiver can accept the transfer type
        if self.transfer_type == 'donation':
            receiver_profile = getattr(self.receiver, 'payment_profile', None)
            if not receiver_profile or not receiver_profile.can_receive_donations():
                raise ValidationError("Receiver cannot accept donations")
    
    @property
    def net_amount(self) -> int:
        """Amount after platform fee."""
        return self.amount - self.platform_fee_amount
    
    @property
    def net_amount_decimal(self) -> Decimal:
        """Net amount as decimal value."""
        return Decimal(self.net_amount) / Decimal('100')
    
    def calculate_platform_fee(self) -> int:
        """Calculate platform fee based on current fee structure."""
        if not self.platform_fee:
            return 0
        
        percentage_fee = int(self.amount * self.platform_fee.percentage / 100)
        return percentage_fee + self.platform_fee.fixed_amount
    
    def sync_from_stripe(self, stripe_payment_intent: Dict[str, Any]) -> None:
        """Sync transfer data from Stripe payment intent."""
        self.amount = stripe_payment_intent.get('amount', self.amount)
        self.currency = stripe_payment_intent.get('currency', self.currency)
        
        # Map Stripe status to our status
        stripe_status = stripe_payment_intent.get('status')
        status_mapping = {
            'succeeded': 'completed',
            'processing': 'processing',
            'requires_payment_method': 'pending',
            'requires_confirmation': 'pending',
            'requires_action': 'pending',
            'canceled': 'canceled',
            'failed': 'failed'
        }
        
        if stripe_status in status_mapping:
            self.update_status(status_mapping[stripe_status], save=False)
        
        self.metadata.update(stripe_payment_intent.get('metadata', {}))
        self.save()
        
        logger.info(f"Synced user transfer {self.pk} from Stripe payment intent {self.stripe_payment_intent_id}")
    
    def get_stripe_object(self):
        """Retrieve the Stripe payment intent object."""
        try:
            return stripe.PaymentIntent.retrieve(self.stripe_payment_intent_id)
        except stripe.error.StripeError as e:
            logger.error(f"Failed to retrieve Stripe payment intent {self.stripe_payment_intent_id}: {e}")
            return None
