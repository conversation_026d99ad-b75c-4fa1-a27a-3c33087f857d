"""
Subscription-related models for billing system.

This module contains models for managing subscriptions and subscription access
for SaaS platform services.
"""
from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from .base import BillingBaseModel, StripeIntegrationMixin, StatusMixin, AccessMixin, CacheableMixin
from .managers import SubscriptionManager, AccessManager
import stripe
import logging
from typing import Optional, Dict, Any
from datetime import timedelta

logger = logging.getLogger(__name__)


class Subscription(BillingBaseModel, StripeIntegrationMixin, StatusMixin, CacheableMixin):
    """
    Represents a Stripe Subscription for SaaS platform services.
    
    This model manages recurring subscriptions and their lifecycle including
    billing periods, cancellations, and status changes.
    """
    SUBSCRIPTION_STATUS_CHOICES = [
        ('active', 'Active'),
        ('past_due', 'Past Due'),
        ('unpaid', 'Unpaid'),
        ('canceled', 'Canceled'),
        ('incomplete', 'Incomplete'),
        ('incomplete_expired', 'Incomplete Expired'),
        ('trialing', 'Trialing'),
        ('paused', 'Paused'),
    ]
    
    customer = models.ForeignKey(
        'billing.Customer',
        on_delete=models.CASCADE,
        related_name='subscriptions'
    )
    stripe_subscription_id = models.CharField(max_length=255, unique=True)
    price = models.ForeignKey(
        'billing.Price',
        on_delete=models.CASCADE,
        related_name='subscriptions'
    )
    current_period_start = models.DateTimeField()
    current_period_end = models.DateTimeField()
    cancel_at_period_end = models.BooleanField(default=False)
    canceled_at = models.DateTimeField(null=True, blank=True)
    trial_end = models.DateTimeField(null=True, blank=True)
    
    status = models.CharField(
        max_length=50,
        choices=SUBSCRIPTION_STATUS_CHOICES,
        default='incomplete'
    )
    
    # Override the stripe_id field from mixin to use stripe_subscription_id
    stripe_id = None
    
    objects = SubscriptionManager()
    
    class Meta:
        db_table = 'billing_subscription'
        indexes = [
            models.Index(fields=['stripe_subscription_id']),
            models.Index(fields=['customer']),
            models.Index(fields=['price']),
            models.Index(fields=['status']),
            models.Index(fields=['current_period_end']),
            models.Index(fields=['cancel_at_period_end']),
        ]
    
    def __str__(self):
        return f"{self.customer.email} - {self.price.product.name}"
    
    def clean(self):
        """Validate subscription data."""
        super().clean()
        
        # Validate period dates
        if self.current_period_start and self.current_period_end:
            if self.current_period_start >= self.current_period_end:
                raise ValidationError("Current period start must be before current period end")
        
        # Validate trial end date
        if self.trial_end and self.current_period_start:
            if self.trial_end < self.current_period_start:
                raise ValidationError("Trial end must be after current period start")
    
    def save(self, *args, **kwargs):
        """Override save to ensure data consistency."""
        self.clean()
        super().save(*args, **kwargs)
        
        # Invalidate related caches
        self.invalidate_cache('access_records')
        self.customer.invalidate_cache('subscriptions')
    
    def sync_from_stripe(self, stripe_subscription: Dict[str, Any]) -> None:
        """Sync subscription data from Stripe subscription object."""
        self.status = stripe_subscription.get('status', self.status)
        self.current_period_start = timezone.datetime.fromtimestamp(
            stripe_subscription.get('current_period_start', self.current_period_start.timestamp()),
            tz=timezone.utc
        )
        self.current_period_end = timezone.datetime.fromtimestamp(
            stripe_subscription.get('current_period_end', self.current_period_end.timestamp()),
            tz=timezone.utc
        )
        self.cancel_at_period_end = stripe_subscription.get('cancel_at_period_end', self.cancel_at_period_end)
        
        # Handle canceled_at timestamp
        canceled_at = stripe_subscription.get('canceled_at')
        if canceled_at:
            self.canceled_at = timezone.datetime.fromtimestamp(canceled_at, tz=timezone.utc)
        
        # Handle trial_end timestamp
        trial_end = stripe_subscription.get('trial_end')
        if trial_end:
            self.trial_end = timezone.datetime.fromtimestamp(trial_end, tz=timezone.utc)
        
        self.metadata.update(stripe_subscription.get('metadata', {}))
        self.save()
        
        logger.info(f"Synced subscription {self.pk} from Stripe subscription {self.stripe_subscription_id}")
    
    def get_stripe_object(self):
        """Retrieve the Stripe subscription object."""
        try:
            return stripe.Subscription.retrieve(self.stripe_subscription_id)
        except stripe.error.StripeError as e:
            logger.error(f"Failed to retrieve Stripe subscription {self.stripe_subscription_id}: {e}")
            return None
    
    @property
    def is_in_trial(self) -> bool:
        """Check if subscription is currently in trial period."""
        if not self.trial_end:
            return False
        return timezone.now() < self.trial_end
    
    @property
    def days_until_renewal(self) -> int:
        """Get number of days until next renewal."""
        if self.current_period_end:
            delta = self.current_period_end - timezone.now()
            return max(0, delta.days)
        return 0
    
    @property
    def is_expiring_soon(self) -> bool:
        """Check if subscription is expiring within 7 days."""
        return self.days_until_renewal <= 7
    
    def cancel_subscription(self, at_period_end: bool = True) -> bool:
        """Cancel the subscription in Stripe."""
        try:
            if at_period_end:
                stripe.Subscription.modify(
                    self.stripe_subscription_id,
                    cancel_at_period_end=True
                )
                self.cancel_at_period_end = True
            else:
                stripe.Subscription.delete(self.stripe_subscription_id)
                self.update_status('canceled')
                self.canceled_at = timezone.now()
            
            self.save()
            logger.info(f"Canceled subscription {self.pk}")
            return True
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to cancel subscription {self.stripe_subscription_id}: {e}")
            return False
    
    def reactivate_subscription(self) -> bool:
        """Reactivate a canceled subscription."""
        try:
            stripe.Subscription.modify(
                self.stripe_subscription_id,
                cancel_at_period_end=False
            )
            self.cancel_at_period_end = False
            self.canceled_at = None
            self.update_status('active')
            
            logger.info(f"Reactivated subscription {self.pk}")
            return True
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to reactivate subscription {self.stripe_subscription_id}: {e}")
            return False
    
    def get_next_invoice(self):
        """Get the next upcoming invoice for this subscription."""
        try:
            return stripe.Invoice.upcoming(subscription=self.stripe_subscription_id)
        except stripe.error.StripeError as e:
            logger.error(f"Failed to retrieve upcoming invoice for subscription {self.stripe_subscription_id}: {e}")
            return None
    
    def update_subscription_price(self, new_price_id: str) -> bool:
        """Update the subscription to use a new price."""
        try:
            # Get the subscription item
            stripe_subscription = self.get_stripe_object()
            if not stripe_subscription:
                return False
            
            subscription_item_id = stripe_subscription['items']['data'][0]['id']
            
            # Update the subscription item with new price
            stripe.Subscription.modify(
                self.stripe_subscription_id,
                items=[{
                    'id': subscription_item_id,
                    'price': new_price_id,
                }],
                proration_behavior='create_prorations'
            )
            
            # Update local price reference
            from .product import Price
            new_price = Price.objects.get(stripe_price_id=new_price_id)
            self.price = new_price
            self.save()
            
            logger.info(f"Updated subscription {self.pk} to new price {new_price_id}")
            return True
            
        except (stripe.error.StripeError, Price.DoesNotExist) as e:
            logger.error(f"Failed to update subscription price: {e}")
            return False


class SubscriptionAccess(BillingBaseModel, StatusMixin, AccessMixin):
    """
    Tracks which users have access to which subscription plans and their status.
    
    This model manages user access to subscription-based services and features.
    """
    ACCESS_STATUS_CHOICES = [
        ('active', 'Active'),
        ('past_due', 'Past Due'),
        ('canceled', 'Canceled'),
        ('expired', 'Expired'),
        ('suspended', 'Suspended'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='subscription_access'
    )
    subscription = models.ForeignKey(
        Subscription, 
        on_delete=models.CASCADE, 
        related_name='access_records'
    )
    
    status = models.CharField(
        max_length=50,
        choices=ACCESS_STATUS_CHOICES,
        default='active'
    )
    
    objects = AccessManager()
    
    class Meta:
        db_table = 'billing_subscription_access'
        unique_together = ('user', 'subscription')
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['subscription']),
            models.Index(fields=['status']),
            models.Index(fields=['access_granted_at']),
            models.Index(fields=['access_expires_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.subscription.price.product.name} ({self.status})"
    
    def clean(self):
        """Validate subscription access data."""
        super().clean()
        
        # Ensure user matches subscription customer
        if self.subscription and self.subscription.customer.user != self.user:
            raise ValidationError("User must match subscription customer")
    
    def save(self, *args, **kwargs):
        """Override save to ensure data consistency."""
        self.clean()
        super().save(*args, **kwargs)
    
    def sync_with_subscription(self) -> None:
        """Sync access status with subscription status."""
        if not self.subscription:
            return
        
        # Map subscription status to access status
        status_mapping = {
            'active': 'active',
            'trialing': 'active',
            'past_due': 'past_due',
            'canceled': 'canceled',
            'incomplete': 'suspended',
            'incomplete_expired': 'expired',
            'unpaid': 'suspended',
            'paused': 'suspended',
        }
        
        new_status = status_mapping.get(self.subscription.status, 'suspended')
        if new_status != self.status:
            self.update_status(new_status)
            
            # Set expiration based on subscription period
            if new_status in ['canceled', 'expired']:
                self.access_expires_at = self.subscription.current_period_end
            elif new_status == 'active':
                self.access_expires_at = None  # Active subscriptions don't expire
            
            self.save()
    
    @property
    def subscription_product(self):
        """Get the product associated with this subscription."""
        return self.subscription.price.product if self.subscription else None
    
    @property
    def is_trial_access(self) -> bool:
        """Check if this access is during trial period."""
        return self.subscription and self.subscription.is_in_trial
    
    def get_feature_access(self, feature_name: str) -> bool:
        """Check if user has access to a specific feature."""
        if not self.has_valid_access:
            return False
        
        # Get feature access from subscription product metadata
        product = self.subscription_product
        if product and product.metadata:
            features = product.metadata.get('features', [])
            return feature_name in features
        
        return False
    
    def get_usage_limits(self) -> Dict[str, Any]:
        """Get usage limits for this subscription."""
        product = self.subscription_product
        if product and product.metadata:
            return product.metadata.get('usage_limits', {})
        return {}
    
    def revoke_access(self, reason: str = '') -> None:
        """Revoke subscription access."""
        super().revoke_access()
        self.set_metadata_value('revocation_reason', reason)
        self.save()
        
        logger.info(f"Revoked subscription access {self.pk} for user {self.user.email}")
    
    def restore_access(self) -> None:
        """Restore subscription access if subscription is valid."""
        if self.subscription and self.subscription.status in ['active', 'trialing']:
            self.update_status('active')
            self.access_expires_at = None
            self.save()
            
            logger.info(f"Restored subscription access {self.pk} for user {self.user.email}")
        else:
            raise ValidationError("Cannot restore access: subscription is not active")
