"""
Customer-related models for billing system.

This module contains models for managing customers and their payment profiles.
"""
from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from .base import BillingBaseModel, StripeIntegrationMixin, StatusMixin, AuditMixin, CacheableMixin
from .managers import CustomerManager
import stripe
import logging
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)


class Customer(BillingBaseModel, StripeIntegrationMixin, CacheableMixin):
    """
    Represents a Stripe Customer linked to a user account.
    
    This model manages the relationship between internal users and Stripe customers,
    providing methods for customer management and synchronization.
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='billing_customer'
    )
    stripe_customer_id = models.CharField(max_length=255, unique=True)
    email = models.EmailField()
    name = models.CharField(max_length=255, blank=True)
    
    # Override the stripe_id field from mixin to use stripe_customer_id
    stripe_id = None
    
    objects = CustomerManager()
    
    class Meta:
        db_table = 'billing_customer'
        indexes = [
            models.Index(fields=['stripe_customer_id']),
            models.Index(fields=['email']),
            models.Index(fields=['user']),
        ]
    
    def __str__(self):
        return self.email
    
    def clean(self):
        """Validate customer data."""
        super().clean()
        
        # Ensure email matches user email if user exists
        if self.user and self.user.email != self.email:
            self.email = self.user.email
        
        # Ensure name is set from user if available
        if self.user and not self.name:
            self.name = self.user.get_full_name() or self.user.email
    
    def save(self, *args, **kwargs):
        """Override save to ensure data consistency."""
        self.clean()
        super().save(*args, **kwargs)
        
        # Invalidate related caches
        self.invalidate_cache('subscriptions')
        self.invalidate_cache('payments')
    
    def sync_from_stripe(self, stripe_customer: Dict[str, Any]) -> None:
        """Sync customer data from Stripe customer object."""
        self.email = stripe_customer.get('email', self.email)
        self.name = stripe_customer.get('name', self.name)
        self.metadata.update(stripe_customer.get('metadata', {}))
        self.save()
        
        logger.info(f"Synced customer {self.pk} from Stripe customer {self.stripe_customer_id}")
    
    def get_stripe_object(self):
        """Retrieve the Stripe customer object."""
        try:
            return stripe.Customer.retrieve(self.stripe_customer_id)
        except stripe.error.StripeError as e:
            logger.error(f"Failed to retrieve Stripe customer {self.stripe_customer_id}: {e}")
            return None
    
    def update_stripe_customer(self, **kwargs) -> bool:
        """Update the Stripe customer with new data."""
        try:
            stripe_customer = stripe.Customer.modify(
                self.stripe_customer_id,
                **kwargs
            )
            self.sync_from_stripe(stripe_customer)
            return True
        except stripe.error.StripeError as e:
            logger.error(f"Failed to update Stripe customer {self.stripe_customer_id}: {e}")
            return False
    
    def get_active_subscriptions(self):
        """Get all active subscriptions for this customer."""
        return self.subscriptions.active()
    
    def get_payment_methods(self):
        """Get all payment methods for this customer from Stripe."""
        try:
            return stripe.PaymentMethod.list(
                customer=self.stripe_customer_id,
                type='card'
            )
        except stripe.error.StripeError as e:
            logger.error(f"Failed to retrieve payment methods for customer {self.stripe_customer_id}: {e}")
            return None
    
    def get_total_spent(self) -> int:
        """Calculate total amount spent by this customer (in cents)."""
        from .payment import ServicePayment, EnterprisePayment
        
        service_total = ServicePayment.objects.filter(
            user=self.user,
            status='completed'
        ).aggregate(total=models.Sum('amount'))['total'] or 0
        
        enterprise_total = EnterprisePayment.objects.filter(
            customer=self,
            status='completed'
        ).aggregate(total=models.Sum('amount'))['total'] or 0
        
        return service_total + enterprise_total
    
    def has_active_subscription(self) -> bool:
        """Check if customer has any active subscriptions."""
        return self.subscriptions.active().exists()
    
    def get_subscription_history(self):
        """Get subscription history for this customer."""
        return self.subscriptions.all().order_by('-created_at')


class UserPaymentProfile(BillingBaseModel, StatusMixin, AuditMixin):
    """
    Represents a user's payment profile for receiving payments.
    
    This model manages user settings for receiving donations, payments,
    and transfers from other users.
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='payment_profile'
    )
    stripe_account_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    charges_enabled = models.BooleanField(default=False)
    payouts_enabled = models.BooleanField(default=False)
    details_submitted = models.BooleanField(default=False)
    
    # Donation settings
    accept_donations = models.BooleanField(default=False)
    donation_message = models.TextField(blank=True)
    minimum_donation = models.IntegerField(
        default=100, 
        help_text="Minimum donation amount in cents"
    )
    suggested_donation_amounts = models.JSONField(
        default=list, 
        help_text="List of suggested donation amounts in cents"
    )
    
    # Verification status
    is_verified = models.BooleanField(default=False)
    verification_date = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'billing_user_payment_profile'
        indexes = [
            models.Index(fields=['stripe_account_id']),
            models.Index(fields=['user']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['accept_donations']),
        ]
    
    def __str__(self):
        return f"Payment Profile - {self.user.email}"
    
    def clean(self):
        """Validate payment profile data."""
        super().clean()
        
        # Validate minimum donation amount
        if self.minimum_donation < 50:  # $0.50 minimum
            raise ValidationError("Minimum donation must be at least 50 cents")
        
        # Validate suggested amounts
        if self.suggested_donation_amounts:
            for amount in self.suggested_donation_amounts:
                if not isinstance(amount, int) or amount < self.minimum_donation:
                    raise ValidationError(
                        "All suggested donation amounts must be integers "
                        "and at least the minimum donation amount"
                    )
    
    def verify_account(self) -> None:
        """Mark account as verified."""
        self.is_verified = True
        self.verification_date = timezone.now()
        self.update_status('active')
        
        logger.info(f"Payment profile {self.pk} verified for user {self.user.email}")
    
    def can_receive_payments(self) -> bool:
        """Check if profile can receive payments."""
        return (
            self.is_verified and 
            self.charges_enabled and 
            self.details_submitted and
            self.is_active
        )
    
    def can_receive_donations(self) -> bool:
        """Check if profile can receive donations."""
        return self.can_receive_payments() and self.accept_donations
    
    def get_suggested_amounts_decimal(self) -> List[float]:
        """Get suggested donation amounts as decimal values."""
        return [amount / 100.0 for amount in self.suggested_donation_amounts]
    
    def add_suggested_amount(self, amount_cents: int) -> None:
        """Add a suggested donation amount."""
        if amount_cents >= self.minimum_donation:
            if amount_cents not in self.suggested_donation_amounts:
                self.suggested_donation_amounts.append(amount_cents)
                self.suggested_donation_amounts.sort()
                self.save(update_fields=['suggested_donation_amounts'])
    
    def remove_suggested_amount(self, amount_cents: int) -> None:
        """Remove a suggested donation amount."""
        if amount_cents in self.suggested_donation_amounts:
            self.suggested_donation_amounts.remove(amount_cents)
            self.save(update_fields=['suggested_donation_amounts'])
    
    def get_total_received(self) -> int:
        """Calculate total amount received through this profile (in cents)."""
        from .payment import UserTransfer
        
        return UserTransfer.objects.filter(
            receiver=self.user,
            status='completed'
        ).aggregate(total=models.Sum('amount'))['total'] or 0
    
    def get_recent_transfers(self, days: int = 30):
        """Get recent transfers received through this profile."""
        from .payment import UserTransfer
        
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        return UserTransfer.objects.filter(
            receiver=self.user,
            created_at__gte=cutoff_date
        ).order_by('-created_at')
    
    def sync_from_stripe_account(self, stripe_account: Dict[str, Any]) -> None:
        """Sync profile data from Stripe Connect account."""
        self.charges_enabled = stripe_account.get('charges_enabled', False)
        self.payouts_enabled = stripe_account.get('payouts_enabled', False)
        self.details_submitted = stripe_account.get('details_submitted', False)
        
        # Update verification status based on Stripe account status
        if (self.charges_enabled and self.payouts_enabled and self.details_submitted):
            if not self.is_verified:
                self.verify_account()
        
        self.save()
        logger.info(f"Synced payment profile {self.pk} from Stripe account {self.stripe_account_id}")
