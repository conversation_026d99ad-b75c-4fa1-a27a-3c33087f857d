"""
Custom managers and querysets for billing models.

This module provides specialized database query functionality for billing operations,
including filtering, aggregation, and business logic queries.
"""
from django.db import models
from django.utils import timezone
from django.db.models import Q, Sum, Count, Avg
from decimal import Decimal
from typing import Optional, List, Dict, Any


class PaymentQuerySet(models.QuerySet):
    """Custom QuerySet for payment-related models."""
    
    def completed(self):
        """Filter payments that are completed."""
        return self.filter(status='completed')
    
    def pending(self):
        """Filter payments that are pending."""
        return self.filter(status='pending')
    
    def failed(self):
        """Filter payments that have failed."""
        return self.filter(status='failed')
    
    def refunded(self):
        """Filter payments that have been refunded."""
        return self.filter(status='refunded')
    
    def for_user(self, user):
        """Filter payments for a specific user."""
        return self.filter(user=user)
    
    def in_date_range(self, start_date, end_date):
        """Filter payments within a date range."""
        return self.filter(created_at__range=[start_date, end_date])
    
    def by_amount_range(self, min_amount=None, max_amount=None):
        """Filter payments by amount range (in cents)."""
        filters = Q()
        if min_amount is not None:
            filters &= Q(amount__gte=min_amount)
        if max_amount is not None:
            filters &= Q(amount__lte=max_amount)
        return self.filter(filters)
    
    def total_amount(self):
        """Calculate total amount of payments in queryset."""
        return self.aggregate(total=Sum('amount'))['total'] or 0
    
    def average_amount(self):
        """Calculate average amount of payments in queryset."""
        return self.aggregate(avg=Avg('amount'))['avg'] or 0


class PaymentManager(models.Manager):
    """Custom manager for payment models."""
    
    def get_queryset(self):
        return PaymentQuerySet(self.model, using=self._db)
    
    def completed(self):
        return self.get_queryset().completed()
    
    def pending(self):
        return self.get_queryset().pending()
    
    def failed(self):
        return self.get_queryset().failed()
    
    def for_user(self, user):
        return self.get_queryset().for_user(user)
    
    def recent(self, days=30):
        """Get payments from the last N days."""
        start_date = timezone.now() - timezone.timedelta(days=days)
        return self.get_queryset().filter(created_at__gte=start_date)


class SubscriptionQuerySet(models.QuerySet):
    """Custom QuerySet for subscription-related models."""
    
    def active(self):
        """Filter active subscriptions."""
        return self.filter(status='active')
    
    def inactive(self):
        """Filter inactive subscriptions (canceled, expired, etc.)."""
        return self.exclude(status='active')
    
    def past_due(self):
        """Filter subscriptions that are past due."""
        return self.filter(status='past_due')
    
    def canceled(self):
        """Filter canceled subscriptions."""
        return self.filter(status='canceled')
    
    def expiring_soon(self, days=7):
        """Filter subscriptions expiring within N days."""
        cutoff_date = timezone.now() + timezone.timedelta(days=days)
        return self.filter(
            current_period_end__lte=cutoff_date,
            status='active'
        )
    
    def for_customer(self, customer):
        """Filter subscriptions for a specific customer."""
        return self.filter(customer=customer)
    
    def by_product_type(self, product_type):
        """Filter subscriptions by product type."""
        return self.filter(price__product__product_type=product_type)


class SubscriptionManager(models.Manager):
    """Custom manager for subscription models."""
    
    def get_queryset(self):
        return SubscriptionQuerySet(self.model, using=self._db)
    
    def active(self):
        return self.get_queryset().active()
    
    def inactive(self):
        return self.get_queryset().inactive()
    
    def expiring_soon(self, days=7):
        return self.get_queryset().expiring_soon(days)


class AccessQuerySet(models.QuerySet):
    """Custom QuerySet for access-related models."""
    
    def active(self):
        """Filter active access records."""
        return self.filter(status='active')
    
    def expired(self):
        """Filter expired access records."""
        return self.filter(status='expired')
    
    def revoked(self):
        """Filter revoked access records."""
        return self.filter(status='revoked')
    
    def for_user(self, user):
        """Filter access records for a specific user."""
        return self.filter(user=user)
    
    def expiring_soon(self, days=7):
        """Filter access records expiring within N days."""
        cutoff_date = timezone.now() + timezone.timedelta(days=days)
        return self.filter(
            access_expires_at__lte=cutoff_date,
            status='active'
        ).exclude(access_expires_at__isnull=True)
    
    def by_service_type(self, service_type):
        """Filter access by service type (for ServiceAccess)."""
        return self.filter(service__service_type=service_type)


class AccessManager(models.Manager):
    """Custom manager for access models."""
    
    def get_queryset(self):
        return AccessQuerySet(self.model, using=self._db)
    
    def active(self):
        return self.get_queryset().active()
    
    def expired(self):
        return self.get_queryset().expired()
    
    def for_user(self, user):
        return self.get_queryset().for_user(user)
    
    def expiring_soon(self, days=7):
        return self.get_queryset().expiring_soon(days)


class EnterpriseQuerySet(models.QuerySet):
    """Custom QuerySet for enterprise-related models."""
    
    def verified(self):
        """Filter verified enterprise accounts."""
        return self.filter(
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True
        )
    
    def pending_verification(self):
        """Filter enterprise accounts pending verification."""
        return self.filter(
            Q(charges_enabled=False) |
            Q(payouts_enabled=False) |
            Q(details_submitted=False)
        )
    
    def with_active_services(self):
        """Filter enterprises with active services."""
        return self.filter(billing_services__active=True).distinct()


class EnterpriseManager(models.Manager):
    """Custom manager for enterprise models."""
    
    def get_queryset(self):
        return EnterpriseQuerySet(self.model, using=self._db)
    
    def verified(self):
        return self.get_queryset().verified()
    
    def pending_verification(self):
        return self.get_queryset().pending_verification()


class UsageQuerySet(models.QuerySet):
    """Custom QuerySet for usage tracking models."""
    
    def for_solution_access(self, solution_access):
        """Filter usage for a specific solution access."""
        return self.filter(solution_access=solution_access)
    
    def by_metric_type(self, metric_type):
        """Filter usage by metric type."""
        return self.filter(metric_type=metric_type)
    
    def in_date_range(self, start_date, end_date):
        """Filter usage within a date range."""
        return self.filter(usage_date__range=[start_date, end_date])
    
    def current_month(self):
        """Filter usage for the current month."""
        now = timezone.now()
        start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        return self.filter(usage_date__gte=start_of_month)
    
    def total_usage(self):
        """Calculate total usage amount."""
        return self.aggregate(total=Sum('usage_amount'))['total'] or Decimal('0')


class UsageManager(models.Manager):
    """Custom manager for usage models."""
    
    def get_queryset(self):
        return UsageQuerySet(self.model, using=self._db)
    
    def for_solution_access(self, solution_access):
        return self.get_queryset().for_solution_access(solution_access)
    
    def by_metric_type(self, metric_type):
        return self.get_queryset().by_metric_type(metric_type)
    
    def current_month(self):
        return self.get_queryset().current_month()


class CustomerQuerySet(models.QuerySet):
    """Custom QuerySet for customer models."""
    
    def with_active_subscriptions(self):
        """Filter customers with active subscriptions."""
        return self.filter(subscriptions__status='active').distinct()
    
    def with_recent_payments(self, days=30):
        """Filter customers with recent payments."""
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        return self.filter(
            Q(service_payments__created_at__gte=cutoff_date) |
            Q(enterprise_payments__created_at__gte=cutoff_date)
        ).distinct()
    
    def by_email_domain(self, domain):
        """Filter customers by email domain."""
        return self.filter(email__endswith=f'@{domain}')


class CustomerManager(models.Manager):
    """Custom manager for customer models."""
    
    def get_queryset(self):
        return CustomerQuerySet(self.model, using=self._db)
    
    def with_active_subscriptions(self):
        return self.get_queryset().with_active_subscriptions()
    
    def with_recent_payments(self, days=30):
        return self.get_queryset().with_recent_payments(days)
