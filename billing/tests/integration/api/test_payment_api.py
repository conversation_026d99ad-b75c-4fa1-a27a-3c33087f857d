"""
Integration tests for payment API endpoints.

Tests payment API endpoints with real database and service interactions.
"""
import pytest
import json
from unittest.mock import patch
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from billing.tests.factories import (
    UserFactory,
    CustomerFactory,
    ServiceFactory,
    ProductFactory,
    PriceFactory,
    ServicePaymentFactory,
)


@pytest.mark.integration
class PaymentAPIIntegrationTest(APITestCase):
    """Integration tests for payment API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        self.user = UserFactory()
        self.customer = CustomerFactory(user=self.user)
        self.service = ServiceFactory()
        self.product = ProductFactory(service=self.service)
        self.price = PriceFactory(product=self.product)
        
        # Authenticate user
        self.client.force_authenticate(user=self.user)
    
    @patch('stripe.checkout.Session.retrieve')
    def test_payment_success_endpoint_integration(self, mock_stripe_session):
        """Test payment success endpoint with full integration."""
        # Arrange
        session_id = 'cs_test_123'
        mock_stripe_session.return_value = {
            'id': session_id,
            'payment_intent': 'pi_test_123',
            'customer': self.customer.stripe_customer_id,
            'amount_total': 5000,
            'currency': 'usd',
            'metadata': {'price_id': self.price.stripe_price_id}
        }
        
        # Act
        url = reverse('billing:payment-success')  # Adjust URL name as needed
        response = self.client.get(url, {'session_id': session_id})
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertTrue(response_data['success'])
        self.assertIn('payment_id', response_data)
        self.assertIn('service_id', response_data)
        
        # Verify payment was created in database
        payment_id = response_data['payment_id']
        payment = ServicePayment.objects.get(id=payment_id)
        self.assertEqual(payment.user, self.user)
        self.assertEqual(payment.service, self.service)
        self.assertEqual(payment.amount, 5000)
        self.assertEqual(payment.status, 'completed')
    
    def test_payment_success_endpoint_missing_session_id(self):
        """Test payment success endpoint without session ID."""
        # Act
        url = reverse('billing:payment-success')
        response = self.client.get(url)
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        response_data = response.json()
        self.assertIn('error', response_data)
        self.assertIn('Session ID is required', response_data['error'])
    
    @patch('stripe.checkout.Session.retrieve')
    def test_payment_success_endpoint_stripe_error(self, mock_stripe_session):
        """Test payment success endpoint with Stripe error."""
        # Arrange
        mock_stripe_session.side_effect = Exception('Stripe API error')
        
        # Act
        url = reverse('billing:payment-success')
        response = self.client.get(url, {'session_id': 'cs_invalid'})
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        response_data = response.json()
        self.assertIn('error', response_data)
    
    def test_payment_success_endpoint_unauthenticated(self):
        """Test payment success endpoint without authentication."""
        # Arrange
        self.client.force_authenticate(user=None)
        
        # Act
        url = reverse('billing:payment-success')
        response = self.client.get(url, {'session_id': 'cs_test_123'})
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    @patch('billing.services.enterprise_service.EnterpriseService.create_enterprise_payment')
    def test_create_enterprise_payment_endpoint_integration(self, mock_create_payment):
        """Test enterprise payment creation endpoint."""
        # Arrange
        mock_create_payment.return_value = {
            'payment_intent_id': 'pi_test_123',
            'client_secret': 'pi_test_123_secret',
            'enterprise_payment_id': 'payment_123'
        }
        
        data = {
            'enterprise_id': 'enterprise_123',
            'amount': 10000,
            'payment_method_id': 'pm_test_123'
        }
        
        # Act
        url = reverse('billing:create-enterprise-payment')
        response = self.client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertIn('payment_intent_id', response_data)
        self.assertIn('client_secret', response_data)
        self.assertIn('enterprise_payment_id', response_data)
        
        # Verify service was called correctly
        mock_create_payment.assert_called_once_with(
            user=self.user,
            enterprise_id='enterprise_123',
            amount=10000,
            payment_method_id='pm_test_123'
        )
    
    def test_create_enterprise_payment_missing_data(self):
        """Test enterprise payment creation with missing data."""
        # Arrange
        data = {
            'enterprise_id': 'enterprise_123',
            # Missing amount
        }
        
        # Act
        url = reverse('billing:create-enterprise-payment')
        response = self.client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        response_data = response.json()
        self.assertIn('error', response_data)
        self.assertIn('required', response_data['error'])
    
    def test_get_payment_history_endpoint(self):
        """Test payment history endpoint."""
        # Arrange
        payments = [
            ServicePaymentFactory(user=self.user),
            ServicePaymentFactory(user=self.user),
            ServicePaymentFactory(user=self.user),
        ]
        
        # Act
        url = reverse('billing:payment-history')
        response = self.client.get(url)
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertIn('payments', response_data)
        self.assertEqual(len(response_data['payments']), 3)
        
        # Verify payment data structure
        payment_data = response_data['payments'][0]
        self.assertIn('id', payment_data)
        self.assertIn('amount', payment_data)
        self.assertIn('status', payment_data)
        self.assertIn('created_at', payment_data)
    
    def test_get_payment_history_with_pagination(self):
        """Test payment history endpoint with pagination."""
        # Arrange
        payments = [ServicePaymentFactory(user=self.user) for _ in range(15)]
        
        # Act
        url = reverse('billing:payment-history')
        response = self.client.get(url, {'page': 1, 'page_size': 10})
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertIn('payments', response_data)
        self.assertIn('pagination', response_data)
        self.assertEqual(len(response_data['payments']), 10)
        self.assertEqual(response_data['pagination']['total'], 15)
    
    def test_get_payment_history_other_user(self):
        """Test that users can only see their own payment history."""
        # Arrange
        other_user = UserFactory()
        ServicePaymentFactory(user=other_user)  # Payment for other user
        ServicePaymentFactory(user=self.user)   # Payment for current user
        
        # Act
        url = reverse('billing:payment-history')
        response = self.client.get(url)
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertEqual(len(response_data['payments']), 1)
        self.assertEqual(response_data['payments'][0]['user_id'], str(self.user.id))
    
    @patch('stripe.PaymentIntent.retrieve')
    def test_get_payment_details_endpoint(self, mock_stripe_retrieve):
        """Test payment details endpoint."""
        # Arrange
        payment = ServicePaymentFactory(user=self.user)
        mock_stripe_retrieve.return_value = {
            'id': payment.stripe_payment_intent_id,
            'status': 'succeeded',
            'amount': payment.amount,
            'currency': payment.currency
        }
        
        # Act
        url = reverse('billing:payment-details', kwargs={'payment_id': payment.id})
        response = self.client.get(url)
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertEqual(response_data['id'], str(payment.id))
        self.assertEqual(response_data['amount'], payment.amount)
        self.assertEqual(response_data['status'], payment.status)
        self.assertIn('stripe_details', response_data)
    
    def test_get_payment_details_not_found(self):
        """Test payment details endpoint with non-existent payment."""
        # Act
        url = reverse('billing:payment-details', kwargs={'payment_id': 'nonexistent'})
        response = self.client.get(url)
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_get_payment_details_other_user(self):
        """Test that users cannot access other users' payment details."""
        # Arrange
        other_user = UserFactory()
        payment = ServicePaymentFactory(user=other_user)
        
        # Act
        url = reverse('billing:payment-details', kwargs={'payment_id': payment.id})
        response = self.client.get(url)
        
        # Assert
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
