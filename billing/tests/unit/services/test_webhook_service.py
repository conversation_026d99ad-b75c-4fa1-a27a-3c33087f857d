"""
Unit tests for WebhookService verification payment handling.

Tests the business logic of webhook processing for verification services.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase
from django.contrib.auth import get_user_model

from billing.services.webhook_service import WebhookService
from billing.models import Customer, ServicePayment
from billing.tests.factories import (
    UserFactory,
    CustomerFactory,
    ServiceFactory,
    ProductFactory,
    PriceFactory,
)

User = get_user_model()


class TestWebhookServiceVerificationPayment(TestCase):
    """Test verification payment handling in webhook service"""
    
    def setUp(self):
        """Set up test data"""
        self.webhook_service = WebhookService()
        self.user = UserFactory()
        self.customer = CustomerFactory(user=self.user)
        
        # Create verification service
        self.verification_service = ServiceFactory(name="Verification Services")
        self.verification_product = ProductFactory(
            service=self.verification_service,
            product_type='service'
        )
        self.verification_price = PriceFactory(product=self.verification_product)
        
        # Create non-verification service for comparison
        self.regular_service = ServiceFactory(name="DNA Analysis")
        self.regular_product = ProductFactory(
            service=self.regular_service,
            product_type='service'
        )
        self.regular_price = PriceFactory(product=self.regular_product)
    
    def test_handle_verification_service_payment_by_name(self):
        """Test that verification service is detected by name"""
        # Ensure user starts with paid_for_verification=False
        self.user.paid_for_verification = False
        self.user.save()
        
        # Test verification service detection
        result = self.webhook_service._handle_verification_service_payment(
            self.verification_service, 
            self.user
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Assertions
        self.assertTrue(result)
        self.assertTrue(self.user.paid_for_verification)
    
    def test_handle_regular_service_payment(self):
        """Test that regular service does not trigger verification flag"""
        # Ensure user starts with paid_for_verification=False
        self.user.paid_for_verification = False
        self.user.save()
        
        # Test regular service (should not trigger verification)
        result = self.webhook_service._handle_verification_service_payment(
            self.regular_service, 
            self.user
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Assertions
        self.assertFalse(result)
        self.assertFalse(self.user.paid_for_verification)
    
    def test_verification_service_variations(self):
        """Test different variations of verification service names"""
        test_cases = [
            "Verification Services",
            "Profile Verification",
            "Identity Verification",
            "Account Verification",
            "User Verification",
            "Verify Account",
            "Verify Identity"
        ]
        
        for service_name in test_cases:
            with self.subTest(service_name=service_name):
                # Create service with test name
                service = ServiceFactory(name=service_name)
                
                # Reset user
                self.user.paid_for_verification = False
                self.user.save()
                
                # Test verification
                result = self.webhook_service._handle_verification_service_payment(
                    service, 
                    self.user
                )
                
                # Refresh user
                self.user.refresh_from_db()
                
                # Assertions
                self.assertTrue(result, f"Failed for service name: {service_name}")
                self.assertTrue(self.user.paid_for_verification, f"Failed for service name: {service_name}")
    
    @patch('billing.services.webhook_service.logger')
    def test_verification_service_error_handling(self, mock_logger):
        """Test error handling in verification service payment"""
        # Create a mock service that will cause an error
        mock_service = Mock()
        mock_service.name = "Verification Services"
        
        # Mock user.save() to raise an exception
        with patch.object(self.user, 'save', side_effect=Exception("Database error")):
            result = self.webhook_service._handle_verification_service_payment(
                mock_service, 
                self.user
            )
        
        # Should return False and log error
        self.assertFalse(result)
        mock_logger.error.assert_called_once()
    
    @patch('billing.services.webhook_service.WebhookService._get_payment_service')
    @patch('billing.services.webhook_service.WebhookService._get_customer_service')
    def test_handle_service_payment_integration(self, mock_customer_service, mock_payment_service):
        """Test the full service payment handling flow"""
        # Mock session object
        mock_session = Mock()
        mock_session.id = "cs_test_123"
        mock_session.payment_intent = "pi_test_123"
        
        # Mock metadata
        metadata = {
            'price_id': str(self.verification_price.id)
        }
        
        # Mock payment service
        mock_payment = Mock()
        mock_payment_service.return_value.create_service_payment.return_value = mock_payment
        
        # Ensure user starts with paid_for_verification=False
        self.user.paid_for_verification = False
        self.user.save()
        
        # Call the service payment handler
        result = self.webhook_service._handle_service_payment(
            mock_session, 
            self.customer, 
            metadata
        )
        
        # Refresh user from database
        self.user.refresh_from_db()
        
        # Assertions
        self.assertTrue(result)
        self.assertTrue(self.user.paid_for_verification)
        mock_payment.complete_payment.assert_called_once()
