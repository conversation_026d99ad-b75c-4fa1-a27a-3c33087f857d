"""
Unit tests for PaymentService.

Tests the business logic of payment processing without external dependencies.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from decimal import Decimal
from django.utils import timezone

from billing.services.payment_service import PaymentService
from billing.models import ServicePayment, Customer, Price, Product
from billing.exceptions import PaymentError
from billing.tests.factories import (
    UserFactory,
    CustomerFactory,
    ServiceFactory,
    ProductFactory,
    PriceFactory,
    ServicePaymentFactory,
)


@pytest.mark.unit
class TestPaymentService:
    """Test cases for PaymentService."""
    
    def setup_method(self):
        """Set up test dependencies."""
        self.payment_service = PaymentService()
        self.user = UserFactory()
        self.customer = CustomerFactory(user=self.user)
        self.service = ServiceFactory()
        self.product = ProductFactory(service=self.service)
        self.price = PriceFactory(product=self.product)
    
    @patch('stripe.checkout.Session.retrieve')
    def test_process_checkout_success_valid_session(self, mock_stripe_session):
        """Test successful checkout session processing."""
        # Arrange
        session_id = 'cs_test_123'
        mock_stripe_session.return_value = {
            'id': session_id,
            'payment_intent': 'pi_test_123',
            'customer': self.customer.stripe_customer_id,
            'amount_total': 5000,
            'currency': 'usd',
            'metadata': {'price_id': self.price.stripe_price_id}
        }
        
        # Act
        result = self.payment_service.process_checkout_success(session_id)
        
        # Assert
        assert 'payment_id' in result
        assert 'service_id' in result
        assert 'user_id' in result
        assert result['user_id'] == self.user.id
        
        # Verify payment was created
        payment = ServicePayment.objects.get(id=result['payment_id'])
        assert payment.user == self.user
        assert payment.service == self.service
        assert payment.amount == 5000
        assert payment.currency == 'usd'
        assert payment.status == 'completed'
        assert payment.stripe_payment_intent_id == 'pi_test_123'
    
    @patch('stripe.checkout.Session.retrieve')
    def test_process_checkout_success_invalid_session(self, mock_stripe_session):
        """Test checkout processing with invalid session."""
        # Arrange
        mock_stripe_session.side_effect = Exception('Session not found')
        
        # Act & Assert
        with pytest.raises(PaymentError, match='Error processing checkout success'):
            self.payment_service.process_checkout_success('invalid_session')
    
    @patch('stripe.checkout.Session.retrieve')
    def test_process_checkout_success_missing_customer(self, mock_stripe_session):
        """Test checkout processing with missing customer."""
        # Arrange
        mock_stripe_session.return_value = {
            'payment_intent': 'pi_test_123',
            'customer': 'cus_nonexistent',
            'amount_total': 5000,
            'currency': 'usd',
            'metadata': {'price_id': self.price.stripe_price_id}
        }
        
        # Act & Assert
        with pytest.raises(PaymentError):
            self.payment_service.process_checkout_success('cs_test_123')
    
    @patch('stripe.checkout.Session.retrieve')
    def test_process_checkout_success_missing_price(self, mock_stripe_session):
        """Test checkout processing with missing price."""
        # Arrange
        mock_stripe_session.return_value = {
            'payment_intent': 'pi_test_123',
            'customer': self.customer.stripe_customer_id,
            'amount_total': 5000,
            'currency': 'usd',
            'metadata': {'price_id': 'price_nonexistent'}
        }
        
        # Act & Assert
        with pytest.raises(PaymentError):
            self.payment_service.process_checkout_success('cs_test_123')
    
    def test_get_user_payment_history_with_limit(self):
        """Test getting user payment history with limit."""
        # Arrange
        payments = [
            ServicePaymentFactory(user=self.user),
            ServicePaymentFactory(user=self.user),
            ServicePaymentFactory(user=self.user),
        ]
        
        # Act
        result = self.payment_service.get_user_payment_history(self.user, limit=2)
        
        # Assert
        assert len(result) == 2
        assert all(payment.user == self.user for payment in result)
    
    def test_get_user_payment_history_without_limit(self):
        """Test getting user payment history without limit."""
        # Arrange
        payments = [
            ServicePaymentFactory(user=self.user),
            ServicePaymentFactory(user=self.user),
            ServicePaymentFactory(user=self.user),
        ]
        
        # Act
        result = self.payment_service.get_user_payment_history(self.user)
        
        # Assert
        assert len(result) == 3
        assert all(payment.user == self.user for payment in result)
    
    def test_get_user_payment_history_empty(self):
        """Test getting payment history for user with no payments."""
        # Act
        result = self.payment_service.get_user_payment_history(self.user)
        
        # Assert
        assert len(result) == 0
    
    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent_success(self, mock_stripe_create):
        """Test successful payment intent creation."""
        # Arrange
        mock_stripe_create.return_value = {
            'id': 'pi_test_123',
            'client_secret': 'pi_test_123_secret_test',
            'status': 'requires_payment_method'
        }
        
        # Act
        result = self.payment_service.create_payment_intent(
            amount=5000,
            currency='usd',
            customer_id=self.customer.stripe_customer_id
        )
        
        # Assert
        assert result['id'] == 'pi_test_123'
        assert result['client_secret'] == 'pi_test_123_secret_test'
        assert result['status'] == 'requires_payment_method'
        
        # Verify Stripe was called correctly
        mock_stripe_create.assert_called_once_with(
            amount=5000,
            currency='usd',
            customer=self.customer.stripe_customer_id
        )
    
    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent_stripe_error(self, mock_stripe_create):
        """Test payment intent creation with Stripe error."""
        # Arrange
        mock_stripe_create.side_effect = Exception('Stripe API error')
        
        # Act & Assert
        with pytest.raises(PaymentError):
            self.payment_service.create_payment_intent(
                amount=5000,
                currency='usd',
                customer_id=self.customer.stripe_customer_id
            )
    
    def test_validate_payment_amount_valid(self):
        """Test payment amount validation with valid amount."""
        # Act & Assert - should not raise
        self.payment_service._validate_payment_amount(5000)
    
    def test_validate_payment_amount_zero(self):
        """Test payment amount validation with zero amount."""
        # Act & Assert
        with pytest.raises(PaymentError, match='Amount must be positive'):
            self.payment_service._validate_payment_amount(0)
    
    def test_validate_payment_amount_negative(self):
        """Test payment amount validation with negative amount."""
        # Act & Assert
        with pytest.raises(PaymentError, match='Amount must be positive'):
            self.payment_service._validate_payment_amount(-1000)
    
    def test_validate_payment_amount_too_large(self):
        """Test payment amount validation with amount too large."""
        # Act & Assert
        with pytest.raises(PaymentError, match='Amount exceeds maximum'):
            self.payment_service._validate_payment_amount(100000000)  # $1M
    
    @patch('billing.services.payment_service.logger')
    def test_logging_on_operations(self, mock_logger):
        """Test that operations are properly logged."""
        # Arrange
        payment = ServicePaymentFactory(user=self.user)
        
        # Act
        self.payment_service._log_operation('test_operation', payment.id)
        
        # Assert
        mock_logger.info.assert_called_once()
    
    def test_service_initialization(self):
        """Test service initialization."""
        # Assert
        assert isinstance(self.payment_service, PaymentService)
        assert hasattr(self.payment_service, 'logger')
    
    @patch('stripe.PaymentIntent.retrieve')
    def test_get_payment_intent_success(self, mock_stripe_retrieve):
        """Test successful payment intent retrieval."""
        # Arrange
        mock_stripe_retrieve.return_value = {
            'id': 'pi_test_123',
            'status': 'succeeded',
            'amount': 5000,
            'currency': 'usd'
        }
        
        # Act
        result = self.payment_service.get_payment_intent('pi_test_123')
        
        # Assert
        assert result['id'] == 'pi_test_123'
        assert result['status'] == 'succeeded'
        mock_stripe_retrieve.assert_called_once_with('pi_test_123')
    
    @patch('stripe.PaymentIntent.retrieve')
    def test_get_payment_intent_not_found(self, mock_stripe_retrieve):
        """Test payment intent retrieval with not found error."""
        # Arrange
        mock_stripe_retrieve.side_effect = Exception('Payment intent not found')
        
        # Act & Assert
        with pytest.raises(PaymentError):
            self.payment_service.get_payment_intent('pi_nonexistent')
