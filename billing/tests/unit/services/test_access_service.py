"""
Unit tests for AccessService.

Tests the business logic of access control without external dependencies.
"""
import pytest
from unittest.mock import Mock, patch
from django.utils import timezone
from datetime import timedelta

from billing.services.access_service import AccessService
from billing.models import ServiceAccess, SubscriptionAccess, SolutionAccess
from billing.exceptions import AccessError
from billing.tests.factories import (
    UserFactory,
    ServiceFactory,
    ServiceAccessFactory,
    SubscriptionFactory,
    SubscriptionAccessFactory,
)


@pytest.mark.unit
class TestAccessService:
    """Test cases for AccessService."""
    
    def setup_method(self):
        """Set up test dependencies."""
        self.access_service = AccessService()
        self.user = UserFactory()
        self.service = ServiceFactory()
    
    def test_check_service_access_valid_active(self):
        """Test checking valid active service access."""
        # Arrange
        access = ServiceAccessFactory(
            user=self.user,
            service=self.service,
            status='active',
            access_expires_at=timezone.now() + timedelta(days=30)
        )
        
        # Act
        result = self.access_service.check_service_access(self.user, self.service.id)
        
        # Assert
        assert result['has_access'] is True
        assert result['status_code'] == 200
        assert 'access_granted_at' in result
        assert 'access_expires_at' in result
        
        # Verify last_accessed_at was updated
        access.refresh_from_db()
        assert access.last_accessed_at is not None
    
    def test_check_service_access_no_access(self):
        """Test checking service access when user has no access."""
        # Act
        result = self.access_service.check_service_access(self.user, self.service.id)
        
        # Assert
        assert result['has_access'] is False
        assert result['status_code'] == 404
        assert 'No active access found' in result['message']
    
    def test_check_service_access_expired(self):
        """Test checking expired service access."""
        # Arrange
        access = ServiceAccessFactory(
            user=self.user,
            service=self.service,
            status='active',
            access_expires_at=timezone.now() - timedelta(days=1)  # Expired
        )
        
        # Act
        result = self.access_service.check_service_access(self.user, self.service.id)
        
        # Assert
        assert result['has_access'] is False
        assert result['status_code'] == 403
        assert 'Access has expired' in result['message']
        
        # Verify access status was updated
        access.refresh_from_db()
        assert access.status == 'expired'
    
    def test_check_service_access_no_expiration(self):
        """Test checking service access with no expiration date."""
        # Arrange
        access = ServiceAccessFactory(
            user=self.user,
            service=self.service,
            status='active',
            access_expires_at=None  # No expiration
        )
        
        # Act
        result = self.access_service.check_service_access(self.user, self.service.id)
        
        # Assert
        assert result['has_access'] is True
        assert result['status_code'] == 200
    
    def test_check_subscription_access_valid(self):
        """Test checking valid subscription access."""
        # Arrange
        subscription = SubscriptionFactory(status='active')
        access = SubscriptionAccessFactory(
            user=self.user,
            subscription=subscription,
            status='active'
        )
        
        # Act
        result = self.access_service.check_subscription_access(self.user, subscription.id)
        
        # Assert
        assert result['has_access'] is True
        assert result['status_code'] == 200
        assert result['subscription_status'] == 'active'
    
    def test_check_subscription_access_no_access(self):
        """Test checking subscription access when user has no access."""
        # Arrange
        subscription = SubscriptionFactory()
        
        # Act
        result = self.access_service.check_subscription_access(self.user, subscription.id)
        
        # Assert
        assert result['has_access'] is False
        assert result['status_code'] == 404
        assert 'No active access found' in result['message']
    
    def test_list_user_services_with_access(self):
        """Test listing services for user with access."""
        # Arrange
        service1 = ServiceFactory(name='Service 1')
        service2 = ServiceFactory(name='Service 2')
        
        ServiceAccessFactory(
            user=self.user,
            service=service1,
            status='active'
        )
        ServiceAccessFactory(
            user=self.user,
            service=service2,
            status='active'
        )
        
        # Act
        result = self.access_service.list_user_services(self.user)
        
        # Assert
        assert len(result['services']) == 2
        assert len(result['subscriptions']) == 0
        
        service_names = [s['name'] for s in result['services']]
        assert 'Service 1' in service_names
        assert 'Service 2' in service_names
    
    def test_list_user_services_with_expired_access(self):
        """Test listing services excludes expired access."""
        # Arrange
        active_service = ServiceFactory(name='Active Service')
        expired_service = ServiceFactory(name='Expired Service')
        
        ServiceAccessFactory(
            user=self.user,
            service=active_service,
            status='active',
            access_expires_at=timezone.now() + timedelta(days=30)
        )
        ServiceAccessFactory(
            user=self.user,
            service=expired_service,
            status='active',
            access_expires_at=timezone.now() - timedelta(days=1)  # Expired
        )
        
        # Act
        result = self.access_service.list_user_services(self.user)
        
        # Assert
        assert len(result['services']) == 1
        assert result['services'][0]['name'] == 'Active Service'
    
    def test_list_user_services_empty(self):
        """Test listing services for user with no access."""
        # Act
        result = self.access_service.list_user_services(self.user)
        
        # Assert
        assert len(result['services']) == 0
        assert len(result['subscriptions']) == 0
    
    def test_revoke_service_access_success(self):
        """Test successful service access revocation."""
        # Arrange
        access = ServiceAccessFactory(
            user=self.user,
            service=self.service,
            status='active'
        )
        
        # Act
        result = self.access_service.revoke_service_access(
            self.user, 
            self.service.id, 
            reason='Test revocation'
        )
        
        # Assert
        assert result is True
        
        # Verify access was revoked
        access.refresh_from_db()
        assert access.status == 'revoked'
    
    def test_revoke_service_access_no_access(self):
        """Test revoking service access when user has no access."""
        # Act & Assert
        with pytest.raises(AccessError, match='No active service access found'):
            self.access_service.revoke_service_access(self.user, self.service.id)
    
    def test_revoke_subscription_access_success(self):
        """Test successful subscription access revocation."""
        # Arrange
        subscription = SubscriptionFactory()
        access = SubscriptionAccessFactory(
            user=self.user,
            subscription=subscription,
            status='active'
        )
        
        # Act
        result = self.access_service.revoke_subscription_access(
            self.user,
            subscription.id,
            reason='Test revocation'
        )
        
        # Assert
        assert result is True
        
        # Verify access was revoked
        access.refresh_from_db()
        assert access.status == 'revoked'
    
    def test_revoke_subscription_access_no_access(self):
        """Test revoking subscription access when user has no access."""
        # Arrange
        subscription = SubscriptionFactory()
        
        # Act & Assert
        with pytest.raises(AccessError, match='No active subscription access found'):
            self.access_service.revoke_subscription_access(self.user, subscription.id)
    
    def test_is_access_expired_with_expiration(self):
        """Test access expiration check with expiration date."""
        # Arrange
        expired_access = ServiceAccessFactory(
            access_expires_at=timezone.now() - timedelta(days=1)
        )
        valid_access = ServiceAccessFactory(
            access_expires_at=timezone.now() + timedelta(days=1)
        )
        
        # Act & Assert
        assert self.access_service._is_access_expired(expired_access) is True
        assert self.access_service._is_access_expired(valid_access) is False
    
    def test_is_access_expired_no_expiration(self):
        """Test access expiration check with no expiration date."""
        # Arrange
        access = ServiceAccessFactory(access_expires_at=None)
        
        # Act & Assert
        assert self.access_service._is_access_expired(access) is False
    
    @patch('billing.services.access_service.logger')
    def test_logging_on_operations(self, mock_logger):
        """Test that operations are properly logged."""
        # Arrange
        access = ServiceAccessFactory(user=self.user, service=self.service)
        
        # Act
        self.access_service.check_service_access(self.user, self.service.id)
        
        # Assert
        mock_logger.info.assert_called()
    
    def test_service_initialization(self):
        """Test service initialization."""
        # Assert
        assert isinstance(self.access_service, AccessService)
        assert hasattr(self.access_service, 'logger')
