"""
Basic tests to verify test infrastructure is working.

These tests ensure that the testing environment is properly set up
and basic functionality is working.
"""
import pytest
from django.test import TestCase


@pytest.mark.unit
class TestBasicInfrastructure:
    """Basic tests to verify test infrastructure."""
    
    def test_pytest_is_working(self):
        """Test that pytest is working correctly."""
        assert True
    
    def test_django_is_available(self):
        """Test that Django is available."""
        from django.conf import settings
        assert settings is not None
    
    def test_billing_app_is_available(self):
        """Test that billing app is available."""
        try:
            import billing
            assert billing is not None
        except ImportError:
            pytest.skip("Billing app not available")
    
    def test_user_model_is_available(self):
        """Test that User model is available."""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        assert User is not None
    
    def test_basic_math(self):
        """Test basic math operations."""
        assert 1 + 1 == 2
        assert 2 * 3 == 6
        assert 10 / 2 == 5
    
    def test_string_operations(self):
        """Test string operations."""
        test_string = "Hello, World!"
        assert len(test_string) == 13
        assert test_string.lower() == "hello, world!"
        assert "World" in test_string


class TestDjangoIntegration(TestCase):
    """Django TestCase for database integration tests."""
    
    def test_database_connection(self):
        """Test that database connection is working."""
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            self.assertEqual(result[0], 1)
    
    def test_user_creation(self):
        """Test basic user creation."""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.check_password('testpass123'))
    
    def test_user_model_fields(self):
        """Test user model has expected fields."""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        # Check that User model has expected fields
        field_names = [field.name for field in User._meta.fields]
        
        # Basic fields that should exist
        expected_fields = ['id', 'username', 'email', 'first_name', 'last_name']
        
        for field in expected_fields:
            self.assertIn(field, field_names, f"Field '{field}' not found in User model")


@pytest.mark.unit
def test_simple_function():
    """Simple function test."""
    def add_numbers(a, b):
        return a + b
    
    assert add_numbers(2, 3) == 5
    assert add_numbers(-1, 1) == 0
    assert add_numbers(0, 0) == 0


@pytest.mark.unit
def test_list_operations():
    """Test list operations."""
    test_list = [1, 2, 3, 4, 5]
    
    assert len(test_list) == 5
    assert test_list[0] == 1
    assert test_list[-1] == 5
    assert 3 in test_list
    assert 6 not in test_list


@pytest.mark.unit
def test_dictionary_operations():
    """Test dictionary operations."""
    test_dict = {
        'name': 'Test User',
        'email': '<EMAIL>',
        'age': 25
    }
    
    assert test_dict['name'] == 'Test User'
    assert test_dict.get('email') == '<EMAIL>'
    assert test_dict.get('phone') is None
    assert len(test_dict) == 3


@pytest.mark.unit
class TestErrorHandling:
    """Test error handling."""
    
    def test_exception_handling(self):
        """Test exception handling."""
        with pytest.raises(ValueError):
            int('not_a_number')
    
    def test_zero_division(self):
        """Test zero division error."""
        with pytest.raises(ZeroDivisionError):
            result = 10 / 0
    
    def test_key_error(self):
        """Test key error."""
        test_dict = {'a': 1, 'b': 2}
        with pytest.raises(KeyError):
            value = test_dict['c']


@pytest.mark.unit
def test_environment_variables():
    """Test environment variables."""
    import os
    
    # Test that we can access environment variables
    # These might not be set, so we just test the mechanism
    django_settings = os.environ.get('DJANGO_SETTINGS_MODULE')
    
    # Should be set to test settings
    if django_settings:
        assert 'test' in django_settings.lower()


@pytest.mark.unit
def test_imports():
    """Test that we can import necessary modules."""
    # Test Django imports
    from django.conf import settings
    from django.contrib.auth import get_user_model
    from django.test import TestCase
    from django.db import models
    
    # Test Python standard library
    import json
    import datetime
    import uuid
    
    # Test third-party packages
    import pytest
    
    # All imports successful
    assert True
