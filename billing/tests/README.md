# 🧪 Billing Tests Documentation

## Overview

This directory contains comprehensive tests for the billing module, designed to work seamlessly in Docker environment with high coverage and reliability.

## 📁 Test Structure

```
billing/tests/
├── __init__.py                 # Test package initialization
├── conftest.py                 # Pytest configuration and fixtures
├── README.md                   # This documentation
├── factories/                  # Test data factories
│   ├── __init__.py
│   ├── user_factory.py         # User test data
│   ├── customer_factory.py     # Customer test data
│   ├── service_factory.py      # Service test data
│   ├── payment_factory.py      # Payment test data
│   └── access_factory.py       # Access test data
├── unit/                       # Unit tests (fast, isolated)
│   ├── __init__.py
│   ├── models/                 # Model tests
│   ├── services/               # Service logic tests
│   │   ├── test_payment_service.py
│   │   ├── test_access_service.py
│   │   └── test_enterprise_service.py
│   └── utils/                  # Utility tests
├── integration/                # Integration tests (medium speed)
│   ├── __init__.py
│   ├── api/                    # API endpoint tests
│   │   ├── test_payment_api.py
│   │   ├── test_access_api.py
│   │   └── test_webhook_api.py
│   ├── database/               # Database integration tests
│   └── services/               # Service interaction tests
└── e2e/                       # End-to-end tests (slow, realistic)
    ├── test_payment_flows.py
    ├── test_subscription_flows.py
    └── test_enterprise_flows.py
```

## 🚀 Running Tests in Docker

### Quick Start

```bash
# Run all tests
make test

# Run specific test types
make test-unit           # Fast unit tests
make test-integration    # Integration tests
make test-e2e           # End-to-end tests

# Run with coverage
make test-coverage

# Run in development environment (faster)
make test-dev
```

### Detailed Commands

```bash
# Run all billing tests
make test-billing

# Run specific service tests
make test-billing-services

# Run specific test file
make test-file FILE=billing/tests/unit/services/test_payment_service.py

# Run specific test method
make test-method METHOD=billing/tests/unit/services/test_payment_service.py::TestPaymentService::test_process_checkout_success

# Run tests in watch mode (for development)
make test-watch
```

## 🏗️ Test Categories

### Unit Tests (60% of test suite)
- **Purpose**: Test individual components in isolation
- **Speed**: Very fast (< 1 second per test)
- **Coverage**: Business logic, model methods, utilities
- **Mocking**: Heavy use of mocks for external dependencies

**Example**:
```python
@pytest.mark.unit
def test_payment_service_create_payment_intent():
    # Test PaymentService.create_payment_intent() in isolation
    pass
```

### Integration Tests (30% of test suite)
- **Purpose**: Test component interactions
- **Speed**: Medium (1-5 seconds per test)
- **Coverage**: API endpoints, service interactions, database operations
- **Mocking**: Limited mocking, real database interactions

**Example**:
```python
@pytest.mark.integration
def test_payment_api_endpoint():
    # Test payment API with real database and service calls
    pass
```

### End-to-End Tests (10% of test suite)
- **Purpose**: Test complete user workflows
- **Speed**: Slow (5-30 seconds per test)
- **Coverage**: Critical user journeys, payment flows
- **Mocking**: Minimal mocking, realistic scenarios

**Example**:
```python
@pytest.mark.e2e
def test_complete_payment_flow():
    # Test entire payment flow from checkout to access grant
    pass
```

## 🔧 Test Configuration

### Docker Environment

Tests run in isolated Docker containers with:
- **Separate test database**: PostgreSQL with in-memory storage
- **Isolated Redis**: For caching tests
- **Mock external services**: Stripe, email, etc.
- **Fast execution**: Optimized for speed

### Pytest Configuration

```python
# pyproject.toml
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "--ds=config.settings.test --reuse-db --import-mode=importlib"
python_files = ["tests.py", "test_*.py"]
```

### Test Settings

```python
# config/settings/test.py
- Fast password hashing
- In-memory email backend
- Optimized database settings
- Mock external services
```

## 🎯 Test Fixtures

### Common Fixtures (conftest.py)

```python
@pytest.fixture
def user():
    """Create a test user."""
    return UserFactory()

@pytest.fixture
def customer(user):
    """Create a test customer."""
    return CustomerFactory(user=user)

@pytest.fixture
def mock_stripe():
    """Mock all Stripe API calls."""
    # Comprehensive Stripe mocking
```

### Factory Usage

```python
# Create test data easily
user = UserFactory()
customer = CustomerFactory(user=user)
payment = ServicePaymentFactory(user=user)

# Create specific scenarios
expired_access = ServiceAccessFactory.create_expired()
premium_service = ServiceFactory.create_premium_service()
```

## 📊 Coverage Goals

### Target Coverage
- **Overall**: 90%+
- **Services**: 95%+
- **Models**: 90%+
- **Views**: 85%+
- **Critical paths**: 100%

### Coverage Reports

```bash
# Generate HTML coverage report
make test-coverage

# View coverage report
open htmlcov/index.html
```

## 🔍 Test Patterns

### Service Testing Pattern

```python
class TestPaymentService:
    def setup_method(self):
        self.service = PaymentService()
        self.user = UserFactory()
    
    @patch('stripe.PaymentIntent.create')
    def test_create_payment_intent(self, mock_stripe):
        # Arrange
        mock_stripe.return_value = {'id': 'pi_test'}
        
        # Act
        result = self.service.create_payment_intent(...)
        
        # Assert
        assert result['id'] == 'pi_test'
        mock_stripe.assert_called_once()
```

### API Testing Pattern

```python
class TestPaymentAPI(APITestCase):
    def setUp(self):
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)
    
    def test_payment_endpoint(self):
        # Arrange
        data = {'amount': 5000}
        
        # Act
        response = self.client.post('/api/payments/', data)
        
        # Assert
        self.assertEqual(response.status_code, 200)
```

### E2E Testing Pattern

```python
@pytest.mark.e2e
def test_complete_payment_flow(authenticated_api_client):
    # 1. Create checkout session
    # 2. Process payment
    # 3. Verify access granted
    # 4. Test service usage
    pass
```

## 🚨 Test Best Practices

### 1. Test Isolation
- Each test is independent
- Use factories for test data
- Clean up after tests
- No shared state between tests

### 2. Descriptive Test Names
```python
def test_payment_service_creates_payment_intent_with_valid_data():
    # Clear what the test does
    pass

def test_payment_service_raises_error_when_amount_is_negative():
    # Clear what the test validates
    pass
```

### 3. AAA Pattern (Arrange, Act, Assert)
```python
def test_example():
    # Arrange - Set up test data
    user = UserFactory()
    
    # Act - Execute the code under test
    result = service.do_something(user)
    
    # Assert - Verify the results
    assert result.success is True
```

### 4. Mock External Dependencies
```python
@patch('stripe.PaymentIntent.create')
def test_payment_creation(mock_stripe):
    # Mock external Stripe API
    mock_stripe.return_value = {'id': 'pi_test'}
    # Test internal logic without external dependencies
```

## 🐛 Debugging Tests

### Run Single Test with Debug
```bash
make test-method METHOD=billing/tests/unit/services/test_payment_service.py::TestPaymentService::test_specific_method
```

### Debug with PDB
```bash
# Add breakpoint in test
import pdb; pdb.set_trace()

# Run with PDB support
docker compose -f docker-compose-test.yaml run --rm test-web pytest path/to/test.py -s --pdb
```

### View Test Logs
```bash
# Run with verbose output
make test-dev-unit -v

# View specific test output
docker compose -f docker-compose-test.yaml run --rm test-web pytest path/to/test.py -v -s
```

## 📈 Performance Testing

### Performance Markers
```python
@pytest.mark.performance
def test_payment_processing_performance():
    # Test performance under load
    pass
```

### Run Performance Tests
```bash
make test-performance
```

## 🔄 Continuous Integration

### GitHub Actions Integration
```yaml
# .github/workflows/test.yml
- name: Run Tests
  run: make ci-test
```

### Pre-commit Hooks
```bash
# Run tests before commit
pre-commit install
```

## 📚 Additional Resources

### Pytest Documentation
- [Pytest Official Docs](https://docs.pytest.org/)
- [Factory Boy Docs](https://factoryboy.readthedocs.io/)
- [Django Testing](https://docs.djangoproject.com/en/stable/topics/testing/)

### Test Writing Guidelines
1. Write tests first (TDD approach)
2. Test behavior, not implementation
3. Keep tests simple and focused
4. Use meaningful assertions
5. Document complex test scenarios

## 🎉 Contributing

When adding new features:
1. Write tests first
2. Ensure 90%+ coverage
3. Add integration tests for APIs
4. Update documentation
5. Run full test suite before PR

```bash
# Before submitting PR
make test-coverage
make lint
make type-check
```
