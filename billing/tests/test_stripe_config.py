#!/usr/bin/env python
"""
Test script to diagnose Stripe configuration and customer creation issues
"""
import os
import django
import sys

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

import stripe
from django.conf import settings
from django.contrib.auth import get_user_model
from billing.services import StripeService
from billing.config import BillingConfig

CustomUser = get_user_model()

def test_stripe_configuration():
    """Test Stripe API configuration"""
    print("🔍 Testing Stripe Configuration...")
    
    # Check if Stripe keys are configured
    stripe_keys = BillingConfig.get_stripe_keys()
    
    print(f"✓ Stripe Secret Key: {'✅ Configured' if stripe_keys['secret_key'] else '❌ Missing'}")
    print(f"✓ Stripe Publishable Key: {'✅ Configured' if stripe_keys['publishable_key'] else '❌ Missing'}")
    
    if stripe_keys['secret_key']:
        if stripe_keys['secret_key'].startswith('sk_test_'):
            print("✅ Using Test Mode")
        elif stripe_keys['secret_key'].startswith('sk_live_'):
            print("⚠️  Using Live Mode")
        else:
            print("❌ Invalid Stripe key format")
            return False
    else:
        print("❌ No Stripe secret key found")
        return False
    
    # Test Stripe API connectivity
    try:
        stripe.api_key = stripe_keys['secret_key']
        stripe.api_version = BillingConfig.STRIPE_API_VERSION
        
        # Try to list customers (this will test API connectivity)
        customers = stripe.Customer.list(limit=1)
        print("✅ Stripe API connectivity successful")
        return True
        
    except stripe.error.AuthenticationError as e:
        print(f"❌ Stripe authentication failed: {str(e)}")
        return False
    except stripe.error.StripeError as e:
        print(f"❌ Stripe API error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_user_data():
    """Test user data for customer creation"""
    print("\n🔍 Testing User Data...")
    
    # Get a test user (first user in the system)
    try:
        user = CustomUser.objects.first()
        if not user:
            print("❌ No users found in the system")
            return False, None
        
        print(f"✅ Test user found: {user.email}")
        print(f"✓ User ID: {user.id}")
        print(f"✓ Email: {user.email}")
        print(f"✓ First Name: {getattr(user, 'first_name', 'N/A')}")
        print(f"✓ Last Name: {getattr(user, 'last_name', 'N/A')}")
        print(f"✓ Phone: {getattr(user, 'phone_number', 'N/A')}")
        
        # Validate email format
        if '@' not in user.email or '.' not in user.email:
            print("❌ Invalid email format")
            return False, None
        
        print("✅ User data validation passed")
        return True, user
        
    except Exception as e:
        print(f"❌ Error getting user data: {str(e)}")
        return False, None

def test_customer_creation(user):
    """Test customer creation with the StripeService"""
    print("\n🔍 Testing Customer Creation...")
    
    try:
        # Test the customer creation directly
        customer = StripeService.create_or_update_customer(user)
        
        print(f"✅ Customer created successfully!")
        print(f"✓ Customer ID: {customer.id}")
        print(f"✓ Stripe Customer ID: {customer.stripe_customer_id}")
        print(f"✓ Email: {customer.email}")
        print(f"✓ Name: {customer.name}")
        
        return True, customer
        
    except Exception as e:
        print(f"❌ Customer creation failed: {str(e)}")
        print(f"   Error type: {type(e).__name__}")
        
        # Try to get more details about the error
        if hasattr(e, '__cause__') and e.__cause__:
            print(f"   Underlying cause: {str(e.__cause__)}")
        
        return False, None

def test_solution_and_price():
    """Test solution and price configuration"""
    print("\n🔍 Testing Solution and Price Configuration...")
    
    from content_management.models import Solution
    from billing.models import Product, Price
    
    solution_id = "0196b093-75f1-7007-b90f-3991b2c80fec"
    
    try:
        # Check if solution exists
        solution = Solution.objects.get(id=solution_id)
        print(f"✅ Solution found: {solution.name}")
        
        # Check if product exists
        product = Product.objects.filter(solution=solution, active=True).first()
        if not product:
            print("❌ No active product found for solution")
            return False
        
        print(f"✅ Product found: {product.name}")
        print(f"✓ Stripe Product ID: {product.stripe_product_id}")
        
        # Check if price exists
        price = Price.objects.filter(product=product, active=True).first()
        if not price:
            print("❌ No active price found for product")
            return False
        
        print(f"✅ Price found: ${price.unit_amount/100} {price.currency}")
        print(f"✓ Stripe Price ID: {price.stripe_price_id}")
        
        return True
        
    except Solution.DoesNotExist:
        print(f"❌ Solution {solution_id} not found")
        return False
    except Exception as e:
        print(f"❌ Error checking solution/price: {str(e)}")
        return False

def test_network_connectivity():
    """Test network connectivity to Stripe"""
    print("\n🔍 Testing Network Connectivity...")
    
    import requests
    
    try:
        # Test basic connectivity to Stripe
        response = requests.get("https://api.stripe.com/v1/ping", timeout=10)
        if response.status_code == 200:
            print("✅ Network connectivity to Stripe successful")
            return True
        else:
            print(f"❌ Stripe API returned status code: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Network timeout connecting to Stripe")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Network connection error to Stripe")
        return False
    except Exception as e:
        print(f"❌ Network error: {str(e)}")
        return False

def main():
    """Run all diagnostic tests"""
    print("🚀 Starting Stripe Configuration Diagnostics\n")
    
    tests = [
        ("Stripe Configuration", test_stripe_configuration),
        ("Network Connectivity", test_network_connectivity),
        ("User Data", test_user_data),
        ("Solution and Price", test_solution_and_price),
    ]
    
    passed = 0
    total = len(tests)
    user = None
    
    for test_name, test_func in tests:
        try:
            if test_name == "User Data":
                result, user = test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test error: {str(e)}")
    
    # If basic tests pass, try customer creation
    if passed >= 3 and user:
        print("\n" + "="*50)
        customer_result, customer = test_customer_creation(user)
        if customer_result:
            passed += 1
        total += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Stripe configuration looks good.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
