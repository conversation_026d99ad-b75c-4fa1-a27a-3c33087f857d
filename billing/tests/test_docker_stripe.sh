#!/bin/bash

# Stripe Connect Test Script for Docker Environment
# Tests both basic Stripe and Connect functionality

echo "🚀 Starting Stripe Connect Configuration Test in Docker..."

# Check if Docker containers are running
echo "📋 Checking Docker containers..."
docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(web|db|redis)"

echo ""
echo "🔍 Running Stripe Connect configuration diagnostics..."

# Run the Connect configuration test
echo "1️⃣ Testing Stripe Connect Configuration..."
docker compose -f docker-compose-dev.yaml exec web python billing/tests/test_stripe_connect_config.py

echo ""
echo "2️⃣ Testing Stripe Connect API Endpoints..."
docker compose -f docker-compose-dev.yaml exec web python billing/tests/test_stripe_connect_api.py

echo ""
echo "3️⃣ Testing Basic Stripe Connectivity..."

# Test basic Stripe functionality
docker compose -f docker-compose-dev.yaml exec web bash -c "
echo 'Testing Django management commands...'
python manage.py check --deploy

echo ''
echo 'Testing basic Stripe connectivity...'
python -c \"
import stripe
from django.conf import settings
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

import django
django.setup()

try:
    stripe.api_key = settings.STRIPE_SECRET_KEY
    customers = stripe.Customer.list(limit=1)
    print('✅ Basic Stripe API connectivity successful')

    # Test Connect Client ID
    client_id = getattr(settings, 'STRIPE_CONNECT_CLIENT_ID', '')
    if client_id and client_id.startswith('ca_'):
        print(f'✅ Connect Client ID configured: {client_id[:15]}...')
    else:
        print('❌ Connect Client ID not properly configured')

except Exception as e:
    print(f'❌ Stripe API error: {str(e)}')
\"
"

echo ""
echo "4️⃣ Testing URL Routing..."
docker compose -f docker-compose-dev.yaml exec web python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.urls import reverse
try:
    webhook_url = reverse('billing:stripe_webhook')
    connect_webhook_url = reverse('billing:stripe_connect_webhook')
    print(f'✅ Standard webhook URL: {webhook_url}')
    print(f'✅ Connect webhook URL: {connect_webhook_url}')
except Exception as e:
    print(f'❌ URL routing error: {e}')
"

echo ""
echo "✅ Docker Stripe Connect test completed!"
echo ""
echo "📋 Summary:"
echo "   - Configuration test: Check output above"
echo "   - API test: Check output above"
echo "   - Basic connectivity: Check output above"
echo "   - URL routing: Check output above"
echo ""
echo "🎯 Next steps:"
echo "   1. If tests pass: Set up webhook endpoints in Stripe Dashboard"
echo "   2. If tests fail: Review error messages and fix configuration"
echo "   3. Test Connect account creation via API"
