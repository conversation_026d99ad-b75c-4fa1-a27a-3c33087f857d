#!/usr/bin/env python3
"""
Stripe Connect API Testing Script
Tests the Connect API endpoints for healthcare provider functionality
"""

import os
import sys
import django
import json
import requests
from django.conf import settings
from django.contrib.auth import get_user_model
from django.test import Client
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from billing.models.customer import UserPaymentProfile

User = get_user_model()

def print_header(title):
    """Print formatted test header"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_test(test_name):
    """Print test name"""
    print(f"\n🔍 Testing: {test_name}")

def print_success(message):
    """Print success message"""
    print(f"✅ {message}")

def print_error(message):
    """Print error message"""
    print(f"❌ {message}")

def print_info(message):
    """Print info message"""
    print(f"ℹ️  {message}")

def get_test_user_token():
    """Create test user and get JWT token"""
    try:
        # Get or create test user
        test_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'test_api_user',
                'first_name': 'Test',
                'last_name': 'API',
                'is_active': True
            }
        )
        
        if created:
            test_user.set_password('testpass123')
            test_user.save()
            print_info(f"Created test user: {test_user.email}")
        
        # Generate JWT token
        refresh = RefreshToken.for_user(test_user)
        access_token = str(refresh.access_token)
        
        return test_user, access_token
        
    except Exception as e:
        print_error(f"Error creating test user: {str(e)}")
        return None, None

def test_payment_profile_endpoints():
    """Test 1: Payment Profile API endpoints"""
    print_test("Payment Profile API Endpoints")
    
    try:
        user, token = get_test_user_token()
        if not user or not token:
            return False
        
        client = APIClient()
        client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # Test GET payment profile
        response = client.get('/api/billing/payment-profiles/')
        print(f"   GET /api/billing/payment-profiles/ - Status: {response.status_code}")
        
        if response.status_code in [200, 404]:  # 404 is OK if no profile exists
            print_success("Payment profile endpoint accessible")
        else:
            print_error(f"Unexpected status code: {response.status_code}")
            return False
        
        # Test payment status endpoint
        response = client.get('/api/billing/payment-profiles/payment_status/')
        print(f"   GET payment_status/ - Status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print_error(f"Error testing payment profile endpoints: {str(e)}")
        return False

def test_connect_account_creation_endpoint():
    """Test 2: Connect Account Creation endpoint"""
    print_test("Connect Account Creation Endpoint")

    try:
        user, token = get_test_user_token()
        if not user or not token:
            return False

        client = APIClient()
        client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        # Check if user already has a Connect account
        existing_profile = UserPaymentProfile.objects.filter(user=user).first()
        if existing_profile and existing_profile.stripe_account_id:
            print_info(f"User already has Connect account: {existing_profile.stripe_account_id}")
            return True

        # Test Connect account creation endpoint
        print_info("Testing Connect account creation endpoint...")
        print_info("Note: This will create a real Stripe Connect account in test mode")

        response = client.post('/api/billing/payment-profiles/create_connect_account/')
        print(f"   POST create_connect_account/ - Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            if 'account_link' in data:
                print_success("Connect account creation successful")
                print(f"   Account link generated: {data['account_link'][:50]}...")
                return True
            else:
                print_error("No account link in response")
                return False
        elif response.status_code == 400:
            print_info("Account creation failed (expected in some cases)")
            print(f"   Response: {response.json()}")
            return True  # This might be expected
        else:
            print_error(f"Unexpected status code: {response.status_code}")
            return False

    except Exception as e:
        print_error(f"Error testing Connect account creation: {str(e)}")
        return False

def test_connect_account_deactivation_endpoint():
    """Test 2.5: Connect Account Deactivation endpoint"""
    print_test("Connect Account Deactivation Endpoint")

    try:
        user, token = get_test_user_token()
        if not user or not token:
            return False

        client = APIClient()
        client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        # Check if user has a Connect account to deactivate
        existing_profile = UserPaymentProfile.objects.filter(user=user).first()
        if not existing_profile or not existing_profile.stripe_account_id:
            print_info("No Connect account found to deactivate - testing error handling")

            # Test deactivation when no account exists
            response = client.delete('/api/billing/payment-profiles/deactivate_connect_account/')
            print(f"   DELETE deactivate_connect_account/ (no account) - Status: {response.status_code}")

            if response.status_code == 404:
                print_success("Correctly returns 404 when no account exists")
                return True
            else:
                print_error(f"Expected 404, got {response.status_code}")
                return False
        else:
            print_info(f"Found Connect account to test deactivation: {existing_profile.stripe_account_id}")

            # Test deactivation endpoint
            print_info("Testing Connect account deactivation endpoint...")
            print_info("Note: This will deactivate the real Stripe Connect account")

            response = client.delete('/api/billing/payment-profiles/deactivate_connect_account/')
            print(f"   DELETE deactivate_connect_account/ - Status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print_success("Connect account deactivation successful")
                    print(f"   Method: {data.get('details', {}).get('method', 'unknown')}")
                    print(f"   Transfers affected: {data.get('details', {}).get('transfers_affected', 0)}")
                    return True
                else:
                    print_error("Deactivation response indicates failure")
                    return False
            else:
                print_error(f"Unexpected status code: {response.status_code}")
                print(f"   Response: {response.json() if response.content else 'No content'}")
                return False

    except Exception as e:
        print_error(f"Error testing Connect account deactivation: {str(e)}")
        return False

def test_transfer_endpoints():
    """Test 3: Transfer API endpoints"""
    print_test("Transfer API Endpoints")
    
    try:
        user, token = get_test_user_token()
        if not user or not token:
            return False
        
        client = APIClient()
        client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # Test GET transfers
        response = client.get('/api/billing/transfers/')
        print(f"   GET /api/billing/transfers/ - Status: {response.status_code}")
        
        if response.status_code == 200:
            print_success("Transfers endpoint accessible")
        else:
            print_error(f"Transfers endpoint failed: {response.status_code}")
            return False
        
        # Test sent transfers
        response = client.get('/api/billing/transfers/sent_transfers/')
        print(f"   GET sent_transfers/ - Status: {response.status_code}")
        
        # Test received transfers
        response = client.get('/api/billing/transfers/received_transfers/')
        print(f"   GET received_transfers/ - Status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print_error(f"Error testing transfer endpoints: {str(e)}")
        return False

def test_doctor_payment_endpoints():
    """Test 4: Doctor Payment API endpoints"""
    print_test("Doctor Payment API Endpoints")
    
    try:
        user, token = get_test_user_token()
        if not user or not token:
            return False
        
        client = APIClient()
        client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # Test doctor payment endpoints
        response = client.get('/api/billing/doctor-payments/')
        print(f"   GET /api/billing/doctor-payments/ - Status: {response.status_code}")
        
        # Test setup payment account (might require doctor role)
        response = client.post('/api/billing/doctor-payments/setup_payment_account/')
        print(f"   POST setup_payment_account/ - Status: {response.status_code}")
        
        if response.status_code in [200, 403, 400]:  # 403/400 might be expected
            print_success("Doctor payment endpoints accessible")
        else:
            print_error(f"Unexpected status code: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print_error(f"Error testing doctor payment endpoints: {str(e)}")
        return False

def test_webhook_endpoints():
    """Test 5: Webhook endpoint accessibility"""
    print_test("Webhook Endpoint Accessibility")
    
    try:
        client = Client()
        
        # Test standard webhook endpoint (should reject GET)
        response = client.get('/api/billing/webhook/')
        print(f"   GET /api/billing/webhook/ - Status: {response.status_code}")
        
        # Test Connect webhook endpoint (should reject GET)
        response = client.get('/api/billing/webhook/connect/')
        print(f"   GET /api/billing/webhook/connect/ - Status: {response.status_code}")
        
        # Both should return 405 (Method Not Allowed) for GET requests
        print_success("Webhook endpoints are accessible (rejecting GET as expected)")
        return True
        
    except Exception as e:
        print_error(f"Error testing webhook endpoints: {str(e)}")
        return False

def test_url_routing():
    """Test 6: URL routing configuration"""
    print_test("URL Routing Configuration")
    
    try:
        from django.urls import reverse, NoReverseMatch
        
        # Test URL reversing for key endpoints
        urls_to_test = [
            'billing:stripe_webhook',
            'billing:stripe_connect_webhook',
        ]
        
        for url_name in urls_to_test:
            try:
                url = reverse(url_name)
                print_success(f"URL {url_name} -> {url}")
            except NoReverseMatch:
                print_error(f"URL {url_name} not found")
                return False
        
        return True
        
    except Exception as e:
        print_error(f"Error testing URL routing: {str(e)}")
        return False

def test_environment_configuration():
    """Test 7: Environment configuration for API testing"""
    print_test("Environment Configuration")
    
    try:
        # Check required settings
        required_settings = [
            'STRIPE_SECRET_KEY',
            'STRIPE_PUBLISHABLE_KEY',
            'STRIPE_CONNECT_CLIENT_ID',
            'FRONTEND_URL',
            'BACKEND_URL',
        ]
        
        missing_settings = []
        for setting in required_settings:
            if not getattr(settings, setting, ''):
                missing_settings.append(setting)
        
        if missing_settings:
            print_error(f"Missing settings: {', '.join(missing_settings)}")
            return False
        
        print_success("All required settings configured")
        
        # Check database connectivity
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            
        print_success("Database connectivity OK")
        return True
        
    except Exception as e:
        print_error(f"Error checking environment: {str(e)}")
        return False

def main():
    """Run all API tests"""
    print_header("Stripe Connect API Test Suite")
    print("Testing Ravid Healthcare Platform Connect API Endpoints")
    
    tests = [
        ("Environment Configuration", test_environment_configuration),
        ("URL Routing", test_url_routing),
        ("Payment Profile Endpoints", test_payment_profile_endpoints),
        ("Connect Account Creation", test_connect_account_creation_endpoint),
        ("Connect Account Deactivation", test_connect_account_deactivation_endpoint),
        ("Transfer Endpoints", test_transfer_endpoints),
        ("Doctor Payment Endpoints", test_doctor_payment_endpoints),
        ("Webhook Endpoints", test_webhook_endpoints),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print_error(f"{test_name} test failed with exception: {str(e)}")
    
    print_header("API Test Results")
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print_success("All API tests passed!")
    elif passed >= total - 1:
        print_info("Most API tests passed. Check any warnings above.")
    else:
        print_error("Multiple API tests failed. Review configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
