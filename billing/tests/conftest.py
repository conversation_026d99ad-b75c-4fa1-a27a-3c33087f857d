"""
Pytest configuration and fixtures for billing tests.

This module provides common fixtures and configuration for all billing tests,
optimized for Docker environment with proper database isolation and mocking.
"""
import pytest
import json
from decimal import Decimal
from unittest.mock import Mock, patch
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

# Import factories with error handling
try:
    from .factories import (
        UserFactory,
        CustomerFactory,
        ServiceFactory,
        ProductFactory,
        PriceFactory,
        ServicePaymentFactory,
        ServiceAccessFactory,
    )
except ImportError as e:
    # Create minimal factories if imports fail
    import factory
    from django.contrib.auth import get_user_model

    User = get_user_model()

    class UserFactory(factory.django.DjangoModelFactory):
        class Meta:
            model = User
        email = factory.Sequence(lambda n: f'testuser{n}@example.com')
        first_name = factory.Faker('first_name')
        last_name = factory.Faker('last_name')

    # Set other factories to None if not available
    CustomerFactory = None
    ServiceFactory = None
    ProductFactory = None
    PriceFactory = None
    ServicePaymentFactory = None
    ServiceAccessFactory = None

User = get_user_model()


@pytest.fixture(scope="session")
def django_db_setup():
    """Setup test database for Docker environment."""
    from django.conf import settings
    from django.test.utils import setup_test_environment
    
    setup_test_environment()
    # Database is already configured in test settings


@pytest.fixture
def user():
    """Create a test user."""
    return UserFactory()


@pytest.fixture
def customer(user):
    """Create a test customer with associated user."""
    if CustomerFactory:
        return CustomerFactory(user=user)
    return None


@pytest.fixture
def service():
    """Create a test service."""
    if ServiceFactory:
        return ServiceFactory()
    return None


@pytest.fixture
def product(service):
    """Create a test product linked to service."""
    if ProductFactory and service:
        return ProductFactory(service=service)
    return None


@pytest.fixture
def price(product):
    """Create a test price for product."""
    if PriceFactory and product:
        return PriceFactory(product=product)
    return None


@pytest.fixture
def subscription(customer, price):
    """Create a test subscription."""
    # Will be implemented when SubscriptionFactory is available
    return None


@pytest.fixture
def enterprise_account():
    """Create a test enterprise account."""
    # Will be implemented when EnterpriseAccountFactory is available
    return None


@pytest.fixture
def mock_stripe():
    """Mock all Stripe API calls."""
    with patch('stripe.Customer') as mock_customer, \
         patch('stripe.Product') as mock_product, \
         patch('stripe.Price') as mock_price, \
         patch('stripe.PaymentIntent') as mock_payment_intent, \
         patch('stripe.Subscription') as mock_subscription, \
         patch('stripe.checkout.Session') as mock_session:
        
        # Configure default mock responses
        mock_customer.create.return_value = Mock(id='cus_test_123')
        mock_customer.retrieve.return_value = Mock(
            id='cus_test_123',
            email='<EMAIL>',
            name='Test User'
        )
        
        mock_product.create.return_value = Mock(id='prod_test_123')
        mock_product.retrieve.return_value = Mock(
            id='prod_test_123',
            name='Test Product',
            description='Test Description'
        )
        
        mock_price.create.return_value = Mock(id='price_test_123')
        mock_price.retrieve.return_value = Mock(
            id='price_test_123',
            unit_amount=5000,
            currency='usd',
            product='prod_test_123'
        )
        
        mock_payment_intent.create.return_value = Mock(
            id='pi_test_123',
            client_secret='pi_test_123_secret_test',
            status='requires_payment_method'
        )
        mock_payment_intent.retrieve.return_value = Mock(
            id='pi_test_123',
            status='succeeded',
            amount=5000,
            currency='usd'
        )
        
        mock_subscription.create.return_value = Mock(id='sub_test_123')
        mock_subscription.retrieve.return_value = Mock(
            id='sub_test_123',
            status='active',
            current_period_start=timezone.now().timestamp(),
            current_period_end=(timezone.now() + timedelta(days=30)).timestamp()
        )
        
        mock_session.create.return_value = Mock(
            id='cs_test_123',
            url='https://checkout.stripe.com/test'
        )
        mock_session.retrieve.return_value = Mock(
            id='cs_test_123',
            payment_intent='pi_test_123',
            customer='cus_test_123',
            amount_total=5000,
            currency='usd',
            metadata={'price_id': 'price_test_123'}
        )
        
        yield {
            'customer': mock_customer,
            'product': mock_product,
            'price': mock_price,
            'payment_intent': mock_payment_intent,
            'subscription': mock_subscription,
            'session': mock_session,
        }


@pytest.fixture
def mock_redis():
    """Mock Redis cache operations."""
    with patch('django.core.cache.cache') as mock_cache:
        mock_cache.get.return_value = None
        mock_cache.set.return_value = True
        mock_cache.delete.return_value = True
        yield mock_cache


@pytest.fixture
def mock_celery():
    """Mock Celery task execution."""
    with patch('celery.current_app.send_task') as mock_send_task:
        mock_send_task.return_value = Mock(id='task_test_123')
        yield mock_send_task


@pytest.fixture
def sample_webhook_data():
    """Sample webhook data for testing."""
    return {
        'payment_intent.succeeded': {
            'id': 'evt_test_123',
            'type': 'payment_intent.succeeded',
            'data': {
                'object': {
                    'id': 'pi_test_123',
                    'status': 'succeeded',
                    'amount': 5000,
                    'currency': 'usd',
                    'customer': 'cus_test_123',
                    'metadata': {
                        'user_id': '123',
                        'service_id': '456'
                    }
                }
            }
        },
        'customer.subscription.created': {
            'id': 'evt_test_124',
            'type': 'customer.subscription.created',
            'data': {
                'object': {
                    'id': 'sub_test_123',
                    'status': 'active',
                    'customer': 'cus_test_123',
                    'current_period_start': timezone.now().timestamp(),
                    'current_period_end': (timezone.now() + timedelta(days=30)).timestamp(),
                    'items': {
                        'data': [{
                            'price': {
                                'id': 'price_test_123',
                                'product': 'prod_test_123'
                            }
                        }]
                    }
                }
            }
        }
    }


@pytest.fixture
def api_client():
    """DRF API client for testing."""
    from rest_framework.test import APIClient
    return APIClient()


@pytest.fixture
def authenticated_api_client(api_client, user):
    """Authenticated API client."""
    api_client.force_authenticate(user=user)
    return api_client


@pytest.fixture
def transactional_db(db):
    """Fixture for tests that need transaction support."""
    return db


@pytest.fixture
def freeze_time():
    """Freeze time for consistent testing."""
    with patch('django.utils.timezone.now') as mock_now:
        fixed_time = timezone.datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = fixed_time
        yield fixed_time


@pytest.fixture
def mock_email():
    """Mock email sending."""
    with patch('django.core.mail.send_mail') as mock_send:
        mock_send.return_value = True
        yield mock_send


@pytest.fixture
def performance_timer():
    """Timer for performance testing."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


# Pytest markers for different test types
pytest_plugins = []

def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as an end-to-end test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


# Custom assertions for billing tests
class BillingAssertions:
    """Custom assertions for billing-specific testing."""
    
    @staticmethod
    def assert_payment_completed(payment):
        """Assert that a payment is completed properly."""
        assert payment.status == 'completed'
        assert payment.stripe_payment_intent_id is not None
        assert payment.amount > 0
    
    @staticmethod
    def assert_access_granted(access):
        """Assert that access is granted properly."""
        assert access.status == 'active'
        assert access.access_granted_at is not None
    
    @staticmethod
    def assert_subscription_active(subscription):
        """Assert that subscription is active."""
        assert subscription.status == 'active'
        assert subscription.current_period_end > timezone.now()


@pytest.fixture
def billing_assertions():
    """Provide billing-specific assertions."""
    return BillingAssertions()
