"""
Subscription factories for testing.

Creates test subscription and subscription access records.
"""
import factory
from factory.django import DjangoModelFactory
from django.utils import timezone
from datetime import timedelta

from billing.models import Subscription, SubscriptionAccess
from .user_factory import UserFactory
from .customer_factory import CustomerFactory


class SubscriptionFactory(DjangoModelFactory):
    """Factory for creating test subscription records."""
    
    class Meta:
        model = Subscription
        django_get_or_create = ('stripe_subscription_id',)
    
    customer = factory.SubFactory(CustomerFactory)
    stripe_subscription_id = factory.Sequence(lambda n: f'sub_test_{n}')
    # price will be set by the test
    status = 'active'
    current_period_start = factory.LazyFunction(timezone.now)
    current_period_end = factory.LazyFunction(
        lambda: timezone.now() + timedelta(days=30)
    )
    cancel_at_period_end = False
    canceled_at = None
    trial_end = None
    
    metadata = factory.Dict({
        'test_subscription': True,
        'created_by': 'test_factory',
    })
    
    @classmethod
    def create_trial(cls, **kwargs):
        """Create trial subscription."""
        kwargs.update({
            'status': 'trialing',
            'trial_end': timezone.now() + timedelta(days=14)
        })
        return cls(**kwargs)
    
    @classmethod
    def create_canceled(cls, **kwargs):
        """Create canceled subscription."""
        kwargs.update({
            'status': 'canceled',
            'cancel_at_period_end': True,
            'canceled_at': timezone.now()
        })
        return cls(**kwargs)


class SubscriptionAccessFactory(DjangoModelFactory):
    """Factory for creating test subscription access records."""
    
    class Meta:
        model = SubscriptionAccess
        django_get_or_create = ('user', 'subscription')
    
    user = factory.SubFactory(UserFactory)
    subscription = factory.SubFactory(SubscriptionFactory)
    status = 'active'
    access_granted_at = factory.LazyFunction(timezone.now)
    
    metadata = factory.Dict({
        'access_type': 'subscription',
        'created_by': 'test_factory',
    })
    
    @classmethod
    def create_expired(cls, **kwargs):
        """Create expired subscription access."""
        kwargs.update({
            'status': 'expired',
        })
        return cls(**kwargs)
    
    @classmethod
    def create_past_due(cls, **kwargs):
        """Create past due subscription access."""
        kwargs.update({
            'status': 'past_due',
        })
        return cls(**kwargs) 