"""
Customer and payment profile factories for testing.

Creates test customers and payment profiles with realistic data.
"""
import factory
from factory.django import DjangoModelFactory
from decimal import Decimal

from billing.models import Customer, UserPaymentProfile
from .user_factory import UserFactory


class CustomerFactory(DjangoModelFactory):
    """Factory for creating test customers."""
    
    class Meta:
        model = Customer
        django_get_or_create = ('stripe_customer_id',)
    
    user = factory.SubFactory(UserFactory)
    stripe_customer_id = factory.Sequence(lambda n: f'cus_test_{n:06d}')
    
    @factory.lazy_attribute
    def email(self):
        """Use user's email."""
        return self.user.email
    
    @factory.lazy_attribute
    def name(self):
        """Generate name from user."""
        return f"{self.user.first_name} {self.user.last_name}"
    
    metadata = factory.Dict({
        'created_by': 'test_factory',
        'test_customer': True,
    })
    
    @classmethod
    def create_with_payment_profile(cls, **kwargs):
        """Create customer with associated payment profile."""
        customer = cls(**kwargs)
        UserPaymentProfileFactory(user=customer.user)
        return customer


class UserPaymentProfileFactory(DjangoModelFactory):
    """Factory for creating test user payment profiles."""
    
    class Meta:
        model = UserPaymentProfile
        django_get_or_create = ('user',)
    
    user = factory.SubFactory(UserFactory)
    stripe_account_id = factory.Sequence(lambda n: f'acct_test_{n:06d}')
    charges_enabled = True
    payouts_enabled = True
    details_submitted = True
    accept_donations = True
    donation_message = factory.Faker('text', max_nb_chars=200)
    minimum_donation = 500  # $5.00 in cents
    suggested_donation_amounts = factory.List([
        1000,  # $10.00
        2500,  # $25.00
        5000,  # $50.00
        10000, # $100.00
    ])
    is_verified = True
    verification_date = factory.Faker('date_time_this_year')
    
    @classmethod
    def create_unverified(cls, **kwargs):
        """Create unverified payment profile."""
        kwargs.update({
            'charges_enabled': False,
            'payouts_enabled': False,
            'details_submitted': False,
            'is_verified': False,
            'verification_date': None,
        })
        return cls(**kwargs)
    
    @classmethod
    def create_donation_disabled(cls, **kwargs):
        """Create profile with donations disabled."""
        kwargs.update({
            'accept_donations': False,
            'donation_message': '',
            'suggested_donation_amounts': [],
        })
        return cls(**kwargs)
