"""
Service factory for testing.

Creates test services from content_management app for billing tests.
"""
import factory
from factory.django import DjangoModelFactory

# Import from content_management
from content_management.models import Service


class ServiceFactory(DjangoModelFactory):
    """Factory for creating test services."""
    
    class Meta:
        model = Service
        django_get_or_create = ('name',)
    
    name = factory.Sequence(lambda n: f'Test Service {n}')
    description = factory.Faker('text', max_nb_chars=500)
    service_type = 'analysis'
    active = True
    
    # Service-specific metadata
    metadata = factory.Dict({
        'access_duration_days': 30,
        'max_usage_count': 100,
        'analysis_permissions': {
            'can_upload_files': True,
            'can_view_results': True,
            'can_download_results': True,
            'max_file_size_mb': 100,
            'max_files_per_analysis': 10,
            'analysis_types_allowed': ['basic', 'standard', 'premium']
        },
        'created_by': 'test_factory'
    })
    
    @classmethod
    def create_premium_service(cls, **kwargs):
        """Create a premium service with extended features."""
        kwargs.update({
            'name': f'Premium {kwargs.get("name", "Service")}',
            'metadata': {
                'access_duration_days': 90,
                'max_usage_count': 500,
                'analysis_permissions': {
                    'can_upload_files': True,
                    'can_view_results': True,
                    'can_download_results': True,
                    'can_export_data': True,
                    'max_file_size_mb': 500,
                    'max_files_per_analysis': 50,
                    'analysis_types_allowed': ['basic', 'standard', 'premium', 'enterprise']
                },
                'premium_features': True,
                'created_by': 'test_factory'
            }
        })
        return cls(**kwargs)
    
    @classmethod
    def create_basic_service(cls, **kwargs):
        """Create a basic service with limited features."""
        kwargs.update({
            'name': f'Basic {kwargs.get("name", "Service")}',
            'metadata': {
                'access_duration_days': 7,
                'max_usage_count': 10,
                'analysis_permissions': {
                    'can_upload_files': True,
                    'can_view_results': True,
                    'can_download_results': False,
                    'max_file_size_mb': 10,
                    'max_files_per_analysis': 1,
                    'analysis_types_allowed': ['basic']
                },
                'basic_tier': True,
                'created_by': 'test_factory'
            }
        })
        return cls(**kwargs)
    
    @classmethod
    def create_subscription_service(cls, **kwargs):
        """Create a service for subscription-based access."""
        kwargs.update({
            'service_type': 'subscription',
            'metadata': {
                'subscription_based': True,
                'billing_interval': 'month',
                'trial_period_days': 14,
                'created_by': 'test_factory'
            }
        })
        return cls(**kwargs)
