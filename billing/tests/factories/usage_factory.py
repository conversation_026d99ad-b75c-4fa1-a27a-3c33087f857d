"""
Usage and fee factories for testing.

Creates test usage records and platform fees.
"""
import factory
from factory.django import DjangoModelFactory
from django.utils import timezone
from decimal import Decimal

# Import from billing models
try:
    from billing.models import PlatformFee
    HAS_PLATFORM_FEE = True
except ImportError:
    PlatformFee = None
    HAS_PLATFORM_FEE = False

try:
    from billing.models import SolutionUsage
    HAS_SOLUTION_USAGE = True
except ImportError:
    SolutionUsage = None
    HAS_SOLUTION_USAGE = False

try:
    from billing.models import SolutionUsageLimit
    HAS_SOLUTION_USAGE_LIMIT = True
except ImportError:
    SolutionUsageLimit = None
    HAS_SOLUTION_USAGE_LIMIT = False

from .user_factory import UserFactory


if HAS_PLATFORM_FEE:
    class PlatformFeeFactory(DjangoModelFactory):
        """Factory for creating test platform fee records."""
        
        class Meta:
            model = PlatformFee
        
        fee_percentage = factory.Faker('pydecimal', left_digits=2, right_digits=4, positive=True, min_value=Decimal('0.01'), max_value=Decimal('0.05'))
        fixed_fee_amount = factory.Faker('random_int', min=0, max=100)  # $0-$1.00
        currency = 'usd'
        active = True
        
        metadata = factory.Dict({
            'fee_type': 'standard',
            'created_by': 'test_factory',
        })
        
        @classmethod
        def create_enterprise_fee(cls, **kwargs):
            """Create enterprise platform fee."""
            kwargs.update({
                'fee_percentage': Decimal('0.02'),  # 2%
                'fixed_fee_amount': 0,
                'metadata': {
                    'fee_type': 'enterprise',
                    'created_by': 'test_factory',
                }
            })
            return cls(**kwargs)
else:
    # Create dummy factory if model doesn't exist
    PlatformFeeFactory = None


if HAS_SOLUTION_USAGE:
    class SolutionUsageFactory(DjangoModelFactory):
        """Factory for creating test solution usage records."""
        
        class Meta:
            model = SolutionUsage
        
        user = factory.SubFactory(UserFactory)
        # solution_access will be set by test
        usage_count = factory.Faker('random_int', min=1, max=100)
        usage_date = factory.LazyFunction(timezone.now)
        
        metadata = factory.Dict({
            'usage_type': 'api_call',
            'created_by': 'test_factory',
        })
        
        @classmethod
        def create_heavy_usage(cls, **kwargs):
            """Create heavy usage record."""
            kwargs.update({
                'usage_count': factory.Faker('random_int', min=500, max=1000),
                'metadata': {
                    'usage_type': 'heavy_usage',
                    'created_by': 'test_factory',
                }
            })
            return cls(**kwargs)
else:
    # Create dummy factory if model doesn't exist
    SolutionUsageFactory = None


if HAS_SOLUTION_USAGE_LIMIT:
    class SolutionUsageLimitFactory(DjangoModelFactory):
        """Factory for creating test solution usage limit records."""
        
        class Meta:
            model = SolutionUsageLimit
        
        # solution will be set by test
        limit_type = 'monthly'
        limit_value = factory.Faker('random_int', min=100, max=1000)
        current_usage = factory.Faker('random_int', min=0, max=50)
        reset_date = factory.LazyFunction(lambda: timezone.now().replace(day=1))
        
        metadata = factory.Dict({
            'limit_type': 'api_calls',
            'created_by': 'test_factory',
        })
        
        @classmethod
        def create_unlimited(cls, **kwargs):
            """Create unlimited usage limit."""
            kwargs.update({
                'limit_value': -1,  # -1 indicates unlimited
                'metadata': {
                    'limit_type': 'unlimited',
                    'created_by': 'test_factory',
                }
            })
            return cls(**kwargs)
else:
    # Create dummy factory if model doesn't exist
    SolutionUsageLimitFactory = None 