"""
Test data factories for billing tests.

This module provides factory classes for creating test data consistently
across all billing tests. Factories use factory_boy library and are
optimized for Docker testing environment.
"""

from .user_factory import UserFactory
from .customer_factory import CustomerFactory, UserPaymentProfileFactory
from .product_factory import ProductFactory, PriceFactory
from .payment_factory import ServicePaymentFactory, UserTransferFactory
from .subscription_factory import SubscriptionFactory, SubscriptionAccessFactory
from .access_factory import ServiceAccessFactory, SolutionAccessFactory, SolutionUserAccessFactory
# Enterprise factory removed - EnterpriseService no longer exists
from .usage_factory import PlatformFeeFactory, SolutionUsageFactory, SolutionUsageLimitFactory
from .service_factory import ServiceFactory
from .stripe_factory import StripeObjectFactory

# Create missing factories as needed
try:
    from .product_factory import ProductFactory, PriceFactory
except ImportError:
    # Create basic factories if models don't exist yet
    ProductFactory = None
    PriceFactory = None

try:
    from .subscription_factory import SubscriptionFactory, SubscriptionAccessFactory
except ImportError:
    SubscriptionFactory = None
    SubscriptionAccessFactory = None

try:
    from .access_factory import ServiceAccessFactory, SolutionAccessFactory, SolutionUserAccessFactory
except ImportError:
    ServiceAccessFactory = None
    SolutionAccessFactory = None
    SolutionUserAccessFactory = None

# Enterprise factories removed - EnterpriseService no longer exists
EnterpriseAccountFactory = None
EnterpriseServiceFactory = None

try:
    from .usage_factory import PlatformFeeFactory, SolutionUsageFactory, SolutionUsageLimitFactory
except ImportError:
    PlatformFeeFactory = None
    SolutionUsageFactory = None
    SolutionUsageLimitFactory = None

try:
    from .stripe_factory import StripeObjectFactory
except ImportError:
    StripeObjectFactory = None

__all__ = [
    # User factories
    'UserFactory',
    
    # Customer factories
    'CustomerFactory',
    'UserPaymentProfileFactory',
    
    # Product factories
    'ProductFactory',
    'PriceFactory',
    
    # Payment factories
    'ServicePaymentFactory',

    'UserTransferFactory',
    
    # Subscription factories
    'SubscriptionFactory',
    'SubscriptionAccessFactory',
    
    # Access factories
    'ServiceAccessFactory',
    'SolutionAccessFactory',
    'SolutionUserAccessFactory',
    
    # Enterprise factories (removed - EnterpriseService no longer exists)
    # 'EnterpriseAccountFactory',
    # 'EnterpriseServiceFactory',
    
    # Usage factories
    'PlatformFeeFactory',
    'SolutionUsageFactory',
    'SolutionUsageLimitFactory',
    
    # Service factories
    'ServiceFactory',
    
    # Stripe factories
    'StripeObjectFactory',
]
