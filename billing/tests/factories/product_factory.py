"""
Product and Price factories for testing.

Creates test products and prices with realistic data.
"""
import factory
from factory.django import DjangoModelFactory

from billing.models import Product, Price
from .service_factory import ServiceFactory


class ProductFactory(DjangoModelFactory):
    """Factory for creating test products."""
    
    class Meta:
        model = Product
        django_get_or_create = ('stripe_product_id',)
    
    service = factory.SubFactory(ServiceFactory)
    stripe_product_id = factory.Sequence(lambda n: f'prod_test_{n:06d}')
    name = factory.LazyAttribute(lambda obj: f'Product for {obj.service.name}')
    description = factory.Faker('text', max_nb_chars=200)
    active = True
    
    metadata = factory.Dict({
        'product_type': 'service_access',
        'created_by': 'test_factory',
    })
    
    @classmethod
    def create_premium_product(cls, **kwargs):
        """Create a premium product."""
        kwargs.update({
            'name': f'Premium {kwargs.get("name", "Product")}',
            'metadata': {
                'product_type': 'premium_service_access',
                'premium_features': True,
                'created_by': 'test_factory',
            }
        })
        return cls(**kwargs)


class PriceFactory(DjangoModelFactory):
    """Factory for creating test prices."""
    
    class Meta:
        model = Price
        django_get_or_create = ('stripe_price_id',)
    
    product = factory.SubFactory(ProductFactory)
    stripe_price_id = factory.Sequence(lambda n: f'price_test_{n:06d}')
    unit_amount = factory.Faker('random_int', min=1000, max=10000)  # $10-$100
    currency = 'usd'
    recurring = None  # One-time payment by default
    active = True
    
    metadata = factory.Dict({
        'price_type': 'one_time',
        'created_by': 'test_factory',
    })
    
    @classmethod
    def create_subscription_price(cls, **kwargs):
        """Create a subscription price."""
        kwargs.update({
            'recurring': {
                'interval': 'month',
                'interval_count': 1
            },
            'metadata': {
                'price_type': 'subscription',
                'billing_interval': 'monthly',
                'created_by': 'test_factory',
            }
        })
        return cls(**kwargs)
    
    @classmethod
    def create_annual_price(cls, **kwargs):
        """Create an annual subscription price."""
        kwargs.update({
            'recurring': {
                'interval': 'year',
                'interval_count': 1
            },
            'unit_amount': factory.Faker('random_int', min=10000, max=100000),  # $100-$1000
            'metadata': {
                'price_type': 'subscription',
                'billing_interval': 'yearly',
                'discount_applied': True,
                'created_by': 'test_factory',
            }
        })
        return cls(**kwargs)
