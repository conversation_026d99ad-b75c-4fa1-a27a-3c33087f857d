"""
User factory for testing.

Creates test users with realistic data for billing tests.
"""
import factory
from django.contrib.auth import get_user_model
from factory.django import DjangoModelFactory

User = get_user_model()


class UserFactory(DjangoModelFactory):
    """Factory for creating test users."""
    
    class Meta:
        model = User
        django_get_or_create = ('email',)
    
    email = factory.Sequence(lambda n: f'testuser{n}@example.com')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    is_active = True
    is_staff = False
    is_superuser = False
    
    @factory.lazy_attribute
    def username(self):
        """Generate username from email."""
        return self.email.split('@')[0]
    
    @classmethod
    def create_superuser(cls, **kwargs):
        """Create a superuser."""
        kwargs.update({
            'is_staff': True,
            'is_superuser': True,
        })
        return cls(**kwargs)
    
    @classmethod
    def create_staff_user(cls, **kwargs):
        """Create a staff user."""
        kwargs.update({
            'is_staff': True,
        })
        return cls(**kwargs)
