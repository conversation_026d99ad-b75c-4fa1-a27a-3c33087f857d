"""
Stripe object factories for testing.

Creates mock Stripe objects for testing without hitting the Stripe API.
"""
import factory
from unittest.mock import Mock
from django.utils import timezone
from datetime import timedelta


class StripeObjectFactory(factory.Factory):
    """Factory for creating mock Stripe objects."""
    
    class Meta:
        model = Mock
    
    @classmethod
    def create_customer(cls, **kwargs):
        """Create a mock Stripe customer."""
        defaults = {
            'id': factory.Sequence(lambda n: f'cus_test_{n:06d}'),
            'email': factory.Faker('email'),
            'name': factory.Faker('name'),
            'created': int(timezone.now().timestamp()),
            'metadata': {},
        }
        defaults.update(kwargs)
        
        customer = Mock()
        for key, value in defaults.items():
            setattr(customer, key, callable(value) and value() or value)
        
        return customer
    
    @classmethod
    def create_product(cls, **kwargs):
        """Create a mock Stripe product."""
        defaults = {
            'id': factory.Sequence(lambda n: f'prod_test_{n:06d}'),
            'name': factory.Faker('company'),
            'description': factory.Faker('text', max_nb_chars=200),
            'active': True,
            'created': int(timezone.now().timestamp()),
            'metadata': {},
        }
        defaults.update(kwargs)
        
        product = Mock()
        for key, value in defaults.items():
            setattr(product, key, callable(value) and value() or value)
        
        return product
    
    @classmethod
    def create_price(cls, **kwargs):
        """Create a mock Stripe price."""
        defaults = {
            'id': factory.Sequence(lambda n: f'price_test_{n:06d}'),
            'product': factory.Sequence(lambda n: f'prod_test_{n:06d}'),
            'unit_amount': factory.Faker('random_int', min=1000, max=10000),
            'currency': 'usd',
            'active': True,
            'created': int(timezone.now().timestamp()),
            'recurring': None,
            'metadata': {},
        }
        defaults.update(kwargs)
        
        price = Mock()
        for key, value in defaults.items():
            setattr(price, key, callable(value) and value() or value)
        
        return price
    
    @classmethod
    def create_subscription_price(cls, **kwargs):
        """Create a mock Stripe subscription price."""
        kwargs.update({
            'recurring': {
                'interval': 'month',
                'interval_count': 1
            }
        })
        return cls.create_price(**kwargs)
    
    @classmethod
    def create_payment_intent(cls, **kwargs):
        """Create a mock Stripe payment intent."""
        defaults = {
            'id': factory.Sequence(lambda n: f'pi_test_{n:06d}'),
            'client_secret': factory.LazyAttribute(lambda obj: f'{obj.id}_secret_test'),
            'amount': factory.Faker('random_int', min=1000, max=10000),
            'currency': 'usd',
            'status': 'requires_payment_method',
            'created': int(timezone.now().timestamp()),
            'metadata': {},
        }
        defaults.update(kwargs)
        
        payment_intent = Mock()
        for key, value in defaults.items():
            setattr(payment_intent, key, callable(value) and value() or value)
        
        return payment_intent
    
    @classmethod
    def create_subscription(cls, **kwargs):
        """Create a mock Stripe subscription."""
        now = timezone.now()
        defaults = {
            'id': factory.Sequence(lambda n: f'sub_test_{n:06d}'),
            'customer': factory.Sequence(lambda n: f'cus_test_{n:06d}'),
            'status': 'active',
            'current_period_start': int(now.timestamp()),
            'current_period_end': int((now + timedelta(days=30)).timestamp()),
            'created': int(now.timestamp()),
            'trial_end': None,
            'cancel_at_period_end': False,
            'canceled_at': None,
            'metadata': {},
        }
        defaults.update(kwargs)
        
        subscription = Mock()
        for key, value in defaults.items():
            setattr(subscription, key, callable(value) and value() or value)
        
        return subscription
    
    @classmethod
    def create_checkout_session(cls, **kwargs):
        """Create a mock Stripe checkout session."""
        defaults = {
            'id': factory.Sequence(lambda n: f'cs_test_{n:06d}'),
            'url': factory.LazyAttribute(lambda obj: f'https://checkout.stripe.com/{obj.id}'),
            'payment_intent': factory.Sequence(lambda n: f'pi_test_{n:06d}'),
            'customer': factory.Sequence(lambda n: f'cus_test_{n:06d}'),
            'amount_total': factory.Faker('random_int', min=1000, max=10000),
            'currency': 'usd',
            'status': 'complete',
            'created': int(timezone.now().timestamp()),
            'metadata': {},
        }
        defaults.update(kwargs)
        
        session = Mock()
        for key, value in defaults.items():
            setattr(session, key, callable(value) and value() or value)
        
        return session
    
    @classmethod
    def create_webhook_event(cls, event_type='payment_intent.succeeded', **kwargs):
        """Create a mock Stripe webhook event."""
        defaults = {
            'id': factory.Sequence(lambda n: f'evt_test_{n:06d}'),
            'type': event_type,
            'created': int(timezone.now().timestamp()),
            'data': {
                'object': cls.create_payment_intent()
            },
            'request': {
                'id': factory.Sequence(lambda n: f'req_test_{n:06d}'),
                'idempotency_key': None
            },
            'pending_webhooks': 1,
            'api_version': '2020-08-27'
        }
        defaults.update(kwargs)
        
        event = Mock()
        for key, value in defaults.items():
            setattr(event, key, value)
        
        return event 