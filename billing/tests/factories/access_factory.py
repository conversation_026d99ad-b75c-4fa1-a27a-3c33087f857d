"""
Access factories for testing.

Creates test access records for services, subscriptions, and solutions.
"""
import factory
from factory.django import DjangoModelFactory
from django.utils import timezone
from datetime import timedelta

from billing.models import ServiceAccess, SubscriptionAccess, SolutionAccess, SolutionUserAccess
from .user_factory import UserFactory
from .service_factory import ServiceFactory


class ServiceAccessFactory(DjangoModelFactory):
    """Factory for creating test service access records."""
    
    class Meta:
        model = ServiceAccess
        django_get_or_create = ('user', 'service')
    
    user = factory.SubFactory(UserFactory)
    service = factory.SubFactory(ServiceFactory)
    status = 'active'
    access_granted_at = factory.LazyFunction(timezone.now)
    access_expires_at = factory.LazyFunction(
        lambda: timezone.now() + timedelta(days=30)
    )
    
    metadata = factory.Dict({
        'access_type': 'purchased',
        'created_by': 'test_factory',
    })
    
    @classmethod
    def create_expired(cls, **kwargs):
        """Create expired access."""
        kwargs.update({
            'status': 'expired',
            'access_expires_at': timezone.now() - timedelta(days=1)
        })
        return cls(**kwargs)
    
    @classmethod
    def create_unlimited(cls, **kwargs):
        """Create unlimited access (no expiration)."""
        kwargs.update({
            'access_expires_at': None,
            'metadata': {
                'access_type': 'unlimited',
                'created_by': 'test_factory',
            }
        })
        return cls(**kwargs)


class SubscriptionAccessFactory(DjangoModelFactory):
    """Factory for creating test subscription access records."""
    
    class Meta:
        model = SubscriptionAccess
        django_get_or_create = ('user', 'subscription')
    
    user = factory.SubFactory(UserFactory)
    # subscription will be set by the test
    status = 'active'
    access_granted_at = factory.LazyFunction(timezone.now)
    
    metadata = factory.Dict({
        'access_type': 'subscription',
        'created_by': 'test_factory',
    })


class SolutionAccessFactory(DjangoModelFactory):
    """Factory for creating test solution access records."""
    
    class Meta:
        model = SolutionAccess
        django_get_or_create = ('enterprise', 'solution')
    
    # enterprise and solution will be set by the test
    status = 'active'
    total_seats = 20
    used_seats = 1
    access_granted_at = factory.LazyFunction(timezone.now)
    
    metadata = factory.Dict({
        'access_type': 'enterprise_solution',
        'created_by': 'test_factory',
    })
    
    @property
    def available_seats(self):
        """Calculate available seats."""
        return self.total_seats - self.used_seats


class SolutionUserAccessFactory(DjangoModelFactory):
    """Factory for creating test solution user access records."""
    
    class Meta:
        model = SolutionUserAccess
        django_get_or_create = ('solution_access', 'user')
    
    solution_access = factory.SubFactory(SolutionAccessFactory)
    user = factory.SubFactory(UserFactory)
    status = 'active'
    role = 'member'
    access_granted_at = factory.LazyFunction(timezone.now)
    
    metadata = factory.Dict({
        'access_type': 'solution_user',
        'created_by': 'test_factory',
    })
    
    @classmethod
    def create_admin(cls, **kwargs):
        """Create admin user access."""
        kwargs.update({'role': 'admin'})
        return cls(**kwargs)
    
    @classmethod
    def create_owner(cls, **kwargs):
        """Create owner user access."""
        kwargs.update({'role': 'owner'})
        return cls(**kwargs)
