"""
Payment factories for testing.

Creates test payments with realistic data for different payment types.
"""
import factory
from factory.django import DjangoModelFactory
from decimal import Decimal

from billing.models import ServicePayment, UserTransfer
from .user_factory import UserFactory
from .customer_factory import CustomerFactory
from .service_factory import ServiceFactory


class ServicePaymentFactory(DjangoModelFactory):
    """Factory for creating test service payments."""
    
    class Meta:
        model = ServicePayment
        django_get_or_create = ('stripe_payment_intent_id',)
    
    user = factory.SubFactory(UserFactory)
    service = factory.SubFactory(ServiceFactory)
    amount = factory.Faker('random_int', min=1000, max=50000)  # $10-$500
    currency = 'usd'
    status = 'completed'
    stripe_payment_intent_id = factory.Sequence(lambda n: f'pi_test_{n:010d}')
    
    metadata = factory.Dict({
        'payment_method': 'card',
        'created_by': 'test_factory',
    })
    
    @classmethod
    def create_pending(cls, **kwargs):
        """Create a pending payment."""
        kwargs.update({'status': 'pending'})
        return cls(**kwargs)
    
    @classmethod
    def create_failed(cls, **kwargs):
        """Create a failed payment."""
        kwargs.update({
            'status': 'failed',
            'metadata': {
                'failure_reason': 'card_declined',
                'created_by': 'test_factory',
            }
        })
        return cls(**kwargs)
    
    @classmethod
    def create_refunded(cls, **kwargs):
        """Create a refunded payment."""
        kwargs.update({
            'status': 'refunded',
            'metadata': {
                'refund_reason': 'requested_by_customer',
                'refunded_at': factory.Faker('date_time_this_year'),
                'created_by': 'test_factory',
            }
        })
        return cls(**kwargs)
    
    @classmethod
    def create_bulk_payment(cls, services=None, **kwargs):
        """Create a payment for multiple services."""
        if services is None:
            services = [ServiceFactory().id for _ in range(3)]
        
        kwargs.update({
            'services': [str(service_id) for service_id in services],
            'amount': len(services) * 2500,  # $25 per service
            'metadata': {
                'bulk_payment': True,
                'service_count': len(services),
                'created_by': 'test_factory',
            }
        })
        return cls(**kwargs)





class UserTransferFactory(DjangoModelFactory):
    """Factory for creating test user transfers."""
    
    class Meta:
        model = UserTransfer
        django_get_or_create = ('stripe_payment_intent_id',)
    
    sender = factory.SubFactory(UserFactory)
    receiver = factory.SubFactory(UserFactory)
    amount = factory.Faker('random_int', min=500, max=10000)  # $5-$100
    currency = 'usd'
    transfer_type = 'donation'
    platform_fee_amount = factory.LazyAttribute(lambda obj: int(obj.amount * 0.029) + 30)  # 2.9% + $0.30
    status = 'completed'
    stripe_payment_intent_id = factory.Sequence(lambda n: f'pi_transfer_{n:010d}')
    message = factory.Faker('text', max_nb_chars=200)
    
    metadata = factory.Dict({
        'transfer_type': 'donation',
        'created_by': 'test_factory',
    })
    
    @classmethod
    def create_payment_transfer(cls, **kwargs):
        """Create a payment transfer (not donation)."""
        kwargs.update({
            'transfer_type': 'payment',
            'message': 'Payment for services rendered',
            'metadata': {
                'transfer_type': 'payment',
                'created_by': 'test_factory',
            }
        })
        return cls(**kwargs)
    
    @classmethod
    def create_large_donation(cls, **kwargs):
        """Create a large donation."""
        kwargs.update({
            'amount': factory.Faker('random_int', min=50000, max=100000),  # $500-$1000
            'message': 'Thank you for your amazing work!',
            'metadata': {
                'transfer_type': 'donation',
                'large_donation': True,
                'created_by': 'test_factory',
            }
        })
        return cls(**kwargs)
