"""
Test cases for pending transfers fix functionality
"""
import pytest
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status

from accounts.models import CustomUser
from billing.models import UserTransfer, UserPaymentProfile, PlatformFee
from billing.services.user_transfer_service import UserTransferService
from roles.models import Role


class PendingTransfersFixTestCase(TestCase):
    """Test cases for pending transfers fix functionality"""

    def setUp(self):
        """Set up test data"""
        # Create roles
        self.patient_role, _ = Role.objects.get_or_create(name='patient')
        self.doctor_role, _ = Role.objects.get_or_create(name='doctor')
        
        # Create users
        self.user1 = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=self.patient_role
        )
        
        self.user2 = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=self.doctor_role
        )
        
        # Create payment profiles
        self.profile1 = UserPaymentProfile.objects.create(
            user=self.user1,
            stripe_account_id='acct_test_user1',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True
        )
        
        self.profile2 = UserPaymentProfile.objects.create(
            user=self.user2,
            stripe_account_id='acct_test_user2',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True
        )
        
        # Create platform fee
        self.platform_fee = PlatformFee.objects.create(
            percentage=2.5,
            fixed_amount=30,
            active=True
        )
        
        self.client = APIClient()
        self.transfer_service = UserTransferService()

    def create_pending_transfer(self, sender, receiver, amount=1000):
        """Helper to create pending transfer"""
        return UserTransfer.objects.create(
            sender=sender,
            receiver=receiver,
            amount=amount,
            currency='usd',
            transfer_type='payment',
            platform_fee=self.platform_fee,
            platform_fee_amount=30,
            status='pending',
            stripe_payment_intent_id=f'pi_test_pending_{timezone.now().timestamp()}'
        )

    def test_get_user_pending_transfers_info(self):
        """Test getting pending transfers info"""
        # Create pending transfers
        self.create_pending_transfer(self.user2, self.user1, 1500)  # user1 as receiver
        self.create_pending_transfer(self.user1, self.user2, 2000)  # user1 as sender
        
        # Get pending info
        pending_info = self.transfer_service.get_user_pending_transfers_info(self.user1)
        
        # Verify structure
        self.assertIn('pending_as_sender', pending_info)
        self.assertIn('pending_as_receiver', pending_info)
        
        # Verify counts
        self.assertEqual(pending_info['pending_as_sender']['count'], 1)
        self.assertEqual(pending_info['pending_as_receiver']['count'], 1)
        
        # Verify amounts
        self.assertEqual(pending_info['pending_as_sender']['total_amount'], 2000)
        self.assertEqual(pending_info['pending_as_receiver']['total_amount'], 1500)

    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.cancel')
    def test_cancel_pending_transfer_success(self, mock_cancel):
        """Test successful cancellation of pending transfer"""
        # Mock Stripe response
        mock_payment_intent = MagicMock()
        mock_payment_intent.status = 'canceled'
        mock_cancel.return_value = mock_payment_intent
        
        # Create pending transfer
        transfer = self.create_pending_transfer(self.user1, self.user2)
        
        # Cancel transfer
        success = self.transfer_service.cancel_pending_transfer(transfer)
        
        # Verify success
        self.assertTrue(success)
        
        # Verify transfer status updated
        transfer.refresh_from_db()
        self.assertEqual(transfer.status, 'canceled')
        self.assertEqual(transfer.metadata['cancellation_reason'], 'account_deactivation')

    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.cancel')
    def test_cancel_pending_transfer_stripe_error(self, mock_cancel):
        """Test cancellation when Stripe returns error"""
        # Mock Stripe error
        from stripe.error import InvalidRequestError
        mock_cancel.side_effect = InvalidRequestError(
            'This PaymentIntent cannot be canceled', 
            param='payment_intent'
        )
        
        # Create pending transfer
        transfer = self.create_pending_transfer(self.user1, self.user2)
        
        # Try to cancel
        success = self.transfer_service.cancel_pending_transfer(transfer)
        
        # Verify handling
        self.assertFalse(success)
        
        # Verify transfer marked as failed
        transfer.refresh_from_db()
        self.assertEqual(transfer.status, 'failed')
        self.assertEqual(transfer.metadata['cancellation_reason'], 'cannot_cancel_stripe')

    def test_cancel_pending_transfer_invalid_status(self):
        """Test cancellation of transfer that's not in pending status"""
        # Create completed transfer
        transfer = self.create_pending_transfer(self.user1, self.user2)
        transfer.status = 'completed'
        transfer.save()
        
        # Try to cancel
        success = self.transfer_service.cancel_pending_transfer(transfer)
        
        # Verify it's not cancelled
        self.assertFalse(success)
        
        # Verify status unchanged
        transfer.refresh_from_db()
        self.assertEqual(transfer.status, 'completed')

    @patch('billing.services.user_transfer_service.stripe.Account.delete')
    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.cancel')
    def test_deactivate_with_pending_transfers(self, mock_cancel, mock_delete):
        """Test deactivation with pending transfers"""
        # Mock Stripe responses
        mock_payment_intent = MagicMock()
        mock_payment_intent.status = 'canceled'
        mock_cancel.return_value = mock_payment_intent
        
        # Create pending transfers
        transfer1 = self.create_pending_transfer(self.user2, self.user1, 1000)
        transfer2 = self.create_pending_transfer(self.user2, self.user1, 1500)
        
        # Deactivate account
        result = self.transfer_service.deactivate_connect_account(self.user1)
        
        # Verify success
        self.assertTrue(result['success'])
        self.assertEqual(result['transfers_affected'], 2)
        self.assertEqual(len(result['cancelled_transfers']), 2)
        self.assertEqual(len(result['failed_cancellations']), 0)
        
        # Verify transfers were cancelled
        transfer1.refresh_from_db()
        transfer2.refresh_from_db()
        self.assertEqual(transfer1.status, 'canceled')
        self.assertEqual(transfer2.status, 'canceled')
        
        # Verify profile updated
        self.profile1.refresh_from_db()
        self.assertIsNone(self.profile1.stripe_account_id)
        self.assertFalse(self.profile1.charges_enabled)
        self.assertFalse(self.profile1.accept_donations)


class PendingTransfersAPITestCase(TestCase):
    """Test API endpoints for pending transfers"""

    def setUp(self):
        """Set up test data"""
        # Create role
        self.patient_role, _ = Role.objects.get_or_create(name='patient')
        
        # Create user
        self.user = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=self.patient_role
        )
        
        # Create payment profile
        self.profile = UserPaymentProfile.objects.create(
            user=self.user,
            stripe_account_id='acct_test_123',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True
        )
        
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

    def test_check_pending_transfers_endpoint(self):
        """Test check pending transfers API endpoint"""
        response = self.client.get('/api/billing/payment-profiles/check_pending_transfers/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('total_pending_count', data)
        self.assertIn('can_deactivate_immediately', data)
        self.assertIn('pending_details', data)
        self.assertIn('deactivation_info', data)

    def test_check_pending_transfers_unauthenticated(self):
        """Test endpoint requires authentication"""
        self.client.logout()
        response = self.client.get('/api/billing/payment-profiles/check_pending_transfers/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.cancel')
    def test_cancel_all_pending_transfers_endpoint(self, mock_cancel):
        """Test cancel all pending transfers endpoint"""
        # Mock Stripe response
        mock_payment_intent = MagicMock()
        mock_payment_intent.status = 'canceled'
        mock_cancel.return_value = mock_payment_intent
        
        # Create pending transfer
        UserTransfer.objects.create(
            sender=self.user,
            receiver=self.user,  # Self transfer for simplicity
            amount=1000,
            currency='usd',
            transfer_type='payment',
            platform_fee_amount=30,
            status='pending',
            stripe_payment_intent_id='pi_test_pending'
        )
        
        response = self.client.post('/api/billing/payment-profiles/cancel_all_pending_transfers/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('cancelled_count', data)
        self.assertIn('failed_count', data)
        self.assertIn('next_step', data)

    @patch('billing.services.user_transfer_service.stripe.Account.delete')
    @patch('billing.services.user_transfer_service.stripe.PaymentIntent.cancel')
    def test_deactivate_connect_account_with_pending(self, mock_cancel, mock_delete):
        """Test deactivate endpoint with pending transfers"""
        # Mock Stripe responses
        mock_payment_intent = MagicMock()
        mock_payment_intent.status = 'canceled'
        mock_cancel.return_value = mock_payment_intent
        
        response = self.client.delete('/api/billing/payment-profiles/deactivate_connect_account/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('details', data)
        self.assertIn('summary', data)


@pytest.mark.django_db
class TestPendingTransfersManagementCommand:
    """Test management command functionality"""

    def test_command_check_only(self):
        """Test command with check-only flag"""
        from django.core.management import call_command
        from io import StringIO
        
        out = StringIO()
        call_command('fix_pending_transfers', '--check-only', stdout=out)
        
        output = out.getvalue()
        assert 'Fix Pending Transfers Tool' in output

    def test_command_with_user_id(self):
        """Test command with specific user ID"""
        # Create user
        user = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        from django.core.management import call_command
        from io import StringIO
        
        out = StringIO()
        call_command(
            'fix_pending_transfers', 
            f'--user-id={user.id}', 
            '--check-only', 
            stdout=out
        )
        
        output = out.getvalue()
        assert '<EMAIL>' in output 