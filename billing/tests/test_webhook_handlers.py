"""
Test webhook handlers implementation
"""
import pytest
from unittest.mock import Mock, patch
from django.test import TestCase
from django.contrib.auth import get_user_model

from billing.services.webhook_service import WebhookService
from billing.models.customer import UserPaymentProfile
from billing.constants import WebhookEvents
from billing.config import BillingConfig

User = get_user_model()


class TestWebhookHandlers(TestCase):
    """Test webhook handler implementation"""
    
    def setUp(self):
        """Set up test data"""
        self.webhook_service = WebhookService()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.payment_profile = UserPaymentProfile.objects.create(
            user=self.user,
            stripe_account_id='acct_test123',
            charges_enabled=False,
            payouts_enabled=False,
            details_submitted=False,
            is_verified=False
        )
    
    def test_webhook_handlers_configuration(self):
        """Test that webhook handlers are properly configured"""
        handlers = BillingConfig.get_webhook_handlers()
        
        # Check that Connect event handlers are included
        self.assertIn(WebhookEvents.ACCOUNT_UPDATED, handlers)
        self.assertIn(WebhookEvents.ACCOUNT_DEAUTHORIZED, handlers)
        
        # Check handler method names
        self.assertEqual(handlers[WebhookEvents.ACCOUNT_UPDATED], 'handle_account_updated')
        self.assertEqual(handlers[WebhookEvents.ACCOUNT_DEAUTHORIZED], 'handle_account_deauthorized')
    
    def test_handle_account_updated_method_exists(self):
        """Test that handle_account_updated method exists"""
        self.assertTrue(hasattr(self.webhook_service, 'handle_account_updated'))
        self.assertTrue(callable(getattr(self.webhook_service, 'handle_account_updated')))
    
    def test_handle_account_deauthorized_method_exists(self):
        """Test that handle_account_deauthorized method exists"""
        self.assertTrue(hasattr(self.webhook_service, 'handle_account_deauthorized'))
        self.assertTrue(callable(getattr(self.webhook_service, 'handle_account_deauthorized')))
    
    def test_handle_account_updated_success(self):
        """Test successful account.updated event handling"""
        # Mock Stripe account object
        mock_account = Mock()
        mock_account.id = 'acct_test123'
        mock_account.charges_enabled = True
        mock_account.payouts_enabled = True
        mock_account.details_submitted = True
        
        # Call handler
        result = self.webhook_service.handle_account_updated(mock_account)
        
        # Verify result
        self.assertTrue(result)
        
        # Verify profile was updated
        self.payment_profile.refresh_from_db()
        self.assertTrue(self.payment_profile.charges_enabled)
        self.assertTrue(self.payment_profile.payouts_enabled)
        self.assertTrue(self.payment_profile.details_submitted)
        self.assertTrue(self.payment_profile.is_verified)
        self.assertIsNotNone(self.payment_profile.verification_date)
    
    def test_handle_account_updated_profile_not_found(self):
        """Test account.updated with non-existent profile"""
        # Mock Stripe account object with different ID
        mock_account = Mock()
        mock_account.id = 'acct_nonexistent'
        mock_account.charges_enabled = True
        mock_account.payouts_enabled = True
        mock_account.details_submitted = True
        
        # Call handler
        result = self.webhook_service.handle_account_updated(mock_account)
        
        # Should return False for non-existent profile
        self.assertFalse(result)
    
    def test_handle_account_deauthorized_success(self):
        """Test successful account.application.deauthorized event handling"""
        # Set up verified profile
        self.payment_profile.is_verified = True
        self.payment_profile.charges_enabled = True
        self.payment_profile.payouts_enabled = True
        self.payment_profile.save()
        
        # Mock Stripe account object
        mock_account = Mock()
        mock_account.id = 'acct_test123'
        
        # Call handler
        result = self.webhook_service.handle_account_deauthorized(mock_account)
        
        # Verify result
        self.assertTrue(result)
        
        # Verify profile was deactivated
        self.payment_profile.refresh_from_db()
        self.assertFalse(self.payment_profile.is_verified)
        self.assertFalse(self.payment_profile.charges_enabled)
        self.assertFalse(self.payment_profile.payouts_enabled)
        self.assertFalse(self.payment_profile.details_submitted)
        self.assertIsNone(self.payment_profile.verification_date)
    
    @patch('billing.services.webhook_service.logger')
    def test_handle_account_updated_with_consultation_profile(self, mock_logger):
        """Test account.updated with doctor consultation profile"""
        # Create consultation profile
        from appointments.models.consultation_profile import DoctorConsultationProfile
        consultation_profile = DoctorConsultationProfile.objects.create(
            user=self.user,
            consultation_fee=5000,
            stripe_account_setup=False
        )
        
        # Mock Stripe account object
        mock_account = Mock()
        mock_account.id = 'acct_test123'
        mock_account.charges_enabled = True
        mock_account.payouts_enabled = True
        mock_account.details_submitted = True
        
        # Call handler
        result = self.webhook_service.handle_account_updated(mock_account)
        
        # Verify result
        self.assertTrue(result)
        
        # Verify consultation profile was updated
        consultation_profile.refresh_from_db()
        self.assertTrue(consultation_profile.stripe_account_setup)
    
    def test_webhook_constants_defined(self):
        """Test that webhook event constants are properly defined"""
        self.assertEqual(WebhookEvents.ACCOUNT_UPDATED, 'account.updated')
        self.assertEqual(WebhookEvents.ACCOUNT_DEAUTHORIZED, 'account.application.deauthorized')


class TestConnectWebhookView(TestCase):
    """Test StripeConnectWebhookView integration"""
    
    def test_event_routing_uses_unified_handling(self):
        """Test that Connect webhook view uses unified event handling"""
        from billing.views.stripe_connect_webhook_view import StripeConnectWebhookView
        
        view = StripeConnectWebhookView()
        
        # Mock event
        mock_event = Mock()
        mock_event.type = 'account.updated'
        mock_event.data.object = Mock()
        
        # Mock webhook service
        with patch.object(view, 'webhook_service') as mock_webhook_service:
            mock_webhook_service.handle_event.return_value = True
            
            result = view._handle_event(mock_event)
            
            # Verify unified handling was called
            mock_webhook_service.handle_event.assert_called_once_with(mock_event)
            self.assertTrue(result)
