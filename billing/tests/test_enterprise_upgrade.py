#!/usr/bin/env python
"""
Test script to verify the enterprise solution upgrade functionality
"""
import os
import django
import sys

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from enterprise.models import Enterprise
from content_management.models import Solution
from billing.models import Product, Price
from billing.services import StripeService

CustomUser = get_user_model()

def test_stripe_service_methods():
    """Test that the StripeService methods are available"""
    print("🔍 Testing StripeService methods...")
    
    # Check if methods exist
    methods_to_check = [
        'get_solution_stripe_info',
        'get_service_stripe_info', 
        'get_subscription_plan_stripe_info'
    ]
    
    for method_name in methods_to_check:
        if hasattr(StripeService, method_name):
            print(f"✅ {method_name} method exists")
        else:
            print(f"❌ {method_name} method missing")
            return False
    
    return True

def test_solution_exists():
    """Test that the solution ID exists"""
    print("\n🔍 Testing solution existence...")
    
    solution_id = "0196b093-75f1-7007-b90f-3991b2c80fec"
    
    try:
        solution = Solution.objects.get(id=solution_id)
        print(f"✅ Solution found: {solution.name}")
        
        # Check if solution has Stripe product
        product = Product.objects.filter(solution=solution, active=True).first()
        if product:
            print(f"✅ Stripe product found: {product.stripe_product_id}")
            
            # Check if product has prices
            prices = Price.objects.filter(product=product, active=True)
            if prices.exists():
                print(f"✅ {prices.count()} active price(s) found")
                return True
            else:
                print("❌ No active prices found")
                return False
        else:
            print("❌ No Stripe product found for solution")
            return False
            
    except Solution.DoesNotExist:
        print(f"❌ Solution {solution_id} not found")
        return False

def test_enterprise_creation_logic():
    """Test the enterprise creation logic"""
    print("\n🔍 Testing enterprise creation logic...")
    
    # Test the logic that was causing the issue
    try:
        # Create a test user (don't save to DB)
        test_user = CustomUser(
            email="<EMAIL>",
            first_name="Test",
            last_name="User"
        )
        
        # Check if enterprise exists (should be None for new user)
        enterprise = Enterprise.objects.filter(owner=test_user).first()
        
        if enterprise is None:
            print("✅ No enterprise found for new user (expected behavior)")
            print("✅ The upgrade endpoint should now handle this case properly")
            return True
        else:
            print("❌ Unexpected enterprise found")
            return False
            
    except Exception as e:
        print(f"❌ Error in enterprise logic test: {str(e)}")
        return False

def test_get_solution_stripe_info():
    """Test the get_solution_stripe_info method"""
    print("\n🔍 Testing get_solution_stripe_info method...")
    
    solution_id = "0196b093-75f1-7007-b90f-3991b2c80fec"
    
    try:
        solution = Solution.objects.get(id=solution_id)
        stripe_info = StripeService.get_solution_stripe_info(solution)
        
        if stripe_info:
            print("✅ get_solution_stripe_info returned data:")
            print(f"   - Product ID: {stripe_info.get('stripe_product_id')}")
            print(f"   - Name: {stripe_info.get('name')}")
            print(f"   - Prices: {len(stripe_info.get('prices', []))} price(s)")
            return True
        else:
            print("❌ get_solution_stripe_info returned None")
            return False
            
    except Exception as e:
        print(f"❌ Error testing get_solution_stripe_info: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Enterprise Solution Upgrade Tests\n")
    
    tests = [
        test_stripe_service_methods,
        test_solution_exists,
        test_enterprise_creation_logic,
        test_get_solution_stripe_info
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test error: {str(e)}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The enterprise upgrade fix should work.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
