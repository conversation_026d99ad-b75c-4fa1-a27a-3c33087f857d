"""
User payment profile and doctor bank account serializers
"""
from rest_framework import serializers
from ..models import UserPaymentProfile, UserTransfer
from ..constants import TransferType


class UserPaymentProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for UserPaymentProfile model
    """
    class Meta:
        model = UserPaymentProfile
        fields = [
            'id', 'user', 'stripe_account_id', 'charges_enabled', 'payouts_enabled',
            'details_submitted', 'accept_donations', 'donation_message',
            'minimum_donation', 'suggested_donation_amounts', 'is_verified',
            'verification_date', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user', 'stripe_account_id', 'charges_enabled', 'payouts_enabled',
            'details_submitted', 'is_verified', 'verification_date', 'created_at', 'updated_at'
        ]


# Bank Account Serializers for Doctor API
class DoctorBankAccountSerializer(serializers.Serializer):
    """
    Serializer for doctor bank account data
    """
    account_holder_name = serializers.CharField(
        max_length=255,
        required=True,
        help_text="Name of the person or business that owns the bank account"
    )
    account_holder_type = serializers.ChoiceField(
        choices=['individual', 'company'],
        default='individual',
        help_text="Type of entity that holds the account"
    )
    routing_number = serializers.CharField(
        max_length=9,
        min_length=9,
        required=True,
        help_text="9-digit routing number for the bank"
    )
    account_number = serializers.CharField(
        max_length=17,
        min_length=4,
        required=True,
        help_text="Bank account number (4-17 characters)"
    )
    country = serializers.CharField(
        max_length=2,
        default='US',
        help_text="Two-letter country code (e.g., US)"
    )
    currency = serializers.CharField(
        max_length=3,
        default='usd',
        help_text="Three-letter currency code (e.g., usd)"
    )

    def validate_account_holder_name(self, value):
        """Validate account holder name"""
        if not value or not value.strip():
            raise serializers.ValidationError("Account holder name cannot be empty")
        
        # Remove extra whitespace
        value = value.strip()
        
        # Check for minimum length
        if len(value) < 2:
            raise serializers.ValidationError("Account holder name must be at least 2 characters")
        
        return value

    def validate_routing_number(self, value):
        """Validate routing number format"""
        if not value.isdigit():
            raise serializers.ValidationError("Routing number must contain only digits")
        
        if len(value) != 9:
            raise serializers.ValidationError("Routing number must be exactly 9 digits")
        
        return value

    def validate_account_number(self, value):
        """Validate account number"""
        # Remove any spaces or special characters except alphanumeric
        cleaned_value = ''.join(char for char in value if char.isalnum())
        
        if len(cleaned_value) < 4:
            raise serializers.ValidationError("Account number must be at least 4 characters")
        
        if len(cleaned_value) > 17:
            raise serializers.ValidationError("Account number must not exceed 17 characters")
        
        return cleaned_value

    def validate_country(self, value):
        """Validate country code"""
        if value:
            value = value.upper()
            # Basic validation - could be expanded with comprehensive country code list
            if len(value) != 2:
                raise serializers.ValidationError("Country code must be exactly 2 characters")
        
        return value

    def validate_currency(self, value):
        """Validate currency code"""
        if value:
            value = value.lower()
            # Basic validation - could be expanded with comprehensive currency list
            if len(value) != 3:
                raise serializers.ValidationError("Currency code must be exactly 3 characters")
        
        return value


class DoctorBankAccountUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating doctor bank account data
    """
    external_account_id = serializers.CharField(
        required=True,
        help_text="ID of the external account to update"
    )
    account_holder_name = serializers.CharField(
        max_length=255,
        required=False,
        help_text="Name of the person or business that owns the bank account"
    )
    account_holder_type = serializers.ChoiceField(
        choices=['individual', 'company'],
        required=False,
        help_text="Type of entity that holds the account"
    )

    def validate_account_holder_name(self, value):
        """Validate account holder name"""
        if value is not None:
            if not value.strip():
                raise serializers.ValidationError("Account holder name cannot be empty")
            return value.strip()
        return value


class DoctorBankAccountResponseSerializer(serializers.Serializer):
    """
    Serializer for bank account response data
    """
    id = serializers.CharField(read_only=True)
    account_holder_name = serializers.CharField(read_only=True)
    account_holder_type = serializers.CharField(read_only=True)
    bank_name = serializers.CharField(read_only=True)
    country = serializers.CharField(read_only=True)
    currency = serializers.CharField(read_only=True)
    last4 = serializers.CharField(read_only=True)
    routing_number = serializers.CharField(read_only=True)
    status = serializers.CharField(read_only=True)
    is_default = serializers.BooleanField(read_only=True)


class DoctorPaymentStatusSerializer(serializers.Serializer):
    """
    Serializer for doctor payment status response
    """
    stripe_account_id = serializers.CharField(read_only=True, allow_null=True)
    charges_enabled = serializers.BooleanField(read_only=True)
    payouts_enabled = serializers.BooleanField(read_only=True)
    details_submitted = serializers.BooleanField(read_only=True)
    is_verified = serializers.BooleanField(read_only=True)
    can_receive_payments = serializers.BooleanField(read_only=True)
    can_receive_appointment_payments = serializers.BooleanField(read_only=True)
    can_receive_consultation_payments = serializers.BooleanField(read_only=True)
    bank_account_connected = serializers.BooleanField(read_only=True)
    setup_required = serializers.BooleanField(read_only=True)
    message = serializers.CharField(read_only=True, required=False)


class DoctorEarningsSummarySerializer(serializers.Serializer):
    """
    Serializer for doctor earnings summary
    """
    period_days = serializers.IntegerField(read_only=True)
    total_gross = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_fees = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_net = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    payment_count = serializers.IntegerField(read_only=True)
    
    service_breakdown = serializers.DictField(read_only=True)


# Enhanced serializers for Stripe Connect functionality
class DonationSettingsSerializer(serializers.Serializer):
    """Serializer for user donation settings"""
    accept_donations = serializers.BooleanField(required=False, default=False)
    donation_message = serializers.CharField(
        required=False, 
        allow_blank=True, 
        max_length=500,
        help_text="Custom message displayed to donors"
    )
    minimum_donation = serializers.IntegerField(
        required=False, 
        min_value=50,  # $0.50 minimum
        help_text="Minimum donation amount in cents"
    )
    suggested_donation_amounts = serializers.ListField(
        child=serializers.IntegerField(min_value=50),
        required=False,
        allow_empty=True,
        max_length=10,
        help_text="List of suggested donation amounts in cents"
    )
    
    def validate(self, attrs):
        """Validate donation settings"""
        minimum_donation = attrs.get('minimum_donation', 100)
        suggested_amounts = attrs.get('suggested_donation_amounts', [])
        
        # Validate that all suggested amounts are >= minimum
        for amount in suggested_amounts:
            if amount < minimum_donation:
                raise serializers.ValidationError(
                    f"All suggested amounts must be at least {minimum_donation} cents"
                )
        
        return attrs


class CreateTransferSerializer(serializers.Serializer):
    """Serializer for creating user transfers"""
    receiver_id = serializers.UUIDField(help_text="ID of the user receiving the transfer")
    amount = serializers.IntegerField(
        min_value=50,  # $0.50 minimum
        help_text="Transfer amount in cents"
    )
    currency = serializers.CharField(
        default='usd', 
        max_length=3,
        help_text="Currency code (default: usd)"
    )
    message = serializers.CharField(
        required=False, 
        allow_blank=True, 
        max_length=500,
        help_text="Optional message to include with transfer"
    )
    transfer_type = serializers.ChoiceField(
        choices=[
            (TransferType.DONATION, 'Donation'),
            (TransferType.PAYMENT, 'Payment'),
            (TransferType.TRANSFER, 'Transfer'),
        ],
        default=TransferType.TRANSFER,
        help_text="Type of transfer"
    )
    
    def validate_currency(self, value):
        """Validate currency code"""
        if value not in ['usd', 'eur', 'gbp']:
            raise serializers.ValidationError("Unsupported currency")
        return value.lower()


class UserTransferSerializer(serializers.ModelSerializer):
    """Serializer for UserTransfer model with enhanced fields"""
    sender_name = serializers.CharField(source='sender.get_full_name', read_only=True)
    sender_email = serializers.CharField(source='sender.email', read_only=True)
    receiver_name = serializers.CharField(source='receiver.get_full_name', read_only=True)
    receiver_email = serializers.CharField(source='receiver.email', read_only=True)
    amount_usd = serializers.SerializerMethodField()
    platform_fee_usd = serializers.SerializerMethodField()
    net_amount_usd = serializers.SerializerMethodField()
    
    class Meta:
        model = UserTransfer
        fields = [
            'id', 'sender', 'receiver', 'sender_name', 'sender_email',
            'receiver_name', 'receiver_email', 'amount', 'amount_usd',
            'currency', 'message', 'transfer_type', 'platform_fee_amount',
            'platform_fee_usd', 'net_amount_usd', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_amount_usd(self, obj):
        """Convert amount to USD decimal"""
        return obj.amount / 100.0 if obj.amount else 0
    
    def get_platform_fee_usd(self, obj):
        """Convert platform fee to USD decimal"""
        return obj.platform_fee_amount / 100.0 if obj.platform_fee_amount else 0
    
    def get_net_amount_usd(self, obj):
        """Calculate net amount in USD decimal"""
        return obj.net_amount / 100.0 if obj.net_amount else 0


class ConnectAccountStatusSerializer(serializers.Serializer):
    """Serializer for Stripe Connect account status"""
    stripe_account_id = serializers.CharField(read_only=True, allow_null=True)
    charges_enabled = serializers.BooleanField(read_only=True)
    payouts_enabled = serializers.BooleanField(read_only=True)
    details_submitted = serializers.BooleanField(read_only=True)
    is_verified = serializers.BooleanField(read_only=True)
    can_receive_payments = serializers.BooleanField(read_only=True)
    can_receive_donations = serializers.BooleanField(read_only=True)
    setup_required = serializers.BooleanField(read_only=True)


class PublicDonationInfoSerializer(serializers.Serializer):
    """Serializer for public donation information"""
    user_id = serializers.UUIDField(read_only=True)
    user_name = serializers.CharField(read_only=True)
    accepts_donations = serializers.BooleanField(read_only=True)
    donation_message = serializers.CharField(read_only=True)
    minimum_donation = serializers.IntegerField(read_only=True)
    suggested_donation_amounts = serializers.ListField(read_only=True)
    minimum_donation_usd = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    suggested_amounts_usd = serializers.ListField(read_only=True) 