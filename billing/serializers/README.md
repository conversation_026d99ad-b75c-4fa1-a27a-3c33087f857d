# Billing Serializers

This package contains serializers for the billing module, organized by domain/functionality.

## Structure

```
billing/serializers/
├── __init__.py              # Package initialization and exports
├── base.py                  # Core billing serializers (Product, Price, Customer, PlatformFee)
├── payment.py               # Payment-related serializers (ServicePayment, UserTransfer)
├── subscription.py          # Subscription serializers (Subscription, SubscriptionAccess)
├── access.py                # Access control serializers (ServiceAccess, SolutionAccess)
├── enterprise.py            # Enterprise serializers (EnterpriseAccount, EnterpriseService, EnterprisePayment)
├── user_payment.py          # User payment profiles and doctor bank accounts
└── README.md               # This file
```

## Usage

### Recommended (New Way)
```python
# Import specific serializers from their modules
from billing.serializers.base import ProductSerializer, CustomerSerializer
from billing.serializers.payment import UserTransferSerializer
from billing.serializers.user_payment import DoctorBankAccountSerializer

# Or import from package __init__.py
from billing.serializers import ProductSerializer, UserTransferSerializer
```

### Legacy (Still Supported)
```python
# Old way - still works but shows deprecation warning
from billing.serializers import ProductSerializer, UserTransferSerializer
```

## Module Descriptions

### `base.py`
Contains core billing model serializers:
- `ProductSerializer` - Stripe products
- `PriceSerializer` - Stripe prices
- `CustomerSerializer` - Stripe customers
- `PlatformFeeSerializer` - Platform fee configuration

### `payment.py`
Contains payment-related serializers:
- `ServicePaymentSerializer` - One-time service payments
- `UserTransferSerializer` - User-to-user transfers/donations

### `subscription.py`
Contains subscription-related serializers:
- `SubscriptionSerializer` - Stripe subscriptions
- `SubscriptionAccessSerializer` - User subscription access

### `access.py`
Contains access control serializers:
- `ServiceAccessSerializer` - Service access permissions
- `SolutionAccessSerializer` - Enterprise solution access

### `enterprise.py`
Contains enterprise-related serializers:
- `EnterpriseAccountSerializer` - Enterprise Stripe Connect accounts
- `EnterpriseServiceSerializer` - Enterprise services
- `EnterprisePaymentSerializer` - Enterprise payments

### `user_payment.py`
Contains user payment profile serializers:
- `UserPaymentProfileSerializer` - User payment profiles
- `DoctorBankAccountSerializer` - Doctor bank account setup
- `DoctorBankAccountUpdateSerializer` - Bank account updates
- `DoctorBankAccountResponseSerializer` - Bank account response data
- `DoctorPaymentStatusSerializer` - Doctor payment status
- `DoctorEarningsSummarySerializer` - Doctor earnings summary

## Migration Notes

The old `billing/serializers.py` file has been refactored into this modular structure for better organization and maintainability. The old file is still available for backward compatibility but will show deprecation warnings.

When creating new features, please use the appropriate module based on the functionality you're implementing. 