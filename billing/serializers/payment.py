"""
Payment-related serializers
"""
from rest_framework import serializers
from ..models import ServicePayment, UserTransfer


class ServicePaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ServicePayment
        fields = '__all__'


class UserTransferSerializer(serializers.ModelSerializer):
    """
    Serializer for UserTransfer model
    """
    sender_email = serializers.EmailField(source='sender.email', read_only=True)
    receiver_email = serializers.EmailField(source='receiver.email', read_only=True)
    
    class Meta:
        model = UserTransfer
        fields = [
            'id', 'sender', 'receiver', 'sender_email', 'receiver_email', 
            'amount', 'platform_fee_amount', 'net_amount', 'stripe_transfer_id',
            'status', 'transfer_type', 'description', 'metadata',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'stripe_transfer_id', 'platform_fee_amount', 'net_amount',
            'status', 'created_at', 'updated_at', 'sender_email', 'receiver_email'
        ] 