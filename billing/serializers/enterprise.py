"""
Enterprise-related serializers
"""
from rest_framework import serializers
from ..models import EnterpriseAccount


# Enhanced Enterprise Serializers (from the end of original file)
class EnterpriseAccountSerializer(serializers.ModelSerializer):
    """
    Serializer for EnterpriseAccount model
    """
    enterprise_name = serializers.CharField(source='enterprise.name', read_only=True)
    
    class Meta:
        model = EnterpriseAccount
        fields = [
            'id', 'enterprise', 'enterprise_name', 'stripe_account_id', 
            'charges_enabled', 'payouts_enabled', 'details_submitted',
            'is_verified', 'verification_date', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'stripe_account_id', 'charges_enabled', 'payouts_enabled',
            'details_submitted', 'is_verified', 'verification_date',
            'created_at', 'updated_at', 'enterprise_name'
        ]


