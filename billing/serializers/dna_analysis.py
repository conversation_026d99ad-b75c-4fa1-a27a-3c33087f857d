from rest_framework import serializers

from billing.models.payment import ServicePayment
from content_management.models.base import Service

class DNAAnalysisServicePaymentSerializer(serializers.ModelSerializer):
    service_details = serializers.SerializerMethodField()

    class Meta:
        model = ServicePayment
        fields = ['id', 'services', 'status', 'service_details']

    def get_service_details(self, obj):
        if not obj.services:
            return []

        try:
            # Split the string into a list of service IDs
            service_ids = [sid.strip() for sid in obj.services.split(',') if sid.strip()]

            # Fetch matching Service objects by string ID
            services = Service.objects.filter(id__in=service_ids)

            return [
                {
                    'id': str(service.id),
                    'name': service.name,
                    'price': str(service.price),
                    'description': service.description,
                }
                for service in services
            ]
        except Exception as e:
            logger.error(f"Error getting service details: {e}")
            return [] 