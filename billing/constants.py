"""
Constants for the billing app
"""
from enum import Enum

class PaymentType(Enum):
    """Enum for different payment types"""
    ONE_TIME = "one_time"
    SUBSCRIPTION = "subscription" 
    USER_TRANSFER = "user_transfer"
    APPOINTMENT = "appointment"
    
class PaymentStatus:
    PENDING = 'pending'
    COMPLETED = 'completed'
    FAILED = 'failed'
    REFUNDED = 'refunded'
    
    CHOICES = [
        (PENDING, 'Pending'),
        (COMPLETED, 'Completed'),
        (FAILED, 'Failed'),
        (REFUNDED, 'Refunded'),
    ]

class SubscriptionStatus:
    ACTIVE = 'active'
    PAST_DUE = 'past_due'
    CANCELED = 'canceled'
    EXPIRED = 'expired'
    
    CHOICES = [
        (ACTIVE, 'Active'),
        (PAST_DUE, 'Past Due'),
        (CANCELED, 'Canceled'),
        (EXPIRED, 'Expired'),
    ]

class AccessStatus:
    ACTIVE = 'active'
    EXPIRED = 'expired'
    REVOKED = 'revoked'
    
    CHOICES = [
        (ACTIVE, 'Active'),
        (EXPIRED, 'Expired'),
        (REVOKED, 'Revoked'),
    ]

class ProductType:
    SERVICE = 'service'
    SUBSCRIPTION = 'subscription'
    SOLUTION = 'solution'
    
    CHOICES = [
        (SERVICE, 'Service'),
        (SUBSCRIPTION, 'Subscription'),
        (SOLUTION, 'Solution'),
    ]

class TransferType:
    DONATION = 'donation'
    PAYMENT = 'payment'
    TRANSFER = 'transfer'
    
    CHOICES = [
        (DONATION, 'Donation'),
        (PAYMENT, 'Payment'),
        (TRANSFER, 'Transfer'),
    ]

class WebhookEvents:
    CHECKOUT_SESSION_COMPLETED = 'checkout.session.completed'
    PAYMENT_INTENT_SUCCEEDED = 'payment_intent.succeeded'
    PAYMENT_INTENT_FAILED = 'payment_intent.payment_failed'
    SUBSCRIPTION_CREATED = 'customer.subscription.created'
    SUBSCRIPTION_UPDATED = 'customer.subscription.updated'
    SUBSCRIPTION_DELETED = 'customer.subscription.deleted'
    SUBSCRIPTION_QUANTITY_UPDATED = 'customer.subscription.quantity_updated'
    INVOICE_PAYMENT_SUCCEEDED = 'invoice.payment_succeeded'
    INVOICE_PAYMENT_FAILED = 'invoice.payment_failed'
    # Stripe Connect events
    ACCOUNT_UPDATED = 'account.updated'
    ACCOUNT_DEAUTHORIZED = 'account.application.deauthorized'

class CacheKeys:
    CHECKOUT_SESSION = 'checkout_session_{user_id}'
    ENTERPRISE_CHECKOUT = 'enterprise_checkout_{user_id}'
    CUSTOMER_DATA = 'customer_data_{user_id}'
    PRODUCT_PRICES = 'product_prices_{product_id}'

class RateLimits:
    CHECKOUT_SESSION_TIMEOUT = 60  # seconds
    ENTERPRISE_CHECKOUT_TIMEOUT = 60  # seconds
    WEBHOOK_RETRY_ATTEMPTS = 3
    STRIPE_API_RETRY_ATTEMPTS = 3

class Currencies:
    USD = 'usd'
    DEFAULT = USD
    
    CHOICES = [
        (USD, 'US Dollar'),
    ]

class EnterpriseRoles:
    OWNER = 'owner'
    ADMIN = 'admin'
    MEMBER = 'member'
    
    CHOICES = [
        (OWNER, 'Owner'),
        (ADMIN, 'Administrator'),
        (MEMBER, 'Member'),
    ]

class UsageMetricTypes:
    AI_TOKENS = 'ai_tokens'
    STORAGE = 'storage'
    API_CALLS = 'api_calls'
    
    CHOICES = [
        (AI_TOKENS, 'AI Tokens'),
        (STORAGE, 'Storage'),
        (API_CALLS, 'API Calls'),
    ]

class UsageResetPeriods:
    DAILY = 'daily'
    MONTHLY = 'monthly'
    YEARLY = 'yearly'
    
    CHOICES = [
        (DAILY, 'Daily'),
        (MONTHLY, 'Monthly'),
        (YEARLY, 'Yearly'),
    ]
