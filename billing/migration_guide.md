# Billing Services Migration Guide

## Overview

The billing app has been refactored to use specialized service classes instead of the monolithic `StripeService`. This guide will help you migrate your code to use the new services.

## New Service Classes

### CustomerService
Handles customer management operations.

**Before:**
```python
from billing.services import StripeService
customer = StripeService.create_or_update_customer(user)
```

**After:**
```python
from billing.services import CustomerService
customer_service = CustomerService()
customer = customer_service.create_or_update_customer(user)
```

### PaymentService
Handles payment processing operations.

**Before:**
```python
from billing.services import StripeService
session = StripeService.create_checkout_session(customer, price, success_url, cancel_url)
```

**After:**
```python
from billing.services import PaymentService
payment_service = PaymentService()
session = payment_service.create_checkout_session(customer, price, success_url, cancel_url)
```

### SubscriptionService
Handles subscription management operations.

**Before:**
```python
from billing.services import StripeService
subscription = StripeService.create_subscription(customer, price)
```

**After:**
```python
from billing.services import SubscriptionService
subscription_service = SubscriptionService()
subscription = subscription_service.create_subscription(customer, price)
```

### WebhookService
Handles Stripe webhook events.

**Before:**
```python
from billing.services import StripeService
result = StripeService.handle_webhook_event(event)
```

**After:**
```python
from billing.services import WebhookService
webhook_service = WebhookService()
result = webhook_service.handle_event(event)
```

### ProductSyncService
Handles product synchronization with Stripe.

**Before:**
```python
from billing.services import StripeService
StripeService.sync_products()
```

**After:**
```python
from billing.services import ProductSyncService
sync_service = ProductSyncService()
sync_service.sync_products()
```

### EnterpriseService
Handles enterprise-specific billing operations.

**Before:**
```python
from billing.services import StripeService
account = StripeService.create_connect_account(enterprise)
```

**After:**
```python
from billing.services import EnterpriseService
enterprise_service = EnterpriseService()
account = enterprise_service.create_connect_account(enterprise)
```

### UserTransferService
Handles user-to-user transfers.

**Before:**
```python
from billing.services import UserPaymentService
service = UserPaymentService()
transfer = service.create_transfer(sender, receiver, amount)
```

**After:**
```python
from billing.services import UserTransferService
transfer_service = UserTransferService()
transfer = transfer_service.create_transfer(sender, receiver, amount)
```

## Backward Compatibility

The old `StripeService` class is still available and will delegate to the new services. However, it will issue deprecation warnings. You should migrate to the new services as soon as possible.

## Benefits of the New Architecture

1. **Single Responsibility**: Each service has a focused responsibility
2. **Better Testing**: Easier to test individual components
3. **Improved Maintainability**: Smaller, more manageable code units
4. **Enhanced Scalability**: Services can be optimized independently
5. **Clearer Dependencies**: Explicit service dependencies

## Migration Checklist

- [ ] Replace `StripeService.create_or_update_customer()` with `CustomerService.create_or_update_customer()`
- [ ] Replace `StripeService.create_checkout_session()` with `PaymentService.create_checkout_session()`
- [ ] Replace `StripeService.handle_webhook_event()` with `WebhookService.handle_event()`
- [ ] Replace `StripeService.sync_products()` with `ProductSyncService.sync_products()`
- [ ] Replace `StripeService.create_connect_account()` with `EnterpriseService.create_connect_account()`
- [ ] Update imports to use specific service classes
- [ ] Test all billing functionality after migration
- [ ] Remove any direct imports of the old `StripeService` class

## Common Patterns

### Service Initialization
```python
# You can initialize services once and reuse them
customer_service = CustomerService()
payment_service = PaymentService()

# Or initialize as needed
customer = CustomerService().create_or_update_customer(user)
```

### Error Handling
```python
from billing.exceptions import CustomerNotFoundError, PaymentError

try:
    customer = CustomerService().get_customer_by_user(user)
except CustomerNotFoundError:
    # Handle customer not found
    pass
```

### Caching
The new services include built-in caching for frequently accessed data like customer information.

## Support

If you encounter any issues during migration, please:

1. Check the deprecation warnings for guidance
2. Review the new service class documentation
3. Look at the test files for usage examples
4. Contact the development team for assistance

## Timeline

- **Phase 1**: New services available alongside old `StripeService` (current)
- **Phase 2**: Deprecation warnings added to old methods (current)
- **Phase 3**: Old `StripeService` methods will be removed (future release)

Please migrate your code as soon as possible to avoid issues in future releases.
