# Billing App

This app handles all Stripe payment integration for the Ravid platform. It provides functionality for managing subscriptions, payments, and webhooks.

## Features

- Product and Price Management
- Subscription Management
- Payment Method Management
- Invoice Management
- Webhook Handling
- Usage-based Billing Support

## Setup

1. Add Stripe configuration to your `.env` file:
```
STRIPE_LIVE_MODE=False
STRIPE_PUBLISHABLE_KEY=your_publishable_key
STRIPE_SECRET_KEY=your_secret_key
STRIPE_WEBHOOK_SECRET=your_webhook_secret
```

2. Run migrations:
```bash
python manage.py migrate billing
```

3. Sync products with Stripe:
```bash
python manage.py sync_products
```

## API Endpoints

### Subscriptions
- `POST /api/billing/subscriptions/create/` - Create a new subscription
- `GET /api/billing/subscriptions/` - List user's subscriptions
- `POST /api/billing/subscriptions/<subscription_id>/cancel/` - Cancel a subscription

### Payment Methods
- `GET /api/billing/payment-methods/` - List user's payment methods
- `POST /api/billing/payment-methods/create/` - Add a new payment method
- `POST /api/billing/setup-intent/` - Create a SetupIntent for saving payment methods

### Webhooks
- `POST /api/billing/webhook/` - Stripe webhook endpoint

## Models

### Product
Represents a Stripe Product, mapping to your Service, SubscriptionPlan, and Solution models.

### Price
Represents a Stripe Price, with support for recurring and one-time payments.

### Customer
Represents a Stripe Customer, linked to your user model.

### Subscription
Represents a Stripe Subscription, tracking subscription status and periods.

### Invoice
Represents a Stripe Invoice, storing payment history.

### PaymentMethod
Represents a Stripe Payment Method, storing card and other payment details.

### UsageRecord
Represents usage records for metered billing.

## Webhook Events Handled

- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.paid`
- `invoice.payment_failed`

## Usage

### Creating a Subscription

```python
from billing.services import StripeService

# Get or create customer
customer = StripeService.create_or_update_customer(user)

# Create subscription
subscription, stripe_subscription = StripeService.create_subscription(
    customer=customer,
    price=price,
    payment_method_id=payment_method_id
)
```

### Syncing Products

```python
from billing.services import StripeService

# Sync all products
StripeService.sync_products()
```

## Testing

To test the integration:

1. Use Stripe test mode keys
2. Use Stripe test card numbers:
   - Success: 4242 4242 4242 4242
   - Requires Authentication: 4000 0025 0000 3155
   - Decline: 4000 0000 0000 9995

## Security

- All sensitive operations require authentication
- Webhook signatures are verified
- Payment method details are never stored directly
- All amounts are handled in cents to avoid floating-point issues

## Error Handling

The app includes comprehensive error handling for:
- Invalid payment methods
- Failed payments
- Webhook signature verification
- Subscription creation/updates
- Product synchronization

## Contributing

When adding new features:
1. Add appropriate tests
2. Update documentation
3. Follow the existing code style
4. Add appropriate error handling
5. Update webhook handlers if needed 