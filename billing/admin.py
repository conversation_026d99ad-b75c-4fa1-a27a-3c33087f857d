from django.contrib import admin
from .models import (
    Customer, Product, Price, Subscription
)

@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ('email', 'name', 'stripe_customer_id', 'created_at')
    search_fields = ('email', 'name', 'stripe_customer_id')
    readonly_fields = ('stripe_customer_id',)

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'product_type', 'active', 'created_at')
    list_filter = ('product_type', 'active')
    search_fields = ('name', 'description', 'stripe_product_id')
    readonly_fields = ('stripe_product_id',)

@admin.register(Price)
class PriceAdmin(admin.ModelAdmin):
    list_display = ('product', 'unit_amount', 'currency', 'active', 'created_at')
    list_filter = ('active', 'currency')
    search_fields = ('stripe_price_id', 'product__name')
    readonly_fields = ('stripe_price_id',)

@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = ('customer', 'price', 'status', 'current_period_end', 'cancel_at_period_end')
    list_filter = ('status', 'cancel_at_period_end')
    search_fields = ('customer__email', 'stripe_subscription_id')
    readonly_fields = ('stripe_subscription_id',)

