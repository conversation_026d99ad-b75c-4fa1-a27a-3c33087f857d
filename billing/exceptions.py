"""
Custom exceptions for the billing app
"""


class BillingError(Exception):
    """Base billing exception"""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}
        self.message = message
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class PaymentError(BillingError):
    """Payment processing errors"""
    pass


class StripeAPIError(BillingError):
    """Stripe API related errors"""
    
    def __init__(self, message, stripe_error=None, error_code=None, details=None):
        super().__init__(message, error_code, details)
        self.stripe_error = stripe_error
        
        # Extract additional info from Stripe error if available
        if stripe_error:
            self.error_code = getattr(stripe_error, 'code', error_code)
            self.stripe_error_type = type(stripe_error).__name__
            if hasattr(stripe_error, 'user_message'):
                self.user_message = stripe_error.user_message


class CustomerNotFoundError(BillingError):
    """Customer not found errors"""
    pass


class InvalidPaymentMethodError(PaymentError):
    """Invalid payment method errors"""
    pass


class InsufficientFundsError(PaymentError):
    """Insufficient funds errors"""
    pass


class PaymentDeclinedError(PaymentError):
    """Payment declined errors"""
    pass


class SubscriptionError(BillingError):
    """Subscription related errors"""
    pass


class SubscriptionNotFoundError(SubscriptionError):
    """Subscription not found errors"""
    pass


class SubscriptionAlreadyExistsError(SubscriptionError):
    """Subscription already exists errors"""
    pass


class WebhookError(BillingError):
    """Webhook processing errors"""
    pass


class WebhookSignatureError(WebhookError):
    """Webhook signature verification errors"""
    pass


class ProductSyncError(BillingError):
    """Product synchronization errors"""
    pass


class PricingError(BillingError):
    """Pricing calculation errors"""
    pass


class AccessControlError(BillingError):
    """Access control errors"""
    pass


class AccessError(BillingError):
    """Access control and permission errors"""
    pass


class EnterpriseError(BillingError):
    """Enterprise-related errors"""
    pass


class ConnectAccountError(EnterpriseError):
    """Stripe Connect account errors"""
    pass


class TransferError(BillingError):
    """User transfer errors"""
    pass


class ValidationError(BillingError):
    """Validation errors"""
    pass


class ConfigurationError(BillingError):
    """Configuration errors"""
    pass


class RateLimitError(BillingError):
    """Rate limiting errors"""
    pass


class CacheError(BillingError):
    """Cache-related errors"""
    pass


# Error code mappings for common Stripe errors
STRIPE_ERROR_MAPPINGS = {
    'card_declined': PaymentDeclinedError,
    'insufficient_funds': InsufficientFundsError,
    'invalid_request_error': ValidationError,
    'authentication_error': StripeAPIError,
    'api_connection_error': StripeAPIError,
    'api_error': StripeAPIError,
    'rate_limit_error': RateLimitError,
}


def map_stripe_error(stripe_error):
    """Map Stripe errors to custom exceptions"""
    error_type = getattr(stripe_error, 'type', None)
    error_code = getattr(stripe_error, 'code', None)
    
    # Try to map by error code first
    if error_code in STRIPE_ERROR_MAPPINGS:
        exception_class = STRIPE_ERROR_MAPPINGS[error_code]
        return exception_class(str(stripe_error), stripe_error=stripe_error)
    
    # Fall back to generic StripeAPIError
    return StripeAPIError(str(stripe_error), stripe_error=stripe_error)


"""
Custom exceptions for billing system edge cases
"""


class BillingBaseException(Exception):
    """Base exception for billing system"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__.upper()
        self.details = details or {}


class ValidationError(BillingBaseException):
    """Raised when validation fails"""
    pass


class PaymentError(BillingBaseException):
    """Raised when payment processing fails"""
    
    def __init__(self, message: str, stripe_error_code: str = None, **kwargs):
        super().__init__(message, **kwargs)
        self.stripe_error_code = stripe_error_code


class ConfigurationError(BillingBaseException):
    """Raised when system configuration is invalid"""
    pass


class RateLimitError(BillingBaseException):
    """Raised when rate limit is exceeded"""
    pass


class NetworkError(BillingBaseException):
    """Raised when network/API connection fails"""
    pass


class ServiceUnavailableError(BillingBaseException):
    """Raised when service is temporarily unavailable"""
    pass


class ExpiredCardError(PaymentError):
    """Raised when payment fails due to expired card"""
    pass


class InvalidCardError(PaymentError):
    """Raised when payment fails due to invalid card"""
    pass


class ProcessingError(PaymentError):
    """Raised when payment processing encounters an error"""
    pass


class DuplicateRequestError(BillingBaseException):
    """Raised when duplicate request is detected"""
    pass


class PermissionDeniedError(BillingBaseException):
    """Raised when user lacks permission"""
    pass


class ServiceNotFoundError(BillingBaseException):
    """Raised when service is not found"""
    pass


class PricingError(BillingBaseException):
    """Raised when pricing calculation fails"""
    pass


class MetadataError(BillingBaseException):
    """Raised when metadata handling fails"""
    pass
