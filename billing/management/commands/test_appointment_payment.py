"""
Django management command to test appointment payment checkout functionality
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from unittest.mock import patch, MagicMock

from appointments.models import Appointment, DoctorConsultationProfile
from billing.models import UserPaymentProfile, PlatformFee
from billing.services.appointment_payment_service import AppointmentPaymentService
from roles.models import Role

User = get_user_model()


class Command(BaseCommand):
    help = 'Test appointment payment checkout functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='Create test data (users, appointment, etc.)',
        )
        parser.add_argument(
            '--test-platform-fee',
            action='store_true',
            help='Test platform fee creation and retrieval',
        )
        parser.add_argument(
            '--test-checkout-session',
            action='store_true',
            help='Test checkout session creation',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🧪 Testing Appointment Payment Checkout'))
        self.stdout.write('=' * 60)

        if options['create_test_data']:
            self.create_test_data()

        if options['test_platform_fee']:
            self.test_platform_fee()

        if options['test_checkout_session']:
            self.test_checkout_session()

        if not any([options['create_test_data'], options['test_platform_fee'], options['test_checkout_session']]):
            self.stdout.write(self.style.WARNING('Please specify an action:'))
            self.stdout.write('  --create-test-data')
            self.stdout.write('  --test-platform-fee')
            self.stdout.write('  --test-checkout-session')

    def create_test_data(self):
        """Create test users and appointment"""
        self.stdout.write('\n📋 Creating test data...')

        # Create roles
        patient_role, created = Role.objects.get_or_create(name='patient')
        doctor_role, created = Role.objects.get_or_create(name='doctor')

        # Create or get test users
        patient, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'password': 'testpass123',
                'role': patient_role,
                'first_name': 'Test',
                'last_name': 'Patient'
            }
        )

        doctor, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'password': 'testpass123',
                'role': doctor_role,
                'first_name': 'Dr. Test',
                'last_name': 'Doctor'
            }
        )

        # Create payment profiles
        patient_profile, created = UserPaymentProfile.objects.get_or_create(
            user=patient,
            defaults={'stripe_customer_id': 'cus_test_patient'}
        )

        doctor_profile, created = UserPaymentProfile.objects.get_or_create(
            user=doctor,
            defaults={
                'stripe_account_id': 'acct_test_doctor',
                'charges_enabled': True,
                'payouts_enabled': True
            }
        )

        # Create doctor consultation profile
        consultation_profile, created = DoctorConsultationProfile.objects.get_or_create(
            user=doctor,
            defaults={
                'consultation_fee': 7500,  # $75.00
                'consultation_duration': 45,
                'accepts_telemedicine': True,
                'is_active': True
            }
        )

        # Create test appointment
        appointment, created = Appointment.objects.get_or_create(
            patient=patient,
            doctor=doctor,
            defaults={
                'creator': patient,
                'appointment_type': 'booking',
                'mode': 'video_call',
                'direct_payment': True,
                'start_time': timezone.now() + timedelta(hours=1),
                'end_time': timezone.now() + timedelta(hours=2),
                'title': 'Test telemedicine consultation'
            }
        )

        self.stdout.write(self.style.SUCCESS(f'✅ Created test appointment: {appointment.id}'))
        self.stdout.write(f'   Patient: {patient.email}')
        self.stdout.write(f'   Doctor: {doctor.email}')
        self.stdout.write(f'   Consultation fee: ${consultation_profile.consultation_fee/100:.2f}')

    def test_platform_fee(self):
        """Test platform fee creation and retrieval"""
        self.stdout.write('\n🏦 Testing platform fee functionality...')

        service = AppointmentPaymentService()

        # Test platform fee retrieval/creation
        try:
            platform_fee = service._get_applicable_platform_fee(7500)
            
            if platform_fee:
                self.stdout.write(self.style.SUCCESS(f'✅ Platform fee found/created: {platform_fee.name}'))
                self.stdout.write(f'   Percentage: {platform_fee.percentage}%')
                self.stdout.write(f'   Fixed amount: ${platform_fee.fixed_amount/100:.2f}')
                
                # Test fee calculation
                fee_amount = int(7500 * (platform_fee.percentage / 100))
                self.stdout.write(f'   Calculated fee for $75: ${fee_amount/100:.2f}')
            else:
                self.stdout.write(self.style.WARNING('⚠️ No platform fee found'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Platform fee test failed: {str(e)}'))

        # List all platform fees
        all_fees = PlatformFee.objects.all()
        self.stdout.write(f'\n📊 Total platform fees in database: {all_fees.count()}')
        for fee in all_fees:
            self.stdout.write(f'   - {fee.name}: {fee.percentage}% (Active: {fee.active})')

    def test_checkout_session(self):
        """Test checkout session creation"""
        self.stdout.write('\n💳 Testing checkout session creation...')

        try:
            # Get test data
            patient = User.objects.get(email='<EMAIL>')
            doctor = User.objects.get(email='<EMAIL>')
            appointment = Appointment.objects.filter(patient=patient, doctor=doctor).first()

            if not appointment:
                self.stdout.write(self.style.ERROR('❌ No test appointment found. Run --create-test-data first'))
                return

            consultation_profile = DoctorConsultationProfile.objects.get(user=doctor)

            # Mock Stripe checkout session creation
            with patch('stripe.checkout.Session.create') as mock_create:
                mock_session = MagicMock()
                mock_session.id = 'cs_test_123'
                mock_session.url = 'https://checkout.stripe.com/pay/cs_test_123'
                mock_create.return_value = mock_session

                service = AppointmentPaymentService()

                # Test checkout session creation
                transfer, checkout_url = service.create_appointment_checkout_session(
                    patient=patient,
                    doctor=doctor,
                    appointment=appointment,
                    consultation_fee=consultation_profile.consultation_fee
                )

                self.stdout.write(self.style.SUCCESS('✅ Checkout session created successfully'))
                self.stdout.write(f'   Transfer ID: {transfer.id}')
                self.stdout.write(f'   Checkout URL: {checkout_url}')
                self.stdout.write(f'   Amount: ${transfer.amount/100:.2f}')
                self.stdout.write(f'   Platform fee: ${transfer.platform_fee_amount/100:.2f}')
                self.stdout.write(f'   Net amount: ${transfer.net_amount/100:.2f}')

                # Verify appointment was updated
                appointment.refresh_from_db()
                if appointment.payment_transfer == transfer:
                    self.stdout.write(self.style.SUCCESS('✅ Appointment linked to transfer'))
                else:
                    self.stdout.write(self.style.ERROR('❌ Appointment not linked to transfer'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Checkout session test failed: {str(e)}'))
            import traceback
            self.stdout.write(traceback.format_exc())

        self.stdout.write(self.style.SUCCESS('\n🎉 All tests completed!')) 