"""
Management command to fix UserTransfer records with empty stripe_payment_intent_id
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from billing.models import UserTransfer
import uuid
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Fix UserTransfer records with empty stripe_payment_intent_id'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in dry-run mode without making changes',
        )
        parser.add_argument(
            '--delete-empty',
            action='store_true',
            help='Delete records with empty stripe_payment_intent_id instead of updating them',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        delete_empty = options['delete_empty']
        
        self.stdout.write('🔍 Searching for UserTransfer records with empty stripe_payment_intent_id...')
        
        # Find records with empty or None stripe_payment_intent_id
        empty_records = UserTransfer.objects.filter(
            stripe_payment_intent_id__in=['', None]
        )
        
        count = empty_records.count()
        
        if count == 0:
            self.stdout.write(self.style.SUCCESS('✅ No records found with empty stripe_payment_intent_id'))
            return
        
        self.stdout.write(f'🔍 Found {count} records with empty stripe_payment_intent_id')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('🟡 DRY RUN MODE - No changes will be made'))
            for record in empty_records[:10]:  # Show first 10
                self.stdout.write(f'   Record ID: {record.id}, Created: {record.created_at}, Amount: ${record.amount/100:.2f}')
            if count > 10:
                self.stdout.write(f'   ... and {count - 10} more records')
            return
        
        if delete_empty:
            # Delete records with empty stripe_payment_intent_id
            self.stdout.write(f'❌ Deleting {count} records with empty stripe_payment_intent_id...')
            with transaction.atomic():
                deleted_count, _ = empty_records.delete()
                self.stdout.write(self.style.SUCCESS(f'✅ Deleted {deleted_count} records'))
        else:
            # Update records with temporary unique IDs
            self.stdout.write(f'🔧 Updating {count} records with temporary unique IDs...')
            
            updated_count = 0
            with transaction.atomic():
                for record in empty_records:
                    # Generate temporary unique ID
                    temp_id = f"temp_pi_{uuid.uuid4().hex[:16]}"
                    
                    # Update record
                    record.stripe_payment_intent_id = temp_id
                    
                    # Add flag to metadata to indicate this is temporary
                    if not record.metadata:
                        record.metadata = {}
                    record.metadata['temp_payment_intent'] = True
                    record.metadata['fixed_by_command'] = True
                    
                    record.save()
                    updated_count += 1
                    
                    if updated_count % 100 == 0:
                        self.stdout.write(f'   Updated {updated_count}/{count} records...')
            
            self.stdout.write(self.style.SUCCESS(f'✅ Updated {updated_count} records with temporary IDs'))
            self.stdout.write('💡 These records can be updated with real payment intent IDs when payments are processed')

        self.stdout.write(self.style.SUCCESS('🎉 Command completed successfully')) 