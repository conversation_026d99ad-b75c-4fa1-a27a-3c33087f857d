"""
Management command to fix pending transfers that are blocking account deactivation
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import models
from accounts.models import CustomUser
from billing.models import UserTransfer, UserPaymentProfile
from billing.services.user_transfer_service import UserTransferService
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Fix pending transfers that are blocking account deactivation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user-id',
            type=str,
            help='Specific user ID to fix pending transfers for'
        )
        parser.add_argument(
            '--check-only',
            action='store_true',
            help='Only check pending transfers without cancelling them'
        )
        parser.add_argument(
            '--force-cancel',
            action='store_true',
            help='Force cancel all pending transfers'
        )
        parser.add_argument(
            '--older-than-hours',
            type=int,
            default=24,
            help='Only process transfers older than X hours (default: 24)'
        )

    def handle(self, *args, **options):
        user_id = options.get('user_id')
        check_only = options.get('check_only')
        force_cancel = options.get('force_cancel')
        older_than_hours = options.get('older_than_hours')

        self.stdout.write(self.style.SUCCESS('=== Fix Pending Transfers Tool ==='))

        # Calculate cutoff time
        cutoff_time = timezone.now() - timezone.timedelta(hours=older_than_hours)
        
        if user_id:
            try:
                user = CustomUser.objects.get(id=user_id)
                self.process_user_transfers(user, check_only, force_cancel, cutoff_time)
            except CustomUser.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'User with ID {user_id} not found'))
        else:
            self.process_all_pending_transfers(check_only, force_cancel, cutoff_time)

    def process_all_pending_transfers(self, check_only, force_cancel, cutoff_time):
        """Process all pending transfers in the system"""
        
        # Find all pending transfers
        pending_transfers = UserTransfer.objects.filter(
            status__in=['pending', 'processing'],
            created_at__lt=cutoff_time
        ).order_by('created_at')

        self.stdout.write(f'Found {pending_transfers.count()} pending transfers older than {cutoff_time}')

        if not pending_transfers.exists():
            self.stdout.write(self.style.SUCCESS('No pending transfers found to process'))
            return

        # Group by user (as receiver)
        users_with_pending = {}
        for transfer in pending_transfers:
            receiver_id = transfer.receiver.id
            if receiver_id not in users_with_pending:
                users_with_pending[receiver_id] = {
                    'user': transfer.receiver,
                    'transfers': []
                }
            users_with_pending[receiver_id]['transfers'].append(transfer)

        self.stdout.write(f'Found {len(users_with_pending)} users with pending transfers')

        # Process each user
        for user_id, data in users_with_pending.items():
            user = data['user']
            transfers = data['transfers']
            
            self.stdout.write(f'\n--- User: {user.email} ({len(transfers)} pending transfers) ---')
            
            if check_only:
                self.display_user_pending_info(user, transfers)
            else:
                self.process_user_transfers(user, check_only, force_cancel, cutoff_time)

    def process_user_transfers(self, user, check_only, force_cancel, cutoff_time):
        """Process pending transfers for a specific user"""
        
        transfer_service = UserTransferService()
        
        # Get pending transfer info
        pending_info = transfer_service.get_user_pending_transfers_info(user)
        
        total_pending = (
            pending_info['pending_as_sender']['count'] + 
            pending_info['pending_as_receiver']['count']
        )
        
        total_amount = (
            pending_info['pending_as_sender']['total_amount'] + 
            pending_info['pending_as_receiver']['total_amount']
        )

        self.stdout.write(f'User: {user.email}')
        self.stdout.write(f'  Total pending transfers: {total_pending}')
        self.stdout.write(f'  Total amount: ${total_amount/100:.2f}')
        
        if check_only:
            self.display_pending_details(pending_info)
            return

        if total_pending == 0:
            self.stdout.write(self.style.SUCCESS('  No pending transfers to process'))
            return

        # Process transfers
        cancelled_count = 0
        failed_count = 0

        # Get actual transfer objects
        all_pending = UserTransfer.objects.filter(
            models.Q(sender=user) | models.Q(receiver=user),
            status__in=['pending', 'processing'],
            created_at__lt=cutoff_time
        )

        for transfer in all_pending:
            try:
                if force_cancel or self.should_cancel_transfer(transfer):
                    success = transfer_service.cancel_pending_transfer(transfer)
                    if success:
                        cancelled_count += 1
                        self.stdout.write(f'    ✓ Cancelled transfer {transfer.id}: ${transfer.amount/100:.2f}')
                    else:
                        failed_count += 1
                        self.stdout.write(f'    ✗ Failed to cancel transfer {transfer.id}: ${transfer.amount/100:.2f}')
                else:
                    self.stdout.write(f'    - Skipped transfer {transfer.id}: ${transfer.amount/100:.2f} (too recent)')
            except Exception as e:
                failed_count += 1
                self.stdout.write(f'    ✗ Error cancelling transfer {transfer.id}: {str(e)}')

        self.stdout.write(f'  Results: {cancelled_count} cancelled, {failed_count} failed')

    def display_pending_details(self, pending_info):
        """Display detailed pending transfer information"""
        
        if pending_info['pending_as_sender']['count'] > 0:
            self.stdout.write('  Pending as sender:')
            for transfer in pending_info['pending_as_sender']['transfers']:
                self.stdout.write(f'    - ${transfer["amount"]/100:.2f} to {transfer["receiver_email"]} ({transfer["status"]})')

        if pending_info['pending_as_receiver']['count'] > 0:
            self.stdout.write('  Pending as receiver:')
            for transfer in pending_info['pending_as_receiver']['transfers']:
                self.stdout.write(f'    - ${transfer["amount"]/100:.2f} from {transfer["sender_email"]} ({transfer["status"]})')

    def should_cancel_transfer(self, transfer):
        """Determine if a transfer should be cancelled"""
        # Cancel if older than cutoff time
        cutoff_time = timezone.now() - timezone.timedelta(hours=24)
        return transfer.created_at < cutoff_time

    def display_user_pending_info(self, user, transfers):
        """Display user pending transfer information"""
        total_amount = sum(t.amount for t in transfers)
        
        self.stdout.write(f'  Email: {user.email}')
        self.stdout.write(f'  Transfers: {len(transfers)}')
        self.stdout.write(f'  Total amount: ${total_amount/100:.2f}')
        
        for transfer in transfers:
            age = timezone.now() - transfer.created_at
            self.stdout.write(
                f'    - ID: {transfer.id}, Amount: ${transfer.amount/100:.2f}, '
                f'Age: {age.days}d {age.seconds//3600}h, '
                f'Sender: {transfer.sender.email}, Status: {transfer.status}'
            ) 