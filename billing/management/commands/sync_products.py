from django.core.management.base import BaseCommand
from billing.services import StripeService
from billing.models import Product
from content_management.models import Service, SubscriptionPlan, Solution

class Command(BaseCommand):
    help = 'Sync products from content management models to Stripe'

    def handle(self, *args, **options):
        self.stdout.write('Starting product sync...')
        
        try:
            StripeService.sync_products()
            self.stdout.write(self.style.SUCCESS('Successfully synced products to Stripe'))
            
            # Sync content IDs for existing products
            self.sync_content_ids()
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Failed to sync products: {str(e)}'))

    def sync_content_ids(self):
        # Sync services
        for service in Service.objects.all():
            try:
                product = Product.objects.get(
                    product_type='service',
                    service=service
                )
                product.content_id = service.id
                product.save()
            except Product.DoesNotExist:
                pass

        # Sync subscription plans
        for plan in SubscriptionPlan.objects.all():
            try:
                product = Product.objects.get(
                    product_type='subscription',
                    subscription_plan=plan
                )
                product.content_id = plan.id
                product.save()
            except Product.DoesNotExist:
                pass

        # Sync solutions
        for solution in Solution.objects.all():
            try:
                product = Product.objects.get(
                    product_type='solution',
                    solution=solution
                )
                product.content_id = solution.id
                product.save()
            except Product.DoesNotExist:
                pass 