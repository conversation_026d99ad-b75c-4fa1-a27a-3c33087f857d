#!/usr/bin/env python3
"""
Management command to check and fix Stripe Connect accounts capabilities
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
import stripe
from django.conf import settings

from billing.models import UserPaymentProfile

User = get_user_model()

class Command(BaseCommand):
    help = 'Check and fix Stripe Connect accounts capabilities'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Only check accounts without making changes',
        )
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix accounts missing transfers capability',
        )
        parser.add_argument(
            '--user-id',
            type=str,
            help='Check specific user by ID',
        )

    def handle(self, *args, **options):
        stripe.api_key = settings.STRIPE_SECRET_KEY
        
        dry_run = options['dry_run']
        fix_accounts = options['fix']
        user_id = options.get('user_id')
        
        self.stdout.write(
            self.style.SUCCESS(f'Starting Stripe Connect accounts check (dry_run={dry_run}, fix={fix_accounts})')
        )
        
        # Get payment profiles with Stripe accounts
        queryset = UserPaymentProfile.objects.filter(stripe_account_id__isnull=False)
        
        if user_id:
            queryset = queryset.filter(user__id=user_id)
        
        total_accounts = queryset.count()
        self.stdout.write(f'Found {total_accounts} accounts to check')
        
        checked = 0
        issues_found = 0
        fixed = 0
        
        for profile in queryset:
            checked += 1
            self.stdout.write(f'\n[{checked}/{total_accounts}] Checking account: {profile.stripe_account_id} (user: {profile.user.email})')
            
            try:
                # Retrieve account from Stripe
                account = stripe.Account.retrieve(profile.stripe_account_id)
                capabilities = getattr(account, 'capabilities', {})
                transfers_capability = capabilities.get('transfers')
                
                self.stdout.write(f'  Account Status:')
                self.stdout.write(f'    charges_enabled: {account.charges_enabled}')
                self.stdout.write(f'    payouts_enabled: {account.payouts_enabled}')
                self.stdout.write(f'    details_submitted: {account.details_submitted}')
                self.stdout.write(f'    transfers capability: {transfers_capability}')
                
                # Check for issues
                has_issues = False
                
                if not transfers_capability or transfers_capability not in ['active', 'pending']:
                    self.stdout.write(
                        self.style.ERROR(f'  ISSUE: Missing or inactive transfers capability: {transfers_capability}')
                    )
                    has_issues = True
                
                if not account.charges_enabled:
                    self.stdout.write(
                        self.style.WARNING(f'  WARNING: Charges not enabled')
                    )
                    has_issues = True
                
                if not account.payouts_enabled:
                    self.stdout.write(
                        self.style.WARNING(f'  WARNING: Payouts not enabled')
                    )
                
                if has_issues:
                    issues_found += 1
                    
                    if fix_accounts and not dry_run and (not transfers_capability or transfers_capability not in ['active', 'pending']):
                        try:
                            # Attempt to request transfers capability
                            self.stdout.write(f'  FIXING: Requesting transfers capability...')
                            
                            updated_account = stripe.Account.modify(
                                profile.stripe_account_id,
                                capabilities={'transfers': {'requested': True}}
                            )
                            
                            updated_capabilities = getattr(updated_account, 'capabilities', {})
                            updated_transfers = updated_capabilities.get('transfers')
                            
                            self.stdout.write(
                                self.style.SUCCESS(f'  FIXED: Transfers capability now: {updated_transfers}')
                            )
                            fixed += 1
                            
                        except Exception as fix_error:
                            self.stdout.write(
                                self.style.ERROR(f'  FAILED TO FIX: {str(fix_error)}')
                            )
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f'  ✓ Account looks good')
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ERROR checking account: {str(e)}')
                )
                issues_found += 1
        
        # Summary
        self.stdout.write(f'\n' + '='*60)
        self.stdout.write(f'SUMMARY:')
        self.stdout.write(f'  Total accounts checked: {checked}')
        self.stdout.write(f'  Issues found: {issues_found}')
        if fix_accounts and not dry_run:
            self.stdout.write(f'  Accounts fixed: {fixed}')
        elif dry_run:
            self.stdout.write(f'  (Dry run - no changes made)')
        
        if issues_found > 0:
            self.stdout.write(f'\nTo fix issues, run:')
            self.stdout.write(f'  python manage.py fix_connect_accounts --fix')
        
        self.stdout.write(self.style.SUCCESS('\nDone!')) 