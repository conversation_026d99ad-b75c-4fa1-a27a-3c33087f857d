from django.core.management.base import BaseCommand
from billing.services import StripeService
from content_management.models import Solution

class Command(BaseCommand):
    help = 'Sync solution prices with recurring configuration'

    def handle(self, *args, **options):
        self.stdout.write('Starting solution price sync...')
        
        try:
            # Get all active solutions
            solutions = Solution.objects.filter(is_active=True)
            
            for solution in solutions:
                self.stdout.write(f'Syncing prices for solution: {solution.name}')
                StripeService._sync_solution(solution)
            
            self.stdout.write(self.style.SUCCESS('Successfully synced solution prices'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Failed to sync solution prices: {str(e)}')) 