"""
DEPRECATED: This file is maintained for backward compatibility only.
New code should use the views in billing.views package.

This module will be removed in a future version.
"""
import warnings

from django.shortcuts import render
from django.conf import settings
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.core.exceptions import ValidationError
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.cache import never_cache
from django.views.decorators.http import require_http_methods
from django.core.cache import cache
from django.contrib.auth import get_user_model
from django.db.models import Q
from analysis.services.base import DNAAnalysisService
from content_management.models.base import ServicePromotion
import stripe
import json
import hmac
import hashlib
from rest_framework import viewsets, serializers, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from datetime import timedelta
import logging
from django.db import transaction
from analysis.models.base import DNAAnalysis
from content_management.models import Service
from analysis.models.gs_file_manager import GSFileManager

# Issue deprecation warning
warnings.warn(
    "Direct import from billing.views is deprecated. "
    "Use billing.views.checkout_views, billing.views.payment_views, etc. instead.",
    DeprecationWarning,
    stacklevel=2
)

from .models import (
    Customer, Price, Subscription, PlatformFee,
    EnterpriseAccount, EnterpriseService, EnterprisePayment,
    Product, Service, SubscriptionPlan, Solution,
    ServicePayment, ServiceAccess, SubscriptionAccess, SolutionAccess, UserPaymentProfile, UserTransfer
)
from .services import StripeService, UserPaymentService
from .serializers import (
    ProductSerializer, PriceSerializer, CustomerSerializer, ServicePaymentSerializer,
    ServiceAccessSerializer, SubscriptionSerializer, SubscriptionAccessSerializer,
    PlatformFeeSerializer, EnterpriseAccountSerializer, EnterpriseServiceSerializer,
    EnterprisePaymentSerializer, SolutionAccessSerializer, UserPaymentProfileSerializer, UserTransferSerializer
)

stripe.api_key = settings.STRIPE_SECRET_KEY
logger = logging.getLogger(__name__)

# Get the User model
User = get_user_model()

class BasePaymentView(View):
    """Base view for payment-related functionality"""
    
    @method_decorator(login_required)
    @method_decorator(never_cache)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

class CreateCheckoutSessionView(BasePaymentView):
    """Create a Stripe Checkout session for SaaS platform services"""
    
    @method_decorator(require_http_methods(["POST"]))
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            price_id = data.get('price_id')
            
            if not price_id:
                return JsonResponse({
                    'error': 'Price ID is required'
                }, status=400)
            
            # Rate limiting
            cache_key = f'checkout_session_{request.user.id}'
            if cache.get(cache_key):
                return JsonResponse({
                    'error': 'Please wait before creating another checkout session'
                }, status=429)
            cache.set(cache_key, True, 60)  # 1 minute cooldown
            
            # Get or create customer
            try:
                customer = StripeService.create_or_update_customer(request.user)
            except Exception as e:
                logger.error(f"Error creating/updating customer: {str(e)}")
                return JsonResponse({
                    'error': f'Error creating customer: {str(e)}'
                }, status=500)
            
            # Get price
            try:
                price = get_object_or_404(Price, stripe_price_id=price_id)
            except Exception as e:
                logger.error(f"Error getting price: {str(e)}")
                return JsonResponse({
                    'error': f'Error getting price: {str(e)}'
                }, status=500)
            
            # Create checkout session
            try:
                session = StripeService.create_checkout_session(
                    customer=customer,
                    price=price,
                    success_url=request.build_absolute_uri('/api/billing/payment/success/') + '?session_id={CHECKOUT_SESSION_ID}',
                    cancel_url=request.build_absolute_uri('/api/billing/payment/cancel/'),
                    metadata={
                        'price_id': price.id,
                        'customer_id': customer.id,
                    }
                )
            except Exception as e:
                logger.error(f"Error creating checkout session: {str(e)}")
                return JsonResponse({
                    'error': f'Error creating checkout session: {str(e)}'
                }, status=500)
            
            return JsonResponse({
                'session_id': session.id,
                'url': session.url
            })
            
        except ValidationError as e:
            logger.error(f"Validation error: {str(e)}")
            return JsonResponse({
                'error': str(e)
            }, status=400)
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return JsonResponse({
                'error': f'An unexpected error occurred: {str(e)}'
            }, status=500)

class CreateEnterpriseCheckoutSessionView(BasePaymentView):
    """Create a Stripe Checkout session for enterprise services"""
    
    @method_decorator(require_http_methods(["POST"]))
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            service_id = data.get('service_id')
            
            if not service_id:
                return JsonResponse({
                    'error': 'Service ID is required'
                }, status=400)
            
            # Rate limiting
            cache_key = f'enterprise_checkout_{request.user.id}'
            if cache.get(cache_key):
                return JsonResponse({
                    'error': 'Please wait before creating another checkout session'
                }, status=429)
            cache.set(cache_key, True, 60)  # 1 minute cooldown
            
            # Get or create customer
            customer = StripeService.create_or_update_customer(request.user)
            
            # Get service and enterprise
            service = get_object_or_404(EnterpriseService, id=service_id)
            enterprise_account = get_object_or_404(EnterpriseAccount, enterprise=service.enterprise)
            
            # Validate enterprise account status
            if not enterprise_account.charges_enabled:
                return JsonResponse({
                    'error': 'Enterprise account is not ready to accept payments'
                }, status=400)
            
            # Get active platform fee
            platform_fee = PlatformFee.objects.filter(active=True).first()
            if not platform_fee:
                return JsonResponse({
                    'error': 'No active platform fee found'
                }, status=400)
            
            # Create checkout session
            session = StripeService.create_enterprise_checkout_session(
                customer=customer,
                service=service,
                enterprise_account=enterprise_account,
                platform_fee=platform_fee,
                success_url=request.build_absolute_uri('/payment/success/'),
                cancel_url=request.build_absolute_uri('/payment/cancel/')
            )
            
            return JsonResponse({
                'session_id': session.id,
                'url': session.url
            })
            
        except ValidationError as e:
            return JsonResponse({
                'error': str(e)
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'error': 'An unexpected error occurred'
            }, status=500)

class CreateEnterpriseConnectAccountView(BasePaymentView):
    """Create a Stripe Connect account for an enterprise"""
    
    @method_decorator(require_http_methods(["POST"]))
    def post(self, request, *args, **kwargs):
        try:
            enterprise = request.user.enterprise
            if not enterprise:
                return JsonResponse({
                    'error': 'User is not associated with an enterprise'
                }, status=400)
            
            # Check if account already exists
            if hasattr(enterprise, 'stripe_account'):
                return JsonResponse({
                    'error': 'Enterprise already has a Stripe Connect account'
                }, status=400)
            
            # Create Stripe Connect account
            enterprise_account = StripeService.create_connect_account(enterprise)
            
            # Create account link for onboarding
            account_link = stripe.AccountLink.create(
                account=enterprise_account.stripe_account_id,
                refresh_url=request.build_absolute_uri('/enterprise/connect/refresh/'),
                return_url=request.build_absolute_uri('/enterprise/connect/return/'),
                type='account_onboarding',
            )
            
            return JsonResponse({
                'account_id': enterprise_account.stripe_account_id,
                'url': account_link.url
            })
            
        except ValidationError as e:
            return JsonResponse({
                'error': str(e)
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'error': 'An unexpected error occurred'
            }, status=500)

@csrf_exempt
@require_POST
def stripe_webhook(request):
    """Handle Stripe webhook events"""
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    
    logger.info("Received Stripe webhook request")
    logger.debug(f"Signature header: {sig_header}")
    logger.debug(f"Payload: {payload.decode('utf-8')}")
    
    try:
        # Try to verify webhook signature with production secret first
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_TEST_WEBHOOK_SECRET_BILLING
            )
            logger.info("Successfully verified webhook signature with production secret")
        except stripe.error.SignatureVerificationError:
            # If production secret fails, try with test secret
            try:
                event = stripe.Webhook.construct_event(
                    payload, sig_header, settings.STRIPE_TEST_WEBHOOK_SECRET
                )
                logger.info("Successfully verified webhook signature with test secret")
            except stripe.error.SignatureVerificationError as e:
                # Both secrets failed, raise the error
                logger.error(f"Invalid webhook signature with both production and test secrets: {str(e)}")
                return JsonResponse({
                    'error': 'Invalid signature'
                }, status=400)
        
        logger.info(f"Successfully verified webhook signature for event: {event.type}")
        logger.debug(f"Event data: {event.data}")
        
        # Handle specific events
        if event.type == 'checkout.session.completed':
            session = event.data.object
            
            # Log detailed session information for debugging
            logger.info(f"Processing checkout.session.completed event for session ID: {session.id}")
            logger.info(f"Session customer ID: {session.customer}")
            logger.info(f"Session customer details: {session.customer_details}")
            logger.info(f"Session metadata: {session.metadata}")
            
            # Check if this is a DNA analysis payment
            if session.metadata and session.metadata.get('payment_type') == 'dna_analysis':
                try:
                    logger.info(f"Processing DNA analysis payment for session {session.id}")
                    
                    # Get customer
                    try:
                        if session.customer:
                            # If session has a customer ID, find by stripe_customer_id
                            logger.info(f"Looking up customer by Stripe customer ID: {session.customer}")
                            try:
                                customer = Customer.objects.get(stripe_customer_id=session.customer)
                                user = customer.user
                                logger.info(f"Found customer {customer.id} for user {user.id} by Stripe customer ID")
                            except Customer.DoesNotExist:
                                logger.error(f"Customer not found with Stripe ID: {session.customer}")
                                # Try to find by email as fallback
                                email = session.customer_details.get('email')
                                if email:
                                    logger.info(f"Trying to find customer by email: {email}")
                                    try:
                                        # Use the User model directly from get_user_model()
                                        user = get_user_model().objects.get(email=email)
                                        customer = Customer.objects.get(user=user)
                                        logger.info(f"Found customer {customer.id} for user {user.id} by email")
                                    except (get_user_model().DoesNotExist, Customer.DoesNotExist):
                                        logger.error(f"Customer not found for email: {email}")
                                        return JsonResponse({
                                            'error': 'Customer not found'
                                        }, status=400)
                                else:
                                    logger.error(f"No customer email found in session {session.id}")
                                    return JsonResponse({
                                        'error': 'No customer email found'
                                    }, status=400)
                        else:
                            # If no customer ID, find by email from customer_details
                            email = session.customer_details.get('email')
                            if not email:
                                logger.error(f"No customer email found in session {session.id}")
                                return JsonResponse({
                                    'error': 'No customer email found'
                                }, status=400)
                            
                            logger.info(f"Looking up customer by email: {email}")
                            # Find user by email
                            try:
                                # Use the User model directly from get_user_model()
                                user = get_user_model().objects.get(email=email)
                                customer = Customer.objects.get(user=user)
                                logger.info(f"Found customer {customer.id} for user {user.id} by email")
                            except get_user_model().DoesNotExist:
                                logger.error(f"User not found for email: {email}")
                                return JsonResponse({
                                    'error': 'User not found'
                                }, status=400)
                            except Customer.DoesNotExist:
                                logger.error(f"Customer not found for user: {user.id}")
                                
                                # Create a new customer record if it doesn't exist
                                logger.info(f"Creating new customer record for user {user.id}")
                                try:
                                    # Create new Stripe customer
                                    stripe_customer = stripe.Customer.create(
                                        email=user.email,
                                        name=f"{user.first_name} {user.last_name}".strip() or user.email,
                                        metadata={
                                            'user_id': str(user.id)
                                        }
                                    )
                                    # Create customer record in database
                                    customer = Customer.objects.create(
                                        user=user,
                                        stripe_customer_id=stripe_customer.id,
                                        email=user.email,
                                        name=f"{user.first_name} {user.last_name}".strip() or user.email
                                    )
                                    logger.info(f"Created new customer {customer.id} with Stripe ID {stripe_customer.id}")
                                except Exception as e:
                                    logger.error(f"Error creating customer: {str(e)}")
                                    return JsonResponse({
                                        'error': f'Error creating customer: {str(e)}'
                                    }, status=400)
                            
                        logger.info(f"Found customer {customer.id} for user {user.id}")
                    except Exception as e:
                        logger.error(f"Error finding customer: {str(e)}")
                        return JsonResponse({
                            'error': f'Error finding customer: {str(e)}'
                        }, status=400)
                    
                    # Get DNA code and service IDs from metadata
                    service_ids_json = session.metadata.get('service_ids', '[]')
                    
                    # Parse service IDs from JSON string
                    try:
                        service_ids = json.loads(service_ids_json)
                        if not service_ids:
                            logger.error(f"No service IDs found in session metadata for session {session.id}")
                            return JsonResponse({
                                'error': 'No service IDs found in session metadata'
                            }, status=400)
                    except json.JSONDecodeError:
                        logger.error(f"Invalid service IDs JSON in session metadata for session {session.id}")
                        return JsonResponse({
                            'error': 'Invalid service IDs format in session metadata'
                        }, status=400)
                    

                    
                    # Get all services first to validate and calculate amounts
                    services = []
                    total_service_price = 0
                    for service_id in service_ids:
                        try:
                            service = Service.objects.get(id=service_id)
                            services.append(service)
                            total_service_price += service.price
                        except Service.DoesNotExist:
                            logger.error(f"Service {service_id} not found")
                            continue
                    
                    if not services:
                        logger.error("No valid services found")
                        return JsonResponse({
                            'error': 'No valid services found'
                        }, status=400)

                    # Fetching or creating DNA analysis record
                    dna_analysis = None
                    auto_start = False
                    try:
                        dna_analysis = DNAAnalysis.objects.get(stripe_session_id=session.id)
                        auto_start = True
                    except DNAAnalysis.DoesNotExist:
                        # If not found, create a new one (should not normally happen)
                        logger.warning(f"DNAAnalysis with stripe_session_id={session.id} not found, creating new record.")
                        dna_analysis = DNAAnalysis.objects.create(
                            user=user,
                            status='pending',
                            stripe_session_id=session.id
                        )
                    
                    # Create service payments and access records
                    with transaction.atomic():
                        # Create a single service payment for all services
                        service_payment = ServicePayment.objects.create(
                            user=user,
                            service=services[0],  # Use first service as primary
                            services=','.join(str(service.id) for service in services),  # Concat service IDs
                            amount=session.amount_total,  # Total amount in cents
                            currency=session.currency,
                            status='completed',
                            stripe_payment_intent_id=session.payment_intent,
                            dna_analysis=dna_analysis,
                            metadata={
                                'service_ids': service_ids,  # Store all service IDs in metadata
                                'total_amount': session.amount_total,
                                'amount_per_service': session.amount_total // len(services)
                            }
                        )
                        logger.info(f"Created service payment {service_payment.id} for {len(services)} services")
                        
                        # Create or update service access for each service
                        for service in services:
                            try:
                                logger.info(f"Processing service {service.id} for DNA analysis {dna_analysis.id}")
                                
                                # Check for existing service access
                                existing_access = ServiceAccess.objects.filter(
                                    user=user,
                                    service=service
                                ).order_by('-access_granted_at').first()
                                
                                if existing_access:
                                    # If there's an existing access, update it
                                    existing_access.payment = service_payment
                                    existing_access.status = 'active'
                                    existing_access.analysis_access_data = {
                                        'dna_analysis_id': str(dna_analysis.id),
                                    }
                                    existing_access.save()
                                    logger.info(f"Updated existing service access {existing_access.id} for service {service.id}")
                                else:
                                    # Create new service access if none exists
                                    service_access = ServiceAccess.objects.create(
                                        user=user,
                                        service=service,
                                        payment=service_payment,
                                        status='active',
                                        access_expires_at=None,  # Set to None for infinite access
                                        analysis_access_data={
                                            'dna_analysis_id': str(dna_analysis.id),
                                        }
                                    )
                                    logger.info(f"Created new service access {service_access.id} for service {service.id}")
                                
                            except Exception as e:
                                logger.error(f"Error processing service {service.id} for DNA analysis {dna_analysis.id}: {str(e)}")
                                raise  # Re-raise the exception to trigger transaction rollback
                    
                    # TODO: Trigger DNA analysis processing
                    # This would be handled by your background task system
                    logger.info(f"Processing DNA analysis record for user {user.id} for session {session.id}")
                    if auto_start:
                        dna_analysis_service = DNAAnalysisService()
                        dna_analysis_service.start_gsba_analysis(dna_analysis)
                    
                    logger.info(f"Successfully processed DNA analysis payment for user {user.id}")
                    
                    # Return success response
                    return JsonResponse({
                        'status': 'success',
                        'dna_analysis_id': str(dna_analysis.id)
                    })
                    
                except Exception as e:
                    logger.error(f"Error processing DNA analysis payment: {str(e)}")
                    return JsonResponse({
                        'error': f'Error processing DNA analysis payment: {str(e)}'
                    }, status=500)
        
        # Handle appointment payments
        elif session.metadata.get('payment_type') == 'appointment':
            try:
                appointment_id = session.metadata.get('appointment_id')
                if appointment_id:
                    from appointments.models import Appointment
                    from billing.models import ServicePayment
                    from accounts.models import CustomUser
                    
                    appointment = get_object_or_404(Appointment, id=appointment_id)
                    patient = appointment.patient
                    
                    logger.info(f"Processing appointment payment for appointment {appointment_id}")
                    
                    # Create service payment record
                    service_payment = ServicePayment.objects.create(
                        user=patient,
                        service_id=session.metadata.get('service_id'),
                        amount=session.amount_total,
                        currency=session.currency,
                        status='completed',
                        stripe_payment_intent_id=session.payment_intent,
                        metadata={
                            'appointment_id': appointment_id,
                            'doctor_id': session.metadata.get('doctor_id'),
                            'payment_type': 'appointment'
                        }
                    )
                    
                    # Auto-confirm appointment if direct payment
                    if appointment.direct_payment and appointment.status == 'pending':
                        appointment.status = 'confirmed'
                        appointment.save()
                        
                        # Send confirmation emails
                        try:
                            from appointments.services.email_service import AppointmentEmailService
                            AppointmentEmailService.send_appointment_confirmation_emails(appointment)
                            logger.info(f"Sent confirmation emails for appointment {appointment_id}")
                        except Exception as e:
                            logger.error(f"Failed to send confirmation emails: {str(e)}")
                    
                    logger.info(f"Successfully processed appointment payment for appointment {appointment_id}")
                    
                    return JsonResponse({
                        'status': 'success',
                        'appointment_id': appointment_id,
                        'payment_id': str(service_payment.id)
                    })
                    
            except Exception as e:
                logger.error(f"Error processing appointment payment: {str(e)}")
                return JsonResponse({
                    'error': f'Error processing appointment payment: {str(e)}'
                }, status=500)
        
        # Handle the event with the StripeService
        success = StripeService.handle_webhook_event(event)
        
        if success:
            logger.info(f"Successfully handled webhook event: {event.type}")
            return JsonResponse({'status': 'success'})
        else:
            logger.error(f"Failed to handle webhook event: {event.type}")
            return JsonResponse({
                'error': 'Failed to handle webhook event'
            }, status=400)
            
    except Exception as e:
        logger.error(f"Webhook error: {str(e)}")
        return JsonResponse({
            'error': 'An unexpected error occurred'
        }, status=500)

@login_required
def create_subscription(request):
    """
    Create a new subscription for the authenticated user
    """
    try:
        data = json.loads(request.body)
        price_id = data.get('price_id')
        payment_method_id = data.get('payment_method_id')
        
        if not price_id:
            return JsonResponse({
                'error': 'Price ID is required'
            }, status=400)
        
        # Get or create customer
        customer = StripeService.create_or_update_customer(request.user)
        
        # Get price
        price = get_object_or_404(Price, stripe_price_id=price_id)
        
        # Create subscription
        subscription, stripe_subscription = StripeService.create_subscription(
            customer=customer,
            price=price,
            payment_method_id=payment_method_id
        )
        
        return JsonResponse({
            'subscription_id': subscription.stripe_subscription_id,
            'client_secret': stripe_subscription.latest_invoice.payment_intent.client_secret
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)

@login_required
def cancel_subscription(request, subscription_id):
    """
    Cancel a subscription
    """
    try:
        subscription = get_object_or_404(
            Subscription,
            stripe_subscription_id=subscription_id,
            customer__user=request.user
        )
        
        updated_subscription = StripeService.cancel_subscription(subscription)
        
        return JsonResponse({
            'status': 'success',
            'subscription': {
                'id': updated_subscription.stripe_subscription_id,
                'status': updated_subscription.status,
                'cancel_at_period_end': updated_subscription.cancel_at_period_end
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)

@login_required
def list_subscriptions(request):
    """
    List all subscriptions for the authenticated user
    """
    try:
        customer = get_object_or_404(Customer, user=request.user)
        subscriptions = Subscription.objects.filter(customer=customer)
        
        return JsonResponse({
            'subscriptions': [{
                'id': sub.stripe_subscription_id,
                'status': sub.status,
                'current_period_end': sub.current_period_end.isoformat(),
                'cancel_at_period_end': sub.cancel_at_period_end,
                'price': {
                    'id': sub.price.stripe_price_id,
                    'amount': sub.price.unit_amount / 100,
                    'currency': sub.price.currency,
                    'product': {
                        'name': sub.price.product.name,
                        'description': sub.price.product.description
                    }
                }
            } for sub in subscriptions]
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)

@login_required
def create_payment_method(request):
    """
    Create a new payment method for the authenticated user
    """
    try:
        data = json.loads(request.body)
        payment_method_id = data.get('payment_method_id')
        
        if not payment_method_id:
            return JsonResponse({
                'error': 'Payment method ID is required'
            }, status=400)
        
        # Get or create customer
        customer = StripeService.create_or_update_customer(request.user)
        
        # Attach payment method to customer
        stripe.PaymentMethod.attach(
            payment_method_id,
            customer=customer.stripe_customer_id
        )
        
        # Set as default payment method
        stripe.Customer.modify(
            customer.stripe_customer_id,
            invoice_settings={
                'default_payment_method': payment_method_id
            }
        )
        
        return JsonResponse({
            'status': 'success'
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)

@login_required
def list_payment_methods(request):
    """
    List all payment methods for the authenticated user
    """
    try:
        customer = get_object_or_404(Customer, user=request.user)
        payment_methods = stripe.PaymentMethod.list(
            customer=customer.stripe_customer_id,
            type='card'
        )
        
        return JsonResponse({
            'payment_methods': payment_methods.data
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)

@login_required
def create_setup_intent(request):
    """
    Create a SetupIntent for saving a payment method
    """
    try:
        setup_intent = stripe.SetupIntent.create()
        
        return JsonResponse({
            'client_secret': setup_intent.client_secret
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)

@login_required
def create_enterprise_payment(request):
    """
    Create a payment from a personal user to an enterprise
    """
    try:
        data = json.loads(request.body)
        enterprise_id = data.get('enterprise_id')
        amount = data.get('amount')  # Amount in cents
        payment_method_id = data.get('payment_method_id')
        
        if not all([enterprise_id, amount, payment_method_id]):
            return JsonResponse({
                'error': 'Enterprise ID, amount and payment method ID are required'
            }, status=400)
        
        # Get or create customer
        customer = StripeService.create_or_update_customer(request.user)
        
        # Get enterprise
        enterprise = get_object_or_404(EnterpriseAccount, id=enterprise_id)
        
        # Get active platform fee
        platform_fee = PlatformFee.objects.filter(active=True).first()
        if not platform_fee:
            return JsonResponse({
                'error': 'No active platform fee found'
            }, status=400)
        
        # Calculate platform fee amount
        platform_fee_amount = int((amount * platform_fee.percentage / 100) + platform_fee.fixed_amount)
        
        # Create payment intent with platform fee
        payment_intent = stripe.PaymentIntent.create(
            amount=amount,
            currency=settings.STRIPE_DEFAULT_CURRENCY,
            customer=customer.stripe_customer_id,
            payment_method=payment_method_id,
            confirm=True,
            transfer_data={
                'destination': enterprise.stripe_account_id,
                'amount': amount - platform_fee_amount,
            },
            application_fee_amount=platform_fee_amount,
        )
        
        # Create enterprise payment record
        enterprise_payment = EnterprisePayment.objects.create(
            customer=customer,
            enterprise=enterprise,
            amount=amount,
            platform_fee=platform_fee,
            platform_fee_amount=platform_fee_amount,
            status=payment_intent.status,
            stripe_payment_intent_id=payment_intent.id
        )
        
        return JsonResponse({
            'payment_intent_id': payment_intent.id,
            'client_secret': payment_intent.client_secret,
            'enterprise_payment_id': str(enterprise_payment.id)
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)

@login_required
def list_enterprise_payments(request):
    """
    List all enterprise payments for the authenticated user
    """
    try:
        customer = get_object_or_404(Customer, user=request.user)
        payments = EnterprisePayment.objects.filter(customer=customer).select_related('enterprise', 'platform_fee')
        
        return JsonResponse({
            'payments': [{
                'id': str(payment.id),
                'enterprise': payment.enterprise.name,
                'amount': payment.amount,
                'platform_fee_amount': payment.platform_fee_amount,
                'net_amount': payment.net_amount,
                'status': payment.status,
                'created_at': payment.created_at
            } for payment in payments]
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)

@login_required
def list_platform_services(request):
    """
    List all available platform services (Service, SubscriptionPlan, Solution)
    """
    try:
        # Get all active products
        products = Product.objects.filter(active=True).prefetch_related('prices')
        
        services = []
        for product in products:
            # Get the corresponding content model
            if product.product_type == 'service':
                content_model = product.service
            elif product.product_type == 'subscription':
                content_model = product.subscription_plan
            elif product.product_type == 'solution':
                content_model = product.solution
            else:
                continue
                
            if not content_model or not hasattr(content_model, 'is_active') or not content_model.is_active:
                continue
                
            # Get the price
            price = product.prices.filter(active=True).first()
            if not price:
                continue
                
            services.append({
                'id': str(product.id),
                'name': product.name,
                'description': product.description,
                'type': product.product_type,
                'price_id': price.stripe_price_id,
                'amount': price.unit_amount / 100,
                'currency': price.currency,
                'is_recurring': bool(price.recurring),
                'recurring_interval': price.recurring.get('interval') if price.recurring else None,
                'button_text': getattr(content_model, 'button_text', ''),
                'features': getattr(content_model, 'features', []),
            })
        
        return JsonResponse({
            'services': services
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)

@login_required
def list_enterprise_services(request):
    """
    List all available enterprise services
    """
    try:
        # Get all active enterprise services
        services = EnterpriseService.objects.filter(active=True).select_related('enterprise')
        
        return JsonResponse({
            'services': [{
                'id': str(service.id),
                'name': service.name,
                'description': service.description,
                'price': float(service.price),
                'price_id': service.stripe_price_id,
                'enterprise': {
                    'id': str(service.enterprise.id),
                    'name': service.enterprise.name,
                }
            } for service in services]
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=400)

class BillingServiceSerializer(serializers.ModelSerializer):
    content_id = serializers.UUIDField(source='content_id')
    type = serializers.CharField(source='product_type')
    price_id = serializers.SerializerMethodField()
    amount = serializers.SerializerMethodField()
    currency = serializers.SerializerMethodField()
    is_recurring = serializers.SerializerMethodField()
    recurring_interval = serializers.SerializerMethodField()
    button_text = serializers.SerializerMethodField()
    features = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id', 'content_id', 'name', 'description', 'type',
            'price_id', 'amount', 'currency', 'is_recurring',
            'recurring_interval', 'button_text', 'features'
        ]

    def get_price_id(self, obj):
        try:
            return obj.prices.first().stripe_price_id
        except:
            return None

    def get_amount(self, obj):
        try:
            return obj.prices.first().unit_amount / 100
        except:
            return 0

    def get_currency(self, obj):
        try:
            return obj.prices.first().currency
        except:
            return 'usd'

    def get_is_recurring(self, obj):
        try:
            return bool(obj.prices.first().recurring)
        except:
            return False

    def get_recurring_interval(self, obj):
        try:
            return obj.prices.first().recurring.get('interval')
        except:
            return None

    def get_button_text(self, obj):
        if obj.service:
            return obj.service.button_text
        elif obj.subscription_plan:
            return obj.subscription_plan.button_text
        elif obj.solution:
            return obj.solution.button_text
        return "Get Started"

    def get_features(self, obj):
        if obj.service:
            return obj.service.features
        elif obj.subscription_plan:
            return obj.subscription_plan.features
        elif obj.solution:
            return obj.solution.features
        return []

class BillingServiceViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = BillingServiceSerializer
    
    def get_queryset(self):
        return Product.objects.filter(active=True)
    
    def get_serializer_class(self):
        return BillingServiceSerializer

class ServicePaymentViewSet(viewsets.ModelViewSet):
    queryset = ServicePayment.objects.all()
    serializer_class = ServicePaymentSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['post'])
    def create_checkout_session(self, request):
        service_id = request.data.get('service_id')
        service = get_object_or_404(Service, id=service_id)
        
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=[{
                'price_data': {
                    'currency': 'usd',
                    'unit_amount': int(service.price * 100),
                    'product_data': {
                        'name': service.name,
                        'description': service.description,
                    },
                },
                'quantity': 1,
            }],
            mode='payment',
            success_url=f'{settings.SITE_URL}/services/{service.id}/success',
            cancel_url=f'{settings.SITE_URL}/services/{service.id}/cancel',
            metadata={
                'user_id': request.user.id,
                'service_id': service.id,
                'type': 'one_time'
            }
        )
        
        return Response({'sessionId': checkout_session.id})

class SubscriptionViewSet(viewsets.ModelViewSet):
    queryset = Subscription.objects.all()
    serializer_class = SubscriptionSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['post'])
    def create_checkout_session(self, request):
        plan_id = request.data.get('plan_id')
        plan = get_object_or_404(SubscriptionPlan, id=plan_id)
        
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=[{
                'price': plan.stripe_price_id,
                'quantity': 1,
            }],
            mode='subscription',
            success_url=f'{settings.SITE_URL}/subscriptions/{plan.id}/success',
            cancel_url=f'{settings.SITE_URL}/subscriptions/{plan.id}/cancel',
            metadata={
                'user_id': request.user.id,
                'plan_id': plan.id,
                'type': 'subscription'
            }
        )
        
        return Response({'sessionId': checkout_session.id})

class EnterprisePaymentViewSet(viewsets.ModelViewSet):
    queryset = EnterprisePayment.objects.all()
    serializer_class = EnterprisePaymentSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['post'])
    def create_checkout_session(self, request):
        enterprise_id = request.data.get('enterprise_id')
        service_id = request.data.get('service_id')
        
        enterprise = get_object_or_404(EnterpriseAccount, id=enterprise_id)
        service = get_object_or_404(EnterpriseService, id=service_id)
        
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=[{
                'price_data': {
                    'currency': 'usd',
                    'unit_amount': int(service.price * 100),
                    'product_data': {
                        'name': service.name,
                        'description': service.description,
                    },
                },
                'quantity': 1,
            }],
            mode='payment',
            success_url=f'{settings.SITE_URL}/enterprise/{enterprise.id}/services/{service.id}/success',
            cancel_url=f'{settings.SITE_URL}/enterprise/{enterprise.id}/services/{service.id}/cancel',
            payment_intent_data={
                'application_fee_amount': int(service.price * 100 * 0.1),  # 10% platform fee
                'transfer_data': {
                    'destination': enterprise.stripe_account_id,
                },
            },
            metadata={
                'user_id': request.user.id,
                'enterprise_id': enterprise.id,
                'service_id': service.id,
                'type': 'enterprise_service'
            }
        )
        
        return Response({'sessionId': checkout_session.id})

class ServiceAccessViewSet(viewsets.ModelViewSet):
    queryset = ServiceAccess.objects.all()
    serializer_class = ServiceAccessSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ServiceAccess.objects.filter(user=self.request.user)

class SubscriptionAccessViewSet(viewsets.ModelViewSet):
    queryset = SubscriptionAccess.objects.all()
    serializer_class = SubscriptionAccessSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return SubscriptionAccess.objects.filter(user=self.request.user)

class SolutionAccessViewSet(viewsets.ModelViewSet):
    queryset = SolutionAccess.objects.all()
    serializer_class = SolutionAccessSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return SolutionAccess.objects.filter(enterprise__user=self.request.user)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_service_access(request, service_id):
    """
    Check if a user has access to a specific service
    """
    try:
        # Get the service access record
        access = ServiceAccess.objects.filter(
            user=request.user,
            service_id=service_id,
            status='active'
        ).first()
        
        if not access:
            return Response({
                'has_access': False,
                'message': 'No active access found for this service'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check if access has expired
        if access.access_expires_at and access.access_expires_at < timezone.now():
            access.status = 'expired'
            access.save()
            return Response({
                'has_access': False,
                'message': 'Access has expired',
                'expired_at': access.access_expires_at
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Return access data including analysis_access_data
        return Response({
            'has_access': True,
            'access_granted_at': access.access_granted_at,
            'access_expires_at': access.access_expires_at,
            'analysis_access_data': access.analysis_access_data
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_subscription_access(request, subscription_id):
    """
    Check if a user has access to a specific subscription
    """
    try:
        # Get the subscription access record
        access = SubscriptionAccess.objects.filter(
            user=request.user,
            subscription_id=subscription_id,
            status='active'
        ).first()
        
        if not access:
            return Response({
                'has_access': False,
                'message': 'No active access found for this subscription'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return access data
        return Response({
            'has_access': True,
            'access_granted_at': access.access_granted_at,
            'subscription_status': access.subscription.status,
            'current_period_end': access.subscription.current_period_end
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_solution_access(request, solution_id):
    """
    Check if an enterprise has access to a specific solution
    """
    try:
        # Check if user is associated with an enterprise
        if not hasattr(request.user, 'enterprise'):
            return Response({
                'has_access': False,
                'message': 'User is not associated with an enterprise'
            }, status=status.HTTP_403_FORBIDDEN)
        
        enterprise = request.user.enterprise
        
        # Get the solution access record
        access = SolutionAccess.objects.filter(
            enterprise=enterprise,
            solution_id=solution_id,
            status='active'
        ).first()
        
        if not access:
            return Response({
                'has_access': False,
                'message': 'No active access found for this solution'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Return access data
        return Response({
            'has_access': True,
            'access_granted_at': access.access_granted_at,
            'subscription_status': access.subscription.status,
            'current_period_end': access.subscription.current_period_end
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_user_services(request):
    """
    List all services that a user has access to
    """
    try:
        # Get all active service access records
        service_access = ServiceAccess.objects.filter(
            user=request.user,
            status='active'
        ).select_related('service')
        
        # Get all active subscription access records
        subscription_access = SubscriptionAccess.objects.filter(
            user=request.user,
            status='active'
        ).select_related('subscription', 'subscription__price', 'subscription__price__product')
        
        # Format service access data
        services = []
        for access in service_access:
            if access.access_expires_at and access.access_expires_at < timezone.now():
                access.status = 'expired'
                access.save()
                continue
                
            services.append({
                'id': access.service.id,
                'name': access.service.name,
                'type': 'service',
                'access_granted_at': access.access_granted_at,
                'access_expires_at': access.access_expires_at,
                'analysis_access_data': access.analysis_access_data
            })
        
        # Format subscription access data
        subscriptions = []
        for access in subscription_access:
            subscriptions.append({
                'id': access.subscription.price.product.id,
                'name': access.subscription.price.product.name,
                'type': 'subscription',
                'access_granted_at': access.access_granted_at,
                'subscription_status': access.subscription.status,
                'current_period_end': access.subscription.current_period_end
            })
        
        return Response({
            'services': services,
            'subscriptions': subscriptions
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_enterprise_solutions(request):
    """
    List all solutions that an enterprise has access to
    """
    try:
        # Check if user is associated with an enterprise
        if not hasattr(request.user, 'enterprise'):
            return Response({
                'has_access': False,
                'message': 'User is not associated with an enterprise'
            }, status=status.HTTP_403_FORBIDDEN)
        
        enterprise = request.user.enterprise
        
        # Get all active solution access records
        solution_access = SolutionAccess.objects.filter(
            enterprise=enterprise,
            status='active'
        ).select_related('solution', 'subscription')
        
        # Format solution access data
        solutions = []
        for access in solution_access:
            solutions.append({
                'id': access.solution.id,
                'name': access.solution.name,
                'access_granted_at': access.access_granted_at,
                'subscription_status': access.subscription.status,
                'current_period_end': access.subscription.current_period_end
            })
        
        return Response({
            'solutions': solutions
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_payment_history(request):
    """
    List payment history for the authenticated user
    """
    try:
        # Get service payments
        service_payments = ServicePayment.objects.filter(
            user=request.user
        ).select_related('service').order_by('-created_at')
        
        # Get subscription payments
        subscription_payments = Subscription.objects.filter(
            customer__user=request.user
        ).select_related('price', 'price__product').order_by('-created_at')
        
        # Get enterprise payments
        enterprise_payments = EnterprisePayment.objects.filter(
            customer__user=request.user
        ).select_related('enterprise', 'service').order_by('-created_at')
        
        # Format service payments
        service_payment_data = []
        for payment in service_payments:
            service_payment_data.append({
                'id': payment.id,
                'service_id': payment.service.id,
                'service_name': payment.service.name,
                'amount_cents': payment.amount,  # Original amount in cents
                'amount_dollars': payment.amount / 100,  # Converted amount in dollars
                'currency': payment.currency,
                'status': payment.status,
                'created_at': payment.created_at,
                'payment_method': payment.metadata.get('payment_method', 'card'),
                'dna_analysis_id': str(payment.dna_analysis.id) if payment.dna_analysis else None,
                'metadata': payment.metadata
            })
        
        # Format subscription payments
        subscription_payment_data = []
        for subscription in subscription_payments:
            subscription_payment_data.append({
                'id': subscription.id,
                'product_id': subscription.price.product.id,
                'product_name': subscription.price.product.name,
                'amount_cents': subscription.price.unit_amount,  # Original amount in cents
                'amount_dollars': subscription.price.unit_amount / 100,  # Converted amount in dollars
                'currency': subscription.price.currency,
                'status': subscription.status,
                'current_period_start': subscription.current_period_start,
                'current_period_end': subscription.current_period_end,
                'cancel_at_period_end': subscription.cancel_at_period_end,
                'metadata': subscription.metadata
            })
        
        # Format enterprise payments
        enterprise_payment_data = []
        for payment in enterprise_payments:
            payment_data = {
                'id': payment.id,
                'enterprise_id': payment.enterprise.id,
                'enterprise_name': payment.enterprise.name,
                'amount_cents': payment.amount,  # Original amount in cents
                'amount_dollars': payment.amount / 100,  # Converted amount in dollars
                'status': payment.status,
                'created_at': payment.created_at,
                'metadata': payment.metadata
            }
            
            # Add service info if available
            if payment.service:
                payment_data.update({
                    'service_id': payment.service.id,
                    'service_name': payment.service.name,
                    'platform_fee_amount_cents': payment.platform_fee_amount,  # Original platform fee in cents
                    'platform_fee_amount_dollars': payment.platform_fee_amount / 100,  # Converted platform fee in dollars
                    'net_amount_cents': payment.net_amount,  # Original net amount in cents
                    'net_amount_dollars': payment.net_amount / 100,  # Converted net amount in dollars
                })
            
            enterprise_payment_data.append(payment_data)
        
        return Response({
            'service_payments': service_payment_data,
            'subscription_payments': subscription_payment_data,
            'enterprise_payments': enterprise_payment_data
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_payment_details(request, payment_id, payment_type):
    """
    Get detailed information about a specific payment
    """
    try:
        if payment_type == 'service':
            payment = get_object_or_404(ServicePayment, id=payment_id, user=request.user)
            payment_data = {
                'id': payment.id,
                'service_id': payment.service.id,
                'service_name': payment.service.name,
                'amount': payment.amount / 100,  # Convert cents to dollars
                'currency': payment.currency,
                'status': payment.status,
                'created_at': payment.created_at,
                'payment_method': payment.metadata.get('payment_method', 'card'),
                'stripe_payment_intent_id': payment.stripe_payment_intent_id,
                'access_granted': ServiceAccess.objects.filter(payment=payment).exists()
            }
        elif payment_type == 'subscription':
            subscription = get_object_or_404(Subscription, id=payment_id, customer__user=request.user)
            payment_data = {
                'id': subscription.id,
                'product_id': subscription.price.product.id,
                'product_name': subscription.price.product.name,
                'amount': subscription.price.unit_amount / 100,  # Convert cents to dollars
                'currency': subscription.price.currency,
                'status': subscription.status,
                'current_period_start': subscription.current_period_start,
                'current_period_end': subscription.current_period_end,
                'cancel_at_period_end': subscription.cancel_at_period_end,
                'canceled_at': subscription.canceled_at,
                'trial_end': subscription.trial_end,
                'stripe_subscription_id': subscription.stripe_subscription_id
            }
        elif payment_type == 'enterprise':
            payment = get_object_or_404(EnterprisePayment, id=payment_id, customer__user=request.user)
            payment_data = {
                'id': payment.id,
                'enterprise_id': payment.enterprise.id,
                'enterprise_name': payment.enterprise.name,
                'service_id': payment.service.id,
                'service_name': payment.service.name,
                'amount': payment.amount / 100,  # Convert cents to dollars
                'platform_fee_amount': payment.platform_fee_amount / 100,  # Convert cents to dollars
                'net_amount': payment.net_amount / 100,  # Convert cents to dollars
                'status': payment.status,
                'created_at': payment.created_at,
                'stripe_payment_intent_id': payment.stripe_payment_intent_id
            }
        else:
            return Response({
                'error': 'Invalid payment type'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(payment_data)
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PaymentSuccessView(BasePaymentView):
    """Handle successful payment"""
    
    def get(self, request, *args, **kwargs):
        try:
            session_id = request.GET.get('session_id')
            if not session_id:
                return JsonResponse({
                    'error': 'Session ID is required'
                }, status=400)
            
            # Retrieve the session
            session = stripe.checkout.Session.retrieve(session_id)
            
            # Get the payment details
            payment_intent = session.payment_intent
            customer_id = session.customer
            price_id = session.metadata.get('price_id')
            
            # Get customer
            customer = Customer.objects.get(stripe_customer_id=customer_id)
            
            # Get price
            price = Price.objects.get(stripe_price_id=price_id)
            
            # Create payment record
            payment = ServicePayment.objects.create(
                user=customer.user,
                service=price.product.service,
                amount=session.amount_total / 100,  # Convert from cents
                currency=session.currency,
                status='succeeded',
                stripe_payment_intent_id=payment_intent,
                stripe_session_id=session_id
            )
            
            # Create service access
            ServiceAccess.objects.create(
                user=customer.user,
                service=price.product.service,
                payment=payment,
                status='active',
                expires_at=timezone.now() + timedelta(days=price.product.service.access_duration_days)
            )
            
            return JsonResponse({
                'success': True,
                'payment_id': payment.id,
                'service_id': price.product.service.id
            })
            
        except Exception as e:
            logger.error(f"Error processing payment success: {str(e)}")
            return JsonResponse({
                'error': str(e)
            }, status=500)

class PaymentCancelView(BasePaymentView):
    """Handle cancelled payment"""
    
    def get(self, request, *args, **kwargs):
        return JsonResponse({
            'success': False,
            'message': 'Payment was cancelled'
        })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def revoke_service_access(request, service_id):
    """
    Revoke access to a service
    """
    try:
        # Get service access
        service_access = get_object_or_404(
            ServiceAccess,
            service_id=service_id,
            user=request.user,
            status='active'
        )

        # Revoke access
        StripeService.revoke_service_access(service_access)

        return Response({
            'status': 'success',
            'message': 'Service access revoked successfully'
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def revoke_subscription_access(request, subscription_id):
    """
    Revoke access to a subscription
    """
    try:
        # Get subscription access
        subscription_access = get_object_or_404(
            SubscriptionAccess,
            subscription_id=subscription_id,
            user=request.user,
            status='active'
        )

        # Revoke access
        StripeService.revoke_subscription_access(subscription_access)

        return Response({
            'status': 'success',
            'message': 'Subscription access revoked successfully'
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def revoke_solution_access(request, solution_id):
    """
    Revoke access to a solution
    """
    try:
        # Check if user is associated with an enterprise
        if not hasattr(request.user, 'enterprise'):
            return Response({
                'error': 'User is not associated with an enterprise'
            }, status=status.HTTP_403_FORBIDDEN)

        enterprise = request.user.enterprise

        # Get solution access
        solution_access = get_object_or_404(
            SolutionAccess,
            solution_id=solution_id,
            enterprise=enterprise,
            status='active'
        )

        # Revoke access
        StripeService.revoke_solution_access(solution_access)

        return Response({
            'status': 'success',
            'message': 'Solution access revoked successfully'
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

class BillingViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def list_dna_analyses(self, request):
        """
        List all DNA analysis records that have been paid for by the user
        """
        try:
            # Get all DNA analyses with completed payments for the authenticated user
            dna_analyses = DNAAnalysis.objects.filter(
                user=request.user,
                service_payments__status='completed'
            ).distinct().order_by('-created_at')

            # Format response data
            analyses_data = []
            for analysis in dna_analyses:
                # Get associated service payments
                service_payments = ServicePayment.objects.filter(
                    dna_analysis=analysis,
                    status='completed'
                ).select_related('service')
                
                # Get service access records
                service_access = ServiceAccess.objects.filter(
                    payment__dna_analysis=analysis,
                    status='active'
                ).select_related('service', 'payment')
                
                # Format service data
                services_data = []
                for payment in service_payments:
                    service = payment.service
                    access = next((a for a in service_access if a.service_id == service.id), None)
                    
                    service_data = {
                        'id': str(service.id),
                        'name': service.name,
                        'description': service.description,
                        'payment': {
                            'id': str(payment.id),
                            'amount': payment.amount / 100,  # Convert cents to dollars
                            'currency': payment.currency,
                            'status': payment.status,
                            'created_at': payment.created_at
                        },
                        'access': {
                            'id': str(access.id) if access else None,
                            'status': access.status if access else None,
                            'access_granted_at': access.access_granted_at if access else None,
                            'access_expires_at': access.access_expires_at if access else None,
                            'last_accessed_at': access.last_accessed_at if access else None
                        } if access else None
                    }
                    services_data.append(service_data)
                
                analysis_data = {
                    'id': str(analysis.id),
                    'status': analysis.status,
                    'created_at': analysis.created_at,
                    'updated_at': analysis.updated_at,
                    'parameters': analysis.parameters,
                    'notes': analysis.notes,
                    'is_paid': analysis.is_paid(),
                    'services': services_data,
                    'result_files': analysis.result_files,
                    'stripe_session_id': analysis.stripe_session_id
                }
                analyses_data.append(analysis_data)
            
            return Response({
                'count': len(analyses_data),
                'results': analyses_data
            })
            
        except Exception as e:
            logger.error(f"Error listing DNA analyses: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def checkout_dna_services(self, request):
        """
        Create a checkout session for DNA services
        """
        try:
            data = request.data
            services_data = data.get('services', [])  # List of services with their promo codes
            redirect_url = data.get('redirect_url')
            input_files = data.get('input_files', [])  # List of GSFileManager ids
            if not services_data:
                return Response({
                    'error': 'At least one service is required'
                }, status=400)
            if not redirect_url:
                return Response({
                    'error': 'Redirect URL is required'
                }, status=400)
            # Validate input_files (must be owned by user)
            gs_files = []
            if input_files:
                gs_files = list(GSFileManager.objects.filter(id__in=input_files, uploaded_file__user=request.user))
                if len(gs_files) != len(input_files):
                    return Response({'error': 'Some files are invalid or do not belong to you.'}, status=400)
            # Get or create customer
            customer = StripeService.create_or_update_customer(request.user)
            # Get services and calculate discounted prices
            services = []
            total_amount = 0
            # NEW: Extract dna_codes and depth if present in any service_item
            dna_codes = None
            depth = None
            for service_item in services_data:
                service_id = service_item.get('service_id')
                promo_code = service_item.get('promo_code')
                # Check for dna_codes and depth
                if service_item.get('dna_codes') is not None:
                    dna_codes = service_item.get('dna_codes')
                if service_item.get('depth') is not None:
                    depth = service_item.get('depth')
                try:
                    service = Service.objects.get(id=service_id)
                    # 1. Tính giá tốt nhất và promotion áp dụng
                    discounted_price, applied_promotion, _ = service.get_best_discounted_price(promo_code)
                    # 2. Lấy đúng Price ID trên Stripe
                    price_id = StripeService.get_or_create_discounted_price(service, discounted_price, applied_promotion)
                    services.append({
                        'service': service,
                        'price_id': price_id,
                        'unit_amount': int(discounted_price * 100)
                    })
                    total_amount += int(discounted_price * 100)
                except Service.DoesNotExist:
                    return Response({'error': f'Service {service_id} not found'}, status=404)
            # Create base session data
            session_data = {
                'customer': customer.stripe_customer_id,
                'payment_method_types': ['card'],
                'line_items': [{
                    'price': service['price_id'],
                    'quantity': 1
                } for service in services],
                'mode': 'payment',
                'success_url': redirect_url,
                'cancel_url': redirect_url,
                'metadata': {
                    'service_ids': json.dumps([str(s['service'].id) for s in services]),  # Convert UUIDs to strings
                    'input_files': json.dumps(input_files) if input_files else '',
                    'payment_type': 'dna_analysis'
                }
            }
            # Không truyền promotion code vào session_data nữa
            # Create checkout session
            try:
                session = stripe.checkout.Session.create(**session_data)
                # Tạo DNAAnalysis và gán input_files
                dna_analysis_kwargs = {
                    'user': request.user,
                    'parameters': {},
                    'status': 'pending',
                    'stripe_session_id': session.id
                }
                if dna_codes is not None:
                    dna_analysis_kwargs['dna_codes'] = dna_codes
                if depth is not None:
                    dna_analysis_kwargs['depth'] = depth
                dna_analysis = DNAAnalysis.objects.create(**dna_analysis_kwargs)
                if gs_files:
                    dna_analysis.input_files.set(gs_files)
                dna_analysis.save()
                return Response({
                    'session_id': session.id,
                    'url': session.url
                })
            except stripe.error.StripeError as e:
                logger.error(f"Stripe error creating checkout session: {str(e)}")
                return Response({
                    'error': f'Payment processing error: {str(e)}'
                }, status=400)
        except Exception as e:
            logger.error(f"Error creating checkout session: {str(e)}")
            return Response({
                'error': f'Error creating checkout session: {str(e)}'
            }, status=500)

    @action(detail=False, methods=['post'])
    def handle_dna_payment_success(self, request):
        """
        Handle successful payment for DNA analysis services
        """
        try:
            session_id = request.data.get('session_id')
            if not session_id:
                return Response(
                    {'error': 'Session ID is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Retrieve Stripe session
            stripe.api_key = settings.STRIPE_SECRET_KEY
            session = stripe.checkout.Session.retrieve(session_id)

            # Verify this is a DNA analysis payment
            if session.metadata.get('payment_type') != 'dna_analysis':
                return Response(
                    {'error': 'Not a DNA analysis payment'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            service_ids_json = session.metadata.get('service_ids', '[]')
            
            # Parse service IDs from JSON string
            try:
                service_ids = json.loads(service_ids_json)
                if not service_ids:
                    logger.error(f"No service IDs found in session metadata for session {session.id}")
                    return JsonResponse({
                        'error': 'No service IDs found in session metadata'
                    }, status=400)
            except json.JSONDecodeError:
                logger.error(f"Invalid service IDs JSON in session metadata for session {session.id}")
                return JsonResponse({
                    'error': 'Invalid service IDs format in session metadata'
                }, status=400)
            
            
            # Create DNA analysis record
            dna_analysis = DNAAnalysis.objects.create(
                user=request.user,
                status='processing',
                stripe_session_id=session.id
            )

            # Create service payments and access records
            with transaction.atomic():
                for service_id in service_ids:
                    try:
                        service = Service.objects.get(id=service_id)
                        
                        # Create service payment
                        service_payment = ServicePayment.objects.create(
                            user=request.user,
                            service=service,
                            amount=int(service.price * 100),  # Convert to cents
                            currency='usd',
                            status='completed',
                            stripe_payment_intent_id=session.payment_intent,
                            dna_analysis=dna_analysis
                        )
                        
                        # Create service access
                        ServiceAccess.objects.create(
                            user=request.user,
                            service=service,
                            payment=service_payment,
                            status='active',
                            access_expires_at=timezone.now() + timedelta(days=30),  # Default 30 days access
                            analysis_access_data={
                                'dna_analysis_id': str(dna_analysis.id),
                            }
                        )
                    except Service.DoesNotExist:
                        # Log error but continue with other services
                        logger.error(f"Service {service_id} not found for DNA analysis {dna_analysis.id}")

            # TODO: Trigger DNA analysis processing
            # This would be handled by your background task system

            return Response({
                'status': 'success',
                'dna_analysis_id': str(dna_analysis.id)
            })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_dna_analysis_status(request, dna_analysis_id):
    """
    Check the status of a DNA analysis and its associated services
    """
    try:
        # Get DNA analysis
        dna_analysis = get_object_or_404(
            DNAAnalysis,
            id=dna_analysis_id,
            user=request.user
        )
        
        # Get associated service payments
        service_payments = ServicePayment.objects.filter(
            dna_analysis=dna_analysis,
            status='completed'
        ).select_related('service')
        
        # Get service access records
        service_access = ServiceAccess.objects.filter(
            payment__dna_analysis=dna_analysis,
            status='active'
        ).select_related('service', 'payment')
        
        # Format service data with payment and access details
        services_data = []
        for payment in service_payments:
            service = payment.service
            access = next((a for a in service_access if a.service_id == service.id), None)
            
            service_data = {
                'id': str(service.id),
                'name': service.name,
                'description': service.description,
                'payment': {
                    'id': str(payment.id),
                    'amount': payment.amount / 100,  # Convert cents to dollars
                    'currency': payment.currency,
                    'status': payment.status,
                    'created_at': payment.created_at
                },
                'access': {
                    'id': str(access.id) if access else None,
                    'status': access.status if access else None,
                    'access_granted_at': access.access_granted_at if access else None,
                    'access_expires_at': access.access_expires_at if access else None,
                    'last_accessed_at': access.last_accessed_at if access else None
                } if access else None
            }
            services_data.append(service_data)
        
        # Return DNA analysis data
        return Response({
            'id': str(dna_analysis.id),
            'status': dna_analysis.status,
            'created_at': dna_analysis.created_at,
            'updated_at': dna_analysis.updated_at,
            'parameters': dna_analysis.parameters,
            'notes': dna_analysis.notes,
            'is_paid': dna_analysis.is_paid(),
            'services': services_data,
            'result_files': dna_analysis.result_files,
            'stripe_session_id': dna_analysis.stripe_session_id
        })
        
    except Exception as e:
        logger.error(f"Error checking DNA analysis status: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UserPaymentProfileViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing user payment profiles
    """
    serializer_class = UserPaymentProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserPaymentProfile.objects.filter(user=self.request.user)

    def get_object(self):
        profile, _ = UserPaymentProfile.objects.get_or_create(user=self.request.user)
        return profile

    @action(detail=False, methods=['post'])
    def create_connect_account(self, request):
        """
        Create a Stripe Connect account for the user
        """
        try:
            service = UserPaymentService()
            account_link = service.create_stripe_connect_account(request.user)
            return Response({'account_link': account_link})
        except Exception as e:
            return Response({'error': str(e)}, status=400)

    @action(detail=False, methods=['post'])
    def update_donation_settings(self, request):
        """
        Update donation settings for the user's payment profile
        """
        try:
            profile = self.get_object()
            serializer = UserPaymentProfileSerializer(
                profile,
                data=request.data,
                partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=400)

class UserTransferViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing user transfers
    """
    serializer_class = UserTransferSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserTransfer.objects.filter(
            Q(sender=self.request.user) | Q(receiver=self.request.user)
        ).order_by('-created_at')

    def perform_create(self, serializer):
        try:
            service = UserPaymentService()
            transfer, client_secret = service.create_transfer(
                sender=self.request.user,
                receiver=serializer.validated_data['receiver'],
                amount=serializer.validated_data['amount'],
                currency=serializer.validated_data.get('currency', 'usd'),
                message=serializer.validated_data.get('message', ''),
                transfer_type=serializer.validated_data.get('transfer_type', 'transfer')
            )
            serializer.instance = transfer
            serializer.validated_data['client_secret'] = client_secret
        except Exception as e:
            raise ValidationError(str(e))

    @action(detail=True, methods=['post'])
    def confirm_payment(self, request, pk=None):
        """
        Confirm a pending transfer payment
        """
        try:
            transfer = self.get_object()
            if transfer.status != 'pending':
                raise ValidationError('Transfer is not in pending status')

            service = UserPaymentService()
            service.confirm_transfer(transfer)
            return Response({'status': 'success'})
        except Exception as e:
            return Response({'error': str(e)}, status=400)

    @action(detail=False, methods=['get'])
    def sent_transfers(self, request):
        """
        Get all transfers sent by the user
        """
        transfers = UserTransfer.objects.filter(sender=request.user).order_by('-created_at')
        page = self.paginate_queryset(transfers)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(transfers, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def received_transfers(self, request):
        """
        Get all transfers received by the user
        """
        transfers = UserTransfer.objects.filter(receiver=request.user).order_by('-created_at')
        page = self.paginate_queryset(transfers)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(transfers, many=True)
        return Response(serializer.data)
