"""
DEPRECATED: This file is maintained for backward compatibility only.
New code should use the models in billing.models package.

This module will be removed in a future version.
"""
import warnings
from django.db import models
from django.conf import settings
from config.models import BaseModel
from content_management.models import Service, SubscriptionPlan, Solution
from analysis.models.base import DNAAnalysis

# Issue deprecation warning
warnings.warn(
    "billing.models is deprecated. Use billing.models.* modules instead. "
    "This module will be removed in a future version.",
    DeprecationWarning,
    stacklevel=2
)

class Product(BaseModel):
    """
    Represents a Stripe Product for SaaS platform services
    Maps to your Service, SubscriptionPlan, and Solution models from content_management
    """
    stripe_product_id = models.CharField(max_length=255, unique=True)
    name = models.CharField(max_length=255)
    content_id = models.UUIDField(unique=True, null=True, blank=True)
    description = models.TextField(blank=True)
    active = models.BooleanField(default=True)
    metadata = models.JSONField(default=dict, blank=True)
    product_type = models.CharField(
        max_length=50,
        choices=[
            ('service', 'Service'),
            ('subscription', 'Subscription'),
            ('solution', 'Solution')
        ]
    )
    service = models.ForeignKey(Service, on_delete=models.SET_NULL, null=True, blank=True, related_name='stripe_products')
    subscription_plan = models.ForeignKey(SubscriptionPlan, on_delete=models.SET_NULL, null=True, blank=True, related_name='stripe_products')
    solution = models.ForeignKey(Solution, on_delete=models.SET_NULL, null=True, blank=True, related_name='stripe_products')

    def __str__(self):
        return f"{self.name} ({self.product_type})"

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.product_type == 'service' and not self.service:
            raise ValidationError('Service must be set for service product type')
        elif self.product_type == 'subscription' and not self.subscription_plan:
            raise ValidationError('Subscription plan must be set for subscription product type')
        elif self.product_type == 'solution' and not self.solution:
            raise ValidationError('Solution must be set for solution product type')
        
        if self.product_type != 'service' and self.service:
            raise ValidationError('Service should not be set for non-service product type')
        if self.product_type != 'subscription' and self.subscription_plan:
            raise ValidationError('Subscription plan should not be set for non-subscription product type')
        if self.product_type != 'solution' and self.solution:
            raise ValidationError('Solution should not be set for non-solution product type')

class Price(BaseModel):
    """
    Represents a Stripe Price for SaaS platform services
    """
    stripe_price_id = models.CharField(max_length=255, unique=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='prices')
    active = models.BooleanField(default=True)
    currency = models.CharField(max_length=3, default='usd')
    unit_amount = models.IntegerField(help_text="Amount in cents")
    recurring = models.JSONField(null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.product.name} - {self.unit_amount/100} {self.currency}"

class Customer(BaseModel):
    """
    Represents a Stripe Customer
    """
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='billing_customer')
    stripe_customer_id = models.CharField(max_length=255, unique=True)
    email = models.EmailField()
    name = models.CharField(max_length=255, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return self.email

class ServicePayment(BaseModel):
    """
    Tracks payments for one-time services
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='service_payments')
    service = models.ForeignKey('content_management.Service', on_delete=models.CASCADE, related_name='payments')
    services = models.JSONField(default=list, blank=True, null=True, help_text="List of service IDs")
    amount = models.IntegerField(help_text="Amount in cents")
    currency = models.CharField(max_length=3, default='usd')
    status = models.CharField(
        max_length=50,
        choices=[
            ('pending', 'Pending'),
            ('completed', 'Completed'),
            ('failed', 'Failed'),
            ('refunded', 'Refunded')
        ]
    )
    stripe_payment_intent_id = models.CharField(max_length=255, unique=True)
    metadata = models.JSONField(default=dict, blank=True)
    dna_analysis = models.ForeignKey(DNAAnalysis, on_delete=models.SET_NULL, null=True, blank=True, related_name='service_payments')

    def __str__(self):
        return f"Payment {self.stripe_payment_intent_id} - {self.user.email} for {self.service.name}"

class ServiceAccess(BaseModel):
    """
    Tracks which users have access to which services and their status
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='service_access')
    service = models.ForeignKey('content_management.Service', on_delete=models.CASCADE, related_name='user_access')
    payment = models.ForeignKey(ServicePayment, on_delete=models.CASCADE, related_name='access_records')
    status = models.CharField(
        max_length=50,
        choices=[
            ('active', 'Active'),
            ('expired', 'Expired'),
            ('revoked', 'Revoked')
        ]
    )
    access_granted_at = models.DateTimeField(auto_now_add=True)
    last_accessed_at = models.DateTimeField(auto_now=True)
    access_expires_at = models.DateTimeField(null=True, blank=True)
    analysis_access_data = models.JSONField(default=dict, blank=True, help_text="Specific access data for analysis app")
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"Access for {self.user.email} to {self.service.name} - {self.status}"

class Subscription(BaseModel):
    """
    Represents a Stripe Subscription for SaaS platform services
    """
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='subscriptions')
    stripe_subscription_id = models.CharField(max_length=255, unique=True)
    price = models.ForeignKey(Price, on_delete=models.CASCADE, related_name='subscriptions')
    status = models.CharField(max_length=50)
    current_period_start = models.DateTimeField()
    current_period_end = models.DateTimeField()
    cancel_at_period_end = models.BooleanField(default=False)
    canceled_at = models.DateTimeField(null=True, blank=True)
    trial_end = models.DateTimeField(null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.customer.email} - {self.price.product.name}"

class SubscriptionAccess(BaseModel):
    """
    Tracks which users have access to which subscription plans and their status
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='subscription_access')
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='access_records')
    status = models.CharField(
        max_length=50,
        choices=[
            ('active', 'Active'),
            ('past_due', 'Past Due'),
            ('canceled', 'Canceled'),
            ('expired', 'Expired')
        ]
    )
    access_granted_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.user.email} - {self.subscription.price.product.name} ({self.status})"

class PlatformFee(BaseModel):
    """
    Represents platform fees for enterprise payments
    """
    percentage = models.DecimalField(max_digits=5, decimal_places=2, help_text="Platform fee percentage")
    fixed_amount = models.IntegerField(default=0, help_text="Fixed platform fee in cents")
    active = models.BooleanField(default=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.percentage}% + {self.fixed_amount/100} {settings.STRIPE_DEFAULT_CURRENCY}"

class EnterpriseAccount(BaseModel):
    """
    Represents a Stripe Connect account for enterprises
    """
    enterprise = models.OneToOneField('enterprise.Enterprise', on_delete=models.CASCADE, related_name='stripe_account')
    stripe_account_id = models.CharField(max_length=255, unique=True)
    charges_enabled = models.BooleanField(default=False)
    payouts_enabled = models.BooleanField(default=False)
    details_submitted = models.BooleanField(default=False)
    requirements = models.JSONField(default=dict, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.enterprise.name} - {self.stripe_account_id}"

class EnterpriseService(BaseModel):
    """
    Represents a service offered by an enterprise
    """
    enterprise = models.ForeignKey('enterprise.Enterprise', on_delete=models.CASCADE, related_name='billing_services')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Price in dollars")
    stripe_price_id = models.CharField(max_length=255, unique=True)
    active = models.BooleanField(default=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.enterprise.name} - {self.name}"

class EnterprisePayment(BaseModel):
    """
    Represents payments from personal users to enterprises
    """
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='enterprise_payments')
    enterprise = models.ForeignKey('enterprise.Enterprise', on_delete=models.CASCADE, related_name='payments')
    service = models.ForeignKey(EnterpriseService, on_delete=models.CASCADE, related_name='payments')
    amount = models.IntegerField(help_text="Amount in cents")
    platform_fee = models.ForeignKey(PlatformFee, on_delete=models.SET_NULL, null=True)
    platform_fee_amount = models.IntegerField(help_text="Platform fee amount in cents")
    status = models.CharField(max_length=50)
    stripe_payment_intent_id = models.CharField(max_length=255, unique=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"Payment {self.stripe_payment_intent_id} - {self.customer.email} to {self.enterprise.name}"

    @property
    def net_amount(self):
        """Amount after platform fee"""
        return self.amount - self.platform_fee_amount

class SolutionAccess(BaseModel):
    """
    Tracks which enterprises have access to which solutions and their status
    """
    enterprise = models.ForeignKey('enterprise.Enterprise', on_delete=models.CASCADE, related_name='solution_access')
    solution = models.ForeignKey('content_management.Solution', on_delete=models.CASCADE, related_name='enterprise_access')
    subscription = models.ForeignKey(Subscription, on_delete=models.SET_NULL, null=True, blank=True, related_name='solution_access')
    status = models.CharField(
        max_length=50,
        choices=[
            ('active', 'Active'),
            ('past_due', 'Past Due'),
            ('canceled', 'Canceled'),
            ('expired', 'Expired')
        ]
    )
    total_seats = models.IntegerField(default=20, help_text="Total number of user seats purchased")
    used_seats = models.IntegerField(default=0, help_text="Number of seats currently in use")
    access_granted_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.enterprise.name} - {self.solution.name} ({self.status})"

    @property
    def available_seats(self):
        return self.total_seats - self.used_seats

class SolutionUserAccess(BaseModel):
    """
    Tracks individual user access to enterprise solutions
    """
    solution_access = models.ForeignKey(SolutionAccess, on_delete=models.CASCADE, related_name='users')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=[
        ('active', 'Active'),
        ('revoked', 'Revoked'),
        ('expired', 'Expired')
    ], default='active')
    assigned_at = models.DateTimeField(auto_now_add=True)
    revoked_at = models.DateTimeField(null=True, blank=True)
    role = models.CharField(max_length=20, choices=[
        ('owner', 'Owner'),
        ('admin', 'Administrator'),
        ('member', 'Member')
    ], default='member')
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        unique_together = ('solution_access', 'user')
        indexes = [
            models.Index(fields=['solution_access', 'user']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.solution_access.solution.name}"

class SolutionUsage(BaseModel):
    """
    Tracks usage metrics for enterprise solutions (AI tokens, storage, etc.)
    """
    solution_access = models.ForeignKey(SolutionAccess, on_delete=models.CASCADE, related_name='usage_metrics')
    metric_type = models.CharField(
        max_length=50,
        choices=[
            ('ai_tokens', 'AI Tokens'),
            ('storage', 'Storage'),
            ('api_calls', 'API Calls')
        ]
    )
    usage_amount = models.DecimalField(max_digits=20, decimal_places=2, help_text="Amount of usage (tokens, bytes, calls)")
    usage_date = models.DateField()
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['solution_access', 'metric_type', 'usage_date']),
        ]

    def __str__(self):
        return f"{self.solution_access.enterprise.name} - {self.metric_type} - {self.usage_date}"

class SolutionUsageLimit(BaseModel):
    """
    Defines usage limits for enterprise solutions
    """
    solution_access = models.ForeignKey(SolutionAccess, on_delete=models.CASCADE, related_name='usage_limits')
    metric_type = models.CharField(
        max_length=50,
        choices=[
            ('ai_tokens', 'AI Tokens'),
            ('storage', 'Storage'),
            ('api_calls', 'API Calls')
        ]
    )
    limit_amount = models.DecimalField(max_digits=20, decimal_places=2, help_text="Usage limit amount")
    reset_period = models.CharField(
        max_length=50,
        choices=[
            ('daily', 'Daily'),
            ('monthly', 'Monthly'),
            ('yearly', 'Yearly')
        ]
    )
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        unique_together = ('solution_access', 'metric_type')
        indexes = [
            models.Index(fields=['solution_access', 'metric_type']),
        ]

    def __str__(self):
        return f"{self.solution_access.enterprise.name} - {self.metric_type} Limit"

class UserPaymentProfile(BaseModel):
    """
    Represents a user's payment profile settings
    """
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payment_profile')
    stripe_account_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    charges_enabled = models.BooleanField(default=False)
    payouts_enabled = models.BooleanField(default=False)
    details_submitted = models.BooleanField(default=False)
    
    # Donation settings
    accept_donations = models.BooleanField(default=False)
    donation_message = models.TextField(blank=True)
    minimum_donation = models.IntegerField(default=100, help_text="Minimum donation amount in cents")
    suggested_donation_amounts = models.JSONField(default=list, help_text="List of suggested donation amounts in cents")
    
    # Verification status
    is_verified = models.BooleanField(default=False)
    verification_date = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return f"Payment Profile - {self.user.email}"

class UserTransfer(BaseModel):
    """
    Represents money transfers between users
    """
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='sent_transfers')
    receiver = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='received_transfers')
    amount = models.IntegerField(help_text="Amount in cents")
    currency = models.CharField(max_length=3, default='usd')
    message = models.TextField(blank=True)
    transfer_type = models.CharField(
        max_length=50,
        choices=[
            ('donation', 'Donation'),
            ('payment', 'Payment'),
            ('transfer', 'Transfer')
        ]
    )
    platform_fee = models.ForeignKey(PlatformFee, on_delete=models.SET_NULL, null=True)
    platform_fee_amount = models.IntegerField(help_text="Platform fee amount in cents")
    status = models.CharField(
        max_length=50,
        choices=[
            ('pending', 'Pending'),
            ('processing', 'Processing'),
            ('completed', 'Completed'),
            ('failed', 'Failed'),
            ('refunded', 'Refunded')
        ]
    )
    stripe_payment_intent_id = models.CharField(max_length=255, unique=True)
    metadata = models.JSONField(default=dict, blank=True)
    
    def __str__(self):
        return f"Transfer {self.stripe_payment_intent_id} - {self.sender.email} to {self.receiver.email}"

    @property
    def net_amount(self):
        """Amount after platform fee"""
        return self.amount - self.platform_fee_amount


# Import all models from the new package structure for backward compatibility
from .models import *
