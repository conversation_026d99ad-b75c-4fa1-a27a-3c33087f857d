"""
DEPRECATED: This file is maintained for backward compatibility only.
New code should use the services in billing.services package.

This module will be removed in a future version.
"""
import warnings
import stripe
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import (
    Customer, Product, Price, SolutionUserAccess, Subscription, PlatformFee, EnterpriseAccount, EnterprisePayment,
    ServicePayment, ServiceAccess, SubscriptionAccess, SolutionAccess, UserPaymentProfile, UserTransfer
)
from content_management.models import Service, SubscriptionPlan, Solution
from tenacity import retry, stop_after_attempt, wait_exponential
import logging
from datetime import timedelta
from decimal import Decimal
import json
from enterprise.models import Enterprise, Location, EnterpriseMember
from django.contrib.auth import get_user_model

# Import new services for delegation
from .services import (
    CustomerService, PaymentService, SubscriptionService,
    WebhookService, ProductSyncService, EnterpriseService, UserTransferService
)

logger = logging.getLogger(__name__)
stripe.api_key = settings.STRIPE_SECRET_KEY

# Issue deprecation warning
warnings.warn(
    "Direct import from billing.services is deprecated. "
    "Use billing.services.CustomerService, billing.services.PaymentService, etc. instead.",
    DeprecationWarning,
    stacklevel=2
)

class StripeService:
    """
    DEPRECATED: Backward compatibility class that delegates to new service classes.
    Use the specific service classes instead:
    - CustomerService for customer operations
    - PaymentService for payment operations
    - SubscriptionService for subscription operations
    - WebhookService for webhook handling
    - ProductSyncService for product synchronization
    - EnterpriseService for enterprise operations
    - UserTransferService for user transfers
    """

    def __init__(self):
        # Lazy initialization to avoid circular imports
        self._customer_service = None
        self._payment_service = None
        self._subscription_service = None
        self._webhook_service = None
        self._product_sync_service = None
        self._enterprise_service = None
        self._user_transfer_service = None

    @property
    def customer_service(self):
        if self._customer_service is None:
            self._customer_service = CustomerService()
        return self._customer_service

    @property
    def payment_service(self):
        if self._payment_service is None:
            self._payment_service = PaymentService()
        return self._payment_service

    @property
    def subscription_service(self):
        if self._subscription_service is None:
            self._subscription_service = SubscriptionService()
        return self._subscription_service

    @property
    def webhook_service(self):
        if self._webhook_service is None:
            self._webhook_service = WebhookService()
        return self._webhook_service

    @property
    def product_sync_service(self):
        if self._product_sync_service is None:
            self._product_sync_service = ProductSyncService()
        return self._product_sync_service

    @property
    def enterprise_service(self):
        if self._enterprise_service is None:
            self._enterprise_service = EnterpriseService()
        return self._enterprise_service

    @property
    def user_transfer_service(self):
        if self._user_transfer_service is None:
            self._user_transfer_service = UserTransferService()
        return self._user_transfer_service

    # Delegate methods to appropriate services for backward compatibility
    @classmethod
    def create_or_update_customer(cls, user):
        """Create or update a Stripe customer for a user"""
        warnings.warn(
            "StripeService.create_or_update_customer is deprecated. Use CustomerService.create_or_update_customer instead.",
            DeprecationWarning,
            stacklevel=2
        )
        return CustomerService().create_or_update_customer(user)

    @classmethod
    def create_checkout_session(cls, customer, price, success_url, cancel_url, metadata=None, mode='subscription'):
        """Create a Stripe Checkout session"""
        warnings.warn(
            "StripeService.create_checkout_session is deprecated. Use PaymentService.create_checkout_session instead.",
            DeprecationWarning,
            stacklevel=2
        )
        return PaymentService().create_checkout_session(customer, price, success_url, cancel_url, metadata, mode)

    @classmethod
    def handle_webhook_event(cls, event):
        """Handle Stripe webhook events"""
        warnings.warn(
            "StripeService.handle_webhook_event is deprecated. Use WebhookService.handle_event instead.",
            DeprecationWarning,
            stacklevel=2
        )
        return WebhookService().handle_event(event)

    @classmethod
    def sync_products(cls):
        """Sync products from content_management models to Stripe"""
        warnings.warn(
            "StripeService.sync_products is deprecated. Use ProductSyncService.sync_products instead.",
            DeprecationWarning,
            stacklevel=2
        )
        return ProductSyncService().sync_products()

    @classmethod
    def create_connect_account(cls, enterprise):
        """Create a Stripe Connect account for an enterprise"""
        warnings.warn(
            "StripeService.create_connect_account is deprecated. Use EnterpriseService.create_connect_account instead.",
            DeprecationWarning,
            stacklevel=2
        )
        return EnterpriseService().create_connect_account(enterprise)

    @classmethod
    def create_enterprise_checkout_session(cls, customer, service, enterprise_account, platform_fee, success_url, cancel_url):
        """Create a Stripe Checkout session for enterprise services"""
        warnings.warn(
            "StripeService.create_enterprise_checkout_session is deprecated. Use EnterpriseService.create_enterprise_checkout_session instead.",
            DeprecationWarning,
            stacklevel=2
        )
        return EnterpriseService().create_enterprise_checkout_session(customer, service, enterprise_account, platform_fee, success_url, cancel_url)

    # Keep the rest of the methods for now but mark them as deprecated
    # These will be gradually migrated to the new services

    # Additional deprecated methods - these delegate to the old implementation for now
    # TODO: Migrate these to the new service classes

    @classmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _legacy_create_checkout_session(cls, customer, price, success_url, cancel_url, metadata=None, mode='subscription'):
        """Create a Stripe Checkout session"""
        try:
            logger.info(f"Creating checkout session for customer {customer.id} and price {price.id}")
            session = stripe.checkout.Session.create(
                customer=customer.stripe_customer_id,
                payment_method_types=['card'],
                line_items=[{
                    'price': price.stripe_price_id,
                    'quantity': 1,
                }],
                mode=mode,
                success_url=success_url,
                cancel_url=cancel_url,
                metadata=metadata or {}
            )
            logger.info(f"Created checkout session {session.id} for customer {customer.id}")
            return session
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe API error creating checkout session for customer {customer.id}: {str(e)}")
            raise ValidationError(f"Stripe API error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error creating checkout session for customer {customer.id}: {str(e)}")
            raise ValidationError(f"Error creating checkout session: {str(e)}")

    @classmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def create_enterprise_checkout_session(cls, customer, service, enterprise_account, platform_fee, success_url, cancel_url):
        """Create a Stripe Checkout session for enterprise services"""
        try:
            logger.info(f"Creating enterprise checkout session for customer {customer.id} and service {service.id}")
            
            # Calculate platform fee amount
            fee_amount = int(
                (service.price * platform_fee.percentage / 100) + platform_fee.fixed_amount
            )
            
            session = stripe.checkout.Session.create(
                customer=customer.stripe_customer_id,
                payment_method_types=['card'],
                line_items=[{
                    'price': service.stripe_price_id,
                    'quantity': 1,
                }],
                mode='payment',
                success_url=success_url,
                cancel_url=cancel_url,
                payment_intent_data={
                    'application_fee_amount': fee_amount,
                    'transfer_data': {
                        'destination': enterprise_account.stripe_account_id,
                    },
                },
                metadata={
                    'service_id': service.id,
                    'enterprise_id': service.enterprise.id,
                    'customer_id': customer.id,
                    'platform_fee_id': platform_fee.id,
                }
            )
            logger.info(f"Created enterprise checkout session {session.id} for customer {customer.id}")
            return session
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe API error creating enterprise checkout session for customer {customer.id}: {str(e)}")
            raise ValidationError(f"Stripe API error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error creating enterprise checkout session for customer {customer.id}: {str(e)}")
            raise ValidationError(f"Error creating enterprise checkout session: {str(e)}")

    @classmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def create_connect_account(cls, enterprise):
        """Create a Stripe Connect account for an enterprise"""
        try:
            logger.info(f"Creating Stripe Connect account for enterprise {enterprise.id}")
            
            account = stripe.Account.create(
                type='express',
                country='US',
                email=enterprise.email,
                capabilities={
                    'card_payments': {'requested': True},
                    'transfers': {'requested': True},
                },
                business_type='company',
                metadata={'enterprise_id': enterprise.id}
            )
            
            enterprise_account = EnterpriseAccount.objects.create(
                enterprise=enterprise,
                stripe_account_id=account.id,
                charges_enabled=account.charges_enabled,
                payouts_enabled=account.payouts_enabled,
                details_submitted=account.details_submitted,
                requirements=account.requirements,
            )
            
            logger.info(f"Created Stripe Connect account {account.id} for enterprise {enterprise.id}")
            return enterprise_account
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe API error creating connect account for enterprise {enterprise.id}: {str(e)}")
            raise ValidationError(f"Stripe API error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error creating connect account for enterprise {enterprise.id}: {str(e)}")
            raise ValidationError(f"Error creating connect account: {str(e)}")

    @classmethod
    def handle_webhook_event(cls, event):
        """Handle Stripe webhook events"""
        try:
            logger.info(f"Processing Stripe webhook event: {event.type}")
            
            if event.type == 'checkout.session.completed':
                cls._handle_checkout_session_completed(event.data.object)
            elif event.type == 'payment_intent.succeeded':
                cls._handle_payment_intent_succeeded(event.data.object)
            elif event.type == 'payment_intent.payment_failed':
                cls._handle_payment_intent_failed(event.data.object)
            elif event.type == 'customer.subscription.created':
                cls._handle_subscription_created(event.data.object)
            elif event.type == 'customer.subscription.updated':
                cls._handle_subscription_updated(event.data.object)
            elif event.type == 'customer.subscription.deleted':
                cls._handle_subscription_deleted(event.data.object)
            elif event.type == 'customer.subscription.quantity_updated':
                cls._handle_subscription_quantity_updated(event.data.object)
            elif event.type == 'invoice.payment_succeeded':
                cls._handle_invoice_payment_succeeded(event.data.object)
            elif event.type == 'invoice.payment_failed':
                cls._handle_invoice_payment_failed(event.data.object)
            
            logger.info(f"Successfully processed Stripe webhook event: {event.type}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing Stripe webhook event {event.type}: {str(e)}")
            return False

    @classmethod
    def _handle_checkout_session_completed(cls, session):
        """Handle completed checkout session for both enterprise payments and solution subscriptions"""
        import traceback
        try:
            logger.info(f"Processing completed checkout session {getattr(session, 'id', None)}")
            logger.debug(f"Session object: {session}")
            logger.debug(f"Session metadata: {getattr(session, 'metadata', None)}")
            logger.debug(f"Session amount_total: {getattr(session, 'amount_total', None)}")
            logger.debug(f"Session payment_intent: {getattr(session, 'payment_intent', None)}")

            # Get customer
            try:
                customer = Customer.objects.get(stripe_customer_id=session.customer)
                logger.debug(f"Found customer: {customer.id} for stripe_customer_id: {session.customer}")
            except Exception as e:
                logger.error(f"Customer not found for session {getattr(session, 'id', None)}: {str(e)}")
                logger.error(traceback.format_exc())
                raise ValidationError("Customer not found")

            metadata = getattr(session, 'metadata', {})
            mode = getattr(session, 'mode', None)

            # --- Branch 1: Enterprise Solution Subscription (SaaS) ---
            if mode == 'subscription' and 'solution_id' in metadata:
                logger.info(f"Processing enterprise solution subscription for session {getattr(session, 'id', None)}")
                
                # 1. Create or update enterprise
                enterprise = cls._create_or_update_enterprise_from_metadata(metadata)
                
                # 2. Create solution access
                solution_access = cls._create_solution_access(enterprise, metadata['solution_id'])
                
                # 3. Check if admin should be included as member
                include_admin = metadata.get('include_admin_as_member', 'True').lower() == 'true'
                admin_wants_subscription = metadata.get('admin_wants_subscription', 'False').lower() == 'true'
                
                if include_admin:
                    # 4a. Add owner as first user (SolutionUserAccess) if they want to be included
                    cls._add_owner_to_solution_access(solution_access, enterprise.owner)
                    
                    # 4b. Add owner as EnterpriseMember (role=owner)
                    from enterprise.models import EnterpriseMember
                    member, created = EnterpriseMember.objects.get_or_create(
                        enterprise=enterprise,
                        user=enterprise.owner,
                        defaults={
                            "role": "owner", 
                            "is_active": True,
                            "can_manage_members": True,
                            "can_manage_billing": True
                        }
                    )
                    if not created:
                        # Update existing member to owner if needed
                        member.role = "owner"
                        member.can_manage_members = True
                        member.can_manage_billing = True
                        member.save()
                    
                    logger.info(f"EnterpriseMember for owner created: {created}, id: {member.id}")
                else:
                    logger.info(f"Admin chose not to be included as member of the enterprise solution")
                
                # 5. Handle admin personal subscription if requested
                if admin_wants_subscription:
                    logger.info(f"Admin requested personal subscription - this should be handled separately")
                    # TODO: This would need to be implemented as a separate subscription flow
                
                # 6. (No EnterprisePayment for subscription mode!)
                # 7. TODO: Link SolutionAccess to Subscription after subscription.created webhook
                
                logger.info(f"Successfully processed enterprise solution subscription for enterprise {enterprise.id}")
                return solution_access

            # --- Branch 2: Enterprise Service Payment ---
            elif mode == 'payment' and 'service_id' in metadata and 'enterprise_id' in metadata:
                logger.info(f"Processing enterprise service payment for session {getattr(session, 'id', None)}")
                cls._create_enterprise_payment_from_session(session, customer)
                logger.info(f"Successfully processed enterprise service payment for session {getattr(session, 'id', None)}")
                return True

            else:
                logger.warning(f"Unknown or unsupported checkout session type or missing metadata for session {getattr(session, 'id', None)}")
                return False

        except Exception as e:
            logger.error(f"Error processing checkout session {getattr(session, 'id', None)}: {str(e)}")
            logger.error(traceback.format_exc())
            raise ValidationError(f"Error processing checkout: {str(e)}")

    @staticmethod
    def _create_or_update_enterprise_from_metadata(metadata):
        """Create or update an enterprise from session metadata."""
        from enterprise.models import Enterprise, Location
        import json
        User = get_user_model()
        try:
            user = User.objects.get(id=metadata['user_id'])
        except Exception as e:
            logger.error(f"User not found for ID {metadata['user_id']}: {str(e)}")
            raise ValidationError("User not found")
        
        # Use the validated enterprise_name from frontend, not user name
        enterprise_name = metadata.get('enterprise_name', f"{user.first_name} {user.last_name}".strip())
        
        # Create or update enterprise
        enterprise, created = Enterprise.objects.get_or_create(
            owner=user,
            defaults={
                'name': enterprise_name,  # Use proper enterprise name
                'additional_email': metadata.get('enterprise_email', user.email),
                'contact_number': metadata.get('enterprise_phone', user.phone_number or ''),
                'status': 'active'
            }
        )
        if not created:
            # Update enterprise info with provided data
            enterprise.name = enterprise_name
            enterprise.additional_email = metadata.get('enterprise_email', user.email)
            enterprise.contact_number = metadata.get('enterprise_phone', user.phone_number or '')
            enterprise.save()
        
        # Update user enterprise reference
        user.enterprise = enterprise
        user.save()
        
        # Parse and create location if needed
        try:
            address_str = metadata.get('enterprise_address', '{}')
            if isinstance(address_str, str):
                address = json.loads(address_str)
            else:
                address = address_str
            
            # Only create location if we have meaningful data
            if address and any(v for k, v in address.items() if k != 'default' and v != 'default'):
                Location.objects.get_or_create(
                    enterprise=enterprise,
                    defaults={
                        'address': address.get('street', ''),
                        'city': address.get('city', ''),
                        'state': address.get('state', ''),
                        'country': address.get('country', ''),
                        'zip_code': address.get('zip_code', '')
                    }
                )
        except Exception as e:
            logger.warning(f"Could not create location for enterprise {enterprise.id}: {str(e)}")
        
        return enterprise

    @staticmethod
    def _create_solution_access(enterprise, solution_id):
        from content_management.models import Solution
        try:
            solution = Solution.objects.get(id=solution_id)
        except Exception as e:
            logger.error(f"Solution not found for ID {solution_id}: {str(e)}")
            raise ValidationError("Solution not found")
        solution_access = SolutionAccess.objects.create(
            enterprise=enterprise,
            solution=solution,
            total_seats=20,
            used_seats=1,
            status='active'
        )
        return solution_access

    @staticmethod
    def _add_owner_to_solution_access(solution_access, user):
        try:
            SolutionUserAccess.objects.create(
                solution_access=solution_access,
                user=user,
                status='active',
                role='owner'
            )
        except Exception as e:
            logger.warning(f"Could not add owner {user.id} to solution access {solution_access.id}: {str(e)}")

    @staticmethod
    def _create_enterprise_payment_from_session(session, customer):
        from .models import EnterprisePayment, EnterpriseService, PlatformFee, Enterprise
        metadata = getattr(session, 'metadata', {})
        try:
            enterprise = Enterprise.objects.get(id=metadata['enterprise_id'])
            service = EnterpriseService.objects.get(id=metadata['service_id'])
            platform_fee = None
            platform_fee_amount = 0
            if 'platform_fee_id' in metadata:
                platform_fee = PlatformFee.objects.get(id=metadata['platform_fee_id'])
                platform_fee_amount = int(session.amount_total * float(platform_fee.percentage) / 100) + int(platform_fee.fixed_amount)
            EnterprisePayment.objects.create(
                customer=customer,
                enterprise=enterprise,
                service=service,
                amount=session.amount_total,
                platform_fee=platform_fee,
                platform_fee_amount=platform_fee_amount,
                status='succeeded',
                stripe_payment_intent_id=getattr(session, 'payment_intent', None),
                metadata={
                    'checkout_session_id': getattr(session, 'id', None),
                    'service_id': str(service.id)
                }
            )
        except Exception as e:
            logger.error(f"Error creating enterprise payment from session: {str(e)}")
            raise ValidationError(f"Error creating enterprise payment: {str(e)}")

    @classmethod
    def _handle_payment_intent_succeeded(cls, payment_intent):
        """Handle payment_intent.succeeded event"""
        try:
            logger.info(f"Processing payment_intent.succeeded for payment {payment_intent.id}")
            
            # Update enterprise payment status if it exists
            if payment_intent.metadata.get('service_id'):
                payment = EnterprisePayment.objects.filter(
                    stripe_payment_intent_id=payment_intent.id
                ).first()
                
                if payment:
                    payment.status = 'succeeded'
                    payment.save()
                    logger.info(f"Updated enterprise payment {payment.id} status to succeeded")
            
            return True
                
        except Exception as e:
            logger.error(f"Error handling payment intent succeeded for payment {payment_intent.id}: {str(e)}")
            return False

    @classmethod
    def _handle_payment_intent_failed(cls, payment_intent):
        """Handle payment_intent.payment_failed event"""
        try:
            logger.info(f"Processing payment_intent.payment_failed for payment {payment_intent.id}")
            
            # Update enterprise payment status if it exists
            if payment_intent.metadata.get('service_id'):
                payment = EnterprisePayment.objects.filter(
                    stripe_payment_intent_id=payment_intent.id
                ).first()
                
                if payment:
                    payment.status = 'failed'
                    payment.save()
                    logger.info(f"Updated enterprise payment {payment.id} status to failed")
            
            return True
                
        except Exception as e:
            logger.error(f"Error handling payment intent failed for payment {payment_intent.id}: {str(e)}")
            return False

    @classmethod
    @classmethod
    def _handle_subscription_created(cls, subscription):
        """Handle customer.subscription.created event"""
        try:
            logger.info(f"Processing customer.subscription.created for subscription {subscription.id}")

            # Lấy customer object từ stripe_customer_id
            customer_obj = Customer.objects.filter(stripe_customer_id=subscription.customer).first()
            if not customer_obj:
                logger.error(f"Customer not found for stripe_customer_id: {subscription.customer}")
                return

            # Lấy price_id từ subscription.items
            price_id = None
            if hasattr(subscription, 'items') and hasattr(subscription.items, 'data') and subscription.items.data:
                price_id = subscription.items.data[0].price.id

            Subscription.objects.create(
                customer=customer_obj,
                stripe_subscription_id=subscription.id,
                price_id=price_id,
                status=subscription.status,
                current_period_start=timezone.datetime.fromtimestamp(subscription.current_period_start),
                current_period_end=timezone.datetime.fromtimestamp(subscription.current_period_end),
                cancel_at_period_end=subscription.cancel_at_period_end,
                canceled_at=timezone.datetime.fromtimestamp(subscription.canceled_at) if subscription.canceled_at else None,
                trial_end=timezone.datetime.fromtimestamp(subscription.trial_end) if subscription.trial_end else None,
            )
            logger.info(f"Created subscription record for Stripe subscription {subscription.id}")

        except Exception as e:
            logger.error(f"Error handling subscription created for subscription {subscription.id}: {str(e)}")
    
    @classmethod
    def _handle_subscription_updated(cls, subscription):
        """Handle customer.subscription.updated event"""
        try:
            logger.info(f"Processing customer.subscription.updated for subscription {subscription.id}")
            
            Subscription.objects.filter(
                stripe_subscription_id=subscription.id
            ).update(
                status=subscription.status,
                current_period_start=timezone.datetime.fromtimestamp(subscription.current_period_start),
                current_period_end=timezone.datetime.fromtimestamp(subscription.current_period_end),
                cancel_at_period_end=subscription.cancel_at_period_end,
                canceled_at=timezone.datetime.fromtimestamp(subscription.canceled_at) if subscription.canceled_at else None,
                trial_end=timezone.datetime.fromtimestamp(subscription.trial_end) if subscription.trial_end else None,
            )
            logger.info(f"Updated subscription record for Stripe subscription {subscription.id}")
            
        except Exception as e:
            logger.error(f"Error handling subscription updated for subscription {subscription.id}: {str(e)}")

    @classmethod
    def _handle_subscription_deleted(cls, subscription):
        """Handle customer.subscription.deleted event"""
        try:
            logger.info(f"Processing customer.subscription.deleted for subscription {subscription.id}")
            
            Subscription.objects.filter(
                stripe_subscription_id=subscription.id
            ).update(
                status='canceled',
                canceled_at=timezone.now()
            )
            logger.info(f"Updated subscription record for Stripe subscription {subscription.id} to canceled")
            
        except Exception as e:
            logger.error(f"Error handling subscription deleted for subscription {subscription.id}: {str(e)}")

    @classmethod
    def _handle_subscription_quantity_updated(cls, subscription):
        """Handle subscription quantity updates (adding/removing users)"""
        try:
            solution_access = SolutionAccess.objects.get(
                subscription__stripe_subscription_id=subscription.id
            )
            
            # Update number of users
            new_quantity = subscription.items.data[0].quantity
            solution_access.total_seats = new_quantity
            solution_access.save()
            
            logger.info(f"Updated solution access {solution_access.id} with {new_quantity} seats")
            return True
            
        except SolutionAccess.DoesNotExist:
            logger.error(f"Solution access not found for subscription {subscription.id}")
            return False
        except Exception as e:
            logger.error(f"Error handling subscription quantity update: {str(e)}")
            return False

    @classmethod
    def _handle_invoice_payment_succeeded(cls, invoice):
        """Handle successful invoice payments"""
        try:
            if invoice.subscription:
                solution_access = SolutionAccess.objects.get(
                    subscription__stripe_subscription_id=invoice.subscription
                )
                
                # Create payment record
                EnterprisePayment.objects.create(
                    enterprise=solution_access.enterprise,
                    amount=invoice.amount_paid,
                    currency=invoice.currency,
                    status='succeeded',
                    stripe_payment_intent_id=invoice.payment_intent,
                    metadata={
                        'invoice_id': invoice.id,
                        'subscription_id': invoice.subscription
                    }
                )
                
                logger.info(f"Created payment record for invoice {invoice.id}")
                return True
                
        except SolutionAccess.DoesNotExist:
            logger.error(f"Solution access not found for invoice {invoice.id}")
            return False
        except Exception as e:
            logger.error(f"Error handling invoice payment: {str(e)}")
            return False

    @classmethod
    def _handle_invoice_payment_failed(cls, invoice):
        """Handle failed invoice payments"""
        try:
            if invoice.subscription:
                solution_access = SolutionAccess.objects.get(
                    subscription__stripe_subscription_id=invoice.subscription
                )
                
                # Update solution access status
                solution_access.status = 'past_due'
                solution_access.save()
                
                # Notify enterprise admin
                cls._notify_payment_failed(solution_access)
                
                logger.info(f"Updated solution access {solution_access.id} status to past_due")
                return True
                
        except SolutionAccess.DoesNotExist:
            logger.error(f"Solution access not found for invoice {invoice.id}")
            return False
        except Exception as e:
            logger.error(f"Error handling failed invoice payment: {str(e)}")
            return False

    @staticmethod
    def _notify_payment_failed(solution_access):
        """Send notification to enterprise admin about failed payment"""
        try:
            # Get enterprise admin
            admin = solution_access.enterprise.owner
            
            # Send email notification
            subject = "Payment Failed for Enterprise Solution"
            message = f"""
            Dear {admin.get_full_name()},
            
            The payment for your enterprise solution {solution_access.solution.name} has failed.
            Please update your payment method to avoid service interruption.
            
            Best regards,
            Your Platform Team
            """
            
            admin.email_user(subject, message)
            logger.info(f"Sent payment failed notification to {admin.email}")
            
        except Exception as e:
            logger.error(f"Error sending payment failed notification: {str(e)}")

    @staticmethod
    def sync_products():
        """
        Sync products from content_management models to Stripe
        """
        # Sync Services
        for service in Service.objects.filter(is_active=True):
            StripeService._sync_service(service)
        
        # Sync Subscription Plans
        for plan in SubscriptionPlan.objects.filter(is_active=True):
            StripeService._sync_subscription_plan(plan)
        
        # Sync Solutions
        for solution in Solution.objects.filter(is_active=True):
            StripeService._sync_solution(solution)

    @staticmethod
    def _sync_service(service):
        """Sync a service with Stripe"""
        try:
            # Create or update Stripe product
            product_data = {
                'name': service.name,
                'description': service.description,
                'metadata': {
                    'type': 'service',
                    'content_id': str(service.id)
                }
            }
            
            # Try to get existing product
            try:
                product = Product.objects.get(content_id=service.id)
                stripe_product = stripe.Product.modify(
                    product.stripe_product_id,
                    **product_data
                )
            except Product.DoesNotExist:
                stripe_product = stripe.Product.create(**product_data)
                product = Product.objects.create(
                    stripe_product_id=stripe_product.id,
                    content_id=service.id,
                    name=service.name,
                    description=service.description,
                    product_type='service',
                    service=service
                )
            
            # Sync prices with original price (no promo applied)
            StripeService._sync_product_prices(product, service, apply_promo=False)
            
            # Sync promotions
            StripeService._sync_service_promotions(service)
                
        except Exception as e:
            logger.error(f"Error syncing service {service.id} with Stripe: {str(e)}")
            raise

    @staticmethod
    def _sync_product_prices(product, content_model, apply_promo=False):
        """
        Sync product prices with Stripe
        """
        try:
            # Get original price amount
            price_amount = content_model.price
            has_discount = False
            discount_percentage = None
            
            # Only apply promo if requested
            if apply_promo and hasattr(content_model, 'active_promotions'):
                active_promos = content_model.active_promotions.filter(
                    is_active=True,
                    start_date__lte=timezone.now(),
                    end_date__gte=timezone.now()
                )
                if active_promos.exists():
                    promo = active_promos.order_by('-discount_percentage').first()
                    price_amount = content_model.get_discounted_price()
                    has_discount = True
                    discount_percentage = promo.discount_percentage
            
            # Convert price_amount to cents for Stripe
            unit_amount = int(price_amount * 100)
            
            # Check if a price with the same amount already exists and is active
            existing_prices = stripe.Price.list(
                product=product.stripe_product_id,
                active=True
            ).data
            
            # Determine if this is a subscription price
            is_subscription = product.product_type in ['subscription', 'solution']
            recurring_config = {
                'interval': 'month',  # Default to monthly for solutions
                'interval_count': 1
            } if is_subscription else None
            
            matching_price = next(
                (p for p in existing_prices 
                if p.unit_amount == unit_amount 
                and p.currency.lower() == 'usd'
                and p.metadata.get('has_discount') == str(has_discount)
                and p.metadata.get('discount_percentage') == str(discount_percentage)),
                None
            )
            
            if matching_price:
                # If a matching price exists, use it
                stripe_price = matching_price
                logger.info(f"Using existing price {stripe_price.id} for product {product.stripe_product_id}")
            else:
                # Archive all other active prices
                for price in existing_prices:
                    stripe.Price.modify(price.id, active=False)
                    logger.info(f"Archived price {price.id} for product {product.stripe_product_id}")
                
                # Create new price in Stripe
                price_params = {
                    'product': product.stripe_product_id,
                    'unit_amount': unit_amount,
                    'currency': 'usd',
                    'metadata': {
                        'content_id': str(content_model.id),
                        'has_discount': str(has_discount),
                        'discount_percentage': str(discount_percentage)
                    }
                }
                
                # Add recurring configuration for subscription prices
                if is_subscription:
                    price_params['recurring'] = recurring_config
                
                stripe_price = stripe.Price.create(**price_params)
                logger.info(f"Created new price {stripe_price.id} for product {product.stripe_product_id}")
            
            # Update or create price in database
            Price.objects.update_or_create(
                stripe_price_id=stripe_price.id,
                defaults={
                    'product': product,
                    'active': True,
                    'unit_amount': unit_amount,
                    'currency': 'usd',
                    'recurring': recurring_config
                }
            )
            
        except Exception as e:
            logger.error(f"Error syncing product prices: {str(e)}")
            raise

    @staticmethod
    def _sync_subscription_plan(plan):
        """
        Sync a SubscriptionPlan model to Stripe Product
        """
        try:
            product = Product.objects.get(
                product_type='subscription',
                subscription_plan=plan
            )
            stripe_product = stripe.Product.modify(
                product.stripe_product_id,
                name=plan.name,
                description=plan.description,
                active=plan.is_active,
                metadata={
                    'type': 'subscription',
                    'content_id': str(plan.id)
                }
            )
        except Product.DoesNotExist:
            stripe_product = stripe.Product.create(
                name=plan.name,
                description=plan.description,
                metadata={
                    'type': 'subscription',
                    'content_id': str(plan.id)
                }
            )
            product = Product.objects.create(
                stripe_product_id=stripe_product.id,
                name=plan.name,
                description=plan.description,
                active=plan.is_active,
                product_type='subscription',
                subscription_plan=plan,
                content_id=plan.id
            )
        
        # Sync prices with original price (no promo applied)
        StripeService._sync_product_prices(product, plan, apply_promo=False)

    @staticmethod
    def _sync_solution(solution):
        """
        Sync a Solution model to Stripe Product
        """
        try:
            product = Product.objects.get(
                product_type='solution',
                solution=solution
            )
            stripe_product = stripe.Product.modify(
                product.stripe_product_id,
                name=solution.name,
                description=solution.description,
                active=solution.is_active,
                metadata={
                    'type': 'solution',
                    'solution_type': solution.solution_type,
                    'content_id': str(solution.id)
                }
            )
        except Product.DoesNotExist:
            stripe_product = stripe.Product.create(
                name=solution.name,
                description=solution.description,
                metadata={
                    'type': 'solution',
                    'solution_type': solution.solution_type,
                    'content_id': str(solution.id)
                }
            )
            product = Product.objects.create(
                stripe_product_id=stripe_product.id,
                name=solution.name,
                description=solution.description,
                active=solution.is_active,
                product_type='solution',
                solution=solution,
                content_id=solution.id
            )
        
        # Sync prices with original price (no promo applied)
        StripeService._sync_product_prices(product, solution, apply_promo=False)

    @staticmethod
    def create_subscription(customer, price, quantity=1):
        """
        Create a new subscription
        """
        try:
            # Create subscription in Stripe
            subscription = stripe.Subscription.create(
                customer=customer.stripe_customer_id,
                items=[{
                    'price': price.stripe_price_id,
                    'quantity': quantity
                }],
                payment_behavior='default_incomplete',
                expand=['latest_invoice.payment_intent']
            )
            
            # Create subscription record
            subscription_record = Subscription.objects.create(
                customer=customer,
                stripe_subscription_id=subscription.id,
                stripe_subscription_item_id=subscription.items.data[0].id,
                status=subscription.status,
                current_period_start=timezone.datetime.fromtimestamp(subscription.current_period_start),
                current_period_end=timezone.datetime.fromtimestamp(subscription.current_period_end)
            )
            
            return subscription_record
            
        except Exception as e:
            logger.error(f"Error creating subscription: {str(e)}")
            raise ValidationError(f"Error creating subscription: {str(e)}")

    @staticmethod
    def update_subscription_quantity(subscription_id, quantity):
        """
        Update subscription quantity (number of seats)
        """
        try:
            subscription = Subscription.objects.get(id=subscription_id)
            
            # Update subscription in Stripe
            stripe_subscription = stripe.Subscription.modify(
                subscription.stripe_subscription_id,
                items=[{
                    'id': subscription.stripe_subscription_item_id,
                    'quantity': quantity
                }]
            )
            
            # Update subscription record
            subscription.status = stripe_subscription.status
            subscription.current_period_start = timezone.datetime.fromtimestamp(stripe_subscription.current_period_start)
            subscription.current_period_end = timezone.datetime.fromtimestamp(stripe_subscription.current_period_end)
            subscription.save()
            
            return subscription
            
        except Subscription.DoesNotExist:
            raise ValidationError("Subscription not found")
        except Exception as e:
            logger.error(f"Error updating subscription: {str(e)}")
            raise ValidationError(f"Error updating subscription: {str(e)}")

    @classmethod
    def get_service_stripe_info(cls, service):
        """Get Stripe product and price information for a service"""
        try:
            product = Product.objects.filter(
                service=service,
                active=True
            ).first()
            
            if not product:
                return None
                
            # Get active prices from our database
            db_prices = Price.objects.filter(
                product=product,
                active=True
            ).values('stripe_price_id', 'unit_amount', 'currency', 'recurring')
            
            # Get active prices from Stripe
            stripe_prices = stripe.Price.list(
                product=product.stripe_product_id,
                active=True
            ).data
            
            # Create a set of active Stripe price IDs
            active_stripe_price_ids = {price.id for price in stripe_prices}
            
            # Filter prices to only include those that are active in both our DB and Stripe
            active_prices = [
                price for price in db_prices 
                if price['stripe_price_id'] in active_stripe_price_ids
            ]
            
            return {
                'stripe_product_id': product.stripe_product_id,
                'name': product.name,
                'description': product.description,
                'prices': active_prices
            }
            
        except Exception as e:
            logger.error(f"Error getting Stripe info for service {service.id}: {str(e)}")
            return None

    @classmethod
    def get_subscription_plan_stripe_info(cls, plan):
        """Get Stripe product and price information for a subscription plan"""
        try:
            product = Product.objects.filter(
                subscription_plan=plan,
                active=True
            ).first()
            
            if not product:
                return None
                
            # Get active prices from our database
            db_prices = Price.objects.filter(
                product=product,
                active=True
            ).values('stripe_price_id', 'unit_amount', 'currency', 'recurring')
            
            # Get active prices from Stripe
            stripe_prices = stripe.Price.list(
                product=product.stripe_product_id,
                active=True
            ).data
            
            # Create a set of active Stripe price IDs
            active_stripe_price_ids = {price.id for price in stripe_prices}
            
            # Filter prices to only include those that are active in both our DB and Stripe
            active_prices = [
                price for price in db_prices 
                if price['stripe_price_id'] in active_stripe_price_ids
            ]
            
            return {
                'stripe_product_id': product.stripe_product_id,
                'name': product.name,
                'description': product.description,
                'prices': active_prices
            }
            
        except Exception as e:
            logger.error(f"Error getting Stripe info for subscription plan {plan.id}: {str(e)}")
            return None

    @classmethod
    def get_solution_stripe_info(cls, solution):
        """Get Stripe product and price information for a solution"""
        try:
            product = Product.objects.filter(
                solution=solution,
                active=True
            ).first()
            
            if not product:
                return None
                
            # Get active prices from our database
            db_prices = Price.objects.filter(
                product=product,
                active=True
            ).values('stripe_price_id', 'unit_amount', 'currency', 'recurring')
            
            # Get active prices from Stripe
            stripe_prices = stripe.Price.list(
                product=product.stripe_product_id,
                active=True
            ).data
            
            # Create a set of active Stripe price IDs
            active_stripe_price_ids = {price.id for price in stripe_prices}
            
            # Filter prices to only include those that are active in both our DB and Stripe
            active_prices = [
                price for price in db_prices 
                if price['stripe_price_id'] in active_stripe_price_ids
            ]
            
            return {
                'stripe_product_id': product.stripe_product_id,
                'name': product.name,
                'description': product.description,
                'prices': active_prices
            }
            
        except Exception as e:
            logger.error(f"Error getting Stripe info for solution {solution.id}: {str(e)}")
            return None

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _delete_stripe_product(product):
        """
        Archive a Stripe product and its associated prices
        """
        try:
            logger.info(f"Starting archiving of Stripe product {product.stripe_product_id}")
            
            # Archive all prices associated with the product
            existing_prices = stripe.Price.list(product=product.stripe_product_id)
            logger.info(f"Found {len(existing_prices.data)} prices for product {product.stripe_product_id}")
            
            for price in existing_prices.data:
                try:
                    # Archive the price instead of deleting it
                    stripe.Price.modify(price.id, active=False)
                    logger.info(f"Archived Stripe price {price.id}")
                except stripe.error.StripeError as e:
                    logger.error(f"Error archiving Stripe price {price.id}: {str(e)}")
                    raise
            
            # Archive the product from Stripe first
            try:
                # First verify the product exists in Stripe
                stripe_product = stripe.Product.retrieve(product.stripe_product_id)
                if stripe_product:
                    # Then archive it
                    stripe.Product.modify(product.stripe_product_id, active=False)
                    logger.info(f"Archived Stripe product {product.stripe_product_id}")
                else:
                    logger.warning(f"Product {product.stripe_product_id} not found in Stripe")
            except stripe.error.InvalidRequestError as e:
                if "No such product" in str(e):
                    logger.warning(f"Product {product.stripe_product_id} not found in Stripe")
                else:
                    raise
            except stripe.error.StripeError as e:
                logger.error(f"Stripe error archiving product {product.stripe_product_id}: {str(e)}")
                raise
            
            # Only delete from database after successful Stripe archiving
            product.delete()
            logger.info(f"Deleted product {product.id} from database")
            
        except stripe.error.StripeError as e:
            logger.error(f"Error archiving Stripe product {product.stripe_product_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error archiving Stripe product: {str(e)}")
            raise

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def delete_service_stripe_product(service):
        """
        Archive Stripe product and prices for a service
        """
        try:
            logger.info(f"Starting archiving of Stripe product for service {service.id}")
            
            # Find product by service or content_id
            product = Product.objects.filter(
                product_type='service',
                service=service
            ).first()
            
            # If not found by service, try content_id
            if not product:
                product = Product.objects.filter(
                    product_type='service',
                    content_id=service.id
                ).first()
                if product:
                    logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for service {service.id} using content_id")
            
            # If still not found, try to find by metadata
            if not product:
                try:
                    # Try to find the product directly in Stripe by metadata
                    stripe_products = stripe.Product.list(
                        active=True,
                        limit=100  # Increase limit to ensure we find the product
                    )
                    
                    # Filter products by metadata after retrieving them
                    matching_products = [
                        p for p in stripe_products.data 
                        if p.metadata.get('type') == 'service' and p.metadata.get('content_id') == str(service.id)
                    ]
                    
                    if matching_products:
                        stripe_product = matching_products[0]
                        logger.info(f"Found Stripe product {stripe_product.id} for service {service.id} using Stripe API")
                        
                        # Create a temporary product object to delete
                        product = Product(
                            stripe_product_id=stripe_product.id,
                            name=stripe_product.name,
                            product_type='service',
                            content_id=service.id
                        )
                    else:
                        logger.warning(f"No matching Stripe product found for service {service.id}")
                        return False
                except stripe.error.StripeError as e:
                    logger.error(f"Error finding Stripe product for service {service.id}: {str(e)}")
                    raise
            
            if product and product.stripe_product_id:
                logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for service {service.id}")
                StripeService._delete_stripe_product(product)
                logger.info(f"Successfully archived Stripe product for service {service.id}")
                return True
            else:
                logger.warning(f"No valid Stripe product found for service {service.id}")
                return False
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error archiving product for service {service.id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error archiving Stripe product for service {service.id}: {str(e)}")
            raise

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def delete_subscription_plan_stripe_product(plan):
        """
        Archive Stripe product and prices for a subscription plan
        """
        try:
            logger.info(f"Starting archiving of Stripe product for subscription plan {plan.id}")
            
            # Find product by subscription_plan or content_id
            product = Product.objects.filter(
                product_type='subscription',
                subscription_plan=plan
            ).first()
            
            # If not found by subscription_plan, try content_id
            if not product:
                product = Product.objects.filter(
                    product_type='subscription',
                    content_id=plan.id
                ).first()
                if product:
                    logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for subscription plan {plan.id} using content_id")
            
            # If still not found, try to find by metadata
            if not product:
                try:
                    # Try to find the product directly in Stripe by metadata
                    stripe_products = stripe.Product.list(
                        active=True,
                        limit=100  # Increase limit to ensure we find the product
                    )
                    
                    # Filter products by metadata after retrieving them
                    matching_products = [
                        p for p in stripe_products.data 
                        if p.metadata.get('type') == 'subscription' and p.metadata.get('content_id') == str(plan.id)
                    ]
                    
                    if matching_products:
                        stripe_product = matching_products[0]
                        logger.info(f"Found Stripe product {stripe_product.id} for subscription plan {plan.id} using Stripe API")
                        
                        # Create a temporary product object to delete
                        product = Product(
                            stripe_product_id=stripe_product.id,
                            name=stripe_product.name,
                            product_type='subscription',
                            content_id=plan.id
                        )
                    else:
                        logger.warning(f"No matching Stripe product found for subscription plan {plan.id}")
                        return False
                except stripe.error.StripeError as e:
                    logger.error(f"Error finding Stripe product for subscription plan {plan.id}: {str(e)}")
                    raise
            
            if product and product.stripe_product_id:
                logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for subscription plan {plan.id}")
                StripeService._delete_stripe_product(product)
                logger.info(f"Successfully archived Stripe product for subscription plan {plan.id}")
                return True
            else:
                logger.warning(f"No valid Stripe product found for subscription plan {plan.id}")
                return False
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error archiving product for subscription plan {plan.id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error archiving Stripe product for subscription plan {plan.id}: {str(e)}")
            raise

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def delete_solution_stripe_product(solution):
        """
        Archive Stripe product and prices for a solution
        """
        try:
            logger.info(f"Starting archiving of Stripe product for solution {solution.id}")
            
            # Find product by solution or content_id
            product = Product.objects.filter(
                product_type='solution',
                solution=solution
            ).first()
            
            # If not found by solution, try content_id
            if not product:
                product = Product.objects.filter(
                    product_type='solution',
                    content_id=solution.id
                ).first()
                if product:
                    logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for solution {solution.id} using content_id")
            
            if product and product.stripe_product_id:
                logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for solution {solution.id}")
                try:
                    # Try to retrieve the product directly from Stripe using stripe_product_id
                    stripe_product = stripe.Product.retrieve(product.stripe_product_id)
                    if stripe_product:
                        logger.info(f"Found Stripe product {stripe_product.id} for solution {solution.id}")
                        StripeService._delete_stripe_product(product)
                        logger.info(f"Successfully archived Stripe product for solution {solution.id}")
                        return True
                    else:
                        logger.warning(f"Stripe product {product.stripe_product_id} not found in Stripe")
                        return False
                except stripe.error.InvalidRequestError as e:
                    if "No such product" in str(e):
                        logger.warning(f"Stripe product {product.stripe_product_id} not found in Stripe")
                        return False
                    else:
                        raise
                except stripe.error.StripeError as e:
                    logger.error(f"Stripe error retrieving product {product.stripe_product_id}: {str(e)}")
                    raise
            else:
                logger.warning(f"No valid Stripe product found for solution {solution.id}")
                return False
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error archiving product for solution {solution.id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error archiving Stripe product for solution {solution.id}: {str(e)}")
            raise

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def delete_stripe_product_by_id(stripe_product_id):
        """
        Archive a Stripe product directly by its ID
        This can be used as a fallback if the database record is not found
        """
        try:
            logger.info(f"Starting direct archiving of Stripe product {stripe_product_id}")
            
            # Archive all prices associated with the product
            existing_prices = stripe.Price.list(product=stripe_product_id)
            logger.info(f"Found {len(existing_prices.data)} prices for product {stripe_product_id}")
            
            for price in existing_prices.data:
                try:
                    # Archive the price instead of deleting it
                    stripe.Price.modify(price.id, active=False)
                    logger.info(f"Archived Stripe price {price.id}")
                except stripe.error.StripeError as e:
                    logger.error(f"Error archiving Stripe price {price.id}: {str(e)}")
            
            # Archive the product
            try:
                # First verify the product exists in Stripe
                stripe_product = stripe.Product.retrieve(stripe_product_id)
                if stripe_product:
                    # Then archive it
                    stripe.Product.modify(stripe_product_id, active=False)
                    logger.info(f"Archived Stripe product {stripe_product_id}")
                else:
                    logger.warning(f"Product {stripe_product_id} not found in Stripe")
            except stripe.error.InvalidRequestError as e:
                if "No such product" in str(e):
                    logger.warning(f"Product {stripe_product_id} not found in Stripe")
                else:
                    raise
            except stripe.error.StripeError as e:
                logger.error(f"Stripe error archiving product {stripe_product_id}: {str(e)}")
                raise
            
            return True
            
        except stripe.error.StripeError as e:
            logger.error(f"Error archiving Stripe product {stripe_product_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error archiving Stripe product: {str(e)}")
            raise

    @staticmethod
    def revoke_service_access(service_access):
        """
        Revoke access to a service
        """
        try:
            # Update service access status
            service_access.status = 'revoked'
            service_access.save()

            # If there's a payment, update its status
            if service_access.payment:
                service_access.payment.status = 'refunded'
                service_access.payment.save()

            return True
        except Exception as e:
            logger.error(f"Error revoking service access {service_access.id}: {str(e)}")
            raise

    @staticmethod
    def revoke_subscription_access(subscription_access):
        """
        Revoke access to a subscription
        """
        try:
            # Cancel subscription in Stripe
            subscription = subscription_access.subscription
            stripe_subscription = stripe.Subscription.delete(
                subscription.stripe_subscription_id
            )

            # Update subscription status
            subscription.status = 'canceled'
            subscription.canceled_at = timezone.now()
            subscription.save()

            # Update subscription access status
            subscription_access.status = 'canceled'
            subscription_access.save()

            return True
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error revoking subscription access {subscription_access.id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error revoking subscription access {subscription_access.id}: {str(e)}")
            raise

    @staticmethod
    def revoke_solution_access(solution_access):
        """
        Revoke access to a solution
        """
        try:
            # Cancel subscription in Stripe
            subscription = solution_access.subscription
            stripe_subscription = stripe.Subscription.delete(
                subscription.stripe_subscription_id
            )

            # Update subscription status
            subscription.status = 'canceled'
            subscription.canceled_at = timezone.now()
            subscription.save()

            # Update solution access status
            solution_access.status = 'canceled'
            solution_access.save()

            return True
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error revoking solution access {solution_access.id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error revoking solution access {solution_access.id}: {str(e)}")
            raise

    @staticmethod
    def _create_stripe_promo_code(promo):
        """Create a promo code in Stripe for a ServicePromotion"""
        try:
            # Get the first service that has this promotion
            service = promo.services.first()
            if not service:
                raise ValueError(f"No service found for promotion {promo.id}")
            
            # Create coupon
            stripe_coupon = stripe.Coupon.create(
                percent_off=float(promo.discount_percentage),
                duration='once',
                metadata={
                    'promo_id': str(promo.id),
                    'service_id': str(service.id)
                }
            )
            
            # Create promotion code
            stripe_promo = stripe.PromotionCode.create(
                coupon=stripe_coupon.id,
                code=promo.code,
                restrictions={
                    'minimum_amount': int(service.price * 100),  # Convert to cents
                    'minimum_amount_currency': 'usd'
                }
            )
            
            return stripe_promo.id
            
        except Exception as e:
            logger.error(f"Error creating Stripe promo code for promotion {promo.id}: {str(e)}")
            raise

    @staticmethod
    def _sync_service_promotions(service):
        """Sync all active promotions for a service to Stripe"""
        try:
            active_promos = service.active_promotions.filter(
                is_active=True,
                start_date__lte=timezone.now(),
                end_date__gte=timezone.now()
            )
            
            for promo in active_promos:
                if not promo.stripe_promo_id:
                    # Create new promo code in Stripe
                    stripe_promo_id = StripeService._create_stripe_promo_code(promo)
                    promo.stripe_promo_id = stripe_promo_id
                    promo.save()
                    
        except Exception as e:
            logger.error(f"Error syncing promotions for service {service.id}: {str(e)}")
            raise

    @staticmethod
    def get_or_create_discounted_price(service, discounted_price, promotion=None):
        """
        Lấy hoặc tạo Price Stripe cho service với giá đã giảm (theo promotion).
        """

        # Lấy product Stripe
        product = Product.objects.get(service=service, active=True)
        unit_amount = int(discounted_price * 100)
        currency = 'usd'
        promo_id = str(promotion.id) if promotion else ''

        # Tìm price đã tồn tại
        existing_prices = stripe.Price.list(
            product=product.stripe_product_id,
            active=True
        ).data

        for price in existing_prices:
            if (
                price.unit_amount == unit_amount
                and price.currency.lower() == currency
                and price.metadata.get('promotion_id', '') == promo_id
            ):
                # Đã có price đúng
                Price.objects.update_or_create(
                    stripe_price_id=price.id,
                    defaults={
                        'product': product,
                        'active': True,
                        'unit_amount': unit_amount,
                        'currency': currency,
                    }
                )
                return price.id

        # Nếu chưa có, tạo mới
        price_params = {
            'product': product.stripe_product_id,
            'unit_amount': unit_amount,
            'currency': currency,
            'metadata': {
                'content_id': str(service.id),
                'promotion_id': promo_id,
            }
        }
        stripe_price = stripe.Price.create(**price_params)
        Price.objects.update_or_create(
            stripe_price_id=stripe_price.id,
            defaults={
                'product': product,
                'active': True,
                'unit_amount': unit_amount,
                'currency': currency,
            }
        )
        return stripe_price.id

class UserPaymentService:
    """
    Service for handling user payment operations
    """
    def __init__(self):
        self.stripe = stripe
        self.stripe.api_key = settings.STRIPE_SECRET_KEY

    def create_stripe_connect_account(self, user):
        """
        Create a Stripe Connect account for a user
        """
        try:
            account = self.stripe.Account.create(
                type='express',
                country='US',
                email=user.email,
                capabilities={
                    'card_payments': {'requested': True},
                    'transfers': {'requested': True},
                },
                business_type='individual',
            )

            # Create or update user payment profile
            profile, _ = UserPaymentProfile.objects.get_or_create(user=user)
            profile.stripe_account_id = account.id
            profile.save()

            # Create account link for onboarding
            account_link = self.stripe.AccountLink.create(
                account=account.id,
                refresh_url=f"{settings.FRONTEND_URL}",
                return_url=f"{settings.FRONTEND_URL}",
                type='account_onboarding',
            )

            return account_link.url

        except stripe.error.StripeError as e:
            logger.error(f"Error creating Stripe Connect account: {str(e)}")
            raise

    def create_transfer(self, sender, receiver, amount, currency='usd', message='', transfer_type='transfer'):
        """
        Create a transfer between users
        """
        try:
            # Validate users have payment profiles
            sender_profile = getattr(sender, 'payment_profile', None)
            receiver_profile = getattr(receiver, 'payment_profile', None)

            if not sender_profile or not receiver_profile:
                raise ValueError("Both users must have payment profiles")

            if not receiver_profile.stripe_account_id:
                raise ValueError("Receiver must have a connected Stripe account")

            # Calculate platform fee
            platform_fee = PlatformFee.objects.filter(
                is_active=True,
                min_amount__lte=amount,
                max_amount__gte=amount
            ).first()

            if not platform_fee:
                raise ValueError("No applicable platform fee found")

            fee_amount = int(amount * (platform_fee.percentage / 100))

            # Create payment intent
            payment_intent = self.stripe.PaymentIntent.create(
                amount=amount,
                currency=currency,
                transfer_data={
                    'destination': receiver_profile.stripe_account_id,
                    'application_fee_amount': fee_amount,
                },
                metadata={
                    'sender_id': str(sender.id),
                    'receiver_id': str(receiver.id),
                    'transfer_type': transfer_type,
                }
            )

            # Create transfer record
            transfer = UserTransfer.objects.create(
                sender=sender,
                receiver=receiver,
                amount=amount,
                currency=currency,
                message=message,
                transfer_type=transfer_type,
                platform_fee=platform_fee,
                platform_fee_amount=fee_amount,
                status='pending',
                stripe_payment_intent_id=payment_intent.id,
            )

            return transfer, payment_intent.client_secret

        except stripe.error.StripeError as e:
            logger.error(f"Error creating transfer: {str(e)}")
            raise

    def handle_transfer_webhook(self, event):
        """
        Handle Stripe webhook events for transfers
        """
        try:
            if event.type == 'payment_intent.succeeded':
                payment_intent = event.data.object
                transfer = UserTransfer.objects.get(stripe_payment_intent_id=payment_intent.id)
                transfer.status = 'completed'
                transfer.save()

            elif event.type == 'payment_intent.payment_failed':
                payment_intent = event.data.object
                transfer = UserTransfer.objects.get(stripe_payment_intent_id=payment_intent.id)
                transfer.status = 'failed'
                transfer.save()

        except UserTransfer.DoesNotExist:
            logger.error(f"Transfer not found for payment intent: {payment_intent.id}")
        except Exception as e:
            logger.error(f"Error handling transfer webhook: {str(e)}")
            raise 