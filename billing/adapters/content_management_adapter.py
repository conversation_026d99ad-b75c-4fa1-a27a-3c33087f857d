"""
Adapter to connect billing with content_management without tight coupling
"""
import logging
from typing import Dict, List, Optional, Any
from decimal import Decimal
from django.apps import apps

from ..interfaces.service_provider import (
    ServiceProviderInterface,
    PricingProviderInterface,
    PaymentMetadataProviderInterface,
    ServiceDiscoveryInterface
)

logger = logging.getLogger(__name__)


class ContentManagementAdapter(
    ServiceProviderInterface,
    PricingProviderInterface,
    PaymentMetadataProviderInterface,
    ServiceDiscoveryInterface
):
    """
    Adapter that implements interfaces to connect with content_management
    Uses Django's app registry to avoid direct imports
    """
    
    def __init__(self):
        # Use Django's app registry to get models without direct imports
        self._service_model = None
        self._subscription_plan_model = None
        self._solution_model = None
    
    @property
    def service_model(self):
        """Lazy loading of Service model"""
        if not self._service_model:
            try:
                self._service_model = apps.get_model('content_management', 'Service')
            except LookupError:
                logger.error("content_management.Service model not found")
                return None
        return self._service_model
    
    def get_service_by_type(self, service_type: str, service_code: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get service by type and optional code with enhanced validation"""
        try:
            # Input validation
            if not service_type or not isinstance(service_type, str):
                logger.error("Invalid service_type provided")
                return None
                
            service_type = service_type.strip()
            if not service_type:
                logger.error("Empty service_type provided")
                return None
            
            if not self.service_model:
                logger.error("Service model not available")
                return None
                
            filter_kwargs = {
                'service_type': service_type,
                'is_active': True
            }
            
            if service_code:
                if isinstance(service_code, str) and service_code.strip():
                    filter_kwargs['service_code'] = service_code.strip()
                else:
                    logger.warning(f"Invalid service_code provided: {service_code}")
            
            # Database query with error handling
            try:
                service = self.service_model.objects.filter(**filter_kwargs).first()
            except Exception as db_error:
                logger.error(f"Database error while fetching service: {str(db_error)}")
                return None
            
            if not service:
                logger.info(f"No service found for type: {service_type}, code: {service_code}")
                return None
            
            # Validate service before serialization
            if not hasattr(service, 'id') or not service.id:
                logger.error("Service missing required ID field")
                return None
            
            serialized = self._serialize_service(service)
            
            # Validate serialized data
            if not serialized or not isinstance(serialized, dict):
                logger.error("Service serialization failed")
                return None
            
            # Check required fields
            required_fields = ['id', 'name', 'price']
            for field in required_fields:
                if field not in serialized or serialized[field] is None:
                    logger.error(f"Serialized service missing required field: {field}")
                    return None
            
            return serialized
            
        except Exception as e:
            logger.error(f"Error getting service by type {service_type}: {str(e)}")
            return None
    
    def get_service_pricing(self, service_id: str) -> Dict[str, Any]:
        """Get pricing information for a service"""
        try:
            if not self.service_model:
                return {}
                
            service = self.service_model.objects.filter(id=service_id).first()
            
            if not service:
                return {}
            
            # Calculate pricing (assuming service has pricing methods)
            base_price = getattr(service, 'price', 0)
            discounted_price = getattr(service, 'get_discounted_price', lambda: base_price)()
            
            return {
                'base_price': Decimal(str(base_price)),
                'discounted_price': Decimal(str(discounted_price)),
                'currency': getattr(service, 'currency', 'usd'),
                'stripe_price_id': getattr(service, 'stripe_price_id', None)
            }
            
        except Exception as e:
            logger.error(f"Error getting service pricing for {service_id}: {str(e)}")
            return {}
    
    def validate_service_access(self, service_id: str, user_id: str) -> bool:
        """Check if user has access to the service"""
        try:
            if not self.service_model:
                return False
                
            service = self.service_model.objects.filter(id=service_id, is_active=True).first()
            return service is not None
            
        except Exception as e:
            logger.error(f"Error validating service access: {str(e)}")
            return False
    
    def get_subscription_plan(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """Get subscription plan details - placeholder implementation"""
        return None
    
    def get_solution(self, solution_id: str) -> Optional[Dict[str, Any]]:
        """Get enterprise solution details - placeholder implementation"""
        return None
    
    def calculate_service_price(self, service_id: str, user_id: Optional[str] = None) -> Decimal:
        """Calculate final price for service including discounts"""
        try:
            pricing = self.get_service_pricing(service_id)
            return pricing.get('discounted_price', Decimal('0'))
            
        except Exception as e:
            logger.error(f"Error calculating service price: {str(e)}")
            return Decimal('0')
    
    def calculate_platform_fee(self, amount: Decimal, service_type: str) -> Decimal:
        """Calculate platform fee for a transaction"""
        try:
            # Default platform fee logic - can be customized
            if service_type == 'enterprise':
                return amount * Decimal('0.10')  # 10% for enterprise
            elif service_type == 'CONSULTATION':
                return amount * Decimal('0.05')  # 5% for consultation
            else:
                return amount * Decimal('0.03')  # 3% for other services
                
        except Exception as e:
            logger.error(f"Error calculating platform fee: {str(e)}")
            return Decimal('0')
    
    def get_available_promotions(self, service_id: str, user_id: str) -> List[Dict[str, Any]]:
        """Get available promotions for a service and user"""
        return []  # Placeholder implementation
    
    def build_payment_metadata(self, payment_type: str, **kwargs) -> Dict[str, str]:
        """Build standardized payment metadata"""
        metadata = {
            'payment_type': payment_type,
            'created_by': 'billing_system'
        }
        
        # Add type-specific metadata
        if payment_type == 'appointment':
            metadata.update({
                'appointment_id': str(kwargs.get('appointment_id', '')),
                'doctor_id': str(kwargs.get('doctor_id', '')),
                'patient_id': str(kwargs.get('patient_id', ''))
            })
        elif payment_type == 'consultation':
            metadata.update({
                'doctor_id': str(kwargs.get('doctor_id', '')),
                'consultation_type': kwargs.get('consultation_type', 'general')
            })
        
        # Add service information if available
        if 'service_id' in kwargs:
            metadata['service_id'] = str(kwargs['service_id'])
        
        return metadata
    
    def extract_payment_info(self, metadata: Dict[str, str]) -> Dict[str, Any]:
        """Extract payment information from metadata"""
        return {
            'payment_type': metadata.get('payment_type', 'unknown'),
            'service_id': metadata.get('service_id'),
            'appointment_id': metadata.get('appointment_id'),
            'doctor_id': metadata.get('doctor_id'),
            'patient_id': metadata.get('patient_id'),
            'consultation_type': metadata.get('consultation_type')
        }
    
    def find_services_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Find services by category"""
        try:
            if not self.service_model:
                return []
                
            services = self.service_model.objects.filter(
                category=category,
                is_active=True
            )
            
            return [self._serialize_service(service) for service in services]
            
        except Exception as e:
            logger.error(f"Error finding services by category {category}: {str(e)}")
            return []
    
    def search_services(self, query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search services with filters"""
        try:
            if not self.service_model:
                return []
                
            queryset = self.service_model.objects.filter(is_active=True)
            
            # Apply search query
            if query:
                queryset = queryset.filter(name__icontains=query)
            
            # Apply filters
            if filters:
                queryset = queryset.filter(**filters)
            
            return [self._serialize_service(service) for service in queryset[:20]]  # Limit results
            
        except Exception as e:
            logger.error(f"Error searching services: {str(e)}")
            return []
    
    def _serialize_service(self, service) -> Dict[str, Any]:
        """Serialize service object to dictionary"""
        return {
            'id': str(service.id),
            'name': getattr(service, 'name', ''),
            'price': Decimal(str(getattr(service, 'price', 0))),
            'currency': getattr(service, 'currency', 'usd'),
            'service_type': getattr(service, 'service_type', ''),
            'is_active': getattr(service, 'is_active', False),
            'stripe_price_id': getattr(service, 'stripe_price_id', None),
            'description': getattr(service, 'description', ''),
            'category': getattr(service, 'category', '')
        } 