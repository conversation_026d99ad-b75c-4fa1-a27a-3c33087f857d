from django.contrib import admin
from .models import Service, SubscriptionPlan, Solution, TabAlert, ServicePromotion, InvitationCode

@admin.register(InvitationCode)
class InvitationCodeAdmin(admin.ModelAdmin):
    list_display = ('code', 'description', 'current_uses', 'max_uses', 'is_active', 'expires_at', 'created_by', 'created_at')
    list_filter = ('is_active', 'created_at', 'expires_at')
    search_fields = ('code', 'description')
    ordering = ('-created_at',)
    readonly_fields = ('current_uses', 'created_at', 'updated_at')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('code', 'description', 'created_by')
        }),
        ('Usage Settings', {
            'fields': ('max_uses', 'current_uses', 'is_active', 'expires_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('name', 'service_code', 'service_type', 'price', 'is_active', 'order')
    list_filter = ('service_type', 'is_active')
    search_fields = ('name', 'service_code')
    ordering = ('order', 'name')

@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    list_display = ('name', 'price', 'billing_cycle', 'is_active', 'order')
    list_filter = ('billing_cycle', 'is_active')
    search_fields = ('name',)
    ordering = ('order', 'name')

@admin.register(Solution)
class SolutionAdmin(admin.ModelAdmin):
    list_display = ('name', 'solution_type', 'price', 'is_active', 'order')
    list_filter = ('solution_type', 'is_active')
    search_fields = ('name',)
    ordering = ('order', 'name')

@admin.register(ServicePromotion)
class ServicePromotionAdmin(admin.ModelAdmin):
    list_display = ('code', 'discount_percentage', 'start_date', 'end_date', 'is_active', 'promotion_type')
    list_filter = ('is_active', 'promotion_type', 'start_date', 'end_date')
    search_fields = ('code', 'description')
    ordering = ('-created_at',)

@admin.register(TabAlert)
class TabAlertAdmin(admin.ModelAdmin):
    list_display = ('message', 'tab', 'start_date', 'end_date', 'is_active')
    list_filter = ('tab', 'is_active', 'start_date', 'end_date')
    search_fields = ('message',)
    ordering = ('-created_at',)
