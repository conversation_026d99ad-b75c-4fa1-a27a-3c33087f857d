from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .api.views.admin import (
    ServiceViewSet, SubscriptionPlanViewSet, SolutionViewSet,
    TabAlertViewSet, ServicePromotionViewSet, ServiceWithPromotionViewSet
)
from .api.views.public import (
    PublicServiceViewSet, PublicSubscriptionPlanViewSet,
    PublicSolutionViewSet, PublicTabAlertViewSet
)
from .api.views.promo_code import VerifyPromoCodeView
from .api.views.consultation_views import ConsultationServiceViewSet
from .api.views.invitation_code import InvitationCodeViewSet

# Admin router
admin_router = DefaultRouter()
admin_router.register(r'services', ServiceViewSet, basename='admin-service')
admin_router.register(r'subscription-plans', SubscriptionPlanViewSet, basename='admin-subscription-plan')
admin_router.register(r'solutions', SolutionViewSet, basename='admin-solution')
admin_router.register(r'tab-alerts', TabAlertViewSet, basename='admin-tab-alert')
admin_router.register(r'promotions', ServicePromotionViewSet, basename='admin-promotion')
admin_router.register(r'services-with-promotions', ServiceWithPromotionViewSet, basename='admin-service-with-promotions')
admin_router.register(r'invitation-codes', InvitationCodeViewSet, basename='admin-invitation-code')

# Public router
public_router = DefaultRouter()
public_router.register(r'services', PublicServiceViewSet, basename='public-service')
public_router.register(r'subscription-plans', PublicSubscriptionPlanViewSet, basename='public-subscription-plan')
public_router.register(r'solutions', PublicSolutionViewSet, basename='public-solution')
public_router.register(r'tab-alerts', PublicTabAlertViewSet, basename='public-tab-alert')
public_router.register(r'consultation-services', ConsultationServiceViewSet, basename='consultation-service')

app_name = 'content_management'

urlpatterns = [
    path('admin/', include(admin_router.urls)),
    path('public/', include(public_router.urls)),
    # API endpoint mới để verify promo code
    path('public/verify-promo-code/', VerifyPromoCodeView.as_view(), name='verify-promo-code'),
] 