# from django.urls import path, include
# from rest_framework.routers import DefaultRouter
# from .admin import (
#     ServiceViewSet,
#     ServiceWithPromotionViewSet,
#     SubscriptionPlanViewSet,
#     SolutionViewSet,
#     TabAlertViewSet
# )

# router = DefaultRouter()
# router.register(r'services', ServiceViewSet)
# router.register(r'services-with-promotions', ServiceWithPromotionViewSet)
# router.register(r'subscription-plans', SubscriptionPlanViewSet)
# router.register(r'solutions', SolutionViewSet)
# router.register(r'tab-alerts', TabAlertViewSet)

# urlpatterns = [
#     path('admin/', include(router.urls)),
# ] 