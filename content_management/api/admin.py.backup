from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from django.db.models import Q
from django.shortcuts import get_object_or_404
from ..models import Service, SubscriptionPlan, Solution, TabAlert, ServicePromotion
from ..serializers import (
    ServiceSerializer, SubscriptionPlanSerializer,
    SolutionSerializer, TabAlertSerializer, ServicePromotionSerializer,
    ServiceWithPromotionSerializer, ServiceBulkOrderSerializer,
    SubscriptionPlanBulkOrderSerializer, SolutionBulkOrderSerializer
)
import logging
logger = logging.getLogger(__name__)
class ServicePromotionViewSet(viewsets.ModelViewSet):
    queryset = ServicePromotion.objects.all()
    serializer_class = ServicePromotionSerializer

    def get_object(self):
        """
        Override get_object to use all_objects manager
        """
        queryset = ServicePromotion.all_objects.all()
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
        obj = get_object_or_404(queryset, **filter_kwargs)
        self.check_object_permissions(self.request, obj)
        return obj

    def get_queryset(self):
        # Get filter params
        show_deleted = self.request.query_params.get('show_deleted', 'false').lower() == 'true'
        show_expired = self.request.query_params.get('show_expired', 'false').lower() == 'true'
        show_inactive = self.request.query_params.get('show_inactive', 'false').lower() == 'true'
        show_only_inactive = self.request.query_params.get('show_only_inactive', 'false').lower() == 'true'
        print("show_deleted:", show_deleted)
        print("show_expired:", show_expired)
        print("show_inactive:", show_inactive)
        print("show_only_inactive:", show_only_inactive)
        # Get base queryset
        if show_deleted:
            queryset = ServicePromotion.all_objects.all()
        else:
            queryset = ServicePromotion.objects.all()
            
        print("Initial queryset:", queryset)
        
        # Apply additional filters
        now = timezone.now()
        print("Current time:", now)
        
        if show_only_inactive:
            # Show only inactive, expired or deleted promotions
            queryset = queryset.filter(
                Q(is_active=False) |  # Not active
                Q(end_date__lte=now) |  # Expired
                Q(is_deleted=True)  # Deleted
            )
        else:
            # Normal filtering
            if not show_inactive:
                queryset = queryset.filter(is_active=True)
                
            if not show_expired:
                queryset = queryset.filter(end_date__gt=now)
            
        print("Final queryset:", queryset)
        return queryset

    @action(detail=True, methods=['post'])
    def soft_delete(self, request, pk=None):
        promotion = self.get_object()
        promotion.is_deleted = True
        promotion.is_active = False
        promotion.save()
        return Response(self.get_serializer(promotion).data)

    @action(detail=True, methods=['post'])
    def restore(self, request, pk=None):
        promotion = self.get_object()
        promotion.is_deleted = False
        promotion.save()
        return Response(self.get_serializer(promotion).data)

class ServiceWithPromotionViewSet(viewsets.ModelViewSet):
    queryset = Service.objects.all()
    serializer_class = ServiceWithPromotionSerializer

    def get_serializer_class(self):
        if self.action in ['list', 'retrieve']:
            return ServiceSerializer
        return ServiceWithPromotionSerializer

    def create(self, request, *args, **kwargs):
        # Let the serializer handle all the logic
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(ServiceSerializer(serializer.instance).data, status=status.HTTP_201_CREATED, headers=headers)

    def update(self, request, *args, **kwargs):
        # Let the serializer handle all the logic
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(ServiceSerializer(serializer.instance).data)

    @action(detail=False, methods=['put'])
    def update_order(self, request):
        """
        Update the order of multiple services at once.
        Expected format:
        {
            "items": [
                {"id": "uuid1", "order": 1},
                {"id": "uuid2", "order": 2},
                ...
            ]
        }
        """
        serializer = ServiceBulkOrderSerializer(data=request.data)
        if serializer.is_valid():
            items = serializer.validated_data['items']
            
            # Update each service's order
            for item in items:
                try:
                    service = Service.objects.get(id=item['id'])
                    service.order = item['order']
                    service.save()
                except Service.DoesNotExist:
                    return Response(
                        {"error": f"Service with id {item['id']} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            return Response({"message": "Order updated successfully"})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ServiceViewSet(viewsets.ModelViewSet):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    #permission_classes = [IsAdminUser]

    @action(detail=True, methods=['post'])
    def add_promotion(self, request, pk=None):
        service = self.get_object()
        promotion_ids = request.data.get('promotion_ids', [])
        
        logger.info(f"Adding promotions {promotion_ids} to service {service.id}")
        
        if not promotion_ids:
            return Response(
                {"error": "At least one promotion_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Process each promotion individually
            for promotion_id in promotion_ids:
                try:
                    promotion = ServicePromotion.objects.get(id=promotion_id)
                    logger.info(f"Found promotion {promotion.id} with code {promotion.code}")
                    
                    # Check if promotion is already added
                    if promotion in service.active_promotions.all():
                        logger.info(f"Promotion {promotion.id} already added to service {service.id}")
                        continue
                    
                    # Add promotion
                    service.active_promotions.add(promotion)
                    logger.info(f"Added promotion {promotion.id} to service {service.id}")
                    
                except ServicePromotion.DoesNotExist:
                    logger.error(f"Promotion {promotion_id} not found")
                    return Response(
                        {"error": f"Promotion {promotion_id} not found"}, 
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            # Save service to trigger signals
            service.save()
            
            # Sync with Stripe after adding all promotions
            from billing.services import StripeService
            logger.info(f"Syncing service {service.id} with Stripe")
            StripeService._sync_service(service)
            
            # Get updated service data
            serializer = ServiceSerializer(service)
            logger.info(f"Service data after adding promotions: {serializer.data}")
            
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error adding promotions to service {service.id}: {str(e)}")
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def remove_promotion(self, request, pk=None):
       
        
        service = self.get_object()
        promotion_ids = request.data.get('promotion_ids', [])
        
        logger.info(f"Removing promotions {promotion_ids} from service {service.id}")
        
        if not promotion_ids:
            return Response(
                {"error": "At least one promotion_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Process each promotion individually
            for promotion_id in promotion_ids:
                try:
                    promotion = ServicePromotion.objects.get(id=promotion_id)
                    logger.info(f"Found promotion {promotion.id} with code {promotion.code}")
                    
                    # Check if promotion is actually added to the service
                    if promotion not in service.active_promotions.all():
                        logger.info(f"Promotion {promotion.id} not found in service {service.id}")
                        continue
                    
                    # Remove promotion
                    service.active_promotions.remove(promotion)
                    logger.info(f"Removed promotion {promotion.id} from service {service.id}")
                    
                except ServicePromotion.DoesNotExist:
                    logger.error(f"Promotion {promotion_id} not found")
                    return Response(
                        {"error": f"Promotion {promotion_id} not found"}, 
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            # Sync with Stripe after removing all promotions
            from billing.services import StripeService
            logger.info(f"Syncing service {service.id} with Stripe")
            StripeService._sync_service(service)
            
            # Get updated service data
            serializer = ServiceSerializer(service)
            logger.info(f"Service data after removing promotions: {serializer.data}")
            
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error removing promotions from service {service.id}: {str(e)}")
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class SubscriptionPlanViewSet(viewsets.ModelViewSet):
    queryset = SubscriptionPlan.objects.all()
    serializer_class = SubscriptionPlanSerializer
    #permission_classes = [IsAdminUser]

    @action(detail=False, methods=['put'])
    def update_order(self, request):
        """
        Update the order of multiple subscription plans at once.
        Expected format:
        {
            "items": [
                {"id": "uuid1", "order": 1},
                {"id": "uuid2", "order": 2},
                ...
            ]
        }
        """
        serializer = SubscriptionPlanBulkOrderSerializer(data=request.data)
        if serializer.is_valid():
            items = serializer.validated_data['items']
            
            # Update each subscription plan's order
            for item in items:
                try:
                    plan = SubscriptionPlan.objects.get(id=item['id'])
                    plan.order = item['order']
                    plan.save()
                except SubscriptionPlan.DoesNotExist:
                    return Response(
                        {"error": f"Subscription plan with id {item['id']} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            return Response({"message": "Order updated successfully"})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class SolutionViewSet(viewsets.ModelViewSet):
    queryset = Solution.objects.all()
    serializer_class = SolutionSerializer
    #permission_classes = [IsAdminUser]

    def destroy(self, request, *args, **kwargs):
        """
        Override destroy method to ensure proper deletion
        """
       
        instance = self.get_object()
        logger.info(f"Deleting solution {instance.id} via API")
        
        # Perform the deletion
        self.perform_destroy(instance)
        
        logger.info(f"Solution {instance.id} deleted successfully")
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=False, methods=['put'])
    def update_order(self, request):
        """
        Update the order of multiple solutions at once.
        Expected format:
        {
            "items": [
                {"id": "uuid1", "order": 1},
                {"id": "uuid2", "order": 2},
                ...
            ]
        }
        """
        serializer = SolutionBulkOrderSerializer(data=request.data)
        if serializer.is_valid():
            items = serializer.validated_data['items']
            
            # Update each solution's order
            for item in items:
                try:
                    solution = Solution.objects.get(id=item['id'])
                    solution.order = item['order']
                    solution.save()
                except Solution.DoesNotExist:
                    return Response(
                        {"error": f"Solution with id {item['id']} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            return Response({"message": "Order updated successfully"})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class TabAlertViewSet(viewsets.ModelViewSet):
    queryset = TabAlert.objects.all()
    serializer_class = TabAlertSerializer
    #permission_classes = [IsAdminUser] 