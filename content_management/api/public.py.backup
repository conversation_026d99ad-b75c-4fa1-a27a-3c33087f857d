from rest_framework import mixins, viewsets
from rest_framework.permissions import AllowAny
from django.utils import timezone
from ..models import Service, SubscriptionPlan, Solution, TabAlert
from ..serializers import (
    ServiceSerializer, SubscriptionPlanSerializer,
    SolutionSerializer, TabAlertSerializer
)

class ReadOnlyViewSet(mixins.ListModelMixin,
                     mixins.RetrieveModelMixin,
                     viewsets.GenericViewSet):
    pass

class PublicServiceViewSet(ReadOnlyViewSet):
    serializer_class = ServiceSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        return Service.objects.filter(is_active=True)

class PublicSubscriptionPlanViewSet(ReadOnlyViewSet):
    serializer_class = SubscriptionPlanSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        return SubscriptionPlan.objects.filter(is_active=True)

class PublicSolutionViewSet(ReadOnlyViewSet):
    serializer_class = SolutionSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        return Solution.objects.filter(is_active=True)

class PublicTabAlertViewSet(ReadOnlyViewSet):
    serializer_class = TabAlertSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        now = timezone.now()
        return TabAlert.objects.filter(
            is_active=True,
            start_date__lte=now,
            end_date__gte=now
        ) 