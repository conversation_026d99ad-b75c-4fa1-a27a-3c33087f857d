from rest_framework import serializers
from content_management.models import InvitationCode
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class InvitationCodeSerializer(serializers.ModelSerializer):
    """
    Serializer for CRUD operations on InvitationCode
    """
    is_valid_status = serializers.SerializerMethodField()
    remaining_uses = serializers.SerializerMethodField()
    
    class Meta:
        model = InvitationCode
        fields = [
            'id', 'code', 'description', 'max_uses', 'current_uses', 
            'is_active', 'expires_at', 'created_by', 'created_at', 
            'updated_at', 'is_valid_status', 'remaining_uses'
        ]
        read_only_fields = ['id', 'current_uses', 'created_at', 'updated_at']
    
    def get_is_valid_status(self, obj):
        """Get the validity status of the invitation code"""
        is_valid, message = obj.is_valid()
        return {
            'is_valid': is_valid,
            'message': message
        }
    
    def get_remaining_uses(self, obj):
        """Get remaining uses for this invitation code"""
        return max(0, obj.max_uses - obj.current_uses)
    
    def validate_code(self, value):
        """Validate that code is unique and follows naming conventions"""
        # Remove spaces and convert to uppercase
        value = value.strip().upper()
        
        # Check for minimum length
        if len(value) < 3:
            raise serializers.ValidationError("Invitation code must be at least 3 characters long")
        
        # Check for maximum length
        if len(value) > 50:
            raise serializers.ValidationError("Invitation code cannot exceed 50 characters")
        
        return value
    
    def validate_max_uses(self, value):
        """Validate max_uses is positive"""
        if value < 1:
            raise serializers.ValidationError("Maximum uses must be at least 1")
        if value > 1000:
            raise serializers.ValidationError("Maximum uses cannot exceed 1000")
        return value
    
    def validate_expires_at(self, value):
        """Validate that expiration date is in the future"""
        if value and value <= timezone.now():
            raise serializers.ValidationError("Expiration date must be in the future")
        return value
    
    def create(self, validated_data):
        """Create a new invitation code"""
        # Set created_by to current user if available
        request = self.context.get('request')
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            validated_data['created_by'] = request.user
        
        return super().create(validated_data)

class InvitationCodeValidationSerializer(serializers.Serializer):
    """
    Serializer for validating invitation codes during registration
    """
    invitation_code = serializers.CharField(max_length=50, required=True)
    
    def validate_invitation_code(self, value):
        """Validate that the invitation code exists and is valid"""
        value = value.strip().upper()
        
        try:
            invitation = InvitationCode.objects.get(code=value)
        except InvitationCode.DoesNotExist:
            raise serializers.ValidationError("Invalid invitation code")
        
        is_valid, message = invitation.is_valid()
        if not is_valid:
            raise serializers.ValidationError(message)
        
        # Store the invitation object for later use
        self.invitation = invitation
        return value
    
    def get_invitation(self):
        """Return the validated invitation object"""
        return getattr(self, 'invitation', None)

class InvitationCodeBulkCreateSerializer(serializers.Serializer):
    """
    Serializer for bulk creating invitation codes
    """
    prefix = serializers.CharField(max_length=20, required=True, help_text="Prefix for all codes")
    count = serializers.IntegerField(min_value=1, max_value=100, required=True, help_text="Number of codes to generate")
    description = serializers.CharField(required=False, allow_blank=True)
    max_uses = serializers.IntegerField(min_value=1, max_value=1000, default=1)
    expires_at = serializers.DateTimeField(required=False, allow_null=True)
    
    def validate_prefix(self, value):
        """Validate prefix"""
        value = value.strip().upper()
        if len(value) < 2:
            raise serializers.ValidationError("Prefix must be at least 2 characters long")
        return value
    
    def validate_expires_at(self, value):
        """Validate that expiration date is in the future"""
        if value and value <= timezone.now():
            raise serializers.ValidationError("Expiration date must be in the future")
        return value
    
    def create(self, validated_data):
        """Generate multiple invitation codes"""
        import random
        import string
        
        prefix = validated_data['prefix']
        count = validated_data['count']
        description = validated_data.get('description', '')
        max_uses = validated_data.get('max_uses', 1)
        expires_at = validated_data.get('expires_at')
        
        request = self.context.get('request')
        created_by = None
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            created_by = request.user
        
        created_codes = []
        
        for i in range(count):
            # Generate unique suffix
            while True:
                suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
                code = f"{prefix}-{suffix}"
                
                # Check if code already exists
                if not InvitationCode.objects.filter(code=code).exists():
                    break
            
            invitation_code = InvitationCode.objects.create(
                code=code,
                description=f"{description} (Bulk generated {i+1}/{count})" if description else f"Bulk generated {i+1}/{count}",
                max_uses=max_uses,
                expires_at=expires_at,
                created_by=created_by
            )
            created_codes.append(invitation_code)
        
        return created_codes 