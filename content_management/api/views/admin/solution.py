from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser
import logging

from content_management.models.base import Solution
from content_management.serializers import SolutionBulkOrderSerializer, SolutionSerializer

logger = logging.getLogger(__name__)


class SolutionViewSet(viewsets.ModelViewSet):
    queryset = Solution.objects.all()
    serializer_class = SolutionSerializer
    #permission_classes = [IsAdminUser]

    def destroy(self, request, *args, **kwargs):
        """
        Override destroy method to ensure proper deletion
        """
        instance = self.get_object()
        logger.info(f"Deleting solution {instance.id} via API")
        
        # Perform the deletion
        self.perform_destroy(instance)
        
        logger.info(f"Solution {instance.id} deleted successfully")
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=False, methods=['put'])
    def update_order(self, request):
        """
        Update the order of multiple solutions at once.
        Expected format:
        {
            "items": [
                {"id": "uuid1", "order": 1},
                {"id": "uuid2", "order": 2},
                ...
            ]
        }
        """
        serializer = SolutionBulkOrderSerializer(data=request.data)
        if serializer.is_valid():
            items = serializer.validated_data['items']
            
            # Update each solution's order
            for item in items:
                try:
                    solution = Solution.objects.get(id=item['id'])
                    solution.order = item['order']
                    solution.save()
                except Solution.DoesNotExist:
                    return Response(
                        {"error": f"Solution with id {item['id']} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            return Response({"message": "Order updated successfully"})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
