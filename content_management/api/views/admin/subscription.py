from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser
import logging

from content_management.models.base import SubscriptionPlan
from content_management.serializers import SubscriptionPlanBulkOrderSerializer, SubscriptionPlanSerializer

logger = logging.getLogger(__name__)


class SubscriptionPlanViewSet(viewsets.ModelViewSet):
    queryset = SubscriptionPlan.objects.all()
    serializer_class = SubscriptionPlanSerializer
    #permission_classes = [IsAdminUser]

    @action(detail=False, methods=['put'])
    def update_order(self, request):
        """
        Update the order of multiple subscription plans at once.
        Expected format:
        {
            "items": [
                {"id": "uuid1", "order": 1},
                {"id": "uuid2", "order": 2},
                ...
            ]
        }
        """
        serializer = SubscriptionPlanBulkOrderSerializer(data=request.data)
        if serializer.is_valid():
            items = serializer.validated_data['items']
            
            # Update each subscription plan's order
            for item in items:
                try:
                    plan = SubscriptionPlan.objects.get(id=item['id'])
                    plan.order = item['order']
                    plan.save()
                except SubscriptionPlan.DoesNotExist:
                    return Response(
                        {"error": f"Subscription plan with id {item['id']} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            return Response({"message": "Order updated successfully"})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
