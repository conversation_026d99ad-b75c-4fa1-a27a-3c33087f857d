from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from django.db.models import Q
from django.shortcuts import get_object_or_404

from content_management.models.base import Service, ServicePromotion

import logging

from content_management.serializers import ServiceBulkOrderSerializer, ServiceSerializer, ServiceWithPromotionSerializer

logger = logging.getLogger(__name__)


class ServiceWithPromotionViewSet(viewsets.ModelViewSet):
    queryset = Service.objects.all()
    serializer_class = ServiceWithPromotionSerializer

    def get_serializer_class(self):
        if self.action in ['list', 'retrieve']:
            return ServiceSerializer
        return ServiceWithPromotionSerializer

    def create(self, request, *args, **kwargs):
        # Let the serializer handle all the logic
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(ServiceSerializer(serializer.instance).data, status=status.HTTP_201_CREATED, headers=headers)

    def update(self, request, *args, **kwargs):
        # Let the serializer handle all the logic
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        # Return with ServiceWithPromotionSerializer to include promotion data
        response_serializer = ServiceWithPromotionSerializer(serializer.instance)
        return Response(response_serializer.data)

    @action(detail=False, methods=['put'])
    def update_order(self, request):
        """
        Update the order of multiple services at once.
        Expected format:
        {
            "items": [
                {"id": "uuid1", "order": 1},
                {"id": "uuid2", "order": 2},
                ...
            ]
        }
        """
        serializer = ServiceBulkOrderSerializer(data=request.data)
        if serializer.is_valid():
            items = serializer.validated_data['items']
            
            # Update each service's order
            for item in items:
                try:
                    service = Service.objects.get(id=item['id'])
                    service.order = item['order']
                    service.save()
                except Service.DoesNotExist:
                    return Response(
                        {"error": f"Service with id {item['id']} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            return Response({"message": "Order updated successfully"})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ServiceViewSet(viewsets.ModelViewSet):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    #permission_classes = [IsAdminUser]

    @action(detail=True, methods=['post'])
    def add_promotion(self, request, pk=None):
        service = self.get_object()
        promotion_ids = request.data.get('promotion_ids', [])
        
        logger.info(f"Adding promotions {promotion_ids} to service {service.id}")
        
        if not promotion_ids:
            return Response(
                {"error": "At least one promotion_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Process each promotion individually
            for promotion_id in promotion_ids:
                try:
                    promotion = ServicePromotion.objects.get(id=promotion_id)
                    logger.info(f"Found promotion {promotion.id} with code {promotion.code}")
                    
                    # Check if promotion is already added
                    if promotion in service.active_promotions.all():
                        logger.info(f"Promotion {promotion.id} already added to service {service.id}")
                        continue
                    
                    # Add promotion
                    service.active_promotions.add(promotion)
                    logger.info(f"Added promotion {promotion.id} to service {service.id}")
                    
                except ServicePromotion.DoesNotExist:
                    logger.error(f"Promotion {promotion_id} not found")
                    return Response(
                        {"error": f"Promotion {promotion_id} not found"}, 
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            # Save service to trigger signals
            service.save()
            
            # Sync with Stripe after adding all promotions
            from billing.services import StripeService
            logger.info(f"Syncing service {service.id} with Stripe")
            StripeService._sync_service(service)
            
            # Get updated service data
            serializer = ServiceSerializer(service)
            logger.info(f"Service data after adding promotions: {serializer.data}")
            
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error adding promotions to service {service.id}: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def remove_promotion(self, request, pk=None):
        service = self.get_object()
        promotion_ids = request.data.get('promotion_ids', [])

        logger.info(f"Removing promotions {promotion_ids} from service {service.id}")

        if not promotion_ids:
            return Response(
                {"error": "At least one promotion_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Process each promotion individually
            for promotion_id in promotion_ids:
                try:
                    promotion = ServicePromotion.objects.get(id=promotion_id)
                    logger.info(f"Found promotion {promotion.id} with code {promotion.code}")

                    # Check if promotion is actually added to the service
                    if promotion not in service.active_promotions.all():
                        logger.info(f"Promotion {promotion.id} not found in service {service.id}")
                        continue

                    # Remove promotion
                    service.active_promotions.remove(promotion)
                    logger.info(f"Removed promotion {promotion.id} from service {service.id}")

                except ServicePromotion.DoesNotExist:
                    logger.error(f"Promotion {promotion_id} not found")
                    return Response(
                        {"error": f"Promotion {promotion_id} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )

            # Sync with Stripe after removing all promotions
            from billing.services import StripeService
            logger.info(f"Syncing service {service.id} with Stripe")
            StripeService._sync_service(service)

            # Get updated service data
            serializer = ServiceSerializer(service)
            logger.info(f"Service data after removing promotions: {serializer.data}")

            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error removing promotions from service {service.id}: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
