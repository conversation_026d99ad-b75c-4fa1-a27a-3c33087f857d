from rest_framework import viewsets
from rest_framework.permissions import IsAdminUser
import logging

from content_management.models.base import TabAlert
from content_management.serializers import TabAlertSerializer

logger = logging.getLogger(__name__)


class TabAlertViewSet(viewsets.ModelViewSet):
    queryset = TabAlert.objects.all()
    serializer_class = TabAlertSerializer
    #permission_classes = [IsAdminUser]
