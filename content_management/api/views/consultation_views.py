"""
API views for consultation services
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.db.models import Q

from clinic.serializers import ServiceSerializer
from content_management.models import Service


class ConsultationServiceViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for consultation services (telemedicine packages)
    """
    serializer_class = ServiceSerializer
    permission_classes = [AllowAny]  # Public access for browsing packages
    
    def get_queryset(self):
        """Get all active consultation services"""
        return Service.objects.filter(
            service_type='CONSULTATION',
            is_active=True
        ).order_by('order', 'price')
    
    @action(detail=False, methods=['get'])
    def packages(self, request):
        """Get all consultation packages for telemedicine"""
        services = self.get_queryset()
        serializer = self.get_serializer(services, many=True)
        
        # Format response for frontend
        packages = []
        for service_data in serializer.data:
            package = {
                'id': service_data['id'],
                'name': service_data['name'],
                'service_code': service_data.get('service_code'),
                'description': service_data['description'],
                'price': float(service_data['price']),
                'features': service_data['features'],
                'button_text': service_data.get('button_text', 'Choose Package'),
                'is_active': service_data['is_active'],
                'order': service_data.get('order', 0)
            }
            packages.append(package)
        
        return Response({
            'packages': packages,
            'total_count': len(packages)
        })
    
    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """Get consultation services by consultation type"""
        consultation_type = request.query_params.get('type', 'all')
        
        queryset = self.get_queryset()
        
        if consultation_type != 'all':
            queryset = queryset.filter(service_code=consultation_type)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def pricing(self, request, pk=None):
        """Get detailed pricing information for a consultation service"""
        service = self.get_object()
        
        # Calculate any discounts
        active_promotion = service.get_active_promotion()
        
        pricing_info = {
            'service_id': str(service.id),
            'name': service.name,
            'base_price': float(service.price),
            'currency': 'USD',
            'has_discount': bool(active_promotion),
            'discount_percentage': active_promotion.discount_percentage if active_promotion else 0,
            'discounted_price': float(service.get_discounted_price()) if hasattr(service, 'get_discounted_price') else float(service.price),
            'features': service.features,
            'service_code': service.service_code
        }
        
        return Response(pricing_info) 