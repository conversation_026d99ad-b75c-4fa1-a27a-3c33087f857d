from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from ...models import ServicePromotion, Service
import logging

logger = logging.getLogger(__name__)

class VerifyPromoCodeView(APIView):
    """
    API endpoint để verify promo code và kiểm tra áp dụng được cho service nào.
    Request: {"code": "PROMO123", "service_ids": ["uuid1", "uuid2"]}
    """
    
    def post(self, request, *args, **kwargs):
        code = request.data.get('code')
        service_ids = request.data.get('service_ids', [])
        
        if not code:
            return Response(
                {"valid": False, "message": "Promo code is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        logger.info(f"Verifying promo code: {code} for services: {service_ids}")
        
        # Tìm promotion theo code và kiểm tra còn hiệu lực không
        now = timezone.now()
        try:
            promotion = ServicePromotion.objects.get(
                code=code,
                is_active=True, 
                is_deleted=False,
                promotion_type='OFFLINE',  # Chỉ verify offline promo (was ONLINE)
                start_date__lte=now,
                end_date__gte=now
            )
        except ServicePromotion.DoesNotExist:
            logger.info(f"Invalid or expired promo code: {code}")
            return Response(
                {"valid": False, "message": "Invalid promo code or expired"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Kiểm tra áp dụng được cho service nào
        applicable_services = []
        not_applicable_services = []
        
        for service_id in service_ids:
            try:
                service = Service.objects.get(id=service_id)
                # Kiểm tra service có trong danh sách active_promotions của promotion này không
                if service in promotion.services.all():
                    applicable_services.append(service_id)
                else:
                    not_applicable_services.append(service_id)
            except Service.DoesNotExist:
                not_applicable_services.append(service_id)
                logger.warning(f"Service not found: {service_id}")
        
        # Trả về response
        response_data = {
            "valid": len(applicable_services) > 0,
            "message": "Promo code applied successfully" if applicable_services else "Promo code cannot be applied to selected services",
            "discount_percentage": promotion.discount_percentage,
            "applicable_services": applicable_services,
            "not_applicable_services": not_applicable_services,
            "expires_at": promotion.end_date.isoformat()
        }
        
        logger.info(f"Promo code verification result: {response_data}")
        return Response(response_data) 