from rest_framework import serializers
from .models import Service, SubscriptionPlan, Solution, TabAlert, ServicePromotion
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from billing.services import StripeService

class ServicePromotionSerializer(serializers.ModelSerializer):
    services = serializers.SerializerMethodField()
    stripe_promo_id = serializers.SerializerMethodField()

    class Meta:
        model = ServicePromotion
        fields = '__all__'
        read_only_fields = ['services']

    def get_services(self, obj):
        return list(obj.services.values_list('id', flat=True))
        
    def get_stripe_promo_id(self, obj):
        # Admin/Staff luôn thấy
        request = self.context.get('request')
        if request and request.user and request.user.is_staff:
            return obj.stripe_promo_id
            
        # Trong quá trình tạo/sửa cũng hiển thị
        if not request or request.method in ['POST', 'PUT', 'PATCH']:
            return obj.stripe_promo_id
            
        # GET request từ user thường -> không hiển thị
        return None

class ServiceSerializer(serializers.ModelSerializer):
    discounted_price = serializers.SerializerMethodField()
    active_promotions = ServicePromotionSerializer(many=True, read_only=True)
    stripe_info = serializers.SerializerMethodField()

    class Meta:
        model = Service
        fields = '__all__'

    def get_discounted_price(self, obj):
        """
        Calculate the best possible discounted price considering all active promotions
        (both ONLINE and OFFLINE) to show potential savings in API response
        """
        from django.utils import timezone
        from decimal import Decimal
        
        now = timezone.now()
        best_discount = Decimal('0')
        
        # Get all active promotions (both ONLINE and OFFLINE)
        active_promotions = obj.active_promotions.filter(
            is_active=True,
            is_deleted=False,
            start_date__lte=now,
            end_date__gte=now
        )
        
        # Find the highest discount among all active promotions
        for promotion in active_promotions:
            if promotion.discount_percentage is None:
                continue
            try:
                promo_discount = Decimal(str(promotion.discount_percentage)) / Decimal('100')
                if promo_discount > best_discount:
                    best_discount = promo_discount
            except Exception:
                continue
        
        # Calculate final price with best discount
        if best_discount > 0:
            final_price = obj.price * (Decimal('1') - best_discount)
            return float(final_price.quantize(Decimal('0.01')))
        
        return float(obj.price)

    def get_stripe_info(self, obj):
        return StripeService.get_service_stripe_info(obj)

class ServiceWithPromotionSerializer(serializers.ModelSerializer):
    promotions = serializers.ListField(
        child=serializers.DictField(),
        required=False
    )
    # Add computed fields for better response data
    discounted_price = serializers.SerializerMethodField()
    active_promotions = ServicePromotionSerializer(many=True, read_only=True)
    stripe_info = serializers.SerializerMethodField()

    class Meta:
        model = Service
        fields = '__all__'

    def get_discounted_price(self, obj):
        """
        Get the best possible discounted price considering all active promotions
        (both ONLINE and OFFLINE) to show potential savings in API response
        """
        from django.utils import timezone
        from decimal import Decimal
        
        now = timezone.now()
        best_discount = Decimal('0')
        
        # Get all active promotions (both ONLINE and OFFLINE)
        active_promotions = obj.active_promotions.filter(
            is_active=True,
            is_deleted=False,
            start_date__lte=now,
            end_date__gte=now
        )
        
        # Find the highest discount among all active promotions
        for promotion in active_promotions:
            if promotion.discount_percentage is None:
                continue
            try:
                promo_discount = Decimal(str(promotion.discount_percentage)) / Decimal('100')
                if promo_discount > best_discount:
                    best_discount = promo_discount
            except Exception:
                continue
        
        # Calculate final price with best discount
        if best_discount > 0:
            final_price = obj.price * (Decimal('1') - best_discount)
            return float(final_price.quantize(Decimal('0.01')))
        
        return float(obj.price)

    def get_stripe_info(self, obj):
        """Get Stripe product and pricing information"""
        return StripeService.get_service_stripe_info(obj)

    def _handle_promotions(self, instance, promotions_data, clear_existing=False):
        if clear_existing:
            instance.active_promotions.clear()
        promotion_ids = []
        for promo_data in promotions_data:
            if 'id' in promo_data:
                promotion_ids.append(promo_data['id'])
        promotions = ServicePromotion.objects.filter(id__in=promotion_ids)
        for promotion in promotions:
            if promotion not in instance.active_promotions.all():
                instance.active_promotions.add(promotion)
        StripeService._sync_service(instance)

    def create(self, validated_data):
        promotions_data = validated_data.pop('promotions', [])
        service = Service.objects.create(**validated_data)
        self._handle_promotions(service, promotions_data)
        return service

    def update(self, instance, validated_data):
        promotions_data = validated_data.pop('promotions', [])
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()  # Save instance to persist changes to database
        if 'promotions' in self.initial_data:
            self._handle_promotions(instance, promotions_data, clear_existing=True)
        return instance

# Serializers for bulk order updates
class ServiceOrderSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    order = serializers.IntegerField()

class ServiceBulkOrderSerializer(serializers.Serializer):
    items = serializers.ListField(child=ServiceOrderSerializer())

class SubscriptionPlanOrderSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    order = serializers.IntegerField()

class SubscriptionPlanBulkOrderSerializer(serializers.Serializer):
    items = serializers.ListField(child=SubscriptionPlanOrderSerializer())

class SolutionOrderSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    order = serializers.IntegerField()

class SolutionBulkOrderSerializer(serializers.Serializer):
    items = serializers.ListField(child=SolutionOrderSerializer())

class SubscriptionPlanSerializer(serializers.ModelSerializer):
    stripe_info = serializers.SerializerMethodField()

    class Meta:
        model = SubscriptionPlan
        fields = '__all__'

    def get_stripe_info(self, obj):
        return StripeService.get_subscription_plan_stripe_info(obj)

class SolutionSerializer(serializers.ModelSerializer):
    stripe_info = serializers.SerializerMethodField()

    class Meta:
        model = Solution
        fields = '__all__'

    def get_stripe_info(self, obj):
        return StripeService.get_solution_stripe_info(obj)

class TabAlertSerializer(serializers.ModelSerializer):
    class Meta:
        model = TabAlert
        fields = '__all__'