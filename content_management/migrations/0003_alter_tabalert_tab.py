# Generated by Django 5.0.9 on 2025-04-09 07:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('content_management', '0002_servicepromotion_service_discount_end_date_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='tabalert',
            name='tab',
            field=models.CharField(choices=[('ALL', 'All Tabs'), ('AI', 'AI'), ('APPOINTMENTS', 'Appointments'), ('DIAGNOSES', 'Diagnoses'), ('MEDICATIONS', 'Medications'), ('LABORATORY', 'Laboratory'), ('OTHER', 'Other'), ('MOBILE_HEALTH', 'Mobile Health'), ('NOTES', 'Notes'), ('PRESCRIPTIONS', 'Prescriptions'), ('PREVENTIVE_SERVICES', 'Preventive Services'), ('TELEMEDICINE', 'Telemedicine')], max_length=50),
        ),
    ]
