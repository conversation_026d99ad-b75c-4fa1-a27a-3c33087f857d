# Generated by Django 5.0.9 on 2025-04-08 13:09

import django.core.validators
import uuid6
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('content_management', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServicePromotion',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('discount_percentage', models.IntegerField(validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('description', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='service',
            name='discount_end_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='service',
            name='discount_percentage',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
        migrations.AddField(
            model_name='service',
            name='discount_start_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='service',
            name='has_discount',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='service',
            name='active_promotions',
            field=models.ManyToManyField(blank=True, related_name='services', to='content_management.servicepromotion'),
        ),
    ]
