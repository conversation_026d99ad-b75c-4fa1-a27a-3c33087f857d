# Generated by Django 5.0.9 on 2025-05-11 14:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('content_management', '0013_solution_seat_price'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='service',
            name='discount_end_date',
        ),
        migrations.RemoveField(
            model_name='service',
            name='discount_percentage',
        ),
        migrations.RemoveField(
            model_name='service',
            name='discount_start_date',
        ),
        migrations.RemoveField(
            model_name='service',
            name='has_discount',
        ),
        migrations.RemoveField(
            model_name='subscriptionplan',
            name='discount_end_date',
        ),
        migrations.RemoveField(
            model_name='subscriptionplan',
            name='discount_percentage',
        ),
        migrations.RemoveField(
            model_name='subscriptionplan',
            name='discount_start_date',
        ),
        migrations.RemoveField(
            model_name='subscriptionplan',
            name='has_discount',
        ),
        migrations.AddField(
            model_name='servicepromotion',
            name='promotion_type',
            field=models.CharField(choices=[('ONLINE', 'Online (requires code)'), ('OFFLINE', 'Offline (auto-applied)')], default='ONLINE', max_length=10),
        ),
    ]
