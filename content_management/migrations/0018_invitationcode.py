# Generated by Django 5.0.9 on 2025-06-17 15:12

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('content_management', '0017_alter_service_service_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='InvitationCode',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(help_text='Unique invitation code', max_length=50, unique=True)),
                ('description', models.TextField(blank=True, help_text='Description or purpose of this invitation code')),
                ('max_uses', models.PositiveIntegerField(default=1, help_text='Maximum number of times this code can be used')),
                ('current_uses', models.PositiveIntegerField(default=0, help_text='Current number of times this code has been used')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this invitation code is active')),
                ('expires_at', models.DateTimeField(blank=True, help_text='When this invitation code expires', null=True)),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this invitation code', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_invitation_codes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['code'], name='content_man_code_5b64fb_idx'), models.Index(fields=['is_active'], name='content_man_is_acti_572f8d_idx'), models.Index(fields=['expires_at'], name='content_man_expires_ed1320_idx')],
            },
        ),
    ]
