# Generated by Django 5.0.9 on 2025-04-23 13:08

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('content_management', '0008_service_service_code'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscriptionplan',
            name='active_promotions',
            field=models.ManyToManyField(blank=True, related_name='subscription_plans', to='content_management.servicepromotion'),
        ),
        migrations.AddField(
            model_name='subscriptionplan',
            name='discount_end_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='subscriptionplan',
            name='discount_percentage',
            field=models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
        migrations.AddField(
            model_name='subscriptionplan',
            name='discount_start_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='subscriptionplan',
            name='has_discount',
            field=models.BooleanField(default=False),
        ),
    ]
