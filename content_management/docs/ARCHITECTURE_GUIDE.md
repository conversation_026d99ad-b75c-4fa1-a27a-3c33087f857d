# Content Management Architecture Guide

This document provides a detailed architectural overview of the Content Management app, covering design patterns, model relationships, business logic implementation, and technical decisions.

## 🏗️ Overall Architecture

### High-Level Overview

The Content Management app follows a **service-oriented architecture** with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────────┐
│                        Content Management App                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Public APIs   │  │   Admin APIs    │  │  Business Logic │  │
│  │  (Read-only)    │  │  (Full CRUD)    │  │   (Services)    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Serializers   │  │     Models      │  │   Validators    │  │
│  │  (Data Trans.)  │  │  (Data Layer)   │  │  (Rules)        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Database      │  │   Integrations  │  │   Caching       │  │
│  │  (PostgreSQL)   │  │  (Stripe, etc.) │  │  (Redis)        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### Core Design Principles

1. **Separation of Concerns**: Each component has a single responsibility
2. **API-First Design**: Well-defined APIs for both admin and public access
3. **Business Logic Encapsulation**: Complex logic isolated in model methods
4. **Data Integrity**: Comprehensive validation at multiple levels
5. **Scalability**: Designed for horizontal scaling and high availability

## 📊 Data Model Architecture

### Entity Relationship Diagram

```
┌─────────────────────┐      ┌─────────────────────┐
│    ServicePromotion │      │       Service       │
│                     │      │                     │
│ - code              │      │ - name              │
│ - discount_%        │◄────►│ - service_code      │
│ - start_date        │  M:M │ - description       │
│ - end_date          │      │ - price             │
│ - promotion_type    │      │ - features (JSON)   │
│ - stripe_promo_id   │      │ - service_type      │
└─────────────────────┘      └─────────────────────┘
                                       │
                                       │ 1:1 (optional)
                                       ▼
┌─────────────────────┐      ┌─────────────────────┐
│  SubscriptionPlan   │      │     Solution        │
│                     │      │                     │
│ - name              │      │ - name              │
│ - description       │      │ - description       │
│ - features (JSON)   │      │ - solution_type     │
│ - price             │      │ - price             │
│ - billing_cycle     │      │ - seat_price        │
└─────────────────────┘      │ - features (JSON)   │
                             └─────────────────────┘

┌─────────────────────┐      ┌─────────────────────┐
│     TabAlert        │      │   InvitationCode    │
│                     │      │                     │
│ - message           │      │ - code              │
│ - tab               │      │ - description       │
│ - start_date        │      │ - max_uses          │
│ - end_date          │      │ - current_uses      │
│ - is_active         │      │ - expires_at        │
└─────────────────────┘      │ - created_by        │
                             └─────────────────────┘
```

### Model Relationships

#### 1. Service ↔ ServicePromotion (Many-to-Many)
- Services can have multiple active promotions
- Promotions can apply to multiple services
- Automatic best-price calculation based on active promotions

#### 2. InvitationCode ↔ User (One-to-Many)
- Each invitation code is created by a specific user
- Tracks usage and manages expiration
- Enforces usage limits and validation rules

### Model Inheritance Hierarchy

```python
BaseModel (Abstract)
├── Service
├── SubscriptionPlan
├── Solution
├── TabAlert
└── InvitationCode

SoftDeleteModel (Abstract)
└── ServicePromotion
```

## 🔧 Business Logic Implementation

### Service Pricing System

The pricing system implements sophisticated discount calculation logic:

#### Pricing Calculation Flow
```python
def get_best_discounted_price(self, promo_code=None):
    """
    Multi-step pricing calculation:
    1. Start with base price
    2. Check online promotions (auto-applied)
    3. Check offline promotions (if code provided)
    4. Apply best available discount
    5. Return final price with metadata
    """
```

#### Promotion Types
- **ONLINE**: Automatically applied to all applicable services
- **OFFLINE**: Requires user input of promotion code

#### Discount Validation
```python
def is_valid_promotion(promotion):
    """
    Validation checks:
    - Is promotion active?
    - Is current time within promotion period?
    - Is promotion not soft-deleted?
    - Does promotion have valid discount percentage?
    """
```

### Invitation Code System

#### Code Validation Logic
```python
def is_valid(self):
    """
    Multi-layer validation:
    1. Check if code is active
    2. Verify expiration date
    3. Check usage limits
    4. Validate against current time
    """
```

#### Usage Tracking
```python
def use_code(self):
    """
    Atomic usage increment:
    1. Check if code is still valid
    2. Increment current_uses
    3. Save with database transaction
    4. Handle concurrent usage attempts
    """
```

### Tab Alert System

#### Alert Targeting
- **Tab-Specific**: Target specific application tabs
- **Global**: Apply to all tabs (tab='ALL')
- **Time-Based**: Display only within specified time range

#### Alert Priority
- Ordered by creation date (newest first)
- Support for manual ordering via admin interface

## 🎯 API Architecture

### Dual API Design

#### Admin APIs (`/admin/`)
- **Full CRUD Operations**: Create, Read, Update, Delete
- **Advanced Filtering**: Complex query parameters
- **Bulk Operations**: Batch processing capabilities
- **Authentication Required**: JWT with admin privileges

#### Public APIs (`/public/`)
- **Read-Only Access**: GET operations only
- **Service Discovery**: Browse available services
- **Pricing Information**: Get current prices with discounts
- **No Authentication**: Public access for service browsing

### ViewSet Architecture

```python
class ContentManagementViewSet(ModelViewSet):
    """
    Base viewset with common functionality:
    - Dynamic serializer selection
    - Permission handling
    - Query optimization
    - Error handling
    """
    
    def get_serializer_class(self):
        """Select appropriate serializer based on action"""
        
    def get_permissions(self):
        """Apply action-specific permissions"""
        
    def get_queryset(self):
        """Optimize queries with prefetching"""
```

## 🔐 Security Architecture

### Authentication & Authorization

#### Multi-Level Security
1. **API Level**: JWT authentication for admin APIs
2. **View Level**: Permission classes for action-specific access
3. **Model Level**: Validation and business rules
4. **Database Level**: Constraints and indexes

#### Permission Matrix
```
Action          | Admin API | Public API
----------------|-----------|------------
List            | ✓ Admin   | ✓ Public
Create          | ✓ Admin   | ✗
Read/Detail     | ✓ Admin   | ✓ Public
Update          | ✓ Admin   | ✗
Delete          | ✓ Admin   | ✗
```

### Data Validation

#### Input Validation Pipeline
1. **Serializer Validation**: Field-level validation
2. **Model Validation**: Business rule validation
3. **Database Constraints**: Data integrity enforcement
4. **Custom Validators**: Domain-specific validation

#### Security Measures
- **SQL Injection Prevention**: ORM usage
- **XSS Protection**: Input sanitization
- **CSRF Protection**: Token validation
- **Rate Limiting**: Request throttling

## 📈 Performance Architecture

### Database Optimization

#### Indexing Strategy
```python
class Service(BaseModel):
    class Meta:
        indexes = [
            models.Index(fields=['service_type']),
            models.Index(fields=['is_active']),
            models.Index(fields=['order']),
            models.Index(fields=['created_at']),
        ]
```

#### Query Optimization
```python
def get_queryset(self):
    return Service.objects.select_related(
        'created_by'
    ).prefetch_related(
        'active_promotions'
    ).filter(
        is_active=True
    )
```

### Caching Strategy

#### Cache Layers
1. **Database Query Cache**: Query result caching
2. **API Response Cache**: Full response caching
3. **Model Instance Cache**: Individual object caching
4. **Computed Value Cache**: Expensive calculation caching

#### Cache Invalidation
```python
def save(self, *args, **kwargs):
    """Invalidate relevant caches on model changes"""
    super().save(*args, **kwargs)
    cache.delete_pattern('service_list_*')
    cache.delete(f'service_detail_{self.id}')
```

## 🔄 Integration Architecture

### External System Integration

#### Payment Processing (Stripe)
```python
class ServicePromotion(Model):
    stripe_promo_id = CharField(...)  # Stripe promotion ID
    
    def sync_with_stripe(self):
        """Synchronize promotion with Stripe"""
```

#### Appointment System Integration
```python
# Consultation service integration
consultation_service = Service.objects.get(
    service_type='CONSULTATION',
    service_code='basic_monitoring'
)
```

### Event-Driven Architecture

#### Model Signals
```python
@receiver(post_save, sender=Service)
def service_updated(sender, instance, **kwargs):
    """Handle service updates"""
    # Invalidate caches
    # Notify dependent systems
    # Update search indexes
```

## 🧪 Testing Architecture

### Test Strategy

#### Test Pyramid
```
┌─────────────────────────────────────────────┐
│              E2E Tests                      │
│         (Integration Tests)                 │
├─────────────────────────────────────────────┤
│              API Tests                      │
│         (ViewSet Tests)                     │
├─────────────────────────────────────────────┤
│              Unit Tests                     │
│    (Model/Serializer/Logic Tests)          │
└─────────────────────────────────────────────┘
```

#### Test Categories

1. **Model Tests**
   - Validation logic
   - Business method functionality
   - Relationship handling
   - Constraint enforcement

2. **API Tests**
   - Endpoint functionality
   - Authentication/authorization
   - Data serialization
   - Error handling

3. **Integration Tests**
   - Service integration
   - Database transactions
   - External API calls
   - End-to-end workflows

### Test Data Management

#### Factory Pattern
```python
class ServiceFactory(DjangoModelFactory):
    """Factory for creating test services"""
    class Meta:
        model = Service
    
    name = factory.Faker('company')
    service_code = factory.Sequence(lambda n: f'service_{n}')
    price = factory.Faker('pydecimal', left_digits=3, right_digits=2)
```

## 📊 Monitoring & Observability

### Logging Strategy

#### Log Levels
- **DEBUG**: Detailed debugging information
- **INFO**: General information about system operation
- **WARNING**: Warning messages for potential issues
- **ERROR**: Error messages for failures
- **CRITICAL**: Critical errors requiring immediate attention

#### Structured Logging
```python
logger.info(
    "Service pricing calculated",
    extra={
        'service_id': self.id,
        'original_price': self.price,
        'final_price': final_price,
        'discount_applied': discount_percentage,
        'promotion_code': promo_code
    }
)
```

### Metrics & Monitoring

#### Key Metrics
- **API Response Times**: Average response time per endpoint
- **Error Rates**: HTTP error rates by endpoint
- **Service Usage**: Most popular services
- **Promotion Effectiveness**: Promotion usage statistics

#### Health Checks
```python
def health_check():
    """System health check"""
    return {
        'database': check_database_connection(),
        'cache': check_cache_connection(),
        'external_apis': check_external_apis(),
        'services_count': Service.objects.filter(is_active=True).count()
    }
```

## 🚀 Deployment Architecture

### Containerization

#### Docker Configuration
```dockerfile
FROM python:3.10-slim

# Install dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application code
COPY . /app
WORKDIR /app

# Run application
CMD ["gunicorn", "config.wsgi:application"]
```

### Scalability Considerations

#### Horizontal Scaling
- **Stateless Design**: No server-side state
- **Database Connection Pooling**: Efficient connection management
- **Load Balancer Ready**: Multiple instance support
- **Caching Strategy**: Reduce database load

#### Database Scaling
- **Read Replicas**: Separate read/write operations
- **Connection Pooling**: Manage database connections
- **Query Optimization**: Minimize database queries
- **Indexing Strategy**: Optimize query performance

## 📋 Maintenance & Operations

### Regular Maintenance Tasks

#### Data Cleanup
```python
# Management command for cleaning expired promotions
class Command(BaseCommand):
    def handle(self, *args, **options):
        expired_promotions = ServicePromotion.objects.filter(
            end_date__lt=timezone.now()
        )
        expired_promotions.update(is_active=False)
```

#### Cache Management
```python
# Clear expired cache entries
def clear_expired_cache():
    """Clear expired cache entries"""
    cache.delete_pattern('expired_*')
```

### Backup & Recovery

#### Database Backup Strategy
- **Daily Backups**: Full database backup
- **Point-in-Time Recovery**: Transaction log backup
- **Cross-Region Replication**: Disaster recovery

#### Application Recovery
- **Configuration Backup**: Environment variables
- **Code Deployment**: Version control integration
- **Data Migration**: Schema change management

## 🔮 Future Architecture Considerations

### Planned Enhancements

#### Microservices Migration
- **Service Decomposition**: Split into smaller services
- **API Gateway**: Centralized API management
- **Service Discovery**: Dynamic service registration
- **Circuit Breaker**: Fault tolerance patterns

#### Advanced Features
- **Machine Learning Integration**: Dynamic pricing
- **Real-time Analytics**: Usage pattern analysis
- **Multi-tenant Architecture**: Enterprise isolation
- **GraphQL API**: Flexible data fetching

### Technology Roadmap

#### Short-term (3-6 months)
- Enhanced caching strategies
- Advanced search capabilities
- Improved monitoring and alerting
- Performance optimization

#### Medium-term (6-12 months)
- Microservices architecture
- Event-driven updates
- Advanced analytics
- Multi-region deployment

#### Long-term (12+ months)
- AI-powered recommendations
- Real-time pricing optimization
- Advanced enterprise features
- Global content delivery

---

This architecture guide provides a comprehensive overview of the Content Management app's design and implementation. It serves as a reference for developers working on the system and guides future architectural decisions. 