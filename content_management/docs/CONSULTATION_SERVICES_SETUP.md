# Consultation Services Setup Guide

## Overview
This guide explains how to set up consultation services for telemedicine appointments in the Ravid Healthcare Platform.

## 1. Database Migration

First, apply the migration to add CONSULTATION service type:

```bash
make dev  # Start Docker containers
docker compose exec web python manage.py migrate content_management
```

## 2. Create Predefined Consultation Services

Run the management command to create the default consultation packages:

```bash
docker compose exec web python manage.py create_consultation_services
```

This will create three consultation packages:
- **Basic Monitoring** ($49.99)
- **Monthly Care** ($99.99) 
- **Premium Care** ($149.99)

## 3. API Endpoints

### List All Consultation Packages
```
GET /content_management/public/consultation-services/packages/
```

Response:
```json
{
  "packages": [
    {
      "id": "uuid",
      "name": "Basic Monitoring",
      "service_code": "basic_monitoring",
      "description": "Basic telemedicine consultation package",
      "price": 49.99,
      "features": [
        "3 Hrs uninterrupted Video Chat with Specialist",
        "e-Prescriptions and Diagnosis analysis",
        "Follow-ups after a week with updates (Limited)",
        "Advanced Gene analysis and consultations (Not included)"
      ],
      "button_text": "Choose Package",
      "is_active": true,
      "order": 1
    }
  ],
  "total_count": 3
}
```

### Get Service by Type
```
GET /content_management/public/consultation-services/by_type/?type=basic_monitoring
```

### Get Pricing Information
```
GET /content_management/public/consultation-services/{service_id}/pricing/
```

## 4. Integration with Appointment Payments

The consultation services are now integrated with the appointment payment system:

### Generate Payment Link for Consultation
```python
# billing/services/payment_link_service.py
payment_link_service = PaymentLinkService()
link_data = payment_link_service.generate_consultation_payment_link(
    consultation_type='basic_monitoring',  # or 'monthly_care', 'premium_care'
    doctor_id=doctor_id,
    user=patient
)
```

### Appointment Payment Flow
1. Patient selects consultation package
2. Creates appointment with `direct_payment=True`
3. Payment link generated using selected service
4. Payment processed via Stripe
5. Appointment auto-confirmed on successful payment
6. Google Meet link created automatically

## 5. Admin Management

Admins can manage consultation services through:
- Django Admin interface
- Content Management API endpoints
- Direct database access

### Add New Consultation Service
```python
from content_management.models import Service
from decimal import Decimal

service = Service.objects.create(
    name='Emergency Consultation',
    service_code='emergency_consultation',
    description='24/7 emergency telemedicine consultation',
    storage_size='N/A',
    features=[
        'Immediate Video Chat with Specialist',
        'Emergency Prescriptions',
        'Priority Support'
    ],
    price=Decimal('199.99'),
    service_type='CONSULTATION',
    order=4,
    button_text='Get Emergency Care',
    is_active=True
)
```

## 6. Frontend Integration

### Fetch Consultation Packages
```javascript
const response = await fetch('/content_management/public/consultation-services/packages/');
const data = await response.json();
const packages = data.packages;
```

### Create Appointment with Selected Package
```javascript
const appointmentData = {
    doctor_id: doctorId,
    consultation_type: 'basic_monitoring',  // Selected package
    mode: 'video_call',
    direct_payment: true,
    start_time: '2024-01-01T10:00:00Z',
    end_time: '2024-01-01T11:00:00Z'
};

const response = await fetch('/appointments/appointments/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(appointmentData)
});
```

## 7. Service Configuration

Each consultation service includes:
- **name**: Display name for the package
- **service_code**: Unique identifier for API calls
- **description**: Package description
- **features**: List of included features
- **price**: Package price in USD
- **service_type**: Always 'CONSULTATION'
- **order**: Display order (lower numbers first)
- **button_text**: CTA button text
- **is_active**: Whether the service is available

## 8. Troubleshooting

### Service Not Found Error
If you get "Consultation service not configured" error:
1. Check if consultation services exist in database
2. Verify service_type is 'CONSULTATION'
3. Ensure services are active (is_active=True)

### Payment Link Generation Fails
1. Verify doctor has payment profile setup
2. Check if selected service exists
3. Ensure Stripe configuration is correct

### Migration Issues
If migration fails:
1. Check database connection
2. Verify no conflicting migrations
3. Run migrations manually if needed

## 9. Next Steps

After setup:
1. Configure Stripe products for each consultation service
2. Set up promotional codes if needed
3. Test the complete payment flow
4. Monitor service usage and pricing
5. Add more consultation types as needed 