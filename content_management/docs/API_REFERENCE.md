# Content Management API Reference

This document provides a comprehensive reference for all Content Management API endpoints, including request/response formats, parameters, and authentication requirements.

## 📋 API Overview

### Base URLs
- **Admin APIs**: `/content_management/admin/`
- **Public APIs**: `/content_management/public/`

### Authentication
- **Admin APIs**: Requires JWT authentication with admin privileges
- **Public APIs**: No authentication required (read-only access)

### Response Format
All APIs return JSON responses with consistent structure:

```json
{
  "success": true,
  "data": {...},
  "message": "Success message",
  "errors": []
}
```

## 🔧 Admin APIs

### Services Management

#### List Services
```
GET /content_management/admin/services/
```

**Query Parameters:**
- `service_type` (optional): Filter by service type (DNA, CONSULTATION, OTHER)
- `is_active` (optional): Filter by active status (true/false)
- `search` (optional): Search in name and description
- `ordering` (optional): Sort by field (name, price, created_at)

**Response:**
```json
{
  "count": 25,
  "next": "http://api.example.com/content_management/admin/services/?page=2",
  "previous": null,
  "results": [
    {
      "id": "uuid-string",
      "name": "Basic DNA Analysis",
      "service_code": "basic_dna",
      "description": "Complete DNA analysis service",
      "storage_size": "20GB",
      "features": [
        "Comprehensive genetic analysis",
        "Health risk assessment",
        "Ancestry information"
      ],
      "price": "299.99",
      "service_type": "DNA",
      "is_active": true,
      "order": 1,
      "button_text": "Get Started",
      "active_promotions": [
        {
          "id": "promo-uuid",
          "code": "SAVE20",
          "discount_percentage": 20,
          "promotion_type": "ONLINE"
        }
      ],
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

#### Create Service
```
POST /content_management/admin/services/
```

**Request Body:**
```json
{
  "name": "Premium DNA Analysis",
  "service_code": "premium_dna",
  "description": "Advanced DNA analysis with personalized recommendations",
  "storage_size": "50GB",
  "features": [
    "Advanced genetic analysis",
    "Personalized health recommendations",
    "Pharmacogenomics insights",
    "Family history analysis"
  ],
  "price": "599.99",
  "service_type": "DNA",
  "is_active": true,
  "order": 2,
  "button_text": "Start Premium Analysis"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "new-uuid-string",
    "name": "Premium DNA Analysis",
    "service_code": "premium_dna",
    // ... other fields
  },
  "message": "Service created successfully"
}
```

#### Update Service
```
PUT /content_management/admin/services/{service_id}/
PATCH /content_management/admin/services/{service_id}/
```

**Request Body:** (Same as create, all fields optional for PATCH)

#### Delete Service
```
DELETE /content_management/admin/services/{service_id}/
```

**Response:**
```json
{
  "success": true,
  "message": "Service deleted successfully"
}
```

### Subscription Plans Management

#### List Subscription Plans
```
GET /content_management/admin/subscription-plans/
```

**Query Parameters:**
- `billing_cycle` (optional): Filter by billing cycle (DAY, WEEK, MONTH, YEAR)
- `is_active` (optional): Filter by active status

**Response:**
```json
{
  "count": 12,
  "results": [
    {
      "id": "uuid-string",
      "name": "Monthly Health Monitoring",
      "description": "Comprehensive monthly health monitoring service",
      "features": [
        "Monthly health reports",
        "24/7 AI health assistant",
        "Telemedicine consultations",
        "Prescription management"
      ],
      "price": "49.99",
      "billing_cycle": "MONTH",
      "button_text": "Subscribe Now",
      "is_active": true,
      "order": 1,
      "active_promotions": [],
      "created_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

#### Create Subscription Plan
```
POST /content_management/admin/subscription-plans/
```

**Request Body:**
```json
{
  "name": "Annual Premium Plan",
  "description": "Full-featured annual subscription",
  "features": [
    "Unlimited consultations",
    "Advanced AI analysis",
    "Priority support",
    "Family sharing (up to 4 members)"
  ],
  "price": "499.99",
  "billing_cycle": "YEAR",
  "button_text": "Subscribe Annually",
  "is_active": true,
  "order": 1
}
```

### Promotional Codes Management

#### List Promotions
```
GET /content_management/admin/promotions/
```

**Query Parameters:**
- `promotion_type` (optional): Filter by type (ONLINE, OFFLINE)
- `is_active` (optional): Filter by active status
- `code` (optional): Search by promotion code

**Response:**
```json
{
  "count": 15,
  "results": [
    {
      "id": "uuid-string",
      "code": "WELCOME20",
      "discount_percentage": 20,
      "start_date": "2024-01-01T00:00:00Z",
      "end_date": "2024-12-31T23:59:59Z",
      "is_active": true,
      "description": "Welcome discount for new users",
      "stripe_promo_id": "promo_1234567890",
      "promotion_type": "OFFLINE",
      "services": [
        {
          "id": "service-uuid",
          "name": "Basic DNA Analysis"
        }
      ]
    }
  ]
}
```

#### Create Promotion
```
POST /content_management/admin/promotions/
```

**Request Body:**
```json
{
  "code": "SUMMER25",
  "discount_percentage": 25,
  "start_date": "2024-06-01T00:00:00Z",
  "end_date": "2024-08-31T23:59:59Z",
  "is_active": true,
  "description": "Summer promotion - 25% off all services",
  "promotion_type": "ONLINE"
}
```

### Solutions Management

#### List Solutions
```
GET /content_management/admin/solutions/
```

**Response:**
```json
{
  "count": 8,
  "results": [
    {
      "id": "uuid-string",
      "name": "Clinic Management Suite",
      "description": "Complete healthcare management solution for clinics",
      "solution_type": "CLINIC",
      "features": [
        "Patient management system",
        "Appointment scheduling",
        "Electronic health records",
        "Billing integration",
        "Telemedicine platform"
      ],
      "price": "299.99",
      "seat_price": "15.99",
      "is_active": true,
      "order": 1,
      "button_text": "Get Clinic Solution"
    }
  ]
}
```

### Tab Alerts Management

#### List Tab Alerts
```
GET /content_management/admin/tab-alerts/
```

**Response:**
```json
{
  "count": 5,
  "results": [
    {
      "id": "uuid-string",
      "message": "New AI features available! Check out the enhanced diagnostic capabilities.",
      "tab": "AI",
      "start_date": "2024-01-15T00:00:00Z",
      "end_date": "2024-02-15T23:59:59Z",
      "is_active": true,
      "created_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

### Invitation Codes Management

#### List Invitation Codes
```
GET /content_management/admin/invitation-codes/
```

**Query Parameters:**
- `is_active` (optional): Filter by active status
- `code` (optional): Search by invitation code

**Response:**
```json
{
  "count": 50,
  "results": [
    {
      "id": "uuid-string",
      "code": "BETA2024",
      "description": "Beta testing invitation code",
      "max_uses": 100,
      "current_uses": 45,
      "is_active": true,
      "expires_at": "2024-12-31T23:59:59Z",
      "created_by": {
        "id": "user-uuid",
        "email": "<EMAIL>"
      },
      "created_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

#### Create Invitation Code
```
POST /content_management/admin/invitation-codes/
```

**Request Body:**
```json
{
  "code": "SPECIAL2024",
  "description": "Special access code for premium users",
  "max_uses": 50,
  "is_active": true,
  "expires_at": "2024-06-30T23:59:59Z"
}
```

## 🌐 Public APIs

### Public Services

#### List Public Services
```
GET /content_management/public/services/
```

**Query Parameters:**
- `service_type` (optional): Filter by service type
- `is_active` (optional): Always filters to active services only

**Response:**
```json
{
  "count": 12,
  "results": [
    {
      "id": "uuid-string",
      "name": "Basic DNA Analysis",
      "service_code": "basic_dna",
      "description": "Complete DNA analysis service",
      "features": [
        "Comprehensive genetic analysis",
        "Health risk assessment",
        "Ancestry information"
      ],
      "price": "299.99",
      "discounted_price": "239.99",
      "service_type": "DNA",
      "order": 1,
      "button_text": "Get Started",
      "active_promotion": {
        "code": "AUTO20",
        "discount_percentage": 20,
        "promotion_type": "ONLINE"
      }
    }
  ]
}
```

#### Get Service Details
```
GET /content_management/public/services/{service_id}/
```

**Response:**
```json
{
  "id": "uuid-string",
  "name": "Basic DNA Analysis",
  "service_code": "basic_dna",
  "description": "Complete DNA analysis service with detailed health insights",
  "storage_size": "20GB",
  "features": [
    "Comprehensive genetic analysis",
    "Health risk assessment",
    "Ancestry information",
    "Personalized recommendations"
  ],
  "price": "299.99",
  "discounted_price": "239.99",
  "service_type": "DNA",
  "button_text": "Get Started",
  "active_promotion": {
    "code": "AUTO20",
    "discount_percentage": 20,
    "promotion_type": "ONLINE"
  },
  "pricing_details": {
    "original_price": "299.99",
    "best_price": "239.99",
    "savings": "60.00",
    "discount_percentage": 20
  }
}
```

### Consultation Services

#### List Consultation Packages
```
GET /content_management/public/consultation-services/packages/
```

**Response:**
```json
{
  "packages": [
    {
      "id": "uuid-string",
      "name": "Basic Monitoring",
      "service_code": "basic_monitoring",
      "description": "Basic telemedicine consultation package",
      "price": "49.99",
      "discounted_price": "49.99",
      "features": [
        "3 Hrs uninterrupted Video Chat with Specialist",
        "e-Prescriptions and Diagnosis analysis",
        "Follow-ups after a week with updates (Limited)",
        "Advanced Gene analysis and consultations (Not included)"
      ],
      "button_text": "Choose Package",
      "is_active": true,
      "order": 1
    }
  ],
  "total_count": 3
}
```

#### Get Service by Type
```
GET /content_management/public/consultation-services/by_type/?type=basic_monitoring
```

**Response:**
```json
{
  "service": {
    "id": "uuid-string",
    "name": "Basic Monitoring",
    "service_code": "basic_monitoring",
    "description": "Basic telemedicine consultation package",
    "price": "49.99",
    "features": [
      "3 Hrs uninterrupted Video Chat with Specialist",
      "e-Prescriptions and Diagnosis analysis",
      "Follow-ups after a week with updates (Limited)"
    ]
  }
}
```

### Promotional Code Verification

#### Verify Promo Code
```
POST /content_management/public/verify-promo-code/
```

**Request Body:**
```json
{
  "promo_code": "SAVE20",
  "service_id": "uuid-string"
}
```

**Response (Valid Code):**
```json
{
  "success": true,
  "data": {
    "is_valid": true,
    "promotion": {
      "code": "SAVE20",
      "discount_percentage": 20,
      "description": "Save 20% on all services"
    },
    "pricing": {
      "original_price": "299.99",
      "discounted_price": "239.99",
      "savings": "60.00"
    }
  },
  "message": "Promo code applied successfully"
}
```

**Response (Invalid Code):**
```json
{
  "success": false,
  "data": {
    "is_valid": false,
    "reason": "expired"
  },
  "message": "Promo code has expired",
  "errors": ["The promotional code 'SAVE20' has expired"]
}
```

### Public Subscription Plans

#### List Public Subscription Plans
```
GET /content_management/public/subscription-plans/
```

**Response:**
```json
{
  "count": 6,
  "results": [
    {
      "id": "uuid-string",
      "name": "Monthly Health Monitoring",
      "description": "Comprehensive monthly health monitoring service",
      "features": [
        "Monthly health reports",
        "24/7 AI health assistant",
        "Telemedicine consultations"
      ],
      "price": "49.99",
      "discounted_price": "39.99",
      "billing_cycle": "MONTH",
      "button_text": "Subscribe Now"
    }
  ]
}
```

### Public Solutions

#### List Public Solutions
```
GET /content_management/public/solutions/
```

**Response:**
```json
{
  "count": 4,
  "results": [
    {
      "id": "uuid-string",
      "name": "Clinic Management Suite",
      "description": "Complete healthcare management solution",
      "solution_type": "CLINIC",
      "features": [
        "Patient management system",
        "Appointment scheduling",
        "Electronic health records"
      ],
      "price": "299.99",
      "seat_price": "15.99",
      "button_text": "Get Clinic Solution"
    }
  ]
}
```

### Public Tab Alerts

#### List Active Tab Alerts
```
GET /content_management/public/tab-alerts/
```

**Query Parameters:**
- `tab` (optional): Filter by specific tab (AI, APPOINTMENTS, etc.)

**Response:**
```json
{
  "count": 3,
  "results": [
    {
      "id": "uuid-string",
      "message": "New AI features available!",
      "tab": "AI",
      "start_date": "2024-01-15T00:00:00Z",
      "end_date": "2024-02-15T23:59:59Z"
    }
  ]
}
```

## 🚨 Error Responses

### Common Error Codes

#### 400 Bad Request
```json
{
  "success": false,
  "message": "Validation error",
  "errors": [
    {
      "field": "price",
      "message": "Price must be a positive number"
    }
  ]
}
```

#### 401 Unauthorized
```json
{
  "success": false,
  "message": "Authentication required",
  "errors": ["JWT token is required for this endpoint"]
}
```

#### 403 Forbidden
```json
{
  "success": false,
  "message": "Permission denied",
  "errors": ["Admin privileges required"]
}
```

#### 404 Not Found
```json
{
  "success": false,
  "message": "Resource not found",
  "errors": ["Service with id 'uuid-string' not found"]
}
```

#### 409 Conflict
```json
{
  "success": false,
  "message": "Resource conflict",
  "errors": ["Service code 'basic_dna' already exists"]
}
```

## 📊 Rate Limiting

- **Public APIs**: 100 requests per minute per IP
- **Admin APIs**: 200 requests per minute per authenticated user
- **Promotional Code Verification**: 20 requests per minute per IP

## 🔒 Security Headers

All API responses include security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000`

## 📝 Notes

1. All date/time fields use ISO 8601 format in UTC
2. Decimal fields (prices) are returned as strings to preserve precision
3. UUID fields are returned as strings
4. Pagination uses cursor-based pagination for large datasets
5. All endpoints support CORS for cross-origin requests
6. Request bodies must use `Content-Type: application/json` 