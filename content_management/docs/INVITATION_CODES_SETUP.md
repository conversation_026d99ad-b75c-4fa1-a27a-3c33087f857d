# Invitation Codes System Setup Guide

## Overview
The Invitation Codes system enables invite-only registration where users need a valid invitation code to create an account. This provides controlled access and better user management.

## Features
- **Invitation-based Registration**: Users must provide a valid invitation code to register
- **CRUD Management**: Full CRUD operations for invitation codes through API and Django Admin
- **Usage Tracking**: Track how many times each code has been used
- **Expiration Control**: Set expiration dates for invitation codes
- **Bulk Generation**: Create multiple invitation codes with common prefixes
- **Validation API**: Check if invitation codes are valid without using them

## Quick Setup

### 1. Run Migrations
```bash
make dev  # Start Docker containers
docker compose exec web python manage.py migrate content_management
```

### 2. Create Sample Invitation Codes
```bash
docker compose exec web python manage.py create_sample_invitation_codes --count 20 --prefix RAVID --max-uses 1 --expires-days 30
```

### 3. Start Using Invitation-Based Registration
The new invitation register API is now available at:
```
POST /api/accounts/register/invitation/
```

## API Endpoints

### Invitation-Based Registration
```bash
POST /api/accounts/register/invitation/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "invitation_code": "RAVID-ABC123",
  "is_clinic_signup": false,
  "is_enterprise_signup": false,
  "is_mobile_signup": false
}
```

**Response:**
```json
{
  "status": 201,
  "message": "Invitation code accepted. Verification email sent to the email address you provided. Please check your inbox.",
  "email": "<EMAIL>",
  "invitation_code": "RAVID-ABC123",
  "is_clinic_signup": false,
  "is_enterprise_signup": false,
  "is_mobile_signup": false
}
```

### Validate Invitation Code (Without Using)
```bash
POST /api/content_management/admin/invitation-codes/validate/
Content-Type: application/json

{
  "invitation_code": "RAVID-ABC123"
}
```

**Response:**
```json
{
  "valid": true,
  "message": "Valid",
  "code": "RAVID-ABC123",
  "remaining_uses": 1,
  "expires_at": "2024-02-15T10:30:00Z"
}
```

## Management APIs

### List Invitation Codes
```bash
GET /api/content_management/admin/invitation-codes/
# Optional query parameters:
# ?is_active=true
# ?is_valid=true  
# ?search=RAVID
# ?created_by=user_id
```

### Create Single Invitation Code
```bash
POST /api/content_management/admin/invitation-codes/
Content-Type: application/json

{
  "code": "SPECIAL-2024",
  "description": "Special invitation for 2024",
  "max_uses": 5,
  "expires_at": "2024-12-31T23:59:59Z"
}
```

### Bulk Create Invitation Codes
```bash
POST /api/content_management/admin/invitation-codes/bulk_create/
Content-Type: application/json

{
  "prefix": "BULK",
  "count": 50,
  "description": "Bulk generated codes",
  "max_uses": 1,
  "expires_at": "2024-12-31T23:59:59Z"
}
```

### Update Invitation Code
```bash
PUT /api/content_management/admin/invitation-codes/{id}/
Content-Type: application/json

{
  "description": "Updated description",
  "max_uses": 10,
  "is_active": true
}
```

### Deactivate/Activate Invitation Code
```bash
POST /api/content_management/admin/invitation-codes/{id}/deactivate/
POST /api/content_management/admin/invitation-codes/{id}/activate/
```

### Get Statistics
```bash
GET /api/content_management/admin/invitation-codes/stats/
```

**Response:**
```json
{
  "total_codes": 100,
  "active_codes": 85,
  "valid_codes": 70,
  "expired_codes": 15,
  "used_up_codes": 25,
  "total_uses": 150,
  "total_possible_uses": 200,
  "usage_rate": 75.0
}
```

## Django Admin Interface

Access invitation codes management through Django Admin:
```
http://yoursite.com/admin/content_management/invitationcode/
```

Features:
- List view with filtering and search
- Detailed form with organized fieldsets
- Readonly fields for usage tracking
- Bulk actions for managing multiple codes

## Model Structure

### InvitationCode Model
```python
class InvitationCode(BaseModel):
    code = CharField(max_length=50, unique=True)  # Unique invitation code
    description = TextField(blank=True)  # Purpose description
    max_uses = PositiveIntegerField(default=1)  # Maximum usage count
    current_uses = PositiveIntegerField(default=0)  # Current usage count
    is_active = BooleanField(default=True)  # Active status
    expires_at = DateTimeField(null=True, blank=True)  # Expiration date
    created_by = ForeignKey(CustomUser)  # Creator
    
    def is_valid(self):
        """Check if invitation code is valid for use"""
        
    def use_code(self):
        """Increment usage count"""
```

## Validation Logic

An invitation code is considered **valid** if:
1. ✅ `is_active = True`
2. ✅ Not expired (`expires_at` is null or in the future)
3. ✅ Has remaining uses (`current_uses < max_uses`)

## Integration with Registration Flow

### Original Registration (No Code Required)
```
POST /api/accounts/register/
```
- Still available for open registration
- No invitation code required

### Invitation-Based Registration (Code Required)
```
POST /api/accounts/register/invitation/
```
- Requires valid invitation code
- Code usage is tracked automatically
- Same verification flow (email/OTP)

## Security Features

1. **Unique Codes**: All invitation codes must be unique
2. **Usage Tracking**: Automatic tracking prevents code abuse
3. **Expiration**: Time-based expiration for security
4. **Admin Controls**: Only admin users can manage codes
5. **Validation**: Codes are validated before use

## Frontend Integration Example

```javascript
// Validate invitation code before showing registration form
async function validateInvitationCode(code) {
  const response = await fetch('/api/content_management/admin/invitation-codes/validate/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify({ invitation_code: code })
  });
  
  const result = await response.json();
  return result.valid;
}

// Register with invitation code
async function registerWithInvitation(userData) {
  const response = await fetch('/api/accounts/register/invitation/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: userData.email,
      password: userData.password,
      invitation_code: userData.invitationCode,
      is_clinic_signup: userData.isClinic || false,
      is_enterprise_signup: userData.isEnterprise || false
    })
  });
  
  return await response.json();
}
```

## Troubleshooting

### Common Issues

1. **"Invalid invitation code"**
   - Check if code exists and is spelled correctly
   - Verify code is active (`is_active = True`)

2. **"Invitation code has expired"**
   - Check `expires_at` field
   - Create new codes or extend expiration

3. **"Invitation code has reached maximum uses"**
   - Check `current_uses` vs `max_uses`
   - Increase `max_uses` or create new codes

### Debug Commands

```bash
# Check invitation code details
docker compose exec web python manage.py shell
>>> from content_management.models import InvitationCode
>>> code = InvitationCode.objects.get(code="RAVID-ABC123")
>>> print(f"Valid: {code.is_valid()}")
>>> print(f"Uses: {code.current_uses}/{code.max_uses}")
>>> print(f"Expires: {code.expires_at}")
```

## Best Practices

1. **Code Generation**: Use meaningful prefixes for different campaigns
2. **Expiration**: Set reasonable expiration dates
3. **Usage Limits**: Single-use codes for security, multi-use for convenience
4. **Monitoring**: Regular check of usage statistics
5. **Cleanup**: Remove expired/used codes periodically

## Migration Notes

- The invitation system is additive - existing registration still works
- No breaking changes to current user flow
- Migration creates new `InvitationCode` table
- Fully backward compatible 