# Content Management App Documentation

Welcome to the comprehensive documentation for the Content Management app in the Ravid Healthcare Platform. This app manages all content-related services including service packages, subscription plans, promotional codes, invitation codes, and enterprise solutions for the healthcare platform.

## 📚 Documentation Overview

### 1. [CONSULTATION_SERVICES_SETUP.md](./CONSULTATION_SERVICES_SETUP.md)
**Setup guide for telemedicine consultation services**

- Service package configuration and database setup
- API endpoints for consultation services
- Integration with appointment payment system
- Frontend integration examples

### 2. [INVITATION_CODES_SETUP.md](./INVITATION_CODES_SETUP.md)
**Invitation code system configuration and management**

- Invitation code creation and validation
- User registration flow with invitation codes
- Admin management interface

### 3. [API_REFERENCE.md](./API_REFERENCE.md)
**Comprehensive API reference for all endpoints**

- Complete endpoint documentation with examples
- Request/response formats and parameters
- Authentication requirements and error handling
- Rate limiting and security considerations

### 4. [ARCHITECTURE_GUIDE.md](./ARCHITECTURE_GUIDE.md)
**Detailed architectural overview and design patterns**

- System architecture and design principles
- Data model relationships and business logic
- Security, performance, and integration architecture
- Testing strategy and deployment considerations

## 🏗️ Architecture Overview

### Core Components

The Content Management app consists of several key models and their associated APIs:

#### 🎯 Core Models

1. **Service** - Main service packages (DNA, Consultation, Other services)
2. **SubscriptionPlan** - Recurring subscription plans for ongoing services
3. **Solution** - Enterprise solutions for clinics and corporations
4. **ServicePromotion** - Promotional codes and discount management
5. **TabAlert** - System-wide notifications and alerts
6. **InvitationCode** - User registration invitation system

### Design Principles

1. **Service-Oriented Architecture**
   - Clear separation between admin and public APIs
   - Dedicated serializers for different operations
   - Comprehensive validation at model level

2. **Promotional System**
   - Flexible discount system with online/offline promotions
   - Automatic best-price calculation
   - Time-based promotion validity

3. **Enterprise Support**
   - Multi-tenant solution management
   - Scalable seat-based pricing
   - Corporate account management

4. **Security & Access Control**
   - Invitation-based registration system
   - Admin-only content management
   - Public read-only access for services

## 🎯 Key Features

### 📦 Service Management
- **Multiple Service Types**: Support for DNA analysis, consultation, and custom services
- **Feature-Rich Packages**: JSON-based feature lists with flexible configuration
- **Pricing Management**: Decimal precision pricing with promotional discounts
- **Service Ordering**: Display order management for frontend presentation

### 💳 Promotional System
- **Dual Promotion Types**: Online (auto-applied) and offline (code-required) promotions
- **Discount Calculation**: Automatic best-price calculation with multiple promotion handling
- **Time-Based Validity**: Start/end date validation for promotions
- **Stripe Integration**: Stripe promotion ID support for payment processing

### 🔐 Invitation System
- **Code-Based Registration**: Secure invitation code system for user onboarding
- **Usage Tracking**: Track code usage with limits and expiration
- **Admin Management**: Full CRUD operations for invitation codes
- **Validation Logic**: Comprehensive validation for code usage and expiration

### 🏢 Enterprise Solutions
- **Seat-Based Pricing**: Flexible pricing model for enterprise clients
- **Solution Types**: Clinic and enterprise-specific solution packages
- **Feature Management**: JSON-based feature configuration
- **Active Management**: Enable/disable solutions dynamically

### 📢 Alert System
- **Tab-Specific Alerts**: Target specific application tabs
- **Time-Based Display**: Schedule alerts with start/end dates
- **Priority Management**: Order-based alert priority
- **Multi-Tab Support**: Support for system-wide or tab-specific alerts

## 🚀 API Architecture

### Admin APIs (Protected)
Located under `/content_management/admin/`:
- Full CRUD operations for all models
- Advanced filtering and search capabilities
- Bulk operations support
- Admin-only permissions

### Public APIs (Read-Only)
Located under `/content_management/public/`:
- Service browsing and pricing information
- Consultation service packages
- Active promotions and alerts
- Public solution information

### Key Endpoints

#### Service Management
```
GET /content_management/public/services/                    # List all active services
GET /content_management/public/services/{id}/              # Get service details
POST /content_management/public/verify-promo-code/         # Verify promotional codes

# Admin endpoints
POST /content_management/admin/services/                   # Create service
PUT /content_management/admin/services/{id}/               # Update service
DELETE /content_management/admin/services/{id}/            # Delete service
```

#### Consultation Services
```
GET /content_management/public/consultation-services/packages/     # List consultation packages
GET /content_management/public/consultation-services/by_type/      # Get service by type
GET /content_management/public/consultation-services/{id}/pricing/ # Get pricing info
```

#### Promotional System
```
GET /content_management/admin/promotions/                  # List all promotions
POST /content_management/admin/promotions/                 # Create promotion
PUT /content_management/admin/promotions/{id}/             # Update promotion
```

#### Invitation Codes
```
GET /content_management/admin/invitation-codes/            # List invitation codes
POST /content_management/admin/invitation-codes/           # Create invitation code
PUT /content_management/admin/invitation-codes/{id}/       # Update invitation code
```

## 🔧 Business Logic

### Service Pricing Logic

The app implements sophisticated pricing logic that handles:

1. **Base Pricing**: Original service price
2. **Promotional Discounts**: Both online and offline promotions
3. **Best Price Calculation**: Automatic selection of best available discount
4. **Discount Validation**: Time-based and usage-based validation

```python
def get_best_discounted_price(self, promo_code=None):
    """
    Calculate the best discounted price for the service, considering:
    - Active online promotions (auto-applied)
    - Offline promotion (if promo_code provided)
    Returns: (final_price, applied_promotion, applied_type)
    """
```

### Invitation Code System

Comprehensive invitation code management with:

1. **Code Generation**: Unique code creation
2. **Usage Tracking**: Current usage vs maximum allowed
3. **Expiration Handling**: Time-based code expiration
4. **Validation Logic**: Multi-layer validation for code usage

```python
def is_valid(self):
    """Check if invitation code is valid for use"""
    
def use_code(self):
    """Increment usage count when code is used"""
```

## 🎨 Data Models

### Service Model Structure
```python
class Service(BaseModel):
    name = CharField(max_length=255)
    service_code = CharField(max_length=50, unique=True)
    description = TextField()
    storage_size = CharField(max_length=50)
    features = JSONField()  # List of features
    price = DecimalField(max_digits=10, decimal_places=2)
    service_type = CharField(choices=SERVICE_TYPES)
    active_promotions = ManyToManyField(ServicePromotion)
```

### Service Types
- **DNA**: DNA analysis services
- **CONSULTATION**: Telemedicine consultation packages
- **OTHER**: Custom or miscellaneous services

### Promotion System
```python
class ServicePromotion(SoftDeleteModel):
    code = CharField(max_length=50, unique=True)
    discount_percentage = IntegerField(validators=[MinValueValidator(0), MaxValueValidator(100)])
    promotion_type = CharField(choices=PROMOTION_TYPE_CHOICES)
    # ONLINE: Auto-applied promotions
    # OFFLINE: Code-required promotions
```

## 🧪 Testing Strategy

### Test Coverage Areas

1. **Model Validation**
   - Price calculation logic
   - Promotion validity checks
   - Invitation code validation
   - Business rule enforcement

2. **API Testing**
   - CRUD operations for all models
   - Permission checking
   - Data serialization/deserialization
   - Error handling

3. **Business Logic Testing**
   - Discount calculation accuracy
   - Best price selection logic
   - Code usage tracking
   - Expiration handling

### Running Tests
```bash
# Run all content_management tests
python manage.py test content_management

# Run specific test modules
python manage.py test content_management.tests.test_service_api
python manage.py test content_management.tests.test_promotion_logic
```

## 🔍 Integration Points

### With Billing System
- Service pricing integration for payment processing
- Promotional discount application in checkout
- Stripe promotion ID synchronization

### With Appointments System
- Consultation service integration
- Direct payment flow with service selection
- Automatic appointment confirmation after payment

### With User Registration
- Invitation code validation during signup
- Code usage tracking and limit enforcement
- Registration flow control

## 📊 Performance Considerations

### Database Optimization
- Strategic indexing on frequently queried fields
- Query optimization with select_related/prefetch_related
- Efficient promotional code lookups
- Pagination for large datasets

### Caching Strategy
- Service data caching for public APIs
- Promotional code validation caching
- Price calculation result caching
- Active alerts caching

### Background Processing
- Promotion expiration cleanup
- Usage statistics collection
- Invitation code cleanup
- Alert scheduling

## 🔐 Security Features

### Access Control
- Admin-only access for management operations
- Public read-only access for service browsing
- Invitation code-based registration security

### Data Validation
- Comprehensive input validation
- Business rule enforcement
- Promotion code security
- Price calculation validation

### Privacy Protection
- Secure invitation code generation
- Protected admin operations
- Audit trail for sensitive operations

## 🚀 Getting Started

### For Developers
1. **Review the models** in `content_management/models/base.py`
2. **Examine API endpoints** in `content_management/api/views/`
3. **Check existing tests** for usage patterns
4. **Read setup guides** for consultation services and invitation codes

### For Frontend Developers
1. **Use public APIs** for service browsing and pricing
2. **Integrate consultation services** with appointment booking
3. **Implement promotional code** validation flow
4. **Handle tab alerts** in the user interface

### For Admin Users
1. **Use Django admin** for basic content management
2. **Access admin APIs** for advanced operations
3. **Monitor invitation codes** usage and expiration
4. **Manage promotional campaigns** and pricing

## 📈 Future Enhancements

### Planned Features
- Advanced analytics for service usage
- Dynamic pricing based on demand
- Multi-language support for service content
- Advanced promotional campaign management
- Integration with external payment providers
- Automated service recommendation engine

### Extension Points
- Custom service types
- Advanced pricing models
- Third-party integration hooks
- Custom promotional logic
- Enhanced reporting capabilities

## 🛠️ Maintenance

### Regular Tasks
- Clean up expired invitation codes
- Archive old promotional campaigns
- Monitor service usage patterns
- Update pricing based on market analysis
- Review and optimize database queries

### Monitoring
- Track API performance and usage
- Monitor promotional code usage patterns
- Alert on service pricing anomalies
- Dashboard for content management metrics

---

## 📞 Support

For questions or issues related to the Content Management app:
1. Check existing documentation and setup guides
2. Review test cases for usage examples
3. Examine API responses for data structure
4. Contact the development team for complex integration questions

This documentation is maintained alongside the codebase and updated with each major release. 