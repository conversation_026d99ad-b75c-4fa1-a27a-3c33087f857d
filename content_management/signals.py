# content_management/signals.py
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
import stripe
import logging
from django.utils import timezone

from billing.models import Product
from .models import Service, SubscriptionPlan, Solution, ServicePromotion
from billing.services import StripeService

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Service)
def sync_service_to_stripe(sender, instance, created, **kwargs):
    """
    Tự động đồng bộ Service với Stripe khi có thay đổi
    <PERSON>p logic từ billing/signals.py và tối ưu hóa để tránh trùng lặp
    """
    try:
        logger.info(f"Service {instance.id} {'created' if created else 'updated'}")

        if instance.is_active:
            # Service đang active - sync to Stripe
            logger.info(f"Syncing active service {instance.id} to Stripe")
            StripeService._sync_service(instance)
        else:
            # Service không active - deactivate trong Stripe
            logger.info(f"Deactivating service {instance.id} in Stripe")
            try:
                product = Product.objects.get(
                    product_type='service',
                    content_id=instance.id
                )
                stripe.Product.modify(
                    product.stripe_product_id,
                    active=False
                )
                product.active = False
                product.save()
                logger.info(f"Successfully deactivated service {instance.id} in Stripe")
            except Product.DoesNotExist:
                logger.warning(f"No Stripe product found for service {instance.id}")
                pass
            except Exception as e:
                logger.error(f"Error deactivating service {instance.id} in Stripe: {str(e)}")

    except Exception as e:
        logger.error(f"Error syncing service {instance.id} to Stripe: {str(e)}")
        logger.exception("Full traceback:")

@receiver(post_save, sender=SubscriptionPlan)
def sync_subscription_plan_to_stripe(sender, instance, created, **kwargs):
    """
    Tự động đồng bộ SubscriptionPlan với Stripe khi có thay đổi
    """
    if instance.is_active:
        StripeService._sync_subscription_plan(instance)
    else:
        # Nếu subscription plan không active, cập nhật trạng thái trong Stripe
        try:
            product = Product.objects.get(
                product_type='subscription',
                content_id=instance.id
            )
            stripe.Product.modify(
                product.stripe_product_id,
                active=False
            )
            product.active = False
            product.save()
        except Product.DoesNotExist:
            pass

@receiver(post_save, sender=Solution)
def sync_solution_to_stripe(sender, instance, created, **kwargs):
    """
    Tự động đồng bộ Solution với Stripe khi có thay đổi
    """
    if instance.is_active:
        StripeService._sync_solution(instance)
    else:
        # Nếu solution không active, cập nhật trạng thái trong Stripe
        try:
            product = Product.objects.get(
                product_type='solution',
                content_id=instance.id
            )
            stripe.Product.modify(
                product.stripe_product_id,
                active=False
            )
            product.active = False
            product.save()
        except Product.DoesNotExist:
            pass

@receiver(post_delete, sender=Service)
def delete_service_from_stripe(sender, instance, **kwargs):
    """
    Xóa Service khỏi Stripe khi bị xóa
    """
    logger.info(f"Signal delete_service_from_stripe triggered for service {instance.id}")

    try:
        # First try to find the product in our database
        product = Product.objects.filter(
            product_type='service',
            content_id=instance.id
        ).first()

        if product and product.stripe_product_id:
            logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for service {instance.id}")
            StripeService.delete_stripe_product_by_id(product.stripe_product_id)
            logger.info(f"Successfully deleted Stripe product {product.stripe_product_id} directly")
        else:
            # If not found in database, try the regular method
            logger.info(f"Calling StripeService.delete_service_stripe_product for service {instance.id}")
            StripeService.delete_service_stripe_product(instance)
            logger.info(f"Successfully deleted Stripe product for service {instance.id}")
    except Exception as e:
        # Log error but don't prevent deletion
        logger.error(f"Error deleting service {instance.id} from Stripe: {str(e)}")
        logger.exception("Full traceback:")

@receiver(post_delete, sender=SubscriptionPlan)
def delete_subscription_plan_from_stripe(sender, instance, **kwargs):
    """
    Xóa SubscriptionPlan khỏi Stripe khi bị xóa
    """
    logger.info(f"Signal delete_subscription_plan_from_stripe triggered for subscription plan {instance.id}")

    try:
        # First try to find the product in our database
        product = Product.objects.filter(
            product_type='subscription',
            content_id=instance.id
        ).first()

        if product and product.stripe_product_id:
            logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for subscription plan {instance.id}")
            StripeService.delete_stripe_product_by_id(product.stripe_product_id)
            logger.info(f"Successfully deleted Stripe product {product.stripe_product_id} directly")
        else:
            # If not found in database, try the regular method
            logger.info(f"Calling StripeService.delete_subscription_plan_stripe_product for subscription plan {instance.id}")
            StripeService.delete_subscription_plan_stripe_product(instance)
            logger.info(f"Successfully deleted Stripe product for subscription plan {instance.id}")
    except Exception as e:
        # Log error but don't prevent deletion
        logger.error(f"Error deleting subscription plan {instance.id} from Stripe: {str(e)}")
        logger.exception("Full traceback:")

@receiver(post_delete, sender=Solution)
def delete_solution_from_stripe(sender, instance, **kwargs):
    """
    Xóa Solution khỏi Stripe khi bị xóa
    """
    logger.info(f"Signal delete_solution_from_stripe triggered for solution {instance.id}")

    try:
        # First try to find the product in our database
        product = Product.objects.filter(
            product_type='solution',
            content_id=instance.id
        ).first()

        if product and product.stripe_product_id:
            logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for solution {instance.id}")
            StripeService.delete_stripe_product_by_id(product.stripe_product_id)
            logger.info(f"Successfully deleted Stripe product {product.stripe_product_id} directly")
        else:
            # If not found in database, try the regular method
            logger.info(f"Calling StripeService.delete_solution_stripe_product for solution {instance.id}")
            StripeService.delete_solution_stripe_product(instance)
            logger.info(f"Successfully deleted Stripe product for solution {instance.id}")
    except Exception as e:
        # Log error but don't prevent deletion
        logger.error(f"Error deleting solution {instance.id} from Stripe: {str(e)}")
        logger.exception("Full traceback:")

@receiver(post_save, sender=ServicePromotion)
def sync_services_on_promotion_change(sender, instance, created, **kwargs):
    """
    Đồng bộ tất cả services liên quan khi có thay đổi về promotion
    Gộp logic từ billing/signals.py và tối ưu hóa
    """
    try:
        logger.info(f"ServicePromotion {instance.id} {'created' if created else 'updated'}")

        # Lấy tất cả các service đang áp dụng promotion này
        # Sử dụng active_promotions để tương thích với logic cũ từ billing/signals.py
        services = Service.objects.filter(active_promotions=instance)

        if not services.exists():
            logger.info(f"No services found for promotion {instance.id}")
            return

        # Sync từng service to Stripe
        for service in services:
            if service.is_active:  # Chỉ sync service đang active
                logger.info(f"Syncing service {service.id} to Stripe after promotion {instance.id} change")
                StripeService._sync_service(service)
            else:
                logger.info(f"Skipping inactive service {service.id} for promotion {instance.id}")

    except Exception as e:
        logger.error(f"Error syncing services after promotion {instance.id} change: {str(e)}")
        logger.exception("Full traceback:")