SERVICE_TYPES = [
    ('DNA', 'DNA Services'),
    ('CONSULTATION', 'Consultation Services'),
    ('OTHER', 'Other Services')
]

BILLING_CYCLES = [
    ('DAY', 'day'),
    ('WEEK', 'week'),
    ('MONTH', 'month'),
    ('YEAR', 'year')
]

SOLUTION_TYPES = [
    ('CLINIC', 'Clinic Solution'),
    ('ENTERPRISE', 'Enterprise Solution')
]

TAB_TYPES = [
    ('ALL', 'All Tabs'),
    ('AI', 'AI'),
    ('APPOINTMENTS', 'Appointments'),
    ('DIAGNOSES', 'Diagnoses'),
    ('MEDICATIONS', 'Medications'),
    ('LABORATORY', 'Laboratory'),
    ('OTHER', 'Other'),
    ('MOBILE_HEALTH', 'Mobile Health'),
    ('NOTES', 'Notes'),
    ('PRESCRIPTIONS', 'Prescriptions'),
    ('PREVENTIVE_SERVICES', 'Preventive Services'),
    ('TELEMEDICINE', 'Telemedicine'),
]

PROMOTION_TYPE_CHOICES = [
    ("OFFLINE", "Offline (requires code)"),
    ("ONLINE", "Online (auto-applied)")
] 