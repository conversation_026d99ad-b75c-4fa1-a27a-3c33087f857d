"""
Management command to create predefined consultation services for telemedicine
"""
from django.core.management.base import BaseCommand
from content_management.models import Service
from decimal import Decimal


class Command(BaseCommand):
    help = 'Create predefined consultation services for telemedicine'

    def handle(self, *args, **options):
        consultation_services = [
            {
                'name': 'Basic Monitoring',
                'service_code': 'basic_monitoring',
                'description': 'Basic telemedicine consultation package',
                'storage_size': 'N/A',
                'features': [
                    '3 Hrs uninterrupted Video Chat with Specialist',
                    'e-Prescriptions and Diagnosis analysis',
                    'Follow-ups after a week with updates (Limited)',
                    'Advanced Gene analysis and consultations (Not included)'
                ],
                'price': Decimal('49.99'),
                'service_type': 'CONSULTATION',
                'order': 1,
                'button_text': 'Choose Package',
                'is_active': True
            },
            {
                'name': 'Monthly Care',
                'service_code': 'monthly_care',
                'description': 'Comprehensive monthly telemedicine care package',
                'storage_size': 'N/A',
                'features': [
                    '3 Hrs uninterrupted Video Chat with Specialist',
                    'e-Prescriptions and Diagnosis analysis',
                    'Follow-ups after a week with updates',
                    'Advanced Gene analysis and consultations (Limited)'
                ],
                'price': Decimal('99.99'),
                'service_type': 'CONSULTATION',
                'order': 2,
                'button_text': 'Choose Package',
                'is_active': True
            },
            {
                'name': 'Premium Care',
                'service_code': 'premium_care',
                'description': 'Premium telemedicine consultation with full features',
                'storage_size': 'N/A',
                'features': [
                    '3 Hrs uninterrupted Video Chat with Specialist',
                    'e-Prescriptions and Diagnosis analysis',
                    'Follow-ups after a week with updates',
                    'Advanced Gene analysis and consultations'
                ],
                'price': Decimal('149.99'),
                'service_type': 'CONSULTATION',
                'order': 3,
                'button_text': 'Choose Package',
                'is_active': True
            }
        ]

        created_count = 0
        updated_count = 0

        for service_data in consultation_services:
            service, created = Service.objects.update_or_create(
                service_code=service_data['service_code'],
                defaults=service_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created service: {service.name}')
                )
            else:
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated service: {service.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {created_count + updated_count} consultation services '
                f'({created_count} created, {updated_count} updated)'
            )
        ) 