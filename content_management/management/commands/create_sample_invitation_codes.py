from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from content_management.models import InvitationCode
from accounts.models import CustomUser
import random
import string

class Command(BaseCommand):
    help = 'Create sample invitation codes for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of invitation codes to create (default: 10)'
        )
        parser.add_argument(
            '--prefix',
            type=str,
            default='RAVID',
            help='Prefix for invitation codes (default: RAVID)'
        )
        parser.add_argument(
            '--max-uses',
            type=int,
            default=1,
            help='Maximum uses per code (default: 1)'
        )
        parser.add_argument(
            '--expires-days',
            type=int,
            default=30,
            help='Days until expiration (default: 30)'
        )

    def handle(self, *args, **options):
        count = options['count']
        prefix = options['prefix'].upper()
        max_uses = options['max_uses']
        expires_days = options['expires_days']
        
        # Get admin user as creator if available
        admin_user = CustomUser.objects.filter(is_superuser=True).first()
        
        expires_at = timezone.now() + timedelta(days=expires_days)
        
        self.stdout.write(f"Creating {count} invitation codes...")
        
        created_codes = []
        
        for i in range(count):
            # Generate unique code
            while True:
                suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
                code = f"{prefix}-{suffix}"
                
                # Check if code already exists
                if not InvitationCode.objects.filter(code=code).exists():
                    break
            
            # Create invitation code
            invitation_code = InvitationCode.objects.create(
                code=code,
                description=f"Sample invitation code {i+1}/{count}",
                max_uses=max_uses,
                expires_at=expires_at,
                created_by=admin_user,
                is_active=True
            )
            
            created_codes.append(invitation_code)
            
            self.stdout.write(
                self.style.SUCCESS(f"Created: {code} (expires: {expires_at.strftime('%Y-%m-%d')})")
            )
        
        self.stdout.write(
            self.style.SUCCESS(f"\nSuccessfully created {len(created_codes)} invitation codes!")
        )
        
        # Print summary
        self.stdout.write("\nSummary:")
        self.stdout.write(f"  Prefix: {prefix}")
        self.stdout.write(f"  Count: {len(created_codes)}")
        self.stdout.write(f"  Max uses per code: {max_uses}")
        self.stdout.write(f"  Expires: {expires_at.strftime('%Y-%m-%d %H:%M')}")
        
        # Print some example codes
        self.stdout.write("\nExample codes:")
        for i, code in enumerate(created_codes[:5]):
            self.stdout.write(f"  {code.code}")
        
        if len(created_codes) > 5:
            self.stdout.write(f"  ... and {len(created_codes) - 5} more")
        
        self.stdout.write(
            f"\nYou can now use these codes for invitation-based registration at:"
        )
        self.stdout.write("  POST /api/accounts/register/invitation/")
        self.stdout.write("\nOr manage them through:")
        self.stdout.write("  Admin: /admin/content_management/invitationcode/")
        self.stdout.write("  API: /api/content_management/admin/invitation-codes/") 