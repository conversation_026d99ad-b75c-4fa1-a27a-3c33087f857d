from rest_framework import serializers
from .models import (
    Clinic,
    Location, 
    Service, 
    ClinicDoctor, 
    OperatingHours, 
    ClinicDepartment, 
    ClinicDepartmentDoctor,
    ClinicImage
    )
import datetime
from .admin import ClinicAdmin

class ClinicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Clinic
        fields = ['id', 'name', 'about']
        read_only_fields = ['id']
        
class OperatingHoursSerializer(serializers.ModelSerializer):
    open_time = serializers.TimeField(format='%I:%M %p', input_formats=['%I:%M %p', '%H:%M'])
    close_time = serializers.TimeField(format='%I:%M %p', input_formats=['%I:%M %p', '%H:%M'])

    class Meta:
        model = OperatingHours
        fields = ['id', 'day_of_week', 'open_time', 'close_time']
        read_only_fields = ['id']

    def validate(self, data):
        open_time = data.get('open_time')
        close_time = data.get('close_time')
        if open_time and close_time and open_time >= close_time:
            raise serializers.ValidationError("Close time must be after open time.")
        return data


class LocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = ['id', 'address', 'city', 'state', 'zip_code']


class ClinicImageSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = ClinicImage
        fields = ['id', 'image', 'uploaded_at']

class ServiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Service
        fields = ['id', 'name', 'description']


class ClinicDoctorSerializer(serializers.ModelSerializer):
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    
    class Meta:
        model = ClinicDoctor
        fields = ['id', 'doctor', 'doctor_name']
        read_only_fields = ['id']


class ClinicDepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClinicDepartment
        fields = ['id', 'name', 'description']
        read_only_fields = ['id']
        
class ClinicDepartmentDoctorSerializer(serializers.ModelSerializer):
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    class Meta:
        model = ClinicDepartmentDoctor
        fields = ['id', 'department', 'doctor', 'doctor_name']
        read_only_fields = ['id']
        

class ClinicDetailSerializer(serializers.ModelSerializer):
    admin_email = serializers.EmailField(source='user.email', read_only=True)
    contact_number = serializers.CharField(required=False,allow_blank=True)
    logo = serializers.ImageField(required=False,allow_null=True)
    additional_email = serializers.EmailField(required=False,allow_blank=True)
    language_spoken = serializers.CharField(required=False,allow_blank=True)
    insurance_accepted = serializers.BooleanField(required=False,allow_null=True )
    location = LocationSerializer(required=False,allow_null=True)
    services = ServiceSerializer(many=True, required=False,allow_null=True)
    is_clinic_public = serializers.BooleanField(required=False,allow_null=True)
    doctors = ClinicDoctorSerializer(many=True, required=False,allow_null=True)
    operating_hours = OperatingHoursSerializer(many=True, required=False,allow_null=True)
    departments = ClinicDepartmentSerializer(many=True, required=False,allow_null=True)
    clinic_department_doctors = ClinicDepartmentDoctorSerializer(many=True, required=False,allow_null=True)
    clinic_images = ClinicImageSerializer(many=True, required=False,allow_null=True)
    class Meta:
        model = Clinic
        fields = ['id', 'unique_identifier', 'admin_email', 'name', 'about', 'contact_number', 'additional_email', 'language_spoken',
                  'insurance_accepted', 'logo', 'location', 'services', 'doctors', 'operating_hours',
                  'departments', 'clinic_department_doctors', 'clinic_images', 'is_clinic_public'
                  ]
        read_only_fields = ['id', 'unique_identifier']

    def update(self, instance, validated_data):
        user_data = validated_data.pop('user', {})
        
        user = instance.user
        for attr, value in user_data.items():
            setattr(user, attr, value)
        user.save()

        nested_fields = {
            'location': LocationSerializer,
            'services': ServiceSerializer,
            'doctors': ClinicDoctorSerializer,
            'operating_hours': OperatingHoursSerializer,
            'departments': ClinicDepartmentSerializer,
            'clinic_department_doctors': ClinicDepartmentDoctorSerializer,
        }

        for field, serializer_class in nested_fields.items():
            if field in validated_data:
                self.update_nested_data(instance, field, validated_data.pop(field), serializer_class)

        # Handle clinic_images separately
        if 'clinic_images' in validated_data:
            clinic_images_data = validated_data.pop('clinic_images')
            for image_data in clinic_images_data:
                ClinicImage.objects.create(clinic=instance, **image_data)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance
    
    def update_nested_data(self, instance, field_name, data, serializer_class):
        if field_name == 'location':
            # Handle one-to-one relationship (location)
            if hasattr(instance, 'location') and instance.location:
                # Update existing location
                location_serializer = serializer_class(instance.location, data=data, partial=True)
            else:
                # Create new location
                location_serializer = serializer_class(data=data)
            
            if location_serializer.is_valid():
                location = location_serializer.save(clinic=instance)
                instance.location = location
                instance.save()
        elif field_name in ['services', 'doctors', 'operating_hours', 'departments', 'clinic_department_doctors']:
            # Handle many-to-many relationships (services and doctors)
            related_manager = getattr(instance, field_name)
            related_manager.all().delete()
            for item in data:
                serializer = serializer_class(data=item)
                if serializer.is_valid():
                    serializer.save(clinic=instance)


    def to_representation(self, instance):
        return super().to_representation(instance)
 

class AdminInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClinicAdmin
        fields = ['id', 'name', 'email', 'phone_number', 'position']
        read_only_fields = ['id']


class ClinicDoctorActionsSerializer(serializers.ModelSerializer):
    doctor_id = serializers.CharField(write_only=True)
    
    class Meta:
        model = ClinicDoctor
        fields = ('doctor_id',)