import logging
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from accounts.models import CustomUser

logger = logging.getLogger(__name__)

class RequestAdminView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        admin_email = settings.ADMIN_EMAIL  # Make sure to set this in your settings.py
        
        logger.debug(f"Preparing to send admin request email for user: {user.email}")

        user_profile = CustomUser.objects.get(email=user.email)


        # Prepare HTML content
        html_message = render_to_string('clinic/admin_request_email.html', {
            'user_email': user.email,
            'user_profile': {
                'first_name': user_profile.first_name,
                'last_name': user_profile.last_name,
                # Add any other relevant fields from your CustomUser model
            },
            'admin_panel_url': request.build_absolute_uri('/admin/'),
        })
        plain_message = strip_tags(html_message)

        try:
            # Send an email to the admin
            send_mail(
                subject="New Admin Privilege Request",
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[admin_email],
                html_message=html_message,
                fail_silently=False,
            )
            logger.info(f"Admin request email sent to {admin_email} for user {user.email}")
            return Response({"message": "Your request for admin privileges has been submitted."}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Failed to send admin request email: {str(e)}")
            return Response({"error": "Failed to send admin request. Please try again later."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)