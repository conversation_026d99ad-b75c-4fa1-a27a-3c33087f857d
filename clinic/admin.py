from django.contrib import admin
from config.models import BaseModel
from django.db import models
from .models import Clinic

# Register your models here.
class ClinicAdmin(BaseModel):
  name = models.CharField(max_length=255, blank=True)
  email = models.EmailField(blank=True)
  phone_number = models.CharField(max_length=15, blank=True)
  position = models.CharField(max_length=100, blank=True)
  clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE)

  def __str__(self):
    return f"{self.name} - {self.position} - {self.clinic.name}"
