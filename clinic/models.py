from django.db import models
from django.contrib.auth import get_user_model
from django.conf import settings
from django.core.exceptions import ValidationError
import datetime
import uuid

# Create your models here.
class Clinic(models.Model):
    
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    unique_identifier = models.CharField(max_length=10, unique=True, editable=False,null=True)
    name = models.CharField(max_length=255,blank=True,null=True)
    logo = models.CharField(blank=True, null=True)
    about = models.TextField(blank=True,null=True)
    contact_number = models.Char<PERSON>ield(max_length=20,blank=True,null=True)
    additional_email = models.EmailField(blank=True,null=True)
    is_clinic_public = models.BooleanField(default=False)
    insurance_accepted = models.BooleanField(default=False)
    language_spoken = models.CharField(max_length=100,blank=True,null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.unique_identifier:
            self.unique_identifier = self.generate_unique_identifier()
        super().save(*args, **kwargs)

    def generate_unique_identifier(self):
        while True:
            identifier = uuid.uuid4().hex[:8].upper()
            if not Clinic.objects.filter(unique_identifier=identifier).exists():
                return identifier

    def __str__(self):
        return f"{self.name} ({self.unique_identifier})"

class Location(models.Model):
    clinic = models.OneToOneField(Clinic, on_delete=models.CASCADE, related_name='location')
    address = models.CharField(max_length=255,blank=True,null=True)
    city = models.CharField(max_length=100,blank=True,null=True)
    state = models.CharField(max_length=100,blank=True,null=True)
    zip_code = models.CharField(max_length=20,blank=True,null=True)
    
    def __str__(self):
        return f"{self.clinic.name} - {self.city}, {self.state}"

class Service(models.Model):
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, related_name='services')
    name = models.CharField(max_length=255,blank=True,null=True)
    description = models.TextField(blank=True,null=True)
    
    def __str__(self):
        return f"{self.clinic.name} - {self.name}"

class ClinicDoctor(models.Model):
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, related_name='doctors')
    doctor = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='clinic_doctor')
    
    def __str__(self):
        return f"{self.clinic.name} - {self.doctor.name}"


class OperatingHours(models.Model):
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, related_name='operating_hours')
    day_of_week = models.CharField(max_length=100, blank=True, null=True)
    open_time = models.TimeField(blank=True, null=True)
    close_time = models.TimeField(blank=True, null=True)
    
    def __str__(self):
        return f"{self.clinic.name} - {self.day_of_week} - {self.format_time(self.open_time)} to {self.format_time(self.close_time)}"
    
    def format_time(self, time):
        if time:
            return time.strftime("%I:%M %p")
        return "N/A"
    
    def clean(self):
        if self.open_time and self.close_time and self.open_time >= self.close_time:
            raise ValidationError("Close time must be after open time.")
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)


class ClinicDepartment(models.Model):
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, related_name='departments')
    name = models.CharField(max_length=255,blank=True,null=True)
    description = models.TextField(blank=True,null=True)
    
    def __str__(self):
        return f"{self.clinic.name} - {self.name}"      

class ClinicDepartmentDoctor(models.Model):
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, related_name='clinic_department_doctors', blank=True, null=True)
    department = models.ForeignKey(ClinicDepartment, on_delete=models.CASCADE, related_name='doctors')
    doctor = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='clinic_department_doctor')
    def __str__(self):
        return f"{self.department.clinic.name} - {self.department.name} - {self.doctor.name}"



class ClinicImage(models.Model):
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, related_name='clinic_images')
    image = models.CharField(blank=True, null=True)  # Store the blob name
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.clinic.name} - {self.image}"