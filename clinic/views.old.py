from rest_framework.views import APIView

from rest_framework.exceptions import NotFound
from .models import Clinic
from .serializers import ClinicDetailSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from .models import (
    Location,
    Service,
    ClinicDoctor,
    OperatingHours,
    ClinicImage,
    ClinicDepartment,
    ClinicDepartmentDoctor)
from .serializers import (
    LocationSerializer,
    ServiceSerializer,
    ClinicDoctorSerializer,
    OperatingHoursSerializer,
    ClinicDepartmentSerializer,
    ClinicDepartmentDoctorSerializer,
    ClinicImageSerializer
    )        
from rest_framework.parsers import <PERSON><PERSON>art<PERSON>ars<PERSON>, FormParser, JSONParser
from rest_framework.permissions import IsAuthenticated
from roles.permissions import IsAdministrator
from accounts.models import CustomUser
from roles.models import Role
from django.urls import reverse
from django.db import models
from roles.gcp_utils import upload_file_to_gcs, get_clinic_image_signed_url, get_clinic_logo_signed_url
from django.shortcuts import get_object_or_404
import logging
from django.db import transaction
from google.cloud import storage
from google.cloud.exceptions import NotFound
from django.conf import settings
from roles.translation_utils import TranslationService

logger = logging.getLogger(__name__)

class ClinicDetailView(APIView):
    permission_classes = []
    parser_classes = (MultiPartParser, FormParser, JSONParser)
    
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.translation_service = TranslationService()
    
    
    def get_object(self, unique_identifier=None, user=None):
        if unique_identifier:
            try:
                return Clinic.objects.get(unique_identifier=unique_identifier)
            except Clinic.DoesNotExist:
                raise NotFound("Clinic not found")
        elif user:
            try:
                return Clinic.objects.get(user=user)
            except Clinic.DoesNotExist:
                clinic = Clinic.objects.create(
                    user=user,
                    name=f"{user.get_full_name()}'s Clinic"
                )
                return clinic
        return None
    
    def get(self, request, unique_identifier=None):
        try:
            if unique_identifier:
                clinic = self.get_object(unique_identifier=unique_identifier)
                
                if not clinic.is_clinic_public and request.user != clinic.user:
                    if not request.user.is_authenticated:
                        return Response(
                            {"error": "This clinic profile is private"}, 
                            status=status.HTTP_403_FORBIDDEN
                        )
            else:
                if not request.user.is_authenticated:
                    return Response(
                        {"error": "Authentication required to view own clinic"}, 
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                clinic = self.get_object(user=request.user)

            serializer = ClinicDetailSerializer(clinic)
            data = serializer.data
            
            # Handle logo URL generation
            if clinic.logo:
                try:
                    data['logo'] = get_clinic_logo_signed_url(clinic.logo, clinic.user.id)
                except Exception as e:
                    logger.error(f"Error generating logo URL: {str(e)}")
                    data['logo'] = None
            
            # Generate signed URLs for clinic images
            if 'clinic_images' in data:
                for image in data['clinic_images']:
                    try:
                        image['signed_url'] = get_clinic_image_signed_url(image['image'], clinic.user.id)
                    except Exception as e:
                        logger.error(f"Error generating image URL: {str(e)}")
                        image['signed_url'] = None

            # Handle translation
            target_language = self.translation_service.get_target_language(request)
            try:
                exclude_fields = ['clinic_images', 'unique_identifier', 'logo', 'cuid', 'id']
                translated_data = self.translation_service.translate_data(
                    data, 
                    target_language,
                    exclude_fields
                )
                return Response(translated_data)
            except Exception as e:
                logger.error(f"Error in translation: {str(e)}")
                return Response(data)

        except NotFound as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error in get method: {str(e)}")
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def post(self, request):
        if not request.user.is_authenticated:
            return Response(
                {"error": "Authentication required"}, 
                status=status.HTTP_401_UNAUTHORIZED
            )
            
        clinic = self.get_object(user=request.user)
        serializer = ClinicDetailSerializer(clinic, data=request.data, partial=True)
        if serializer.is_valid():
            if 'clinic_images' in request.FILES:
                images = request.FILES.getlist('clinic_images')
                for image in images:
                    try:
                        blob_name = upload_file_to_gcs(image, request.user.id, file_type='clinic_image')
                        ClinicImage.objects.create(clinic=clinic, image=blob_name)
                    except Exception as e:
                        logger.error(f"Error uploading clinic image: {str(e)}")
                        return Response({"error": "Failed to upload clinic image"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            if 'logo' in request.FILES:
                logo = request.FILES.get('logo')
                blob_name = upload_file_to_gcs(logo, request.user.id, file_type='clinic_logo')
                clinic.logo = blob_name
                clinic.save()
            
            
            updated_clinic = serializer.save()
            return Response(ClinicDetailSerializer(updated_clinic).data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    put = post
    patch = post
    
    def delete(self, request):
        clinic = self.get_object(request.user)
        delete_type = request.data.get('type')
        
        if delete_type == 'logo':
            # Handle logo deletion
            if not clinic.logo:
                return Response({"error": "No logo found"}, status=status.HTTP_404_NOT_FOUND)
            
            try:
                client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
                bucket = client.get_bucket(settings.GS_BUCKET_NAME)
                
                # Extract filename from logo field
                file_name = clinic.logo.split('/')[-1]
                full_blob_name = f"UID_{request.user.id}/clinic_logo/{file_name}"
                
                blob = bucket.blob(full_blob_name)
                try:
                    blob.delete()
                    logger.info(f"Deleted logo blob: {full_blob_name}")
                except NotFound:
                    logger.warning(f"Logo blob not found: {full_blob_name}")
                
                # Clear the logo field
                clinic.logo = None
                clinic.save()
                
                return Response({"message": "Clinic logo deleted successfully"}, status=status.HTTP_200_OK)
                
            except Exception as e:
                logger.error(f"Error deleting clinic logo: {str(e)}")
                return Response({"error": "Failed to delete clinic logo"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
        elif delete_type == 'image':
            # Handle clinic image deletion
            image_id = request.data.get('id')
            if not image_id:
                return Response({"error": "Image ID is required"}, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                clinic_image = ClinicImage.objects.get(id=image_id, clinic=clinic)
            except ClinicImage.DoesNotExist:
                return Response({"error": "Image not found"}, status=status.HTTP_404_NOT_FOUND)
            
            try:
                client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
                bucket = client.get_bucket(settings.GS_BUCKET_NAME)
                
                file_name = clinic_image.image.split('/')[-1]
                full_blob_name = f"UID_{request.user.id}/clinic_images/{file_name}"
                
                blob = bucket.blob(full_blob_name)
                try:
                    blob.delete()
                    logger.info(f"Deleted blob: {full_blob_name}")
                except NotFound:
                    logger.warning(f"Blob not found: {full_blob_name}")
                
                clinic_image.delete()
                
                return Response({"message": "Clinic image deleted successfully"}, status=status.HTTP_200_OK)
                
            except Exception as e:
                logger.error(f"Error deleting clinic image: {str(e)}")
                return Response({"error": "Failed to delete clinic image"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        else:
            return Response({"error": "Invalid delete type. Use 'logo' or 'image'"}, status=status.HTTP_400_BAD_REQUEST)
    
    def handle_nested_data(self, data, clinic):
        nested_fields = {
            'location': (Location, LocationSerializer),
            'services': (Service, ServiceSerializer),
            'doctors': (ClinicDoctor, ClinicDoctorSerializer),
            'operating_hours': (OperatingHours, OperatingHoursSerializer),
            'departments': (ClinicDepartment, ClinicDepartmentSerializer),
            'clinic_department_doctors': (ClinicDepartmentDoctor, ClinicDepartmentDoctorSerializer),
            'clinic_images': (ClinicImage, ClinicImageSerializer),
        }
        
        for field, (model, serializer_class) in nested_fields.items():
            if field in data:
                self.update_nested_data(data[field], model, serializer_class, clinic)
                
    def update_nested_data(self, items_data, model, serializer_class, clinic):
        existing_items = model.objects.filter(clinic=clinic)
        existing_ids = set(existing_items.values_list('id', flat=True))
        updated_ids = set()
        
        for item_data in items_data:
            item_id = item_data.get('id')
            if item_id:
                if item_id in existing_ids:
                    item = existing_items.get(id=item_id)
                    serializer = serializer_class(item, data=item_data, partial=True)
                else:
                    continue
            else:
                serializer = serializer_class(data=item_data)
            if serializer.is_valid():
                serializer.save(clinic=clinic)
                updated_ids.add(item_id)
                
        items_to_delete = existing_ids - updated_ids
        existing_items.filter(id__in=items_to_delete).delete()








class AdminPromotionView(APIView):
    permission_classes = [IsAuthenticated, IsAdministrator]

    def post(self, request):
        user_email = request.data.get('user_email')
        if not user_email:
            return Response({"error": "User email is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = CustomUser.objects.get(email=user_email)
            admin_role = Role.objects.get(name='Admin')
            user.role = admin_role
            user.save()
            return Response({"message": f"User {user_email} has been promoted to admin"}, status=status.HTTP_200_OK)
        except CustomUser.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)
        except Role.DoesNotExist:
            return Response({"error": "Admin role not found"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
      
      
from django.shortcuts import render

def emailtemp(request):
    return render(request, 'account/verify_success.html')
