# Generated by Django 5.1.3 on 2024-12-13 12:41

import django.db.models.deletion
import uuid6
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('clinic', '0003_clinic_is_clinic_public'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClinicAdmin',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.Char<PERSON>ield(blank=True, max_length=255)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('phone_number', models.CharField(blank=True, max_length=15)),
                ('position', models.CharField(blank=True, max_length=100)),
                ('clinic', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='clinic.clinic')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
