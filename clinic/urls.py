from django.urls import include, path
from .views import (
    ClinicDetailView,
    AdminPromotionView,
    RequestAdminView,
    emailtemp,
    AdminInfoViewSet,
    ClinicDoctorActionsViewSet, 
    # templateview
    )

from rest_framework.routers import DefaultRouter
router = DefaultRouter()
router.register(r'admin', AdminInfoViewSet, basename='clinic-admin')
router.register(r'doctor', ClinicDoctorActionsViewSet, basename='clinic-doctor')
urlpatterns = [
    path('', include(router.urls)),
    path('profile/', ClinicDetailView.as_view(), name='clinic'),
    path('profile/<str:unique_identifier>/', ClinicDetailView.as_view(), name='clinic-detail'),
    path('promote-admin/', AdminPromotionView.as_view(), name='promote-admin'),
    path('request-admin/', RequestAdminView.as_view(), name='request-admin'),
    path('emailtemp/', emailtemp, name='emailtemp'),
]
