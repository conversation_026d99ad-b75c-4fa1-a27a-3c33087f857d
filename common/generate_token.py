from datetime import datetime, timezone, timedelta
from django.conf import settings
import jwt


expire_time = 10 # minutes

def generate_token(payload):
    payload["exp"] = datetime.now(timezone.utc) + timedelta(minutes=expire_time)
    return jwt.encode(payload, settings.QR_SECRET_KEY, algorithm="HS256")

def verify_token(token):
    try:
        return jwt.decode(token, settings.QR_SECRET_KEY, algorithms=["HS256"])
    except jwt.ExpiredSignatureError:
        raise ValueError("Token has expired")
    except jwt.InvalidTokenError:
        raise ValueError("Invalid token")