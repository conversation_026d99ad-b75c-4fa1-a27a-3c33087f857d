
from django.conf import settings
from twilio.rest import Client
import logging
logger = logging.getLogger(__name__)
client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
def send_sms(receiver , message):
    try:
        if message is None or receiver is None:
            raise ValueError("Message and receiver are required")
        client.messages.create(to=receiver, from_=settings.TWILIO_SENDER_PHONE_NUMBER, body=message)
        logger.info(f"SMS sent to {receiver}")
    except Exception as e:
        logger.error(f"Error sending SMS: {e}")
        return False
    return True
