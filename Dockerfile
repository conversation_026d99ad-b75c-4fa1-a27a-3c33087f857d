
# Stage 1: Build environment
FROM python:3.12.6 AS builder

# Set environment variables to avoid Python from writing pyc files and buffering
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set working directory
WORKDIR /app

# Copy only the requirements to leverage Docker cache for dependencies
COPY ./requirements requirements

# Add an argument to specify the environment
ARG ENVIRONMENT=local



# Upgrade pip and install dependencies into a specific path (/install)
RUN pip install --upgrade pip && \
    pip install --prefix="/install" -r requirements/${ENVIRONMENT}.txt

# Stage 2: Production environment
FROM python:3.12.6-slim AS final

# Install dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \  
    libglib2.0-0 \
    poppler-utils \
    tesseract-ocr \
    curl \
    && rm -rf /var/lib/apt/lists/*
# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set working directory
WORKDIR /app

# Copy the dependencies from the builder stage to the final stage
COPY --from=builder /install /usr/local

# Copy the application source code to the final stage
COPY . /app/

# Expose the port for the Django application
EXPOSE 8000

COPY ./compose/start_celery /start_celery
RUN sed -i 's/\r$//g' /start_celery && chmod +x /start_celery
# Define the default command to run your application
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]

