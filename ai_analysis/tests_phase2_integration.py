"""
Phase 2 Integration Tests
Tests for enhanced unified architecture with intelligent processing
"""

import json
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock
from io import BytesIO
from PIL import Image

from accounts.models import SupportingDocs
from upload.models import UploadedFile
from ai_analysis.models import Chat, ChatMessage, ChatFile
from ai_analysis.file_processor import UnifiedFileProcessor, ProcessingResult

User = get_user_model()


class EnhancedProcessingTestCase(TestCase):
    """Test Phase 2 enhanced processing capabilities"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.processor = UnifiedFileProcessor()
    
    def test_enhanced_medical_image_detection(self):
        """Test enhanced medical image detection"""
        # High priority medical image
        medical_file = UploadedFile(
            filename='chest_xray_patient_001.jpg',
            file_type='image/jpeg'
        )
        method = self.processor._auto_detect_processing_method(medical_file)
        self.assertEqual(method, 'multimodal')
        
        # Medical document image (should use hybrid)
        doc_file = UploadedFile(
            filename='lab_report_results.png',
            file_type='image/png'
        )
        method = self.processor._auto_detect_processing_method(doc_file)
        self.assertEqual(method, 'hybrid')
        
        # Regular document
        text_file = UploadedFile(
            filename='patient_notes.pdf',
            file_type='application/pdf'
        )
        method = self.processor._auto_detect_processing_method(text_file)
        self.assertEqual(method, 'text_extraction')
    
    def test_medical_content_detection(self):
        """Test medical content detection in text"""
        medical_text = "Patient presents with chest pain and shortness of breath. Diagnosis: pneumonia."
        self.assertTrue(self.processor._is_medical_content(medical_text, "report.txt"))
        
        non_medical_text = "This is a regular business document about quarterly sales."
        self.assertFalse(self.processor._is_medical_content(non_medical_text, "sales.txt"))
    
    def test_medical_analysis_suggestions(self):
        """Test medical analysis type suggestions"""
        xray_suggestion = self.processor._suggest_medical_analysis_type("chest_xray.jpg")
        self.assertEqual(xray_suggestion, "radiological_analysis")
        
        ct_suggestion = self.processor._suggest_medical_analysis_type("brain_ct_scan.dcm")
        self.assertEqual(ct_suggestion, "cross_sectional_imaging")
        
        prescription_suggestion = self.processor._suggest_medical_analysis_type("prescription_form.pdf")
        self.assertEqual(prescription_suggestion, "prescription_analysis")
    
    @patch('ai_analysis.file_processor.UnifiedFileProcessor._extract_text_enhanced')
    @patch('ai_analysis.file_processor.UnifiedFileProcessor._prepare_multimodal_enhanced')
    def test_hybrid_processing(self, mock_multimodal, mock_text):
        """Test hybrid processing for medical documents"""
        mock_text.return_value = "Extracted medical text content"
        mock_multimodal.return_value = {"type": "image_url", "image_url": {"url": "test.jpg"}}
        
        uploaded_file = UploadedFile.objects.create(
            file='test_files/medical_report.jpg',
            filename='medical_report.jpg',
            user=self.user,
            file_type='image/jpeg'
        )
        
        chat_file = ChatFile.objects.create(
            chat=Chat.objects.create(user=self.user),
            uploaded_file=uploaded_file,
            source_type='direct_upload',
            processing_method='hybrid'
        )
        
        self.processor._execute_processing(chat_file)
        
        # Verify both methods were called
        mock_text.assert_called_once()
        mock_multimodal.assert_called_once()
        
        # Verify results
        chat_file.refresh_from_db()
        self.assertEqual(chat_file.processing_status, 'completed')
        self.assertIsNotNone(chat_file.extracted_text)
        self.assertIsNotNone(chat_file.multimodal_content)


class EnhancedUnifiedEndpointTestCase(TestCase):
    """Test Phase 2 enhanced unified endpoint"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
        
        # Create test medical file
        self.medical_file = UploadedFile.objects.create(
            file='test_files/chest_xray.jpg',
            filename='chest_xray.jpg',
            user=self.user,
            file_type='image/jpeg'
        )
        
        self.supporting_doc = SupportingDocs.objects.create(
            name='Chest X-ray',
            file=self.medical_file,
            user=self.user,
            type='diagnosis'
        )
    
    @patch('ai_analysis.ai_models.ai_model.AiModel.start_chat_unified_enhanced')
    def test_enhanced_unified_endpoint_with_medical_files(self, mock_method):
        """Test enhanced endpoint with medical file processing"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_method.return_value = mock_response
        
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_unified/',
            {
                'file_ids': [str(self.supporting_doc.id)],
                'message': 'Please analyze this chest X-ray for any abnormalities',
                'model': 'anthropic',
                'processing_method': 'multimodal'
            },
            format='json'
        )
        
        # Verify enhanced method was called
        mock_method.assert_called_once()
        call_args = mock_method.call_args
        
        self.assertEqual(call_args[1]['user'], self.user)
        self.assertEqual(call_args[1]['message'], 'Please analyze this chest X-ray for any abnormalities')
        self.assertEqual(call_args[1]['file_ids'], [str(self.supporting_doc.id)])
        self.assertEqual(call_args[1]['model'], 'anthropic')
        self.assertEqual(call_args[1]['processing_method'], 'multimodal')
        
        self.assertEqual(response.status_code, 200)
    
    @patch('ai_analysis.ai_models.ai_model.AiModel.start_chat_unified_enhanced')
    def test_enhanced_endpoint_auto_processing_method(self, mock_method):
        """Test auto processing method detection"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_method.return_value = mock_response
        
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_unified/',
            {
                'file_ids': [str(self.supporting_doc.id)],
                'message': 'Analyze this medical image',
                'model': 'gemini',
                'processing_method': 'auto'  # Should auto-detect as multimodal
            },
            format='json'
        )
        
        mock_method.assert_called_once()
        call_args = mock_method.call_args
        self.assertEqual(call_args[1]['processing_method'], 'auto')
        
        self.assertEqual(response.status_code, 200)


class EnhancedAIModelIntegrationTestCase(TestCase):
    """Test Phase 2 enhanced AI model integration"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
    
    @patch('ai_analysis.file_processor.UnifiedFileProcessor.process_files')
    @patch('ai_analysis.ai_models.ai_model.ChatAnthropic')
    def test_enhanced_content_preparation(self, mock_anthropic, mock_processor):
        """Test enhanced content preparation with processing results"""
        from ai_analysis.ai_models.ai_model import AiModel
        
        # Mock processing result
        mock_chat_file = MagicMock()
        mock_chat_file.filename = 'chest_xray.jpg'
        mock_chat_file.file_type = 'image/jpeg'
        mock_chat_file.source_type = 'direct_upload'
        mock_chat_file.processing_method = 'multimodal'
        mock_chat_file.processing_status = 'completed'
        mock_chat_file.extracted_text = None
        mock_chat_file.multimodal_content = {
            "type": "image_url",
            "image_url": {"url": "https://example.com/xray.jpg"},
            "medical_context": {"is_medical": True}
        }
        
        mock_result = ProcessingResult([mock_chat_file])
        mock_processor.return_value = mock_result
        
        # Mock AI model
        mock_stream = MagicMock()
        mock_stream.stream.return_value = [MagicMock(content='AI response')]
        mock_anthropic.return_value = mock_stream
        
        ai_model = AiModel()
        
        # Test enhanced content preparation
        content = ai_model._prepare_enhanced_unified_content(
            "Analyze this X-ray", mock_result, False
        )
        
        # Verify enhanced content structure
        self.assertIsInstance(content, list)
        self.assertTrue(len(content) >= 2)  # Text + multimodal + file context
        
        # Check for text content
        text_items = [item for item in content if item.get('type') == 'text']
        self.assertTrue(len(text_items) >= 2)  # Original message + file context
        
        # Check for multimodal content
        multimodal_items = [item for item in content if item.get('type') == 'image_url']
        self.assertEqual(len(multimodal_items), 1)
    
    def test_enhanced_message_type_determination(self):
        """Test enhanced message type determination"""
        from ai_analysis.ai_models.ai_model import AiModel
        
        ai_model = AiModel()
        
        # Mock processing result with multimodal content
        mock_chat_file = MagicMock()
        mock_chat_file.extracted_text = None
        mock_chat_file.multimodal_content = {"type": "image_url"}
        
        mock_result = ProcessingResult([mock_chat_file])
        mock_result.has_files = True
        mock_result.has_multimodal_content = True
        mock_result.has_text_content = False
        
        # Test with text + multimodal files
        message_type = ai_model._determine_enhanced_message_type("Analyze this", mock_result)
        self.assertEqual(message_type, 'mixed')
        
        # Test with files only
        message_type = ai_model._determine_enhanced_message_type("", mock_result)
        self.assertEqual(message_type, 'image')


class BackwardCompatibilityPhase2TestCase(TestCase):
    """Test that Phase 2 maintains backward compatibility"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    @patch('ai_analysis.ai_models.ai_model.AiModel.start_chat_with_files_legacy')
    def test_legacy_endpoints_still_work_phase2(self, mock_method):
        """Test that legacy endpoints still work in Phase 2"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_method.return_value = mock_response
        
        # Test legacy start_chat_with_files
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_with_files/',
            {
                'ids': ['test-id'],
                'model': 'gemini',
                'type': 'diagnosis'
            },
            format='json'
        )
        
        # Verify legacy method is still called
        mock_method.assert_called_once()
        self.assertEqual(response.status_code, 200)
    
    def test_database_schema_backward_compatible(self):
        """Test that database schema changes are backward compatible"""
        # Test that old fields still exist
        chat = Chat.objects.create(
            user=self.user,
            model='gemini',
            referenced_files=['old_file.jpg']  # Old field still works
        )
        
        message = ChatMessage.objects.create(
            chat=chat,
            content='Test message',
            role='user',
            attachments=['old_attachment.jpg']  # Old field still works
        )
        
        # Verify old fields work
        self.assertEqual(chat.referenced_files, ['old_file.jpg'])
        self.assertEqual(message.attachments, ['old_attachment.jpg'])
        
        # Verify new ChatFile model works alongside
        uploaded_file = UploadedFile.objects.create(
            file='test_files/new_file.jpg',
            filename='new_file.jpg',
            user=self.user,
            file_type='image/jpeg'
        )
        
        chat_file = ChatFile.objects.create(
            chat=chat,
            message=message,
            uploaded_file=uploaded_file,
            source_type='direct_upload',
            processing_method='multimodal'
        )
        
        # Verify relationships work
        self.assertIn(chat_file, chat.files.all())
        self.assertIn(chat_file, message.files.all())


print("✅ Phase 2 Integration Tests - Enhanced Unified Architecture với Intelligent Processing!")
