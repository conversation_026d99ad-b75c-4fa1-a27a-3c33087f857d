from django.core.management.base import BaseCommand
from django.conf import settings
import os
import re


class Command(BaseCommand):
    help = 'Toggle AI analysis caching on/off by modifying the settings file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--enable',
            action='store_true',
            help='Enable caching',
        )
        parser.add_argument(
            '--disable',
            action='store_true',
            help='Disable caching',
        )
        parser.add_argument(
            '--status',
            action='store_true',
            help='Show current cache status',
        )

    def handle(self, *args, **options):
        if options['status']:
            self.show_status()
        elif options['enable']:
            self.toggle_cache(True)
        elif options['disable']:
            self.toggle_cache(False)
        else:
            self.stdout.write(
                self.style.WARNING(
                    'Please specify --enable, --disable, or --status'
                )
            )
            self.show_status()

    def show_status(self):
        current_setting = getattr(settings, 'AI_ANALYSIS_SETTINGS', {}).get(
            'ENABLE_DASHBOARD_VARIANTS_CACHE', True
        )
        status = "ENABLED" if current_setting else "DISABLED"
        color = self.style.SUCCESS if current_setting else self.style.ERROR
        
        self.stdout.write(
            color(f'Dashboard variants caching is currently: {status}')
        )

    def toggle_cache(self, enable):
        settings_file = os.path.join(settings.BASE_DIR, 'config', 'settings', 'base.py')
        
        try:
            with open(settings_file, 'r') as f:
                content = f.read()

            # Pattern to match the ENABLE_DASHBOARD_VARIANTS_CACHE setting
            pattern = r"('ENABLE_DASHBOARD_VARIANTS_CACHE':\s*)(True|False)"
            replacement = f"\\g<1>{enable}"
            
            new_content = re.sub(pattern, replacement, content)
            
            if new_content != content:
                with open(settings_file, 'w') as f:
                    f.write(new_content)
                
                status = "ENABLED" if enable else "DISABLED"
                color = self.style.SUCCESS if enable else self.style.WARNING
                
                self.stdout.write(
                    color(f'Dashboard variants caching has been {status}')
                )
                self.stdout.write(
                    self.style.WARNING(
                        'Note: You need to restart your Django server for changes to take effect.'
                    )
                )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        'Could not find the cache setting in the settings file.'
                    )
                )
                
        except FileNotFoundError:
            self.stdout.write(
                self.style.ERROR(
                    f'Settings file not found: {settings_file}'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f'Error updating settings file: {e}'
                )
            )
