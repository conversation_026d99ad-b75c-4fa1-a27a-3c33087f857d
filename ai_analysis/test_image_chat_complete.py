#!/usr/bin/env python3
"""
Test script để verify image chat functionality hoạt động đầy đủ
"""

import os
import sys
import django
import requests
import json
from io import BytesIO
from PIL import Image

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

User = get_user_model()

class ImageChatTester:
    """Test image chat functionality"""
    
    def __init__(self):
        self.client = APIClient()
        self.user = None
        self.chat_id = None
        
    def setup_test_user(self):
        """Create test user"""
        print("🔧 Setting up test user...")
        self.user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={'password': 'testpass123'}
        )
        self.client.force_authenticate(user=self.user)
        print(f"✅ Test user ready: {self.user.email}")
    
    def create_test_image(self):
        """Create test image file"""
        print("🖼️ Creating test image...")
        image = Image.new('RGB', (200, 200), color='red')
        image_file = BytesIO()
        image.save(image_file, format='JPEG')
        image_file.seek(0)
        image_file.name = 'test_medical_image.jpg'
        print("✅ Test image created")
        return image_file
    
    def test_start_chat_with_image_legacy(self):
        """Test legacy start_chat_with_image API"""
        print("\n📋 Testing start_chat_with_image (Legacy API)...")
        
        image_file = self.create_test_image()
        
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_with_image/',
            {
                'message': 'Phân tích ảnh X-quang này có bất thường gì không?',
                'model': 'gemini',
                'enable_search': False,
                'images': image_file
            },
            format='multipart'
        )
        
        print(f"📤 Request sent to start_chat_with_image")
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ start_chat_with_image API works!")
            
            # Try to extract chat_id from streaming response
            if hasattr(response, 'streaming_content'):
                try:
                    first_chunk = next(response.streaming_content).decode('utf-8')
                    metadata = json.loads(first_chunk.strip())
                    self.chat_id = metadata.get('chat_id')
                    print(f"✅ Chat created with ID: {self.chat_id}")
                except:
                    print("⚠️ Could not extract chat_id from response")
            
            return True
        else:
            print(f"❌ start_chat_with_image failed: {response.content}")
            return False
    
    def test_start_chat_unified(self):
        """Test unified start_chat API with images"""
        print("\n📋 Testing start_chat_unified (Enhanced API)...")
        
        image_file = self.create_test_image()
        
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_unified/',
            {
                'message': 'Analyze this medical image using enhanced processing',
                'model': 'anthropic',
                'processing_method': 'auto',
                'files': image_file
            },
            format='multipart'
        )
        
        print(f"📤 Request sent to start_chat_unified")
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ start_chat_unified API works!")
            
            # Try to extract enhanced metadata
            if hasattr(response, 'streaming_content'):
                try:
                    first_chunk = next(response.streaming_content).decode('utf-8')
                    metadata = json.loads(first_chunk.strip())
                    chat_id = metadata.get('chat_id')
                    processing_info = metadata.get('processing_info', {})
                    
                    print(f"✅ Enhanced chat created with ID: {chat_id}")
                    print(f"✅ Processing info: {processing_info}")
                    
                    if not self.chat_id:  # Use this chat for follow-up test
                        self.chat_id = chat_id
                        
                except:
                    print("⚠️ Could not extract metadata from response")
            
            return True
        else:
            print(f"❌ start_chat_unified failed: {response.content}")
            return False
    
    def test_message_with_image(self):
        """Test message_with_image API"""
        print("\n📋 Testing message_with_image (Continue Chat)...")
        
        if not self.chat_id:
            print("❌ No chat_id available, skipping message_with_image test")
            return False
        
        image_file = self.create_test_image()
        
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/message_with_image/',
            {
                'chat_id': self.chat_id,
                'message': 'So sánh ảnh này với ảnh trước đó',
                'model': 'gemini',
                'images': image_file
            },
            format='multipart'
        )
        
        print(f"📤 Request sent to message_with_image")
        print(f"📤 Chat ID: {self.chat_id}")
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ message_with_image API works!")
            
            # Check response content
            if hasattr(response, 'streaming_content'):
                try:
                    first_chunk = next(response.streaming_content).decode('utf-8')
                    metadata = json.loads(first_chunk.strip())
                    message_id = metadata.get('message_id')
                    print(f"✅ Message created with ID: {message_id}")
                except:
                    print("⚠️ Could not extract message metadata")
            
            return True
        else:
            print(f"❌ message_with_image failed: {response.content}")
            return False
    
    def test_error_handling(self):
        """Test error handling scenarios"""
        print("\n📋 Testing error handling...")
        
        # Test without image
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_with_image/',
            {
                'message': '',  # No message
                # No images
            },
            format='multipart'
        )
        
        if response.status_code == 400:
            print("✅ Error handling works - correctly rejects empty request")
            return True
        else:
            print(f"❌ Error handling failed: {response.status_code}")
            return False
    
    def run_all_tests(self):
        """Run all image chat tests"""
        print("🚀 Starting Image Chat Functionality Tests")
        print("=" * 60)
        
        self.setup_test_user()
        
        tests = [
            ("start_chat_with_image (Legacy)", self.test_start_chat_with_image_legacy),
            ("start_chat_unified (Enhanced)", self.test_start_chat_unified),
            ("message_with_image (Continue)", self.test_message_with_image),
            ("Error handling", self.test_error_handling),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 IMAGE CHAT FUNCTIONALITY TEST RESULTS")
        print("=" * 60)
        
        all_passed = True
        for test_name, passed in results:
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{status} {test_name}")
            if not passed:
                all_passed = False
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 ALL TESTS PASSED - IMAGE CHAT FULLY FUNCTIONAL!")
            print("✅ Có thể chat với image cả start và trong lúc chat")
            print("✅ Legacy APIs hoạt động")
            print("✅ Enhanced unified API hoạt động")
            print("✅ Error handling đúng")
        else:
            print("⚠️ SOME TESTS FAILED - CẦN FIX ISSUES")
        print("=" * 60)
        
        return all_passed


if __name__ == "__main__":
    tester = ImageChatTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🔥 KẾT LUẬN: HOÀN TOÀN ĐỦ ĐỂ CHAT VỚI IMAGE!")
        print("📱 Frontend có thể implement ngay:")
        print("   - Start chat với image upload")
        print("   - Send image trong chat đang diễn ra")
        print("   - Cả legacy và enhanced APIs đều sẵn sàng")
        sys.exit(0)
    else:
        print("\n⚠️ CẦN FIX MỘT SỐ ISSUES TRƯỚC KHI SỬ DỤNG")
        sys.exit(1)
