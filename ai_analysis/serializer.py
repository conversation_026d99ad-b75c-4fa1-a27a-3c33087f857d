from rest_framework import serializers
from .models import Chat, ChatMessage

class ChatMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChatMessage
        fields = ['id', 'content', 'role', 'created_at', 'model', 'message_type', 'attachments']

class ChatSerializer(serializers.ModelSerializer):
    messages = ChatMessageSerializer(many=True, read_only=True)
    
    class Meta:
        model = Chat
        fields = ['id', 'model', 'referenced_files', 'messages', 'created_at']

class StartChatInputSerializer(serializers.Serializer):
    message = serializers.CharField(required=True, max_length=5000)
    model = serializers.CharField(required=False, default='gemini')
    enable_search = serializers.BooleanField(required=False, default=False)

class MessageInputSerializer(serializers.Serializer):
    chat_id = serializers.CharField(required=True)
    message = serializers.CharField(required=True, max_length=5000)
    model = serializers.Char<PERSON>ield(required=False, default='gemini')
    enable_search = serializers.BooleanField(required=False, default=False)

    def validate_chat_id(self, value):
        """Kiểm tra xem chat_id có tồn tại trong database không."""
        try:
            Chat.objects.get(id=value)
        except Chat.DoesNotExist:
            raise serializers.ValidationError("Chat with this ID does not exist.")
        return value

class MessageWithImageInputSerializer(serializers.Serializer):
    chat_id = serializers.CharField(required=True)
    message = serializers.CharField(required=False, max_length=5000, allow_blank=True)
    model = serializers.CharField(required=False, default='gemini')
    enable_search = serializers.BooleanField(required=False, default=False)

    def validate_chat_id(self, value):
        """Check if chat_id exists in database."""
        try:
            Chat.objects.get(id=value)
        except Chat.DoesNotExist:
            raise serializers.ValidationError("Chat with this ID does not exist.")
        return value

    def validate(self, data):
        """Ensure either message or images are provided."""
        message = data.get('message', '').strip()
        # Images will be validated in the view since they come from request.FILES
        if not message:
            # We'll check for images in the view
            pass
        return data

class StartChatWithImageInputSerializer(serializers.Serializer):
    message = serializers.CharField(required=False, max_length=5000, allow_blank=True)
    model = serializers.CharField(required=False, default='gemini')
    enable_search = serializers.BooleanField(required=False, default=False)

    def validate(self, data):
        """Ensure either message or images are provided."""
        message = data.get('message', '').strip()
        # Images will be validated in the view since they come from request.FILES
        if not message:
            # We'll check for images in the view
            pass
        return data


class UnifiedChatInputSerializer(serializers.Serializer):
    """
    Unified serializer supporting both pre-uploaded files and direct uploads
    Phase 1: Basic implementation
    """
    message = serializers.CharField(required=False, max_length=5000, allow_blank=True)
    model = serializers.CharField(required=False, default='gemini')
    enable_search = serializers.BooleanField(required=False, default=False)

    # Pre-uploaded file references (Pattern A)
    file_ids = serializers.ListField(
        child=serializers.CharField(max_length=255),
        required=False,
        default=list,
        help_text="List of pre-uploaded file IDs from SupportingDocs"
    )

    # Processing method override
    processing_method = serializers.ChoiceField(
        choices=[
            ('auto', 'Auto Detection'),
            ('text_extraction', 'Text Extraction'),
            ('multimodal', 'Multimodal Vision'),
            ('hybrid', 'Hybrid Processing')
        ],
        required=False,
        default='auto'
    )

    def validate(self, data):
        """Ensure either message, file_ids, or direct files are provided"""
        message = data.get('message', '').strip()
        file_ids = data.get('file_ids', [])

        # Note: Direct files validation will be done in view since they come from request.FILES
        if not message and not file_ids:
            # We'll check for direct files in the view
            pass

        return data


class MessageUnifiedInputSerializer(serializers.Serializer):
    """
    PHASE 2: Serializer for unified message input (continue chat)
    Replaces MessageWithImageInputSerializer with enhanced capabilities
    """
    chat_id = serializers.UUIDField(required=True, help_text="Chat ID to continue")
    message = serializers.CharField(required=False, allow_blank=True)
    model = serializers.ChoiceField(
        choices=[('gemini', 'Gemini'), ('anthropic', 'Anthropic')],
        default='gemini'
    )
    enable_search = serializers.BooleanField(default=False)
    processing_method = serializers.ChoiceField(
        choices=[
            ('auto', 'Auto Detection'),
            ('text_extraction', 'Text Extraction'),
            ('multimodal', 'Multimodal Vision'),
            ('hybrid', 'Hybrid Processing')
        ],
        default='auto',
        help_text="Method to process uploaded files"
    )

    def validate(self, data):
        """Ensure at least message or files will be provided"""
        message = data.get('message', '').strip()

        # Note: files are handled separately in request.FILES
        # We'll validate in the view that either message or files exist

        return data