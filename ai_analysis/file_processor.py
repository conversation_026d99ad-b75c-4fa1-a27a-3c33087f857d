"""
Unified File Processing Engine
Handles both pre-uploaded files and direct uploads with intelligent processing method detection
"""

import logging
from typing import List, Union, Optional
from django.conf import settings
from django.core.files.storage import default_storage
from django.utils import timezone
from django.db import transaction

from accounts.models import SupportingDocs
from upload.models import UploadedFile
from upload.views import FileUploadView
from ai_analysis.models import Chat<PERSON>ile, Chat, ChatMessage
from ai_analysis.utils import load_image, load_pdf
from langchain_community.document_loaders import GCSFileLoader

logger = logging.getLogger(__name__)


class ProcessingResult:
    """Result object for unified file processing"""
    
    def __init__(self, chat_files: List[ChatFile]):
        self.chat_files = chat_files
    
    def get_text_content(self) -> str:
        """Get all extracted text content"""
        texts = []
        for chat_file in self.chat_files:
            if chat_file.extracted_text:
                texts.append(f"File: {chat_file.filename}\n{chat_file.extracted_text}")
        return "\n\n".join(texts)
    
    def get_multimodal_content(self) -> List[dict]:
        """Get multimodal content for AI models"""
        content = []
        for chat_file in self.chat_files:
            if chat_file.multimodal_content and chat_file.multimodal_content != {}:
                content.append(chat_file.multimodal_content)
        return content
    
    def get_file_references(self) -> List[str]:
        """Get file path references"""
        return [cf.uploaded_file.file.name for cf in self.chat_files if cf.uploaded_file]
    
    def get_file_urls(self) -> List[str]:
        """Get file URLs"""
        return [cf.file_url for cf in self.chat_files if cf.file_url]
    
    @property
    def has_files(self) -> bool:
        """Check if any files were processed"""
        return len(self.chat_files) > 0
    
    @property
    def has_text_content(self) -> bool:
        """Check if any text content was extracted"""
        return any(cf.extracted_text for cf in self.chat_files)
    
    @property
    def has_multimodal_content(self) -> bool:
        """Check if any multimodal content is available"""
        return any(cf.multimodal_content and cf.multimodal_content != {} for cf in self.chat_files)


class UnifiedFileProcessor:
    """Unified file processing engine supporting multiple methods"""
    
    def __init__(self):
        self.file_upload_view = FileUploadView()
    
    def process_files(
        self, 
        files_input: Union[List[str], List], 
        user, 
        processing_method: str = 'auto', 
        chat: Optional[Chat] = None, 
        message: Optional[ChatMessage] = None
    ) -> ProcessingResult:
        """
        Unified file processing supporting multiple input types
        
        Args:
            files_input: Can be:
                - List of file IDs (SupportingDocs)
                - List of uploaded files (request.FILES)
                - Mixed list with metadata
            user: User object
            processing_method: 'auto', 'text_extraction', 'multimodal', 'hybrid'
            chat: Chat object (optional)
            message: ChatMessage object (optional)
        
        Returns:
            ProcessingResult object with unified interface
        """
        processed_files = []

        logger.info(f"[UNIFIED] Processing {len(files_input)} files with method: {processing_method}")

        for i, file_input in enumerate(files_input):
            try:
                logger.info(f"[UNIFIED] Processing file {i+1}/{len(files_input)}: {type(file_input)}")

                if isinstance(file_input, str):
                    # File ID reference (SupportingDocs pattern)
                    logger.info(f"[UNIFIED] Processing file ID: {file_input}")
                    chat_file = self._process_file_id(file_input, user, processing_method, chat, message)
                elif hasattr(file_input, 'read'):
                    # Direct upload (new pattern)
                    logger.info(f"[UNIFIED] Processing direct upload: {getattr(file_input, 'name', 'unknown')}")
                    chat_file = self._process_direct_upload(file_input, user, processing_method, chat, message)
                else:
                    logger.error(f"[UNIFIED] Unsupported file input type: {type(file_input)}")
                    continue

                # Link to chat/message - MUST be done before creating ChatFile
                # This is handled in _process_direct_upload and _process_file_id now

                processed_files.append(chat_file)
                logger.info(f"[UNIFIED] Successfully processed file {i+1}: ChatFile ID {chat_file.id}")

            except Exception as e:
                logger.error(f"[UNIFIED] Error processing file {i+1} ({file_input}): {e}", exc_info=True)
                # Continue processing other files
                continue

        logger.info(f"[UNIFIED] Completed processing: {len(processed_files)} files successful")
        return ProcessingResult(processed_files)
    
    @transaction.atomic
    def _process_file_id(self, file_id: str, user, processing_method: str,
                        chat: Optional[Chat] = None, message: Optional[ChatMessage] = None) -> ChatFile:
        """Process pre-uploaded file by ID"""
        try:
            # Get from SupportingDocs
            supporting_doc = SupportingDocs.objects.get(id=file_id, user=user)
            uploaded_file = supporting_doc.file

            # Determine processing method
            if processing_method == 'auto':
                processing_method = self._auto_detect_processing_method(uploaded_file)

            # Create ChatFile record with chat and message
            chat_file = ChatFile.objects.create(
                chat=chat,  # IMPORTANT: Set chat_id here
                message=message,
                uploaded_file=uploaded_file,
                source_type='supporting_doc',
                processing_method=processing_method,
                processing_status='pending'
            )

            # Process based on method
            self._execute_processing(chat_file)
            return chat_file

        except SupportingDocs.DoesNotExist:
            raise ValueError(f"File ID {file_id} not found or not accessible")
        except Exception as e:
            logger.error(f"Error processing file ID {file_id}: {e}")
            raise
    
    def _process_direct_upload(self, file, user, processing_method: str,
                              chat: Optional[Chat] = None, message: Optional[ChatMessage] = None) -> ChatFile:
        """Process directly uploaded file"""
        try:
            logger.info(f"[UNIFIED] Processing direct upload: {file.name}, size: {file.size}")

            # Upload to storage using the same method as legacy API
            from upload.views import FileUploadView
            upload_view = FileUploadView()

            # Upload file to storage
            uploaded_file = upload_view.upload_file(
                file, user, directory='unified_chat_files'
            )

            logger.info(f"[UNIFIED] File uploaded successfully: {uploaded_file.id}, path: {uploaded_file.file.name}")

            # Determine processing method
            if processing_method == 'auto':
                processing_method = self._auto_detect_processing_method(uploaded_file)
                logger.info(f"[UNIFIED] Auto-detected processing method: {processing_method}")

            # Create ChatFile record with chat and message
            with transaction.atomic():
                chat_file = ChatFile.objects.create(
                    chat=chat,  # IMPORTANT: Set chat_id here to avoid null constraint
                    message=message,
                    uploaded_file=uploaded_file,
                    source_type='direct_upload',
                    processing_method=processing_method,
                    processing_status='pending'
                )
                logger.info(f"[UNIFIED] ChatFile created: {chat_file.id} with chat_id: {chat.id if chat else None}")

            # Process based on method
            self._execute_processing(chat_file)
            logger.info(f"[UNIFIED] Processing completed for {chat_file.id}")

            return chat_file

        except Exception as e:
            logger.error(f"[UNIFIED] Error processing direct upload {getattr(file, 'name', 'unknown')}: {e}", exc_info=True)
            raise
    
    def _auto_detect_processing_method(self, uploaded_file: UploadedFile) -> str:
        """
        PHASE 2: Enhanced intelligent processing method detection
        Uses advanced heuristics for optimal medical workflow support
        """
        file_type = uploaded_file.file_type.lower() if uploaded_file.file_type else ''
        file_name = uploaded_file.filename.lower() if uploaded_file.filename else ''

        # Phase 2: Enhanced medical imaging detection
        medical_image_indicators = {
            'high_priority': [  # Definitely medical images - use multimodal
                'xray', 'x-ray', 'ct_scan', 'ct-scan', 'mri', 'ultrasound',
                'mammogram', 'ecg', 'ekg', 'radiograph', 'dicom'
            ],
            'medium_priority': [  # Likely medical - use multimodal
                'scan', 'medical', 'patient', 'diagnosis', 'chest', 'brain',
                'heart', 'lung', 'bone', 'fracture', 'tumor'
            ],
            'document_indicators': [  # Medical documents - use hybrid
                'report', 'result', 'lab', 'test', 'prescription', 'record'
            ]
        }

        # Check for high-priority medical images
        if any(indicator in file_name for indicator in medical_image_indicators['high_priority']):
            logger.info(f"[ENHANCED] Detected high-priority medical image: {file_name} -> multimodal")
            return 'multimodal'

        # Check for medical documents that might benefit from hybrid processing
        if any(indicator in file_name for indicator in medical_image_indicators['document_indicators']):
            if file_type.startswith('image/'):
                logger.info(f"[ENHANCED] Detected medical document image: {file_name} -> hybrid")
                return 'hybrid'  # Both text extraction and visual analysis
            else:
                logger.info(f"[ENHANCED] Detected medical document: {file_name} -> text_extraction")
                return 'text_extraction'

        # Check for medium-priority medical content
        if any(indicator in file_name for indicator in medical_image_indicators['medium_priority']):
            if file_type.startswith('image/'):
                logger.info(f"[ENHANCED] Detected likely medical image: {file_name} -> multimodal")
                return 'multimodal'

        # Enhanced file type detection
        if file_type in ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
            logger.info(f"[ENHANCED] Detected document: {file_type} -> text_extraction")
            return 'text_extraction'

        # Enhanced image file handling
        if file_type.startswith('image/'):
            # Check file size for processing method decision
            file_size_mb = getattr(uploaded_file, 'file_size', 0) / (1024 * 1024) if hasattr(uploaded_file, 'file_size') else 0

            if file_size_mb > 10:  # Large images might be detailed medical scans
                logger.info(f"[ENHANCED] Large image file ({file_size_mb:.1f}MB): {file_name} -> multimodal")
                return 'multimodal'
            else:
                # For smaller images, check if they might contain text
                text_indicators = ['prescription', 'label', 'form', 'document', 'text']
                if any(indicator in file_name for indicator in text_indicators):
                    logger.info(f"[ENHANCED] Text-containing image: {file_name} -> hybrid")
                    return 'hybrid'
                else:
                    logger.info(f"[ENHANCED] Standard image: {file_name} -> multimodal")
                    return 'multimodal'

        # Enhanced fallback with context
        logger.info(f"[ENHANCED] Unknown file type: {file_type}, name: {file_name} -> text_extraction")
        return 'text_extraction'
    
    def _execute_processing(self, chat_file: ChatFile):
        """
        PHASE 2: Enhanced file processing with medical workflow optimization
        """
        try:
            chat_file.processing_status = 'processing'
            chat_file.save()

            method = chat_file.processing_method
            file_path = chat_file.uploaded_file.file.name

            logger.info(f"[ENHANCED] Processing {chat_file.filename} using {method}")

            if method == 'text_extraction':
                chat_file.extracted_text = self._extract_text_enhanced(file_path, chat_file)
            elif method == 'multimodal':
                chat_file.multimodal_content = self._prepare_multimodal_enhanced(file_path, chat_file)
            elif method == 'hybrid':
                # Phase 2: Parallel processing for hybrid method
                chat_file.extracted_text = self._extract_text_enhanced(file_path, chat_file)
                chat_file.multimodal_content = self._prepare_multimodal_enhanced(file_path, chat_file)
                logger.info(f"[ENHANCED] Hybrid processing completed for {chat_file.filename}")

            chat_file.processing_status = 'completed'
            chat_file.processed_at = timezone.now()

            # Phase 2: Add processing quality metrics
            self._add_processing_metrics(chat_file)

        except Exception as e:
            logger.error(f"[ENHANCED] Error processing file {chat_file.filename}: {e}")
            chat_file.processing_status = 'failed'
            chat_file.processing_error = str(e)

        chat_file.save()

    def _extract_text_enhanced(self, file_path: str, chat_file: ChatFile) -> str:
        """
        PHASE 2: Enhanced text extraction with medical context optimization
        """
        try:
            if file_path.endswith(('.png', '.jpg', '.jpeg')):
                # Enhanced image text extraction
                loader = GCSFileLoader(
                    project_name=settings.GS_PROJECT_NAME,
                    bucket=settings.GS_BUCKET_NAME,
                    blob=str(file_path),
                    loader_func=load_image
                )
            elif file_path.endswith('.pdf'):
                # Enhanced PDF text extraction
                loader = GCSFileLoader(
                    project_name=settings.GS_PROJECT_NAME,
                    bucket=settings.GS_BUCKET_NAME,
                    blob=str(file_path),
                    loader_func=load_pdf
                )
            else:
                return ""

            documents = loader.load()
            extracted_text = "\n".join([doc.page_content for doc in documents])

            # Phase 2: Enhanced text processing for medical content
            if self._is_medical_content(extracted_text, chat_file.filename):
                extracted_text = self._enhance_medical_text(extracted_text)

            logger.info(f"[ENHANCED] Extracted {len(extracted_text)} characters from {chat_file.filename}")
            return extracted_text

        except Exception as e:
            logger.error(f"[ENHANCED] Error extracting text from {file_path}: {e}")
            return f"[Error extracting text: {str(e)}]"

    def _prepare_multimodal_enhanced(self, file_path: str, chat_file: ChatFile) -> dict:
        """
        PHASE 2: Enhanced multimodal content preparation with medical optimization
        """
        try:
            file_url = default_storage.url(file_path)

            # Phase 2: Enhanced multimodal structure with medical context
            multimodal_content = {
                "type": "image_url",
                "image_url": {
                    "url": file_url,
                    "detail": self._determine_image_detail_level(chat_file)
                }
            }

            # Add medical context if detected
            if self._is_medical_file(chat_file.filename):
                multimodal_content["medical_context"] = {
                    "is_medical": True,
                    "suggested_analysis": self._suggest_medical_analysis_type(chat_file.filename)
                }

            logger.info(f"[ENHANCED] Prepared multimodal content for {chat_file.filename}")
            return multimodal_content

        except Exception as e:
            logger.error(f"[ENHANCED] Error preparing multimodal content for {file_path}: {e}")
            return {
                "type": "text",
                "text": f"[File attachment: {file_path}]"
            }

    def _is_medical_content(self, text: str, filename: str) -> bool:
        """Detect if content is medical-related"""
        medical_terms = [
            'patient', 'diagnosis', 'treatment', 'symptom', 'medical', 'doctor',
            'hospital', 'clinic', 'prescription', 'medication', 'disease', 'condition'
        ]

        text_lower = text.lower()
        filename_lower = filename.lower()

        return any(term in text_lower or term in filename_lower for term in medical_terms)

    def _enhance_medical_text(self, text: str) -> str:
        """Enhance medical text with structured formatting"""
        # Add medical text enhancement logic here
        enhanced_text = f"[MEDICAL CONTENT]\n{text}"
        return enhanced_text

    def _is_medical_file(self, filename: str) -> bool:
        """Check if file is medical-related"""
        medical_indicators = [
            'xray', 'x-ray', 'ct', 'mri', 'ultrasound', 'scan',
            'medical', 'patient', 'diagnosis', 'prescription'
        ]
        filename_lower = filename.lower()
        return any(indicator in filename_lower for indicator in medical_indicators)

    def _determine_image_detail_level(self, chat_file: ChatFile) -> str:
        """Determine appropriate detail level for image analysis"""
        # For medical images, use high detail
        if self._is_medical_file(chat_file.filename):
            return "high"
        return "auto"

    def _suggest_medical_analysis_type(self, filename: str) -> str:
        """Suggest type of medical analysis based on filename"""
        filename_lower = filename.lower()

        if any(term in filename_lower for term in ['xray', 'x-ray', 'radiograph']):
            return "radiological_analysis"
        elif any(term in filename_lower for term in ['ct', 'mri']):
            return "cross_sectional_imaging"
        elif any(term in filename_lower for term in ['ultrasound', 'echo']):
            return "ultrasound_analysis"
        elif any(term in filename_lower for term in ['prescription', 'medication']):
            return "prescription_analysis"
        else:
            return "general_medical_analysis"

    def _add_processing_metrics(self, chat_file: ChatFile):
        """Add processing quality metrics"""
        metrics = {
            'processing_time': timezone.now().isoformat(),
            'file_size': getattr(chat_file.uploaded_file, 'file_size', 0),
            'processing_method': chat_file.processing_method,
            'has_text': bool(chat_file.extracted_text),
            'has_multimodal': bool(chat_file.multimodal_content and chat_file.multimodal_content != {}),
            'is_medical': self._is_medical_file(chat_file.filename)
        }

        # Store metrics in multimodal_content if it exists, otherwise create new structure
        if chat_file.multimodal_content:
            chat_file.multimodal_content['processing_metrics'] = metrics
        else:
            chat_file.multimodal_content = {'processing_metrics': metrics}
    
    def _extract_text(self, file_path: str) -> str:
        """
        LEGACY METHOD: Extract text using UnstructuredImageLoader approach
        Maintained for backward compatibility
        """
        try:
            if file_path.endswith(('.png', '.jpg', '.jpeg')):
                loader = GCSFileLoader(
                    project_name=settings.GS_PROJECT_NAME,
                    bucket=settings.GS_BUCKET_NAME,
                    blob=str(file_path),
                    loader_func=load_image
                )
            elif file_path.endswith('.pdf'):
                loader = GCSFileLoader(
                    project_name=settings.GS_PROJECT_NAME,
                    bucket=settings.GS_BUCKET_NAME,
                    blob=str(file_path),
                    loader_func=load_pdf
                )
            else:
                return ""

            documents = loader.load()
            return "\n".join([doc.page_content for doc in documents])

        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            return f"[Error extracting text: {str(e)}]"

    def _prepare_multimodal(self, file_path: str) -> dict:
        """
        LEGACY METHOD: Prepare multimodal content structure
        Maintained for backward compatibility
        """
        try:
            file_url = default_storage.url(file_path)
            return {
                "type": "image_url",
                "image_url": {"url": file_url}
            }
        except Exception as e:
            logger.error(f"Error preparing multimodal content for {file_path}: {e}")
            return {
                "type": "text",
                "text": f"[File attachment: {file_path}]"
            }
