"""
Phase 1 Foundation Tests
Tests for unified architecture foundation implementation
"""

import json
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock
from io import BytesIO
from PIL import Image

from accounts.models import SupportingDocs
from upload.models import UploadedFile
from ai_analysis.models import Chat, ChatMessage, ChatFile
from ai_analysis.file_processor import UnifiedFileProcessor, ProcessingResult

User = get_user_model()


class ChatFileModelTestCase(TestCase):
    """Test ChatFile unified model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.chat = Chat.objects.create(user=self.user, model='gemini')
        self.uploaded_file = UploadedFile.objects.create(
            file='test_files/test.jpg',
            filename='test.jpg',
            user=self.user,
            file_url='https://storage.googleapis.com/test/test.jpg',
            file_type='image/jpeg'
        )
    
    def test_chatfile_creation(self):
        """Test ChatFile model creation"""
        chat_file = ChatFile.objects.create(
            chat=self.chat,
            uploaded_file=self.uploaded_file,
            source_type='direct_upload',
            processing_method='multimodal'
        )
        
        self.assertEqual(chat_file.chat, self.chat)
        self.assertEqual(chat_file.uploaded_file, self.uploaded_file)
        self.assertEqual(chat_file.source_type, 'direct_upload')
        self.assertEqual(chat_file.processing_method, 'multimodal')
        self.assertEqual(chat_file.processing_status, 'pending')
    
    def test_chatfile_properties(self):
        """Test ChatFile properties"""
        chat_file = ChatFile.objects.create(
            chat=self.chat,
            uploaded_file=self.uploaded_file,
            source_type='direct_upload'
        )
        
        self.assertEqual(chat_file.filename, 'test.jpg')
        self.assertEqual(chat_file.file_type, 'image/jpeg')
        self.assertEqual(chat_file.file_url, 'https://storage.googleapis.com/test/test.jpg')
    
    def test_chatfile_relationships(self):
        """Test ChatFile relationships"""
        message = ChatMessage.objects.create(
            chat=self.chat,
            content='Test message',
            role='user'
        )
        
        chat_file = ChatFile.objects.create(
            chat=self.chat,
            message=message,
            uploaded_file=self.uploaded_file,
            source_type='direct_upload'
        )
        
        # Test relationships
        self.assertEqual(chat_file.chat, self.chat)
        self.assertEqual(chat_file.message, message)
        self.assertIn(chat_file, self.chat.files.all())
        self.assertIn(chat_file, message.files.all())


class UnifiedFileProcessorTestCase(TestCase):
    """Test UnifiedFileProcessor"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.processor = UnifiedFileProcessor()
        
        # Create test uploaded file
        self.uploaded_file = UploadedFile.objects.create(
            file='test_files/xray.jpg',
            filename='chest_xray.jpg',
            user=self.user,
            file_type='image/jpeg'
        )
        
        # Create supporting doc
        self.supporting_doc = SupportingDocs.objects.create(
            name='Test X-ray',
            file=self.uploaded_file,
            user=self.user,
            type='diagnosis'
        )
    
    def test_auto_detect_processing_method_medical_image(self):
        """Test auto-detection for medical images"""
        medical_file = UploadedFile(
            filename='chest_xray.jpg',
            file_type='image/jpeg'
        )
        
        method = self.processor._auto_detect_processing_method(medical_file)
        self.assertEqual(method, 'multimodal')
    
    def test_auto_detect_processing_method_document(self):
        """Test auto-detection for documents"""
        doc_file = UploadedFile(
            filename='report.pdf',
            file_type='application/pdf'
        )
        
        method = self.processor._auto_detect_processing_method(doc_file)
        self.assertEqual(method, 'text_extraction')
    
    @patch('ai_analysis.file_processor.UnifiedFileProcessor._execute_processing')
    def test_process_file_id(self, mock_execute):
        """Test processing file by ID"""
        chat_file = self.processor._process_file_id(
            str(self.supporting_doc.id),
            self.user,
            'multimodal'
        )
        
        self.assertEqual(chat_file.uploaded_file, self.uploaded_file)
        self.assertEqual(chat_file.source_type, 'supporting_doc')
        self.assertEqual(chat_file.processing_method, 'multimodal')
        mock_execute.assert_called_once()
    
    def test_processing_result(self):
        """Test ProcessingResult functionality"""
        chat_file1 = ChatFile.objects.create(
            chat=Chat.objects.create(user=self.user),
            uploaded_file=self.uploaded_file,
            source_type='direct_upload',
            extracted_text='Test extracted text'
        )
        
        chat_file2 = ChatFile.objects.create(
            chat=chat_file1.chat,
            uploaded_file=self.uploaded_file,
            source_type='direct_upload',
            multimodal_content={'type': 'image_url', 'image_url': {'url': 'test.jpg'}}
        )
        
        result = ProcessingResult([chat_file1, chat_file2])
        
        self.assertTrue(result.has_files)
        self.assertTrue(result.has_text_content)
        self.assertTrue(result.has_multimodal_content)
        self.assertIn('Test extracted text', result.get_text_content())
        self.assertEqual(len(result.get_multimodal_content()), 1)


class UnifiedEndpointTestCase(TestCase):
    """Test unified endpoint Phase 1 implementation"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
        
        # Create test data
        self.uploaded_file = UploadedFile.objects.create(
            file='test_files/xray.jpg',
            filename='xray.jpg',
            user=self.user,
            file_type='image/jpeg'
        )
        
        self.supporting_doc = SupportingDocs.objects.create(
            name='Test X-ray',
            file=self.uploaded_file,
            user=self.user,
            type='diagnosis'
        )
    
    @patch('ai_analysis.ai_models.ai_model.AiModel.start_chat_with_files_legacy')
    def test_unified_endpoint_file_ids_pattern(self, mock_method):
        """Test unified endpoint with file_ids (Pattern A)"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_method.return_value = mock_response
        
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_unified/',
            {
                'file_ids': [str(self.supporting_doc.id)],
                'message': 'Analyze these medical files',
                'model': 'gemini',
                'processing_method': 'auto'
            },
            format='json'
        )
        
        # Verify routing to legacy method
        mock_method.assert_called_once_with(
            user=self.user,
            type='diagnosis',
            ids=[str(self.supporting_doc.id)],
            model='gemini'
        )
        
        self.assertEqual(response.status_code, 200)
    
    @patch('ai_analysis.ai_models.ai_model.AiModel.start_chat_with_image_legacy')
    def test_unified_endpoint_direct_files_pattern(self, mock_method):
        """Test unified endpoint with direct files (Pattern B)"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_method.return_value = mock_response
        
        # Create test image
        image = Image.new('RGB', (100, 100), color='red')
        image_file = BytesIO()
        image.save(image_file, format='JPEG')
        image_file.seek(0)
        image_file.name = 'test_image.jpg'
        
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_unified/',
            {
                'message': 'Analyze this X-ray',
                'model': 'gemini',
                'files': image_file
            },
            format='multipart'
        )
        
        # Verify routing to legacy method
        mock_method.assert_called_once()
        call_args = mock_method.call_args
        self.assertEqual(call_args[0][0], self.user)  # user
        self.assertEqual(call_args[0][1], 'Analyze this X-ray')  # message
        self.assertEqual(call_args[0][3], 'gemini')  # model
        
        self.assertEqual(response.status_code, 200)
    
    @patch('ai_analysis.ai_models.ai_model.AiModel.start_chat')
    def test_unified_endpoint_text_only_pattern(self, mock_method):
        """Test unified endpoint with text only"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_method.return_value = mock_response
        
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_unified/',
            {
                'message': 'What are the symptoms of pneumonia?',
                'model': 'anthropic',
                'enable_search': True
            },
            format='json'
        )
        
        # Verify routing to regular start_chat
        mock_method.assert_called_once_with(
            user=self.user,
            message='What are the symptoms of pneumonia?',
            model='anthropic',
            enable_search=True
        )
        
        self.assertEqual(response.status_code, 200)
    
    def test_unified_endpoint_validation_errors(self):
        """Test unified endpoint validation"""
        # Test no content provided
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_unified/',
            {},
            format='json'
        )
        
        self.assertEqual(response.status_code, 400)
        self.assertIn('error', response.json())
        
        # Test mixing file_ids and direct files
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_unified/',
            {
                'file_ids': [str(self.supporting_doc.id)],
                'files': 'dummy_file'
            },
            format='multipart'
        )
        
        self.assertEqual(response.status_code, 400)
        self.assertIn('Cannot mix file_ids and direct file uploads', response.json()['error'])


class BackwardCompatibilityPhase1TestCase(TestCase):
    """Test that Phase 1 maintains 100% backward compatibility"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    @patch('ai_analysis.ai_models.ai_model.AiModel.start_chat_with_files_legacy')
    def test_legacy_start_chat_with_files_still_works(self, mock_method):
        """Test that legacy start_chat_with_files endpoint still works exactly as before"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_method.return_value = mock_response
        
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_with_files/',
            {
                'ids': ['test-id'],
                'model': 'gemini',
                'type': 'diagnosis'
            },
            format='json'
        )
        
        # Verify legacy method is called
        mock_method.assert_called_once()
        self.assertEqual(response.status_code, 200)
    
    @patch('ai_analysis.ai_models.ai_model.AiModel.start_chat_with_image_legacy')
    def test_legacy_start_chat_with_image_still_works(self, mock_method):
        """Test that legacy start_chat_with_image endpoint still works exactly as before"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_method.return_value = mock_response
        
        image = Image.new('RGB', (100, 100), color='red')
        image_file = BytesIO()
        image.save(image_file, format='JPEG')
        image_file.seek(0)
        image_file.name = 'test.jpg'
        
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/start_chat_with_image/',
            {
                'message': 'Test message',
                'images': image_file
            },
            format='multipart'
        )
        
        # Verify legacy method is called
        mock_method.assert_called_once()
        self.assertEqual(response.status_code, 200)


print("✅ Phase 1 Foundation Tests - Unified Architecture với 100% Backward Compatibility!")
