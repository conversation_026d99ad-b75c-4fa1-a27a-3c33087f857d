# Generated by Django 5.0.9 on 2025-06-05 16:26

import django.contrib.postgres.fields
import django.db.models.deletion
import uuid6
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ai_analysis', '0004_chatmessage_model'),
        ('upload', '0005_alter_uploadedfile_file'),
    ]

    operations = [
        migrations.AddField(
            model_name='chatmessage',
            name='attachments',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, default=list, help_text='Array of file paths/URLs for images attached to this message', size=None),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='message_type',
            field=models.CharField(choices=[('text', 'Text'), ('image', 'Image'), ('mixed', 'Mixed')], default='text', max_length=255),
        ),
        migrations.CreateModel(
            name='ChatFile',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('source_type', models.CharField(choices=[('supporting_doc', 'Supporting Document'), ('direct_upload', 'Direct Upload'), ('generated', 'Generated File')], help_text='How this file was added to the chat', max_length=50)),
                ('processing_method', models.CharField(choices=[('text_extraction', 'Text Extraction'), ('multimodal', 'Multimodal Vision'), ('hybrid', 'Hybrid Processing'), ('auto', 'Auto Detection')], default='auto', help_text='Method used to process this file for AI analysis', max_length=50)),
                ('extracted_text', models.TextField(blank=True, help_text='Cached text content extracted from file', null=True)),
                ('multimodal_content', models.JSONField(blank=True, default=dict, help_text='Cached multimodal content structure for AI models')),
                ('processing_status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('processing_error', models.TextField(blank=True, null=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('chat', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='ai_analysis.chat')),
                ('message', models.ForeignKey(blank=True, help_text='Message this file is attached to (null for chat-level files)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='files', to='ai_analysis.chatmessage')),
                ('uploaded_file', models.ForeignKey(help_text='Reference to the actual uploaded file', on_delete=django.db.models.deletion.CASCADE, to='upload.uploadedfile')),
            ],
            options={
                'indexes': [models.Index(fields=['chat', 'source_type'], name='cf_chat_src_idx'), models.Index(fields=['message', 'processing_method'], name='cf_msg_proc_idx'), models.Index(fields=['processing_status'], name='cf_proc_stat_idx')],
                'unique_together': {('chat', 'uploaded_file', 'message')},
            },
        ),
    ]
