# 🚀 Unified Chat API - Complete Guide

## 📋 **Overview**

Unified Chat API cung cấp một interface thống nhất để chat với AI, hỗ trợ cả text và file uploads với intelligent processing.

## 🔧 **Available APIs**

### **1. Start Chat - Unified**
```
POST /api/ai-analysis/analyze-diagnosis/start_chat_unified/
```

### **2. Continue Chat - Legacy (sẽ có message_unified)**
```
POST /api/ai-analysis/analyze-diagnosis/message_with_image/
```

### **3. Continue Chat - Unified (Available Now!)**
```
POST /api/ai-analysis/analyze-diagnosis/message_unified/
```

## 🧠 **Processing Methods Explained**

### **`auto` (Recommended)**
- **Mô tả:** AI tự động chọn method tối ưu dựa trên file type và content
- **Khi nào dùng:** H<PERSON>u hết các trường hợp, đặc biệt khi không chắc chắn
- **Logic:**
  - Medical images (X-ray, CT, MRI) → `multimodal`
  - Medical documents (lab reports, prescriptions) → `hybrid`
  - Regular documents (PDF, Word) → `text_extraction`
  - Regular images → `multimodal`

### **`multimodal`**
- **Mô tả:** Phân tích visual content của image/file
- **Khi nào dùng:** 
  - Medical images (X-ray, CT scan, MRI)
  - Photos cần visual analysis
  - Charts, graphs, diagrams
- **Output:** AI "nhìn" và mô tả visual content
- **Example:** "Tôi thấy trong ảnh X-ray này có..."

### **`text_extraction`**
- **Mô tả:** Trích xuất và đọc text content từ file
- **Khi nào dùng:**
  - PDF documents
  - Images có text (screenshots, scanned documents)
  - Word files
- **Output:** AI đọc text content và phân tích
- **Example:** "Dựa trên nội dung document: [extracted text]..."

### **`hybrid`**
- **Mô tả:** Kết hợp cả visual analysis VÀ text extraction
- **Khi nào dùng:**
  - Medical reports có cả image và text
  - Lab results với charts và numbers
  - Complex documents
- **Output:** AI vừa "nhìn" vừa "đọc" file
- **Example:** "Từ visual analysis tôi thấy... và từ text content..."

## 📱 **API Usage Examples**

### **1. Start Chat Unified**

#### **Medical X-ray Analysis**
```bash
curl --request POST \
  --url https://your-domain.com/api/ai-analysis/analyze-diagnosis/start_chat_unified/ \
  --header 'authorization: Bearer YOUR_TOKEN' \
  --form 'message=Phân tích ảnh X-ray này có bất thường gì không?' \
  --form 'model=anthropic' \
  --form 'processing_method=auto' \
  --form 'files=@chest_xray.jpg'
```

#### **Lab Report Analysis**
```bash
curl --request POST \
  --url https://your-domain.com/api/ai-analysis/analyze-diagnosis/start_chat_unified/ \
  --header 'authorization: Bearer YOUR_TOKEN' \
  --form 'message=Giải thích kết quả xét nghiệm này' \
  --form 'model=gemini' \
  --form 'processing_method=hybrid' \
  --form 'files=@lab_results.pdf'
```

#### **Multiple Files**
```bash
curl --request POST \
  --url https://your-domain.com/api/ai-analysis/analyze-diagnosis/start_chat_unified/ \
  --header 'authorization: Bearer YOUR_TOKEN' \
  --form 'message=So sánh các kết quả này' \
  --form 'model=anthropic' \
  --form 'processing_method=auto' \
  --form 'files=@xray_before.jpg' \
  --form 'files=@xray_after.jpg'
```

#### **Text Only**
```bash
curl --request POST \
  --url https://your-domain.com/api/ai-analysis/analyze-diagnosis/start_chat_unified/ \
  --header 'authorization: Bearer YOUR_TOKEN' \
  --header 'content-type: application/json' \
  --data '{
    "message": "Triệu chứng của viêm phổi là gì?",
    "model": "gemini",
    "enable_search": true
  }'
```

#### **Pre-uploaded Files (File IDs)**
```bash
curl --request POST \
  --url https://your-domain.com/api/ai-analysis/analyze-diagnosis/start_chat_unified/ \
  --header 'authorization: Bearer YOUR_TOKEN' \
  --header 'content-type: application/json' \
  --data '{
    "message": "Phân tích các file đã upload",
    "model": "anthropic",
    "file_ids": ["doc-id-1", "doc-id-2"],
    "processing_method": "auto"
  }'
```

### **2. Continue Chat - Unified (Enhanced)**

```bash
curl --request POST \
  --url https://your-domain.com/api/ai-analysis/analyze-diagnosis/message_unified/ \
  --header 'authorization: Bearer YOUR_TOKEN' \
  --form 'chat_id=YOUR_CHAT_ID' \
  --form 'message=So sánh với ảnh này' \
  --form 'model=gemini' \
  --form 'processing_method=auto' \
  --form 'files=@new_xray.jpg'
```

### **3. Continue Chat (Legacy)**

```bash
curl --request POST \
  --url https://your-domain.com/api/ai-analysis/analyze-diagnosis/message_with_image/ \
  --header 'authorization: Bearer YOUR_TOKEN' \
  --form 'chat_id=YOUR_CHAT_ID' \
  --form 'message=So sánh với ảnh này' \
  --form 'model=gemini' \
  --form 'images=@new_xray.jpg'
```

## 📊 **Request Parameters**

### **Required Parameters**
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `message` | string | Text message (required if no files) | "Phân tích ảnh này" |
| `files` OR `file_ids` | file/array | Files to upload OR pre-uploaded file IDs | files or ["doc-1"] |

### **Optional Parameters**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `model` | string | `gemini` | AI model: `gemini`, `anthropic` |
| `processing_method` | string | `auto` | How to process files |
| `enable_search` | boolean | `false` | Enable web search |

### **Processing Method Options**
| Method | Use Case | Output |
|--------|----------|--------|
| `auto` | Let AI decide (recommended) | Optimal processing |
| `multimodal` | Visual analysis | AI "sees" content |
| `text_extraction` | Read text content | AI "reads" text |
| `hybrid` | Both visual + text | AI "sees" + "reads" |

## 📥 **Response Format**

### **Metadata (First Chunk)**
```json
{
  "chat_id": "019740fc-1392-7e24-96fb-4796a7bf6943",
  "message_id": "019740fc-13b5-7bba-ba73-76eadbb4ecf4",
  "processing_info": {
    "files_processed": 1,
    "has_text_content": true,
    "has_multimodal_content": true,
    "search_enabled": false
  }
}
```

### **AI Response (Streaming)**
```
Tôi đang phân tích ảnh X-ray bạn đã gửi...

Dựa trên ảnh X-ray ngực này, tôi có thể thấy:

1. Phổi trái và phải có vẻ trong suốt bình thường
2. Không có dấu hiệu tụ dịch màng phổi
3. Tim có kích thước bình thường
...

Processed 1 file(s) using unified architecture. 
Visual content analysis included.
```

## 🎯 **Best Practices**

### **1. Processing Method Selection**
```javascript
// For medical images
processing_method: "auto"  // Will auto-detect as multimodal

// For documents with text
processing_method: "text_extraction"

// For complex medical reports
processing_method: "hybrid"

// When unsure
processing_method: "auto"  // Always safe choice
```

### **2. Model Selection**
```javascript
// For medical analysis
model: "anthropic"  // Better for detailed medical analysis

// For general questions
model: "gemini"     // Faster, good for general use

// For research with web search
model: "anthropic"
enable_search: true
```

### **3. Error Handling**
```javascript
// Handle different response types
if (response.status === 200) {
  // Success - handle streaming response
  handleStreamingResponse(response);
} else if (response.status === 400) {
  // Validation error
  console.error("Validation error:", response.data);
} else if (response.status === 401) {
  // Authentication error
  redirectToLogin();
} else {
  // Server error
  console.error("Server error:", response.data);
}
```

## 🔄 **Migration from Legacy APIs**

### **From start_chat_with_image**
```javascript
// OLD
POST /api/ai-analysis/analyze-diagnosis/start_chat_with_image/
{
  message: "Analyze this",
  model: "gemini",
  images: file
}

// NEW
POST /api/ai-analysis/analyze-diagnosis/start_chat_unified/
{
  message: "Analyze this",
  model: "gemini", 
  processing_method: "auto",  // NEW: intelligent processing
  files: file                 // Changed from 'images' to 'files'
}
```

### **From start_chat_with_files**
```javascript
// OLD
POST /api/ai-analysis/analyze-diagnosis/start_chat_with_files/
{
  ids: ["doc-1", "doc-2"],
  model: "gemini",
  type: "diagnosis"
}

// NEW
POST /api/ai-analysis/analyze-diagnosis/start_chat_unified/
{
  file_ids: ["doc-1", "doc-2"],  // Changed from 'ids' to 'file_ids'
  model: "gemini",
  processing_method: "auto"      // NEW: intelligent processing
  // No 'type' needed
}
```

## 🎉 **NEW: message_unified - Available Now!**

```bash
# Enhanced unified API for continuing chat
POST /api/ai-analysis/analyze-diagnosis/message_unified/

# With files
curl --request POST \
  --url https://your-domain.com/api/ai-analysis/analyze-diagnosis/message_unified/ \
  --header 'authorization: Bearer YOUR_TOKEN' \
  --form 'chat_id=your-chat-id' \
  --form 'message=Continue conversation with this file' \
  --form 'model=anthropic' \
  --form 'processing_method=auto' \
  --form 'files=@additional_file.jpg'

# Text only
curl --request POST \
  --url https://your-domain.com/api/ai-analysis/analyze-diagnosis/message_unified/ \
  --header 'authorization: Bearer YOUR_TOKEN' \
  --header 'content-type: application/json' \
  --data '{
    "chat_id": "your-chat-id",
    "message": "Continue conversation",
    "model": "gemini",
    "enable_search": true
  }'
```

### **Key Benefits of message_unified:**
- ✅ **Consistent API:** Same parameters as start_chat_unified
- ✅ **Intelligent Processing:** Auto-detection for uploaded files
- ✅ **Enhanced Metadata:** Detailed processing information
- ✅ **File Support:** Upload files during conversation
- ✅ **Backward Compatible:** Legacy message_with_image still works

## 📞 **Support & Troubleshooting**

### **Common Issues**
1. **"files_processed": 0** → Check file format and size
2. **"Authentication error"** → Verify Bearer token
3. **"Processing failed"** → Check file corruption or format

### **Supported File Types**
- **Images:** `.jpg`, `.jpeg`, `.png`
- **Documents:** `.pdf`, `.doc`, `.docx`, `.txt`
- **Medical:** DICOM support coming soon

### **File Size Limits**
- **Images:** Max 10MB per file
- **Documents:** Max 25MB per file
- **Total:** Max 100MB per request
