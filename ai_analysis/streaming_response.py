import logging
from django.http import StreamingHttpResponse
from typing import Generator

logger = logging.getLogger(__name__)


class ImprovedStreamingHttpResponse(StreamingHttpResponse):
    """
    Custom StreamingHttpResponse with better error handling and logging for AI model streaming.
    """

    def __init__(self, streaming_content: Generator[str, None, None], *args, **kwargs):
        # Set appropriate headers for streaming
        kwargs.setdefault('content_type', 'text/event-stream')

        # Wrap the streaming content with error handling
        wrapped_content = self._wrap_streaming_content(streaming_content)

        super().__init__(wrapped_content, *args, **kwargs)

        # Set headers to prevent buffering
        self['Cache-Control'] = 'no-cache'
        self['X-Accel-Buffering'] = 'no'  # Disable nginx buffering

    def _wrap_streaming_content(self, streaming_content: Generator[str, None, None]) -> Generator[str, None, None]:
        """
        Wrap the streaming content with error handling and logging.
        """
        try:
            chunk_count = 0
            total_length = 0

            for chunk in streaming_content:
                if chunk:  # Only yield non-empty chunks
                    chunk_count += 1
                    total_length += len(chunk)

                    # Log progress periodically
                    if chunk_count % 100 == 0:
                        logger.debug(f'Streamed {chunk_count} chunks, total length: {total_length}')

                    yield chunk

            logger.info(f'Streaming completed successfully. Total chunks: {chunk_count}, final length: {total_length}')

        except GeneratorExit:
            logger.warning('Streaming was interrupted by client disconnect')
            raise
        except Exception as e:
            logger.error(f'Error during streaming at chunk {chunk_count}: {e}')
            # Yield error message to client
            error_response = f'\n\nStreaming error: {str(e)}'
            yield error_response
            raise



