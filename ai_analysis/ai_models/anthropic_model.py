import datetime
from langchain_core.rate_limiters import InMemoryRateLimiter
import logging
from typing import Dict, List, Tuple
from django.conf import settings
from langchain_anthropic import <PERSON>t<PERSON><PERSON>hropic
from rest_framework.response import Response

from accounts.models import CustomUser, SupportingDocs
from ai_analysis.models import AiAnalysisOutput, Chat, ChatMessage
from ai_analysis.utils import load_image, load_pdf
from upload.views import FileUploadView
from langchain_google_community import GCSFileLoader
from langchain_core.rate_limiters import InMemoryRateLimiter

logger = logging.getLogger(__name__)


class AnthropicModel:
    # Cache ongoing chats
    # Mapping of chat id and Chat object, with another list of token, which is output of
    # model.astream()
    chats: Dict[str, Tuple[Chat, List[str]]] = {}
    def __init__(self, temperature: float = 0.0, max_tokens: int = 1000, max_retries: int = 2):

        rate_limiter = InMemoryRateLimiter(
            # <-- Super slow! We can only make a request once every 10 seconds!!
            requests_per_second=1,
            # Wake up every 100 ms to check whether allowed to make a request,
            check_every_n_seconds=1,
            max_bucket_size=8,  # Controls the maximum burst size.
        )
        self.model = ChatAnthropic(
            model="claude-3-5-haiku-20241022", api_key=settings.ANTHROPIC_API_KEY, temperature=temperature, max_tokens=max_tokens, max_retries=max_retries, timeout=10 * 60, rate_limiter=rate_limiter)
        self.doc_db = SupportingDocs
        self.file_upload_view = FileUploadView()

    def clear_old_chats(self):
        for chat in self.chats.values():
            if chat.created_at < datetime.now() - datetime.timedelta(hours=10):
                self.chats.pop(chat.id)


    
    async def start_chat(self, user: CustomUser, type: str, ids: List[str] | None = None):
        try:
            print(f'Starting chat for user {user.id} with type {type} and ids {ids}')
            # Get all diagnosis docs for the user
            diagnosis_docs = self.doc_db.objects.filter(
                user=user, type=type, id__in=ids).select_related('file') if ids else self.doc_db.objects.filter(user=user, type=type).select_related('file')

            # Generatd signed url for each doc
            file_names: List[str] = []

            for doc in diagnosis_docs:
                # if doc.file.file_type != 'application/pdf':
                #     continue
                file_names.append(str(doc.file.file))
            file_names.sort()

            # Check if the analysis has already been generated for the user and the files
            cached_analysis = AiAnalysisOutput.objects.filter(
                user=user, referenced_files=file_names).order_by('-created_at').first()
            if cached_analysis:
                logger.info(
                    f'Cached analysis found for user {user.id} and files {file_names}')
                return cached_analysis.content

            # If the analysis has not been generated, generate it
            logger.info(
                f'Generating analysis for user {user.id} and files {file_names}')
            file_contents = []
            for doc in file_names:
                if doc.endswith(('.png', '.jpg', '.jpeg')):
                    loader = GCSFileLoader(
                        project_name=settings.GS_PROJECT_NAME,
                        bucket=settings.GS_BUCKET_NAME,
                        blob=str(doc),
                        loader_func=load_image
                    )
                elif doc.endswith('.pdf'):
                    loader = GCSFileLoader(
                        project_name=settings.GS_PROJECT_NAME,
                    bucket=settings.GS_BUCKET_NAME,
                    blob=str(doc),
                    loader_func=load_pdf
                )
                else:
                    logger.warning(f'Unsupported file type: {doc}')
                    continue
                file_contents.append(loader.load())

            # Generate prompt for the model
            prompt = f"Analyze the following diagnosis documents for the user with ID {user.id}: {file_contents}, and provide a summary of the findings. If there are any abnormalities, inconsistencies or variances from established norms, provide a detailed explanation and an assessment summary of the findings and a final analysis along with any follow-up steps. If any of the documents are not relevant to the user's diagnosis, please ignore them, and list the irrelevant documents after the summary."

            chat = Chat.objects.create(user=user, type=type, ids=ids)

            self.chats[chat.id] = (chat, [])
            async for chunk in self.model.astream(prompt):
                print(chunk.content)
                self.chats[chat.id][1].append(chunk.content)
                if chunk.finish_reason is not None:
                    content = ''.join(self.chats[chat.id][1])
                    AiAnalysisOutput.objects.create(
                        user=user, content=content, referenced_files=file_names)
                    
                    ChatMessage.objects.create(
                        chat=chat,
                        role='assistant',
                        content=content
                    )

                    self.chats[chat.id][1].clear()
                    return content
            return Response({'message': 'Chat session start successfully'}, status=200)

                

        except Exception as e:
            print(f'Error analyzing diagnosis: {e}')
            return None
