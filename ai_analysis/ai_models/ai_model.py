import datetime
import io
import json
import time
from django.http import StreamingHttpResponse
from langchain_core.rate_limiters import InMemoryRateLimiter
import logging
from typing import Dict, List, Tuple
from django.conf import settings
from langchain_anthropic import Chat<PERSON>nthropic
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeA<PERSON>
from rest_framework.response import Response

from accounts.models import CustomUser, SupportingDocs
from ai_analysis.models import AiAnalysisOutput, Chat, ChatMessage
from ai_analysis.serializer import ChatSerializer
from ai_analysis.streaming_response import ImprovedStreamingHttpResponse
from ai_analysis.utils import load_image, load_pdf
from upload.models import UploadedFile
from upload.views import FileUploadView
from langchain_google_community import GCSFileLoader
from langchain_core.rate_limiters import InMemoryRateLimiter

from django.core.files.storage import default_storage
from langchain_core.messages import AIMessage, HumanMessage
from langchain_community.tools import DuckDuckGoSearchRun
import pymupdf
import os
import base64
from PIL import Image
import io
from django.db import transaction
from ai_analysis.file_processor import UnifiedFileProcessor, ProcessingResult

logger = logging.getLogger(__name__)


class AiModel:
    chat_history_limit = 10

    def __init__(self, temperature: float = 0.0, max_tokens: int = 4000, max_retries: int = 3):

        rate_limiter = InMemoryRateLimiter(
            # Increased rate limit for better performance
            requests_per_second=2,
            # Wake up every 100 ms to check whether allowed to make a request,
            check_every_n_seconds=0.5,
            max_bucket_size=10,  # Controls the maximum burst size.
        )
        self.anthropic_model = ChatAnthropic(
            model="claude-3-7-sonnet-20250219", api_key=settings.ANTHROPIC_API_KEY, temperature=temperature, max_tokens=max_tokens, max_retries=max_retries, timeout=15 * 60, rate_limiter=rate_limiter)
        self.gemini_model = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash", api_key=settings.GEMINI_API_KEY, temperature=temperature, max_tokens=max_tokens, max_retries=max_retries, timeout=15 * 60, rate_limiter=rate_limiter)
        self.doc_db = SupportingDocs
        self.file_upload_view = FileUploadView()
        ##LLM search init
        self.search_tool = DuckDuckGoSearchRun()
        self.tools = [self.search_tool]
        # Bind tools into LLM
        # self.anthropic_model = self.anthropic_model.bind_tools(self.tools)
        # self.gemini_model = self.gemini_model.bind_tools(self.tools)

    def query_files(self, user: CustomUser, type: str, ids: List[str] | None = None):
        try:
            docs = self.doc_db.objects.filter(
                user=user, type=type, id__in=ids).select_related('file') if ids else self.doc_db.objects.filter(user=user, type=type).select_related('file')
            # Generatd signed url for each doc
            file_names: List[str] = []

            for doc in docs:
                # if doc.file.file_type != 'application/pdf':
                #     continue
                file_names.append(str(doc.file.file))
            file_names.sort()
            print(f'Found {len(file_names)} files for user {user.id} and type {type}')
            return file_names
        except Exception as e:
            logger.error(f'Error querying files: {e}')
            return None

    def find_cached_chat(self, user: CustomUser, file_names: List[str]):
        try:

            cached_chat = Chat.objects.filter(
                user=user,
                referenced_files=file_names
            ).order_by('-created_at').prefetch_related('messages').first()

            if not cached_chat:
                return Response({'message': 'No chat found'}, status=404)

            return cached_chat
        except Exception as e:
            logger.error(f'Error finding cached chat: {e}')
            return None

    def load_files(self, user: CustomUser, type: str, file_names: List[str]):
        file_contents = []
        for doc in file_names:
            if doc.endswith(('.png', '.jpg', '.jpeg')):
                loader = GCSFileLoader(
                    project_name=settings.GS_PROJECT_NAME,
                    bucket=settings.GS_BUCKET_NAME,
                    blob=str(doc),
                    loader_func=load_image
                )
            elif doc.endswith('.pdf'):
                loader = GCSFileLoader(
                    project_name=settings.GS_PROJECT_NAME,
                bucket=settings.GS_BUCKET_NAME,
                blob=str(doc),
                loader_func=load_pdf
            )
            else:
                logger.warning(f'Unsupported file type: {doc}')
                continue
            file_contents.append(loader.load())
        return file_contents

    def find_chat(self, user: CustomUser, type: str, ids: List[str] | None = None, model: str = 'gemini'):
        try:
            logger.info(f'Finding chat for user {user.id} with type {type} and ids {ids}')
            file_names = self.query_files(user, type, ids)
            cached_chat = self.find_cached_chat(user, file_names)
            if cached_chat:
                serializer = ChatSerializer(cached_chat)
                return Response(serializer.data, status=200)
            else:
                return Response({'message': 'Chat with this configuration not found'}, status=404)
        except Exception as e:
            logger.error(f'Error finding chat: {e}')
            return Response({'message': 'Chat with this configuration not found'}, status=404)

    def start_chat(self, user: CustomUser, message: str, model: str = 'gemini', enable_search: bool = False):
        try:
            # Create chat and user message
            chat = Chat.objects.create(user=user, model=model)
            ChatMessage.objects.create(chat=chat, content=message, role='user')
            llm = self.anthropic_model if model == 'anthropic' else self.gemini_model

            # Handle web search if enabled
            content = message
            if enable_search:
                search_query = message
                try:
                    search_results = self.search_tool.invoke(search_query)
                    if not search_results:
                        search_results = "No information found from web search."
                except Exception as e:
                    logger.error(f"Search failed for query '{search_query}': {e}")
                    search_results = "Failed to retrieve information from web search."
                current_year = datetime.datetime.now().year
                if str(current_year + 1) in message or "2025" in message:
                    search_results += f"\n\nNote: As of {datetime.datetime.now().strftime('%Y-%m-%d')}, the query includes a future date (e.g., 2025), so there may be no current information available."
                content = (
                    "Please answer the following question using the provided web search results as the primary source of information. "
                    "If the search results do not provide a clear answer, you may use your general knowledge, but prioritize the search results.\n\n"
                    f"Question: {message}\n\nWeb search results: {search_results}"
                )

            # Prepare messages for the model
            messages = [HumanMessage(content=content)]
            stream = llm.stream(messages)

            # Create response message entry
            response_message = ChatMessage.objects.create(chat=chat, role='assistant', model=model)
            meta_data = {'chat_id': str(chat.id), 'message_id': str(response_message.id)}

            # Define the streaming generator
            def stream_response():
                yield f"{json.dumps(meta_data)}\n\n"
                full_output = ""
                # Stream chunks as they are generated
                for chunk in stream:
                    yield chunk.content
                    full_output += chunk.content
                # Append the web search note after streaming the main response
                if enable_search:
                    if "Failed to retrieve information" in search_results:
                        note = "\n\nWeb search failed, response based on general knowledge."
                    else:
                        note = "\n\nResponse includes information from web search."
                else:
                    note = "\n\nNo web search was performed for this response."
                yield note
                full_output += note
                # Save the complete response to the database
                response_message.content = full_output
                response_message.save()

            return StreamingHttpResponse(stream_response(), content_type='text/event-stream')
        except Exception as e:
            logger.error(f"Error starting chat: {e}")
            return Response({'message': 'Error starting chat', 'error': str(e)}, status=500)

    def start_chat_with_files(self, user: CustomUser, type: str, ids: List[str] | None = None, model: str = 'gemini'):
        try:
            logger.info(f'Starting chat for user {user.id} with type {type} and ids {ids}')

            # Get all docs for the user
            file_names = self.query_files(user, type, ids)
            file_contents = self.load_files(user, type, file_names)

            chat = Chat.objects.create(user=user,referenced_files=file_names, model=model)


            # Generate prompt for the model
            prompt = f"Analyze the following {type} documents for the user with ID {user.id}: {file_contents}, and provide a summary of the findings. If there are any abnormalities, inconsistencies or variances from established norms, provide a detailed explanation and an assessment summary of the findings and a final analysis along with any follow-up steps. If any of the documents are not relevant to the user's {type}, please ignore them, and list the irrelevant documents after the summary."

            ChatMessage.objects.create(chat=chat, content=prompt, role='user')
            # invoke model
            if model == 'anthropic':
                stream =  self.anthropic_model.stream(prompt)
            elif model == 'gemini':
                stream = self.gemini_model.stream(prompt)
            else:
                raise ValueError(f'Invalid model: {model}')

            response_message = ChatMessage.objects.create(chat=chat, role='assistant', model=model)
            meta_data = {'chat_id': str(chat.id), 'message_id': str(response_message.id)}
            def stream_response():
                # Return chat id to user first
                yield f'{json.dumps(meta_data)}\n\n'

                response_stream = ''
                logger.info(f'Starting stream {meta_data}')
                for chunk in stream:
                    response_stream += chunk.content
                    yield chunk.content
                response_message.content = response_stream
                response_message.save()
            return StreamingHttpResponse(stream_response(), content_type='text/event-stream')

        except Exception as e:
            print(f'Error analyzing {type}: {e}')
            return None

    def analyze_diagnosis(self, user: CustomUser, type: str, ids: List[str] | None = None, model: str = 'anthropic'):
        try:
            print(f'Analyzing {type} for user {user.id} with ids {ids} using model {model}')
            # Get all diagnosis docs for the user
            docs = self.doc_db.objects.filter(
                user=user, type=type, id__in=ids).select_related('file') if ids else self.doc_db.objects.filter(user=user, type=type).select_related('file')

            # Generatd signed url for each doc
            file_names: List[str] = []

            for doc in docs:
                # if doc.file.file_type != 'application/pdf':
                #     continue
                file_names.append(str(doc.file.file))
            file_names.sort()

            # Check if the analysis has already been generated for the user and the files
            cached_analysis = AiAnalysisOutput.objects.filter(
                user=user, referenced_files=file_names, model=model).order_by('-created_at').first()
            if cached_analysis:
                logger.info(
                    f'Cached analysis found for user {user.id} and files {file_names} by model {model}')
                return cached_analysis.content, cached_analysis.id

            # If the analysis has not been generated, generate it
            logger.info(
                f'Generating analysis for user {user.id} and files {file_names}')
            file_contents = []
            for doc in file_names:
                if doc.endswith(('.png', '.jpg', '.jpeg')):
                    loader = GCSFileLoader(
                        project_name=settings.GS_PROJECT_NAME,
                        bucket=settings.GS_BUCKET_NAME,
                        blob=str(doc),
                        loader_func=load_image
                    )
                elif doc.endswith('.pdf'):
                    loader = GCSFileLoader(
                        project_name=settings.GS_PROJECT_NAME,
                    bucket=settings.GS_BUCKET_NAME,
                    blob=str(doc),
                    loader_func=load_pdf
                )
                else:
                    logger.warning(f'Unsupported file type: {doc}')
                    continue
                file_contents.append(loader.load())

            # Generate prompt for the model
            prompt = f"Analyze the following {type} documents for the user with ID {user.id}: {file_contents}, and provide a summary of the findings. If there are any abnormalities, inconsistencies or variances from established norms, provide a detailed explanation and an assessment summary of the findings and a final analysis along with any follow-up steps. If any of the documents are not relevant to the user's {type}, please ignore them, and list the irrelevant documents after the summary."

            # invoke model
            if model == 'anthropic':
                data = self.anthropic_model.invoke(prompt)
            elif model == 'gemini':
                data = self.gemini_model.invoke(prompt)
            else:
                raise ValueError(f'Invalid model: {model}')

            output = AiAnalysisOutput.objects.create(
                user=user, content=data.content, referenced_files=file_names, model=model)

            return data.content, output.id

        except Exception as e:
            print(f'Error analyzing {type}: {e}')
            return None

    def analyze_dashboard_variants(self, user: CustomUser, model: str = 'anthropic', use_cache: bool = True):
        """
        Analyze the dashboard variants JSON file with a fixed prompt.
        This method reads the credentials/dashboard_variants.json file and analyzes it
        using the specified AI model with real-time streaming. It creates a Chat and ChatMessage (role=assistant) and returns a StreamingHttpResponse.

        Args:
            user: The user requesting the analysis
            model: The AI model to use ('anthropic' or 'gemini')
            use_cache: Whether to use cached results if available (default: False)
        """
        try:
            logger.info(f'Starting dashboard variants analysis for user {user.id} with model {model}, use_cache={use_cache}')

            # Define file identifier for this analysis
            file_identifier = 'dashboard_variants_analysis'

            # Check for cached analysis only if use_cache is True
            if use_cache:
                cached_chat = Chat.objects.filter(
                    user=user,
                    model=model,
                    referenced_files__contains=[file_identifier]
                ).order_by('-created_at').first()

                if cached_chat:
                    logger.info(f'Found cached dashboard variants analysis for user {user.id}')
                    # Return the existing chat
                    messages = cached_chat.messages.order_by('created_at')
                    assistant_message = messages.filter(role='assistant').first()
                    if assistant_message:
                        meta_data = {'chat_id': str(cached_chat.id), 'message_id': str(assistant_message.id)}
                        def cached_stream_generator():
                            # First yield metadata
                            yield f"{json.dumps(meta_data)}\n\n"
                            # Stream the cached content in chunks
                            content = assistant_message.content
                            chunk_size = 100
                            for i in range(0, len(content), chunk_size):
                                yield content[i:i+chunk_size]
                        return ImprovedStreamingHttpResponse(cached_stream_generator())
            else:
                logger.info(f'Skipping cache lookup for dashboard variants analysis (use_cache=False)')

            file_path = os.path.join(settings.BASE_DIR, 'backend_docs', 'dashboard_variants.json')
            if not os.path.exists(file_path):
                raise FileNotFoundError(f'Dashboard variants file not found at {file_path}')

            with open(file_path, 'r', encoding='utf-8') as file:
                file_content = file.read()

            # Optimize prompt to reduce token count and improve streaming
            prompt = ("As a medical genetics expert, analyze this genetic variants data and provide a comprehensive report with:\n"
                     "1. Key findings summary (prioritize most significant findings)\n"
                     "2. Clinically significant variants (focus on pathogenic/likely pathogenic)\n"
                     "3. Risk assessment (based on variant classifications)\n"
                     "4. Recommendations (actionable next steps)\n\n"
                     "Please provide a complete analysis without truncation.\n\n"
                     f"Data: {file_content}")

            # Create Chat and ChatMessage (role=assistant) first
            chat = Chat.objects.create(user=user, model=model, referenced_files=[file_identifier])
            response_message = ChatMessage.objects.create(chat=chat, role='assistant', model=model)

            # Select the LLM model and stream the response
            llm = self.anthropic_model if model == 'anthropic' else self.gemini_model

            try:
                stream = llm.stream(prompt)
            except Exception as api_error:
                # Handle rate limit errors specifically
                error_str = str(api_error)
                if "rate_limit_error" in error_str or "429" in error_str:
                    logger.warning(f'Rate limit hit for {model}, trying alternative model')
                    # Try with Gemini if Anthropic fails, or vice versa
                    alternative_model = 'gemini' if model == 'anthropic' else 'anthropic'
                    llm = self.gemini_model if alternative_model == 'gemini' else self.anthropic_model
                    chat.model = alternative_model
                    chat.save()
                    response_message.model = alternative_model
                    response_message.save()
                    try:
                        stream = llm.stream(prompt)
                    except Exception as fallback_error:
                        logger.error(f'Both models failed: {fallback_error}')
                        return Response({
                            'error': 'Rate limit exceeded. Please try again in a few minutes.',
                            'details': 'Both AI models are currently rate limited. Please wait and retry.'
                        }, status=429)
                else:
                    raise api_error

            meta_data = {'chat_id': str(chat.id), 'message_id': str(response_message.id)}

            # Define the streaming generator with metadata
            def complete_stream_generator():
                # First yield metadata
                yield f"{json.dumps(meta_data)}\n\n"

                full_output = ""
                try:
                    chunk_count = 0
                    for chunk in stream:
                        chunk_count += 1
                        if chunk.content:  # Only yield non-empty content
                            yield chunk.content
                            full_output += chunk.content

                        # Log progress every 50 chunks
                        if chunk_count % 50 == 0:
                            logger.info(f'Dashboard variants analysis - streamed {chunk_count} chunks, total length: {len(full_output)}')

                    logger.info(f'Dashboard variants analysis completed. Total chunks: {chunk_count}, final length: {len(full_output)}')
                    # Save the complete response to the database
                    response_message.content = full_output
                    response_message.save()
                except Exception as stream_error:
                    logger.error(f'Error during dashboard variants streaming at chunk {chunk_count}: {stream_error}')
                    error_msg = f"\n\nError occurred during analysis: {str(stream_error)}"
                    yield error_msg
                    response_message.content = full_output + error_msg
                    response_message.save()
                    raise

            return ImprovedStreamingHttpResponse(complete_stream_generator())
        except Exception as e:
            # Log the error
            logger.error(f'Error analyzing dashboard variants: {e}')

            error_str = str(e)
            if "rate_limit_error" in error_str or "429" in error_str:
                return Response({
                    'error': 'Rate limit exceeded. Please try again in a few minutes.',
                    'details': str(e)
                }, status=429)
            return Response({'message': 'Error analyzing dashboard variants', 'error': str(e)}, status=500)

    def message(self, user: CustomUser, chat_id: str, new_message: str, model: str = 'anthropic', enable_search: bool = False):
        try:
            # Retrieve the existing chat and its message history
            chat = Chat.objects.get(id=chat_id)
            messages = []
            message_history = ChatMessage.objects.filter(chat=chat).order_by('created_at')
            for old_message in message_history:
                if old_message.role == 'user':
                    messages.append(HumanMessage(content=old_message.content))
                else:
                    messages.append(AIMessage(content=old_message.content))

            # Process new message with optional web search
            if enable_search:
                search_query = new_message
                try:
                    search_results = self.search_tool.invoke(search_query)
                    if not search_results:
                        search_results = "No information found from web search."
                except Exception as e:
                    logger.error(f"Search failed for query '{search_query}': {e}")
                    search_results = "Failed to retrieve information from web search."
                current_year = datetime.datetime.now().year
                if str(current_year + 1) in new_message or "2025" in new_message:
                    search_results += (
                        f"\n\nNote: As of {datetime.datetime.now().strftime('%Y-%m-%d')}, "
                        "the query includes a future date, so there may be no current information available."
                    )
                prompt = (
                    "Please answer the following question using the provided web search results as the primary source of information. "
                    "If the search results do not provide a clear answer, you may use your general knowledge, but prioritize the search results.\n\n"
                    f"Question: {new_message}\n\nWeb search results: {search_results}"
                )
            else:
                prompt = new_message

            # Save the new prompt and add to messages
            ChatMessage.objects.create(chat=chat, content=prompt, role='user')
            messages.append(HumanMessage(content=prompt))

            # Select the LLM model and stream the response
            llm = self.anthropic_model if model == 'anthropic' else self.gemini_model
            stream = llm.stream(messages)

            # Create response message entry
            response_message = ChatMessage.objects.create(chat=chat, role='assistant', model=model)
            meta_data = {'chat_id': str(chat.id), 'message_id': str(response_message.id)}

            # Define the streaming generator
            def stream_response():
                yield f"{json.dumps(meta_data)}\n\n"
                full_output = ""
                # Stream chunks as they are generated
                for chunk in stream:
                    yield chunk.content
                    full_output += chunk.content
                # Append the web search note after streaming the main response
                if enable_search:
                    if "Failed to retrieve information" in search_results:
                        note = "\n\nWeb search failed, response based on general knowledge."
                    else:
                        note = "\n\nResponse includes information from web search."
                else:
                    note = "\n\nNo web search was performed for this response."
                yield note
                full_output += note
                # Save the complete response to the database
                response_message.content = full_output
                response_message.save()

            return StreamingHttpResponse(stream_response(), content_type='text/event-stream')
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return Response({'message': 'Error sending message', 'error': str(e)}, status=500)

    def _upload_images_to_storage(self, images, user):
        """Helper method to upload images to storage and return file paths."""
        uploaded_files = []
        for image in images:
            try:
                uploaded_file = self.file_upload_view.upload_file(image, user, directory='chat_images')
                uploaded_files.append(uploaded_file.file.name)
            except Exception as e:
                logger.error(f"Error uploading image {image.name}: {e}")
                raise Exception(f"Failed to upload image {image.name}: {str(e)}")
        return uploaded_files

    def _prepare_multimodal_content(self, text_message, image_paths):
        """Prepare multimodal content for AI models with both text and images."""
        content = []

        # Add text content if provided
        if text_message.strip():
            content.append({"type": "text", "text": text_message})

        # Add image content
        for image_path in image_paths:
            try:
                # Get the full URL for the image
                image_url = default_storage.url(image_path)
                content.append({
                    "type": "image_url",
                    "image_url": {"url": image_url}
                })
            except Exception as e:
                logger.error(f"Error processing image {image_path}: {e}")
                # Fallback to text description
                content.append({
                    "type": "text",
                    "text": f"[Image attachment: {image_path}]"
                })

        return content

    def start_chat_with_image(self, user: CustomUser, message: str, images, model: str = 'gemini', enable_search: bool = False):
        """Start a new chat session with image upload capability."""
        try:
            # Upload images to storage
            image_paths = []
            if images:
                image_paths = self._upload_images_to_storage(images, user)

            # Create chat and determine message type
            message_type = 'text'
            if images and message.strip():
                message_type = 'mixed'
            elif images:
                message_type = 'image'

            chat = Chat.objects.create(user=user, model=model)

            # Prepare content for AI model
            if image_paths:
                # For multimodal content, we need to structure the message differently
                multimodal_content = self._prepare_multimodal_content(message, image_paths)
                # Store the original message and image references
                user_message_content = message if message.strip() else "[Image message]"
                user_message = ChatMessage.objects.create(
                    chat=chat,
                    content=user_message_content,
                    role='user',
                    message_type=message_type,
                    attachments=image_paths
                )

                # Create HumanMessage with multimodal content
                llm_message = HumanMessage(content=multimodal_content)
            else:
                # Text-only message
                user_message = ChatMessage.objects.create(
                    chat=chat,
                    content=message,
                    role='user',
                    message_type='text'
                )
                llm_message = HumanMessage(content=message)

            # Handle web search if enabled (only for text content)
            if enable_search and message.strip():
                search_query = message
                try:
                    search_results = self.search_tool.invoke(search_query)
                    if not search_results:
                        search_results = "No information found from web search."
                except Exception as e:
                    logger.error(f"Search failed for query '{search_query}': {e}")
                    search_results = "Failed to retrieve information from web search."

                current_year = datetime.datetime.now().year
                if str(current_year + 1) in message or "2025" in message:
                    search_results += f"\n\nNote: As of {datetime.datetime.now().strftime('%Y-%m-%d')}, the query includes a future date (e.g., 2025), so there may be no current information available."

                # Modify the content to include search results
                if image_paths:
                    # Add search results as additional text content
                    search_content = (
                        "Please analyze the provided images and answer the question using both the visual information and the web search results below. "
                        "Prioritize the visual information from the images, but supplement with search results when relevant.\n\n"
                        f"Question: {message}\n\nWeb search results: {search_results}"
                    )
                    multimodal_content.append({"type": "text", "text": search_content})
                    llm_message = HumanMessage(content=multimodal_content)
                else:
                    search_enhanced_message = (
                        "Please answer the following question using the provided web search results as the primary source of information. "
                        "If the search results do not provide a clear answer, you may use your general knowledge, but prioritize the search results.\n\n"
                        f"Question: {message}\n\nWeb search results: {search_results}"
                    )
                    llm_message = HumanMessage(content=search_enhanced_message)

            # Select LLM and stream response
            llm = self.anthropic_model if model == 'anthropic' else self.gemini_model
            messages = [llm_message]
            stream = llm.stream(messages)

            # Create response message entry
            response_message = ChatMessage.objects.create(chat=chat, role='assistant', model=model)
            meta_data = {'chat_id': str(chat.id), 'message_id': str(response_message.id)}

            # Define the streaming generator
            def stream_response():
                yield f"{json.dumps(meta_data)}\n\n"
                full_output = ""
                # Stream chunks as they are generated
                for chunk in stream:
                    yield chunk.content
                    full_output += chunk.content

                # Append notes about search and images
                notes = []
                if enable_search and message.strip():
                    if "Failed to retrieve information" in search_results:
                        notes.append("Web search failed, response based on general knowledge.")
                    else:
                        notes.append("Response includes information from web search.")
                else:
                    notes.append("No web search was performed for this response.")

                if image_paths:
                    notes.append(f"Response includes analysis of {len(image_paths)} uploaded image(s).")

                note = "\n\n" + " ".join(notes)
                yield note
                full_output += note

                # Save the complete response to the database
                response_message.content = full_output
                response_message.save()

            return StreamingHttpResponse(stream_response(), content_type='text/event-stream')
        except Exception as e:
            logger.error(f"Error starting chat with image: {e}")
            return Response({'message': 'Error starting chat with image', 'error': str(e)}, status=500)

    def message_with_image(self, user: CustomUser, chat_id: str, new_message: str, images, model: str = 'anthropic', enable_search: bool = False):
        """Send a message with image attachments to an existing chat."""
        try:
            # Retrieve the existing chat and its message history
            chat = Chat.objects.get(id=chat_id)
            messages = []
            message_history = ChatMessage.objects.filter(chat=chat).order_by('created_at')

            # Build conversation history
            for old_message in message_history:
                if old_message.role == 'user':
                    if old_message.attachments:
                        # Reconstruct multimodal content for historical messages
                        multimodal_content = self._prepare_multimodal_content(old_message.content, old_message.attachments)
                        messages.append(HumanMessage(content=multimodal_content))
                    else:
                        messages.append(HumanMessage(content=old_message.content))
                else:
                    messages.append(AIMessage(content=old_message.content))

            # Upload new images to storage
            image_paths = []
            if images:
                image_paths = self._upload_images_to_storage(images, user)

            # Determine message type
            message_type = 'text'
            if images and new_message.strip():
                message_type = 'mixed'
            elif images:
                message_type = 'image'

            # Process new message with optional web search
            if enable_search and new_message.strip():
                search_query = new_message
                try:
                    search_results = self.search_tool.invoke(search_query)
                    if not search_results:
                        search_results = "No information found from web search."
                except Exception as e:
                    logger.error(f"Search failed for query '{search_query}': {e}")
                    search_results = "Failed to retrieve information from web search."

                current_year = datetime.datetime.now().year
                if str(current_year + 1) in new_message or "2025" in new_message:
                    search_results += (
                        f"\n\nNote: As of {datetime.datetime.now().strftime('%Y-%m-%d')}, "
                        "the query includes a future date, so there may be no current information available."
                    )

            # Prepare content for AI model
            if image_paths:
                # For multimodal content
                multimodal_content = self._prepare_multimodal_content(new_message, image_paths)

                # Add search results if enabled
                if enable_search and new_message.strip():
                    search_content = (
                        "Please analyze the provided images and answer the question using both the visual information and the web search results below. "
                        "Prioritize the visual information from the images, but supplement with search results when relevant.\n\n"
                        f"Question: {new_message}\n\nWeb search results: {search_results}"
                    )
                    multimodal_content.append({"type": "text", "text": search_content})

                # Store the message in database
                user_message_content = new_message if new_message.strip() else "[Image message]"
                ChatMessage.objects.create(
                    chat=chat,
                    content=user_message_content,
                    role='user',
                    message_type=message_type,
                    attachments=image_paths
                )

                # Add to conversation
                messages.append(HumanMessage(content=multimodal_content))
            else:
                # Text-only message
                if enable_search and new_message.strip():
                    prompt = (
                        "Please answer the following question using the provided web search results as the primary source of information. "
                        "If the search results do not provide a clear answer, you may use your general knowledge, but prioritize the search results.\n\n"
                        f"Question: {new_message}\n\nWeb search results: {search_results}"
                    )
                else:
                    prompt = new_message

                # Save the message and add to conversation
                ChatMessage.objects.create(chat=chat, content=prompt, role='user', message_type='text')
                messages.append(HumanMessage(content=prompt))

            # Select the LLM model and stream the response
            llm = self.anthropic_model if model == 'anthropic' else self.gemini_model
            stream = llm.stream(messages)

            # Create response message entry
            response_message = ChatMessage.objects.create(chat=chat, role='assistant', model=model)
            meta_data = {'chat_id': str(chat.id), 'message_id': str(response_message.id)}

            # Define the streaming generator
            def stream_response():
                yield f"{json.dumps(meta_data)}\n\n"
                full_output = ""
                # Stream chunks as they are generated
                for chunk in stream:
                    yield chunk.content
                    full_output += chunk.content

                # Append notes about search and images
                notes = []
                if enable_search and new_message.strip():
                    if "Failed to retrieve information" in search_results:
                        notes.append("Web search failed, response based on general knowledge.")
                    else:
                        notes.append("Response includes information from web search.")
                else:
                    notes.append("No web search was performed for this response.")

                if image_paths:
                    notes.append(f"Response includes analysis of {len(image_paths)} uploaded image(s).")

                note = "\n\n" + " ".join(notes)
                yield note
                full_output += note

                # Save the complete response to the database
                response_message.content = full_output
                response_message.save()

            return StreamingHttpResponse(stream_response(), content_type='text/event-stream')
        except Exception as e:
            logger.error(f"Error sending message with image: {e}")
            return Response({'message': 'Error sending message with image', 'error': str(e)}, status=500)

    # ========== PHASE 2: ENHANCED UNIFIED METHODS ==========

    @transaction.atomic
    def start_chat_unified_enhanced(self, user: CustomUser, message: str = '', file_ids: list = None,
                                  direct_files: list = None, model: str = 'gemini',
                                  enable_search: bool = False, processing_method: str = 'auto'):
        """
        PHASE 2: Enhanced unified chat start method with intelligent processing
        Replaces basic routing with full UnifiedFileProcessor integration
        """
        try:
            logger.info(f'[UNIFIED-ENHANCED] Starting chat for user {user.id} with '
                       f'message="{message[:50]}...", file_ids={len(file_ids or [])}, '
                       f'direct_files={len(direct_files or [])}, model={model}, '
                       f'processing_method={processing_method}')

            # Create chat
            chat = Chat.objects.create(user=user, model=model)

            # Process files using UnifiedFileProcessor
            processing_result = None
            if file_ids or direct_files:
                processor = UnifiedFileProcessor()
                files_input = file_ids if file_ids else direct_files
                processing_result = processor.process_files(
                    files_input, user, processing_method, chat=chat
                )
                logger.info(f'[UNIFIED-ENHANCED] Processed {len(processing_result.chat_files)} files')

            # Prepare enhanced content for AI model
            ai_content = self._prepare_enhanced_unified_content(
                message, processing_result, enable_search
            )

            # Create user message with enhanced metadata
            message_type = self._determine_enhanced_message_type(message, processing_result)
            user_message = ChatMessage.objects.create(
                chat=chat,
                content=message or "[File-based message]",
                role='user',
                message_type=message_type
            )

            # Link files to message if processed
            if processing_result:
                with transaction.atomic():
                    for chat_file in processing_result.chat_files:
                        chat_file.message = user_message
                        chat_file.save()
                        logger.info(f"[UNIFIED-ENHANCED] Linked ChatFile {chat_file.id} to message {user_message.id}")

            # Generate AI response with enhanced context
            llm = self.anthropic_model if model == 'anthropic' else self.gemini_model
            stream = llm.stream([HumanMessage(content=ai_content)])

            # Create response message
            response_message = ChatMessage.objects.create(
                chat=chat, role='assistant', model=model
            )

            # Stream response with enhanced metadata
            return self._create_enhanced_streaming_response(
                stream, chat, response_message, processing_result, enable_search
            )

        except Exception as e:
            logger.error(f"Error in enhanced unified chat start: {e}", exc_info=True)
            return Response({'error': str(e)}, status=500)

    def _prepare_enhanced_unified_content(self, message: str, processing_result: ProcessingResult,
                                        enable_search: bool) -> list:
        """
        PHASE 2: Enhanced content preparation with intelligent multimodal handling
        """
        content = []

        # Add text message if provided
        if message and message.strip():
            content.append({"type": "text", "text": message})

        # Add processed file content with intelligent handling
        if processing_result and processing_result.has_files:

            # Add multimodal content (images, visual elements)
            if processing_result.has_multimodal_content:
                multimodal_content = processing_result.get_multimodal_content()
                content.extend(multimodal_content)
                logger.info(f'[UNIFIED-ENHANCED] Added {len(multimodal_content)} multimodal items')

            # Add extracted text content with context
            if processing_result.has_text_content:
                text_content = processing_result.get_text_content()
                content.append({
                    "type": "text",
                    "text": f"Document content analysis:\n{text_content}"
                })
                logger.info(f'[UNIFIED-ENHANCED] Added text content from {len(processing_result.chat_files)} files')

            # Add file metadata for context
            file_context = self._generate_file_context(processing_result)
            if file_context:
                content.append({
                    "type": "text",
                    "text": f"File context:\n{file_context}"
                })

        # Add web search if enabled
        if enable_search and message:
            search_results = self._perform_enhanced_web_search(message)
            content.append({
                "type": "text",
                "text": f"Web search results:\n{search_results}"
            })

        return content

    def _generate_file_context(self, processing_result: ProcessingResult) -> str:
        """Generate contextual information about processed files"""
        context_parts = []

        for chat_file in processing_result.chat_files:
            file_info = [
                f"File: {chat_file.filename}",
                f"Type: {chat_file.file_type}",
                f"Source: {chat_file.source_type}",
                f"Processing: {chat_file.processing_method}",
                f"Status: {chat_file.processing_status}"
            ]

            if chat_file.processing_error:
                file_info.append(f"Error: {chat_file.processing_error}")

            context_parts.append(" | ".join(file_info))

        return "\n".join(context_parts)

    def _determine_enhanced_message_type(self, message: str, processing_result: ProcessingResult) -> str:
        """Enhanced message type determination with processing context"""
        has_text = bool(message and message.strip())
        has_files = bool(processing_result and processing_result.has_files)

        if has_text and has_files:
            # Check if files have both text and visual content
            if (processing_result.has_text_content and processing_result.has_multimodal_content):
                return 'mixed'  # Text + files with both text and visual content
            else:
                return 'mixed'  # Text + files
        elif has_files:
            # Determine based on file processing results
            if processing_result.has_multimodal_content:
                return 'image'  # Visual content
            else:
                return 'text'   # Text-only files
        else:
            return 'text'

    def _perform_enhanced_web_search(self, query: str) -> str:
        """Enhanced web search with medical context optimization"""
        try:
            # Add medical context to search if detected
            medical_keywords = ['diagnosis', 'symptom', 'treatment', 'medical', 'disease', 'condition']
            is_medical_query = any(keyword in query.lower() for keyword in medical_keywords)

            if is_medical_query:
                enhanced_query = f"medical {query} evidence-based"
                logger.info(f'[UNIFIED-ENHANCED] Enhanced medical search: {enhanced_query}')
            else:
                enhanced_query = query

            search_results = self.search_tool.invoke(enhanced_query)

            if not search_results:
                return "No information found from web search."

            # Add medical disclaimer if applicable
            if is_medical_query:
                search_results += "\n\nNote: This information is for educational purposes only and should not replace professional medical advice."

            return search_results

        except Exception as e:
            logger.error(f"Enhanced search failed for query '{query}': {e}")
            return "Failed to retrieve information from web search."

    def _create_enhanced_streaming_response(self, stream, chat, response_message,
                                          processing_result: ProcessingResult, enable_search: bool):
        """
        PHASE 2: Enhanced streaming response with detailed metadata
        """
        # Enhanced metadata with processing information
        meta_data = {
            'chat_id': str(chat.id),
            'message_id': str(response_message.id),
            'processing_info': {
                'files_processed': len(processing_result.chat_files) if processing_result else 0,
                'has_text_content': processing_result.has_text_content if processing_result else False,
                'has_multimodal_content': processing_result.has_multimodal_content if processing_result else False,
                'search_enabled': enable_search
            }
        }

        def enhanced_stream_response():
            # Send enhanced metadata first
            yield f"{json.dumps(meta_data)}\n\n"

            full_output = ""
            file_processing_notes = []

            # Stream AI response chunks
            for chunk in stream:
                yield chunk.content
                full_output += chunk.content

            # Add enhanced processing notes
            if processing_result and processing_result.has_files:
                file_processing_notes.append(
                    f"Processed {len(processing_result.chat_files)} file(s) using unified architecture."
                )

                # Add processing method details
                methods_used = set(cf.processing_method for cf in processing_result.chat_files)
                if len(methods_used) > 1:
                    file_processing_notes.append(
                        f"Processing methods used: {', '.join(methods_used)}."
                    )

                # Add content type information
                if processing_result.has_multimodal_content:
                    file_processing_notes.append("Visual content analysis included.")
                if processing_result.has_text_content:
                    file_processing_notes.append("Text extraction analysis included.")

                # Add any processing errors
                failed_files = [cf for cf in processing_result.chat_files if cf.processing_status == 'failed']
                if failed_files:
                    file_processing_notes.append(
                        f"Note: {len(failed_files)} file(s) had processing issues."
                    )

            # Add search information
            if enable_search:
                file_processing_notes.append("Web search results included in analysis.")

            # Combine all notes
            if file_processing_notes:
                notes_text = "\n\n" + " ".join(file_processing_notes)
                yield notes_text
                full_output += notes_text

            # Save complete response
            response_message.content = full_output
            response_message.save()

            logger.info(f'[UNIFIED-ENHANCED] Completed response for chat {chat.id}')

        return StreamingHttpResponse(enhanced_stream_response(), content_type='text/event-stream')

    @transaction.atomic
    def message_unified_enhanced(self, user: CustomUser, chat_id: str, message: str = '',
                               direct_files: list = None, model: str = 'gemini',
                               enable_search: bool = False, processing_method: str = 'auto'):
        """
        PHASE 2: Enhanced unified message method for continuing chat conversations
        Replaces message_with_image with full UnifiedFileProcessor integration
        """
        try:
            logger.info(f'[UNIFIED-MESSAGE] Continuing chat {chat_id} for user {user.id} with '
                       f'message="{message[:50]}...", direct_files={len(direct_files or [])}, '
                       f'model={model}, processing_method={processing_method}')

            # Get existing chat
            chat = Chat.objects.get(id=chat_id, user=user)

            # Process files using UnifiedFileProcessor if provided
            processing_result = None
            if direct_files:
                processor = UnifiedFileProcessor()
                processing_result = processor.process_files(
                    direct_files, user, processing_method, chat=chat
                )
                logger.info(f'[UNIFIED-MESSAGE] Processed {len(processing_result.chat_files)} files')

            # Prepare enhanced content for AI model
            ai_content = self._prepare_enhanced_unified_content(
                message, processing_result, enable_search
            )

            # Create user message with enhanced metadata
            message_type = self._determine_enhanced_message_type(message, processing_result)
            user_message = ChatMessage.objects.create(
                chat=chat,
                content=message or "[File-based message]",
                role='user',
                message_type=message_type
            )

            # Link files to message if processed
            if processing_result:
                with transaction.atomic():
                    for chat_file in processing_result.chat_files:
                        chat_file.message = user_message
                        chat_file.save()
                        logger.info(f"[UNIFIED-MESSAGE] Linked ChatFile {chat_file.id} to message {user_message.id}")

            # Get chat history for context
            messages = []
            chat_messages = ChatMessage.objects.filter(chat=chat).order_by('created_at')

            for chat_message in chat_messages:
                if chat_message.role == 'user':
                    if chat_message.message_type == 'image' or chat_message.message_type == 'mixed':
                        # Handle messages with images
                        content = []
                        if chat_message.content and chat_message.content.strip():
                            content.append({"type": "text", "text": chat_message.content})

                        # Add file content from ChatFile relationships
                        for chat_file in chat_message.files.all():
                            if chat_file.multimodal_content:
                                content.append(chat_file.multimodal_content)
                            elif chat_file.extracted_text:
                                content.append({"type": "text", "text": f"Document content: {chat_file.extracted_text}"})

                        messages.append(HumanMessage(content=content))
                    else:
                        messages.append(HumanMessage(content=chat_message.content))
                else:
                    messages.append(AIMessage(content=chat_message.content))

            # Add current message
            messages.append(HumanMessage(content=ai_content))

            # Generate AI response with enhanced context
            llm = self.anthropic_model if model == 'anthropic' else self.gemini_model
            stream = llm.stream(messages)

            # Create response message
            response_message = ChatMessage.objects.create(
                chat=chat, role='assistant', model=model
            )

            # Stream response with enhanced metadata
            return self._create_enhanced_streaming_response(
                stream, chat, response_message, processing_result, enable_search
            )

        except Chat.DoesNotExist:
            logger.error(f"Chat {chat_id} not found for user {user.id}")
            return Response({'error': 'Chat not found'}, status=404)
        except Exception as e:
            logger.error(f"Error in enhanced unified message: {e}", exc_info=True)
            return Response({'error': str(e)}, status=500)

    def save_to_pdf(self, user: CustomUser, output_id: str):
        try:
            try:
                output = AiAnalysisOutput.objects.get(id=output_id)
            except AiAnalysisOutput.DoesNotExist:
                # Not found in AiAnalysisOutput, try finding in chat message
                output = ChatMessage.objects.get(id=output_id)
            content = output.content.replace('**', ' ')
            file_name = f'{output.model}_generated_report_{output.created_at.strftime("%Y-%m-%d_%H-%M-%S")}.pdf'

            date = datetime.datetime.now().strftime("%Y-%m-%d")
            # Create a PDF file with PyMuPDF (fitz)
            doc = pymupdf.Document()
            # Insert data into the PDF by splitting the content into pages. Each page has at most 500 words and it should be split at the character '.'
            remain_chars = len(content)
            is_first_page = True
            while remain_chars > 0:
                dot_index = content[:min(remain_chars, 2000)].rfind('.')
                if dot_index == -1:
                    dot_index = min(remain_chars, 2000)
                page_content = content[:dot_index+1]
                page = doc.new_page()
                rect = pymupdf.Rect(50, 72, 500, 1000)
                # Adding date as header
                if is_first_page:
                    # Write the header in the center of the page
                    header_rect = page.rect + (50, 50, -50, -700)
                    html_content = f'<div style="text-align: center;">\
                        <p style="font-size: 20px; font-weight: bold; color: #5203fc">AI Response</p>\
                        <p style="font-size: 11px;">{date}</p>\
                        </div>'
                    page.insert_htmlbox(header_rect, html_content)
                    is_first_page = False

                    rect += (0, 70, 0, 0)
                else:
                    page.insert_textbox(pymupdf.Rect(50, 50, 500, 100), f'{date}', fontname = "helv", fontsize = 11, rotate = 0)
                page.insert_textbox(rect, page_content, fontname = "helv", fontsize = 11, rotate = 0)
                content = content[dot_index+1:]
                remain_chars -= dot_index+1

            content = doc.tobytes()
            # Upload file using django-storages
            file_path = f'uploaded_files/generated_reports/{file_name}'
            saved_path = default_storage.save(file_path, io.BytesIO(content))
            file_url = default_storage.url(saved_path)



            # Create and return database record
            uploaded_file = UploadedFile.objects.create(
                file=file_path,
                filename=file_name,
                user=user,
                file_url=file_url,
                file_type='application/pdf'
                )



            # Create supporting docs record with uploaded file
            doc = SupportingDocs.objects.create(
                user=user,
                file=uploaded_file,
                type='diagnosis',
                name=file_name
            )

            return Response({'message': 'Report saved successfully', 'doc_id': doc.id}, status=200)
        except Exception as e:
            print(f'Error saving report: {e}')
            return Response({'message': 'Error saving report', 'error': str(e)}, status=500)

    def delete_chats(self, user: CustomUser, chat_ids: List[str]):
        try:
            Chat.objects.filter(id__in=chat_ids, user=user).delete()
            return Response({'message': 'Chats deleted successfully'}, status=200)
        except Exception as e:
            logger.error(f"Error deleting chats: {e}")
            return Response({'message': 'Error deleting chats', 'error': str(e)}, status=500)

    # ========== BACKWARD COMPATIBILITY LAYER ==========
    # These methods ensure existing APIs work exactly as before

    def start_chat_with_files_legacy(self, user: CustomUser, type: str, ids: List[str] | None = None, model: str = 'gemini'):
        """
        LEGACY METHOD: Maintains exact same behavior as original start_chat_with_files
        This ensures 100% backward compatibility for existing medical applications
        """
        try:
            logger.info(f'[LEGACY] Starting chat for user {user.id} with type {type} and ids {ids}')

            # Get all docs for the user - EXACT SAME LOGIC AS BEFORE
            file_names = self.query_files(user, type, ids)
            file_contents = self.load_files(user, type, file_names)

            # Create chat with referenced_files - EXACT SAME AS BEFORE
            chat = Chat.objects.create(user=user, referenced_files=file_names, model=model)

            # Generate prompt for the model - EXACT SAME AS BEFORE
            prompt = f"Analyze the following {type} documents for the user with ID {user.id}: {file_contents}, and provide a summary of the findings. If there are any abnormalities, inconsistencies or variances from established norms, provide a detailed explanation and an assessment summary of the findings and a final analysis along with any follow-up steps. If any of the documents are not relevant to the user's {type}, please ignore them, and list the irrelevant documents after the summary."

            ChatMessage.objects.create(chat=chat, content=prompt, role='user')

            # invoke model - EXACT SAME AS BEFORE
            if model == 'anthropic':
                stream = self.anthropic_model.stream(prompt)
            elif model == 'gemini':
                stream = self.gemini_model.stream(prompt)
            else:
                raise ValueError(f'Invalid model: {model}')

            response_message = ChatMessage.objects.create(chat=chat, role='assistant', model=model)
            meta_data = {'chat_id': str(chat.id), 'message_id': str(response_message.id)}

            def stream_response():
                # Return chat id to user first - EXACT SAME AS BEFORE
                yield f'{json.dumps(meta_data)}\n\n'

                response_stream = ''
                logger.info(f'Starting stream {meta_data}')
                for chunk in stream:
                    response_stream += chunk.content
                    yield chunk.content
                response_message.content = response_stream
                response_message.save()

            return StreamingHttpResponse(stream_response(), content_type='text/event-stream')

        except Exception as e:
            logger.error(f'Error analyzing {type}: {e}')
            return Response({'message': 'Error starting chat with files', 'error': str(e)}, status=500)

    def start_chat_with_image_legacy(self, user: CustomUser, message: str, images, model: str = 'gemini', enable_search: bool = False):
        """
        LEGACY METHOD: Maintains exact same behavior as original start_chat_with_image
        This ensures 100% backward compatibility for existing medical applications
        """
        try:
            # Upload images to storage - EXACT SAME AS BEFORE
            image_paths = []
            if images:
                image_paths = self._upload_images_to_storage(images, user)

            # Create chat and determine message type - EXACT SAME AS BEFORE
            message_type = 'text'
            if images and message.strip():
                message_type = 'mixed'
            elif images:
                message_type = 'image'

            chat = Chat.objects.create(user=user, model=model)

            # Prepare content for AI model - EXACT SAME AS BEFORE
            if image_paths:
                multimodal_content = self._prepare_multimodal_content(message, image_paths)
                user_message_content = message if message.strip() else "[Image message]"
                ChatMessage.objects.create(
                    chat=chat,
                    content=user_message_content,
                    role='user',
                    message_type=message_type,
                    attachments=image_paths
                )
                llm_message = HumanMessage(content=multimodal_content)
            else:
                ChatMessage.objects.create(
                    chat=chat,
                    content=message,
                    role='user',
                    message_type='text'
                )
                llm_message = HumanMessage(content=message)

            # Handle web search if enabled - EXACT SAME AS BEFORE
            if enable_search and message.strip():
                search_query = message
                try:
                    search_results = self.search_tool.invoke(search_query)
                    if not search_results:
                        search_results = "No information found from web search."
                except Exception as e:
                    logger.error(f"Search failed for query '{search_query}': {e}")
                    search_results = "Failed to retrieve information from web search."

                current_year = datetime.datetime.now().year
                if str(current_year + 1) in message or "2025" in message:
                    search_results += f"\n\nNote: As of {datetime.datetime.now().strftime('%Y-%m-%d')}, the query includes a future date (e.g., 2025), so there may be no current information available."

                if image_paths:
                    search_content = (
                        "Please analyze the provided images and answer the question using both the visual information and the web search results below. "
                        "Prioritize the visual information from the images, but supplement with search results when relevant.\n\n"
                        f"Question: {message}\n\nWeb search results: {search_results}"
                    )
                    multimodal_content.append({"type": "text", "text": search_content})
                    llm_message = HumanMessage(content=multimodal_content)
                else:
                    search_enhanced_message = (
                        "Please answer the following question using the provided web search results as the primary source of information. "
                        "If the search results do not provide a clear answer, you may use your general knowledge, but prioritize the search results.\n\n"
                        f"Question: {message}\n\nWeb search results: {search_results}"
                    )
                    llm_message = HumanMessage(content=search_enhanced_message)

            # Select LLM and stream response - EXACT SAME AS BEFORE
            llm = self.anthropic_model if model == 'anthropic' else self.gemini_model
            messages = [llm_message]
            stream = llm.stream(messages)

            response_message = ChatMessage.objects.create(chat=chat, role='assistant', model=model)
            meta_data = {'chat_id': str(chat.id), 'message_id': str(response_message.id)}

            def stream_response():
                yield f"{json.dumps(meta_data)}\n\n"
                full_output = ""
                for chunk in stream:
                    yield chunk.content
                    full_output += chunk.content

                notes = []
                if enable_search and message.strip():
                    if "Failed to retrieve information" in search_results:
                        notes.append("Web search failed, response based on general knowledge.")
                    else:
                        notes.append("Response includes information from web search.")
                else:
                    notes.append("No web search was performed for this response.")

                if image_paths:
                    notes.append(f"Response includes analysis of {len(image_paths)} uploaded image(s).")

                note = "\n\n" + " ".join(notes)
                yield note
                full_output += note

                response_message.content = full_output
                response_message.save()

            return StreamingHttpResponse(stream_response(), content_type='text/event-stream')
        except Exception as e:
            logger.error(f"Error starting chat with image: {e}")
            return Response({'message': 'Error starting chat with image', 'error': str(e)}, status=500)

    def chat_history(self, user: CustomUser, filters: dict = None):
        """
        Retrieves all chat records for a given user with optional filtering by creation date.
        Returns a list of chats where each chat includes:
        - id
        - model used
        - referenced_files
        - created_at timestamp
        - summary: the content of the first (earliest) message
        """
        try:
            filters = filters or {}
            chats = Chat.objects.filter(user=user)

            # Filter chats by creation date if provided.
            if filters.get('start_date'):
                chats = chats.filter(created_at__gte=filters['start_date'])
            if filters.get('end_date'):
                chats = chats.filter(created_at__lte=filters['end_date'])

            # Order chats with the most recent first.
            chats = chats.order_by('-created_at')

            chat_list = []
            for chat in chats:
                # Fetch the earliest message in the chat to create a summary.
                first_message = chat.messages.order_by('created_at').first()
                summary = first_message.content if first_message else ''

                chat_list.append({
                    'id': str(chat.id),
                    'model': chat.model,
                    'referenced_files': chat.referenced_files,
                    'summary': summary,
                    'created_at': chat.created_at.isoformat()
                })

            return Response(chat_list, status=200)
        except Exception as e:
            logger.error(f"Error retrieving chat history for user {user.id}: {e}")
            return Response({'message': 'Error retrieving chat history', 'error': str(e)}, status=500)

