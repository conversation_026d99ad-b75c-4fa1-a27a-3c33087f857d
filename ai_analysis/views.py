import json
import logging
from django.shortcuts import render
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ai_analysis.ai_models import AiModel
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from rest_framework import viewsets
from ai_analysis.models import Chat
from django.http import StreamingHttpResponse

from ai_analysis.serializer import MessageInputSerializer, StartChatInputSerializer, MessageWithImageInputSerializer, StartChatWithImageInputSerializer, UnifiedChatInputSerializer, MessageUnifiedInputSerializer

logger = logging.getLogger(__name__)


model = AiModel()


class AnalyzeDiagnosisView(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def create(self, request):
        try:
            ids = request.data.get('ids', None)
            model_type = request.data.get('model', 'anthropic')
            logger.info(f"Analyzing diagnosis for user {request.user.id} on docs {ids} using model {model_type}")
            content, output_id = model.analyze_diagnosis(request.user, 'diagnosis', ids, model_type)

            return Response({'content': content, 'output_id': output_id})
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['post'])
    def start_chat_with_files(self, request):
        """
        LEGACY ENDPOINT: Hoạt động y như cũ, không có gì thay đổi
        Đảm bảo 100% backward compatibility cho các ứng dụng y tế hiện tại
        """
        try:
            ids = request.data.get('ids', None)
            model_type = request.data.get('model', 'gemini')
            data_type = request.data.get('type', 'diagnosis')
            logger.info(f"[LEGACY] Starting chat for user {request.user.id} on docs {ids} using model {model_type} and type {data_type}")

            # Sử dụng legacy method để đảm bảo hoạt động y như cũ
            return model.start_chat_with_files_legacy(request.user, data_type, ids, model_type)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['post'])
    def start_chat(self, request):
        """
        Init new chat
        """
        serializer = StartChatInputSerializer(data=request.data)
        if serializer.is_valid():
            message = serializer.validated_data['message']
            model_type = serializer.validated_data['model']
            enable_search = serializer.validated_data['enable_search']

            logger.info(
                f"Starting chat for user {request.user.id} with message '{message[:50]}...' "
                f"using model {model_type}, enable_search={enable_search}"
            )

            try:
                response = model.start_chat(request.user, message, model_type, enable_search)
                if isinstance(response, Response):  # Xử lý lỗi từ AiModel
                    return response
                return response  # Trả về StreamingHttpResponse
            except Exception as e:
                logger.error(f"Error in start_chat: {str(e)}", exc_info=True)
                return Response({'error': f"Failed to start chat: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            logger.warning(f"Invalid start_chat request: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def find_chat(self, request):
        try:
            ids = request.data.get('ids', None)
            model_type = request.data.get('model', 'gemini')
            return model.find_chat(request.user, 'diagnosis', ids, model_type)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['post'])
    def message(self, request):
        """
        Gửi một tin nhắn mới trong cuộc trò chuyện hiện có.
        """
        serializer = MessageInputSerializer(data=request.data)
        if serializer.is_valid():
            chat_id = serializer.validated_data['chat_id']
            message = serializer.validated_data['message']
            model_type = serializer.validated_data['model']
            enable_search = serializer.validated_data['enable_search']

            logger.info(
                f"Sending message for user {request.user.id} in chat {chat_id} with message '{message[:50]}...' "
                f"using model {model_type}, enable_search={enable_search}"
            )

            try:
                response = model.message(request.user, chat_id, message, model_type, enable_search)
                if isinstance(response, Response):  # Xử lý lỗi từ AiModel
                    return response
                return response  # Trả về StreamingHttpResponse
            except Exception as e:
                logger.error(f"Error in message: {str(e)}", exc_info=True)
                return Response({'error': f"Failed to send message: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            logger.warning(f"Invalid message request: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def start_chat_with_image(self, request):
        """
        Start a new chat session with image upload capability.
        Supports both text message and image attachments.
        """
        serializer = StartChatWithImageInputSerializer(data=request.data)
        if serializer.is_valid():
            message = serializer.validated_data.get('message', '').strip()
            model_type = serializer.validated_data['model']
            enable_search = serializer.validated_data['enable_search']

            # Handle image uploads
            images = request.FILES.getlist('images')

            # Validate that either message or images are provided
            if not message and not images:
                return Response(
                    {'error': 'Either message text or images must be provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate image file types
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png']
            for image in images:
                if image.content_type not in allowed_types:
                    return Response(
                        {'error': f'Unsupported image type: {image.content_type}. Allowed types: {allowed_types}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            logger.info(
                f"Starting chat with images for user {request.user.id} with message '{message[:50]}...' "
                f"using model {model_type}, enable_search={enable_search}, images_count={len(images)}"
            )

            try:
                # Sử dụng legacy method để đảm bảo hoạt động y như cũ
                response = model.start_chat_with_image_legacy(request.user, message, images, model_type, enable_search)
                if isinstance(response, Response):  # Handle error from AiModel
                    return response
                return response  # Return StreamingHttpResponse
            except Exception as e:
                logger.error(f"Error in start_chat_with_image: {str(e)}", exc_info=True)
                return Response({'error': f"Failed to start chat with image: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            logger.warning(f"Invalid start_chat_with_image request: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def message_with_image(self, request):
        """
        Send a message with image attachments to an existing chat.
        """
        serializer = MessageWithImageInputSerializer(data=request.data)
        if serializer.is_valid():
            chat_id = serializer.validated_data['chat_id']
            message = serializer.validated_data.get('message', '').strip()
            model_type = serializer.validated_data['model']
            enable_search = serializer.validated_data['enable_search']

            # Handle image uploads
            images = request.FILES.getlist('images')

            # Validate that either message or images are provided
            if not message and not images:
                return Response(
                    {'error': 'Either message text or images must be provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate image file types
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png']
            for image in images:
                if image.content_type not in allowed_types:
                    return Response(
                        {'error': f'Unsupported image type: {image.content_type}. Allowed types: {allowed_types}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            logger.info(
                f"Sending message with images for user {request.user.id} in chat {chat_id} with message '{message[:50]}...' "
                f"using model {model_type}, enable_search={enable_search}, images_count={len(images)}"
            )

            try:
                # Sử dụng existing method (không thay đổi)
                response = model.message_with_image(request.user, chat_id, message, images, model_type, enable_search)
                if isinstance(response, Response):  # Handle error from AiModel
                    return response
                return response  # Return StreamingHttpResponse
            except Exception as e:
                logger.error(f"Error in message_with_image: {str(e)}", exc_info=True)
                return Response({'error': f"Failed to send message with image: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            logger.warning(f"Invalid message_with_image request: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def start_chat_unified(self, request):
        """
        PHASE 1: Basic unified chat endpoint supporting both patterns:
        - file_ids: Reference pre-uploaded files (Pattern A)
        - files: Direct upload (Pattern B)

        This is the foundation for the unified architecture.
        """
        serializer = UnifiedChatInputSerializer(data=request.data)
        if serializer.is_valid():
            message = serializer.validated_data.get('message', '').strip()
            model_type = serializer.validated_data.get('model', 'gemini')
            enable_search = serializer.validated_data.get('enable_search', False)
            file_ids = serializer.validated_data.get('file_ids', [])
            processing_method = serializer.validated_data.get('processing_method', 'auto')

            # Get direct uploads
            direct_files = request.FILES.getlist('files')

            # Validation
            if not message and not file_ids and not direct_files:
                return Response(
                    {'error': 'Message text, file_ids, or direct files must be provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if file_ids and direct_files:
                return Response(
                    {'error': 'Cannot mix file_ids and direct file uploads in same request'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            logger.info(
                f"[UNIFIED] Starting chat for user {request.user.id} with message '{message[:50]}...' "
                f"using model {model_type}, file_ids={len(file_ids)}, direct_files={len(direct_files)}, "
                f"processing_method={processing_method}"
            )

            try:
                # Phase 2: Enhanced implementation with UnifiedFileProcessor
                logger.info(f"[UNIFIED-PHASE2] Using enhanced unified processing")
                response = model.start_chat_unified_enhanced(
                    user=request.user,
                    message=message,
                    file_ids=file_ids,
                    direct_files=direct_files,
                    model=model_type,
                    enable_search=enable_search,
                    processing_method=processing_method
                )
                return response

            except Exception as e:
                logger.error(f"Error in start_chat_unified: {str(e)}", exc_info=True)
                return Response(
                    {'error': f"Failed to start unified chat: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        else:
            logger.warning(f"Invalid start_chat_unified request: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def message_unified(self, request):
        """
        PHASE 2: Unified message endpoint for continuing chat conversations
        Supports both text and file uploads with intelligent processing

        This replaces message_with_image with enhanced capabilities:
        - Intelligent processing method detection
        - Support for multiple file types
        - Enhanced metadata responses
        """
        serializer = MessageUnifiedInputSerializer(data=request.data)
        if serializer.is_valid():
            chat_id = serializer.validated_data.get('chat_id')
            message = serializer.validated_data.get('message', '').strip()
            model_type = serializer.validated_data.get('model', 'gemini')
            enable_search = serializer.validated_data.get('enable_search', False)
            processing_method = serializer.validated_data.get('processing_method', 'auto')

            # Get direct uploads
            direct_files = request.FILES.getlist('files')

            # Validation
            if not message and not direct_files:
                return Response(
                    {'error': 'Message text or files must be provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            logger.info(
                f"[UNIFIED-MESSAGE] Continuing chat {chat_id} for user {request.user.id} "
                f"with message '{message[:50]}...', direct_files={len(direct_files)}, "
                f"model={model_type}, processing_method={processing_method}"
            )

            try:
                # Use enhanced unified message method
                response = model.message_unified_enhanced(
                    user=request.user,
                    chat_id=chat_id,
                    message=message,
                    direct_files=direct_files,
                    model=model_type,
                    enable_search=enable_search,
                    processing_method=processing_method
                )
                return response

            except Exception as e:
                logger.error(f"Error in message_unified: {str(e)}", exc_info=True)
                return Response(
                    {'error': f"Failed to send unified message: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        else:
            logger.warning(f"Invalid message_unified request: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def save_to_pdf(self, request):
        try:
            output_id = request.data.get('output_id', None)
            if not output_id:
                raise ValueError('output_id is required')
            return model.save_to_pdf(request.user, output_id)
        except Exception as e:
            return Response({'error': str(e)}, status=500)


    @action(detail=False, methods=['get'])
    def get_dashboard_variants(self, request):
        """
        Analyze the dashboard variants JSON file with a fixed prompt.
        This is a GET endpoint that supports optional query parameters:
        - model: 'anthropic' (default) or 'gemini' - AI model to use for analysis
        - use_cache: 'true' or 'false' (default: follows global setting) - Whether to use cached results
        - force_refresh: 'true' or 'false' (default: 'false') - Force fresh analysis, ignoring cache

        Cache behavior (priority order):
        1. force_refresh=true → Always fresh analysis (highest priority)
        2. use_cache=true/false → Explicit cache control (overrides global setting)
        3. Global setting: AI_ANALYSIS_SETTINGS['ENABLE_DASHBOARD_VARIANTS_CACHE'] (default: True)

        Configuration options:
        - Global toggle: Set ENABLE_DASHBOARD_VARIANTS_CACHE=False in settings to disable caching globally
        - Per-request: ?use_cache=false to disable cache for specific request
        - Force refresh: ?force_refresh=true to get fresh analysis regardless of cache settings

        It analyzes the credentials/dashboard_variants.json file using AI with real-time streaming.
        """
        try:
            # Get model from query parameters, default to 'gemini' to avoid rate limits
            model_type = request.query_params.get('model', 'gemini')
            if model_type not in ['anthropic', 'gemini']:
                return Response({'error': 'Invalid model. Use "anthropic" or "gemini"'}, status=400)

            # Get global cache setting from Django settings
            global_cache_enabled = getattr(settings, 'AI_ANALYSIS_SETTINGS', {}).get('ENABLE_DASHBOARD_VARIANTS_CACHE', True)

            # Get cache preference from query parameters, default to global setting
            use_cache_param = request.query_params.get('use_cache')
            if use_cache_param is not None:
                use_cache = use_cache_param.lower() == 'true'
            else:
                use_cache = global_cache_enabled

            # Check for force_refresh parameter (overrides everything)
            force_refresh = request.query_params.get('force_refresh', 'false').lower() == 'true'
            if force_refresh:
                use_cache = False
                logger.info(f"Force refresh requested - disabling cache for this request")

            logger.info(f"Analyzing dashboard variants for user {request.user.id} using model {model_type}, use_cache={use_cache} (global_cache_enabled={global_cache_enabled})")
            response = model.analyze_dashboard_variants(request.user, model_type, use_cache)
            if isinstance(response, Response):  # Handle error from AiModel
                return response
            return response  # Return StreamingHttpResponse
        except Exception as e:
            logger.error(f"Error analyzing dashboard variants: {e}")
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['get'])
    def chat_history(self, request):
        """
        Retrieves the list of all chats for the authenticated user or a specific chat's full history.
        Query parameters:
        - 'chat_id': (optional) Specific chat ID to retrieve full message history
        - 'start_date': (optional) Filter chats by start date (ISO format: "YYYY-MM-DD")
        - 'end_date': (optional) Filter chats by end date (ISO format: "YYYY-MM-DD")
        - 'has_files': (optional) Filter chats by presence of file references ('true'/'false')

        If chat_id is provided: Returns full message history for that chat
        If chat_id is not provided: Returns list of all chats with summaries
        """
        try:
            chat_id = request.query_params.get('chat_id')

            if chat_id:  # Specific chat requested
                return self.get_specific_chat(request.user, chat_id)
            else:  # Return list of all chats
                return self.get_chat_list(request.user, request.query_params)

        except Exception as e:
            logger.error(f"Error in chat_history view: {e}")
            return Response({'error': str(e)}, status=500)

    def get_chat_list(self, user, query_params):
        """Helper method to get list of all chats with summaries"""
        filters = {
            'start_date': query_params.get('start_date'),
            'end_date': query_params.get('end_date'),
            'has_files': query_params.get('has_files')  # New parameter
        }

        chats = Chat.objects.filter(user=user)

        if filters.get('start_date'):
            chats = chats.filter(created_at__gte=filters['start_date'])
        if filters.get('end_date'):
            chats = chats.filter(created_at__lte=filters['end_date'])
        # Add filter for chats with/without files
        if filters.get('has_files') is not None:
            has_files = filters['has_files'].lower() == 'true'
            if has_files:
                chats = chats.exclude(referenced_files=[])
            else:
                chats = chats.filter(referenced_files=[])
        chats = chats.order_by('-created_at')

        chat_list = []
        for chat in chats:
            first_message = chat.messages.order_by('created_at').first()
            summary = first_message.content if first_message else ''

            chat_list.append({
                'id': str(chat.id),
                'model': chat.model,
                'referenced_files': chat.referenced_files,
                'summary': summary,
                'created_at': chat.created_at.isoformat()
            })

        return Response(chat_list, status=200)

    def get_specific_chat(self, user, chat_id):
        """Helper method to get full message history for a specific chat"""
        try:
            chat = Chat.objects.get(id=chat_id, user=user)

            # Get all messages ordered by creation time
            messages = chat.messages.order_by('created_at')
            message_history = [{
                'id': str(message.id),
                'content': message.content,
                'role': message.role,
                'created_at': message.created_at.isoformat(),
                # Removed is_user_message since it doesn't exist
                # Add other relevant message fields if needed
            } for message in messages]

            response_data = {
                'id': str(chat.id),
                'model': chat.model,
                'referenced_files': chat.referenced_files,
                'created_at': chat.created_at.isoformat(),
                'messages': message_history
            }

            return Response(response_data, status=200)

        except Chat.DoesNotExist:
            return Response({'error': 'Chat not found or you do not have access to it'},
                        status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error retrieving chat {chat_id} for user {user.id}: {e}")
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['delete'])
    def delete_chats(self, request):
        try:
            chat_ids = request.data.get('chat_ids', None)
            return model.delete_chats(request.user, chat_ids)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['get'])
    def cache_status(self, request):
        """
        Get the current cache configuration status for dashboard variants analysis.
        Returns information about global cache setting and how to control it.
        """
        try:
            global_cache_enabled = getattr(settings, 'AI_ANALYSIS_SETTINGS', {}).get('ENABLE_DASHBOARD_VARIANTS_CACHE', True)

            return Response({
                'cache_enabled': global_cache_enabled,
                'configuration': {
                    'global_setting': 'AI_ANALYSIS_SETTINGS.ENABLE_DASHBOARD_VARIANTS_CACHE',
                    'current_value': global_cache_enabled,
                    'per_request_override': 'Use ?use_cache=true/false query parameter',
                    'force_refresh': 'Use ?force_refresh=true to bypass cache completely'
                },
                'usage_examples': {
                    'default_behavior': '/api/ai-analysis/analyze-diagnosis/get_dashboard_variants/',
                    'disable_cache_once': '/api/ai-analysis/analyze-diagnosis/get_dashboard_variants/?use_cache=false',
                    'force_fresh_analysis': '/api/ai-analysis/analyze-diagnosis/get_dashboard_variants/?force_refresh=true',
                    'specific_model_no_cache': '/api/ai-analysis/analyze-diagnosis/get_dashboard_variants/?model=anthropic&use_cache=false'
                }
            })
        except Exception as e:
            logger.error(f"Error getting cache status: {e}")
            return Response({'error': str(e)}, status=500)

