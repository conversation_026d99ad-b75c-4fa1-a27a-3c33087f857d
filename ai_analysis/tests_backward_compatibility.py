"""
Test để chứng minh 100% backward compatibility
Các test n<PERSON><PERSON> đảm bảo API cũ hoạt động y như chưa có gì thay đổi
"""

import json
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock
from io import BytesIO
from PIL import Image

from accounts.models import SupportingDocs
from upload.models import UploadedFile
from ai_analysis.models import Cha<PERSON>, ChatMessage

User = get_user_model()


class BackwardCompatibilityTestCase(TestCase):
    """
    Test suite để đảm bảo API cũ hoạt động y như cũ
    """
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
        
        # Tạo test file
        self.uploaded_file = UploadedFile.objects.create(
            file='test_files/xray.jpg',
            filename='xray.jpg',
            user=self.user,
            file_url='https://storage.googleapis.com/test/xray.jpg',
            file_type='image/jpeg'
        )
        
        # Tạo supporting doc
        self.supporting_doc = SupportingDocs.objects.create(
            name='Test X-ray',
            file=self.uploaded_file,
            user=self.user,
            type='diagnosis'
        )

    def test_start_chat_with_files_exact_same_behavior(self):
        """
        Test start_chat_with_files hoạt động y như cũ
        """
        # Mock AI model response
        with patch('ai_analysis.ai_models.ai_model.AiModel.start_chat_with_files_legacy') as mock_method:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_method.return_value = mock_response
            
            # API call y như cũ
            response = self.client.post(
                '/api/ai-analysis/analyze-diagnosis/start_chat_with_files/',
                {
                    'ids': [str(self.supporting_doc.id)],
                    'model': 'gemini',
                    'type': 'diagnosis'
                },
                format='json'
            )
            
            # Verify API được gọi với đúng parameters như cũ
            mock_method.assert_called_once_with(
                self.user, 'diagnosis', [str(self.supporting_doc.id)], 'gemini'
            )
            
            self.assertEqual(response.status_code, 200)

    def test_start_chat_with_image_exact_same_behavior(self):
        """
        Test start_chat_with_image hoạt động y như cũ
        """
        # Tạo test image
        image = Image.new('RGB', (100, 100), color='red')
        image_file = BytesIO()
        image.save(image_file, format='JPEG')
        image_file.seek(0)
        image_file.name = 'test_image.jpg'
        
        with patch('ai_analysis.ai_models.ai_model.AiModel.start_chat_with_image_legacy') as mock_method:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_method.return_value = mock_response
            
            # API call y như cũ
            response = self.client.post(
                '/api/ai-analysis/analyze-diagnosis/start_chat_with_image/',
                {
                    'message': 'Analyze this X-ray',
                    'model': 'gemini',
                    'enable_search': False,
                    'images': image_file
                },
                format='multipart'
            )
            
            # Verify method được gọi
            mock_method.assert_called_once()
            call_args = mock_method.call_args
            
            # Verify parameters
            self.assertEqual(call_args[0][0], self.user)  # user
            self.assertEqual(call_args[0][1], 'Analyze this X-ray')  # message
            self.assertEqual(call_args[0][3], 'gemini')  # model
            self.assertEqual(call_args[0][4], False)  # enable_search
            
            self.assertEqual(response.status_code, 200)

    def test_message_api_unchanged(self):
        """
        Test message API không thay đổi
        """
        # Tạo chat trước
        chat = Chat.objects.create(user=self.user, model='gemini')
        
        with patch('ai_analysis.ai_models.ai_model.AiModel.message') as mock_method:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_method.return_value = mock_response
            
            response = self.client.post(
                '/api/ai-analysis/analyze-diagnosis/message/',
                {
                    'chat_id': str(chat.id),
                    'message': 'Follow up question',
                    'model': 'gemini',
                    'enable_search': False
                },
                format='json'
            )
            
            # Verify method được gọi với đúng parameters
            mock_method.assert_called_once_with(
                self.user, str(chat.id), 'Follow up question', 'gemini', False
            )
            
            self.assertEqual(response.status_code, 200)

    def test_chat_history_api_unchanged(self):
        """
        Test chat history API không thay đổi
        """
        # Tạo test data
        chat = Chat.objects.create(user=self.user, model='gemini')
        ChatMessage.objects.create(
            chat=chat,
            content='Test message',
            role='user'
        )
        
        response = self.client.get(
            '/api/ai-analysis/analyze-diagnosis/chat_history/'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['id'], str(chat.id))

    def test_find_chat_api_unchanged(self):
        """
        Test find_chat API không thay đổi
        """
        with patch('ai_analysis.ai_models.ai_model.AiModel.find_chat') as mock_method:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_method.return_value = mock_response
            
            response = self.client.post(
                '/api/ai-analysis/analyze-diagnosis/find_chat/',
                {
                    'ids': [str(self.supporting_doc.id)],
                    'model': 'gemini'
                },
                format='json'
            )
            
            mock_method.assert_called_once_with(
                self.user, 'diagnosis', [str(self.supporting_doc.id)], 'gemini'
            )
            
            self.assertEqual(response.status_code, 200)

    def test_response_format_unchanged(self):
        """
        Test format response không thay đổi
        """
        chat = Chat.objects.create(user=self.user, model='gemini')
        message = ChatMessage.objects.create(
            chat=chat,
            content='Test response',
            role='assistant'
        )
        
        response = self.client.get(
            f'/api/ai-analysis/analyze-diagnosis/chat_history/?chat_id={chat.id}'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Verify response structure y như cũ
        self.assertIn('id', data)
        self.assertIn('model', data)
        self.assertIn('messages', data)
        self.assertIn('created_at', data)
        
        # Verify message structure
        message_data = data['messages'][0]
        self.assertIn('id', message_data)
        self.assertIn('content', message_data)
        self.assertIn('role', message_data)
        self.assertIn('created_at', message_data)

    def test_error_handling_unchanged(self):
        """
        Test error handling y như cũ
        """
        # Test với invalid chat_id
        response = self.client.post(
            '/api/ai-analysis/analyze-diagnosis/message/',
            {
                'chat_id': 'invalid-id',
                'message': 'Test message',
                'model': 'gemini'
            },
            format='json'
        )
        
        # Verify error response format y như cũ
        self.assertEqual(response.status_code, 400)
        data = response.json()
        self.assertIn('chat_id', data)

    def test_streaming_response_format_unchanged(self):
        """
        Test streaming response format không thay đổi
        """
        with patch('ai_analysis.ai_models.ai_model.AiModel.start_chat_with_files_legacy') as mock_method:
            # Mock streaming response
            from django.http import StreamingHttpResponse
            
            def mock_stream():
                yield '{"chat_id": "test-id", "message_id": "test-msg-id"}\n\n'
                yield 'AI response content...'
            
            mock_response = StreamingHttpResponse(mock_stream(), content_type='text/event-stream')
            mock_method.return_value = mock_response
            
            response = self.client.post(
                '/api/ai-analysis/analyze-diagnosis/start_chat_with_files/',
                {
                    'ids': [str(self.supporting_doc.id)],
                    'model': 'gemini',
                    'type': 'diagnosis'
                },
                format='json'
            )
            
            # Verify streaming response
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response['Content-Type'], 'text/event-stream')


class LegacyMethodsTestCase(TestCase):
    """
    Test các legacy methods hoạt động đúng
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    @patch('ai_analysis.ai_models.ai_model.AiModel.query_files')
    @patch('ai_analysis.ai_models.ai_model.AiModel.load_files')
    @patch('ai_analysis.ai_models.ai_model.ChatAnthropic')
    @patch('ai_analysis.ai_models.ai_model.ChatGoogleGenerativeAI')
    def test_start_chat_with_files_legacy_exact_behavior(self, mock_gemini, mock_anthropic, mock_load_files, mock_query_files):
        """
        Test legacy method hoạt động y như method cũ
        """
        from ai_analysis.ai_models.ai_model import AiModel
        
        # Setup mocks
        mock_query_files.return_value = ['test_file.jpg']
        mock_load_files.return_value = ['file content']
        
        mock_stream = MagicMock()
        mock_stream.stream.return_value = [MagicMock(content='AI response')]
        mock_gemini.return_value = mock_stream
        
        ai_model = AiModel()
        
        # Call legacy method
        response = ai_model.start_chat_with_files_legacy(
            user=self.user,
            type='diagnosis',
            ids=['test-id'],
            model='gemini'
        )
        
        # Verify calls y như cũ
        mock_query_files.assert_called_once_with(self.user, 'diagnosis', ['test-id'])
        mock_load_files.assert_called_once_with(self.user, 'diagnosis', ['test_file.jpg'])
        
        # Verify chat được tạo với referenced_files như cũ
        chat = Chat.objects.get(user=self.user)
        self.assertEqual(chat.referenced_files, ['test_file.jpg'])
        
        # Verify response type
        from django.http import StreamingHttpResponse
        self.assertIsInstance(response, StreamingHttpResponse)


print("✅ Tất cả tests đảm bảo API cũ hoạt động y như chưa có gì thay đổi!")
