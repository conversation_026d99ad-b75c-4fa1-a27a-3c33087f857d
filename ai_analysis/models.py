from django.db import models

from accounts.models import CustomUser
from ai_analysis.constants import AI_MODELS, CHAT_ROLES, MESSAGE_TYPES
from config.models import BaseModel
from django.contrib.postgres.fields import ArrayField

# Create your models here.


class AiAnalysisOutput(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    content = models.TextField()
    referenced_files = ArrayField(
        models.CharField(max_length=255), default=list)
    model = models.CharField(max_length=255, choices=AI_MODELS, default='anthropic')


class Chat(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='chats')
    referenced_files = ArrayField(
        models.CharField(max_length=255), default=list)
    model = models.CharField(max_length=255, choices=AI_MODELS, default='anthropic')

class ChatMessage(BaseModel):
    chat = models.ForeignKey(Chat, on_delete=models.CASCADE, related_name='messages')
    content = models.TextField()
    role = models.CharField(max_length=255, choices=CHAT_ROLES)
    model = models.CharField(max_length=255, choices=AI_MODELS, default='anthropic',blank=True, null=True)
    message_type = models.CharField(max_length=255, choices=MESSAGE_TYPES, default='text')
    attachments = ArrayField(
        models.CharField(max_length=255), default=list, blank=True,
        help_text="Array of file paths/URLs for images attached to this message"
    )


class ChatFile(BaseModel):
    """
    Unified file reference for chat conversations
    Supports both pre-uploaded files (SupportingDocs) and direct uploads
    """
    chat = models.ForeignKey(Chat, on_delete=models.CASCADE, related_name='files')
    message = models.ForeignKey(
        ChatMessage,
        on_delete=models.CASCADE,
        related_name='files',
        null=True,
        blank=True,
        help_text="Message this file is attached to (null for chat-level files)"
    )

    # File reference - links to existing UploadedFile
    uploaded_file = models.ForeignKey(
        'upload.UploadedFile',
        on_delete=models.CASCADE,
        help_text="Reference to the actual uploaded file"
    )

    # File source tracking
    source_type = models.CharField(
        max_length=50,
        choices=[
            ('supporting_doc', 'Supporting Document'),  # From SupportingDocs system
            ('direct_upload', 'Direct Upload'),         # From chat upload
            ('generated', 'Generated File')             # AI-generated files
        ],
        help_text="How this file was added to the chat"
    )

    # Processing method used
    processing_method = models.CharField(
        max_length=50,
        choices=[
            ('text_extraction', 'Text Extraction'),    # UnstructuredImageLoader
            ('multimodal', 'Multimodal Vision'),       # Direct vision processing
            ('hybrid', 'Hybrid Processing'),           # Both methods
            ('auto', 'Auto Detection')                 # System decides
        ],
        default='auto',
        help_text="Method used to process this file for AI analysis"
    )

    # Processing results cache
    extracted_text = models.TextField(
        blank=True,
        null=True,
        help_text="Cached text content extracted from file"
    )
    multimodal_content = models.JSONField(
        default=dict,
        blank=True,
        help_text="Cached multimodal content structure for AI models"
    )

    # Processing metadata
    processing_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('processing', 'Processing'),
            ('completed', 'Completed'),
            ('failed', 'Failed')
        ],
        default='pending'
    )
    processing_error = models.TextField(blank=True, null=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['chat', 'source_type'], name='cf_chat_src_idx'),
            models.Index(fields=['message', 'processing_method'], name='cf_msg_proc_idx'),
            models.Index(fields=['processing_status'], name='cf_proc_stat_idx'),
        ]
        unique_together = [
            ('chat', 'uploaded_file', 'message'),  # Prevent duplicate file attachments
        ]

    def __str__(self):
        return f"ChatFile({self.uploaded_file.filename} - {self.source_type})"

    @property
    def file_url(self):
        """Get the file URL"""
        return self.uploaded_file.file_url if self.uploaded_file else None

    @property
    def filename(self):
        """Get the filename"""
        return self.uploaded_file.filename if self.uploaded_file else None

    @property
    def file_type(self):
        """Get the file type"""
        return self.uploaded_file.file_type if self.uploaded_file else None


class ChatFile(BaseModel):
    """
    Unified file reference for chat conversations
    Supports both pre-uploaded files (SupportingDocs) and direct uploads
    """
    chat = models.ForeignKey(Chat, on_delete=models.CASCADE, related_name='files')
    message = models.ForeignKey(
        ChatMessage,
        on_delete=models.CASCADE,
        related_name='files',
        null=True,
        blank=True,
        help_text="Message this file is attached to (null for chat-level files)"
    )

    # File reference - links to existing UploadedFile
    uploaded_file = models.ForeignKey(
        'upload.UploadedFile',
        on_delete=models.CASCADE,
        help_text="Reference to the actual uploaded file"
    )

    # File source tracking
    source_type = models.CharField(
        max_length=50,
        choices=[
            ('supporting_doc', 'Supporting Document'),  # From SupportingDocs system
            ('direct_upload', 'Direct Upload'),         # From chat upload
            ('generated', 'Generated File')             # AI-generated files
        ],
        help_text="How this file was added to the chat"
    )

    # Processing method used
    processing_method = models.CharField(
        max_length=50,
        choices=[
            ('text_extraction', 'Text Extraction'),    # UnstructuredImageLoader
            ('multimodal', 'Multimodal Vision'),       # Direct vision processing
            ('hybrid', 'Hybrid Processing'),           # Both methods
            ('auto', 'Auto Detection')                 # System decides
        ],
        default='auto',
        help_text="Method used to process this file for AI analysis"
    )

    # Processing results cache
    extracted_text = models.TextField(
        blank=True,
        null=True,
        help_text="Cached text content extracted from file"
    )
    multimodal_content = models.JSONField(
        default=dict,
        blank=True,
        help_text="Cached multimodal content structure for AI models"
    )

    # Processing metadata
    processing_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('processing', 'Processing'),
            ('completed', 'Completed'),
            ('failed', 'Failed')
        ],
        default='pending'
    )
    processing_error = models.TextField(blank=True, null=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['chat', 'source_type'], name='cf_chat_src_idx'),
            models.Index(fields=['message', 'processing_method'], name='cf_msg_proc_idx'),
            models.Index(fields=['processing_status'], name='cf_proc_stat_idx'),
        ]
        unique_together = [
            ('chat', 'uploaded_file', 'message'),  # Prevent duplicate file attachments
        ]

    def __str__(self):
        return f"ChatFile({self.uploaded_file.filename} - {self.source_type})"

    @property
    def file_url(self):
        """Get the file URL"""
        return self.uploaded_file.file_url if self.uploaded_file else None

    @property
    def filename(self):
        """Get the filename"""
        return self.uploaded_file.filename if self.uploaded_file else None

    @property
    def file_type(self):
        """Get the file type"""
        return self.uploaded_file.file_type if self.uploaded_file else None
