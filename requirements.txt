# amqp==5.2.0
# anyio==4.6.0
# argon2-cffi==23.1.0
# argon2-cffi-bindings==21.2.0
# asgiref==3.8.1
# attrs==24.2.0
# billiard==4.2.1
# celery==5.4.0
# cffi==1.17.1
# click==8.1.7
# click-didyoumean==0.3.1
# click-plugins==1.1.1
# click-repl==0.3.0
# colorama==0.4.6
# crispy-bootstrap5==2024.2
# cron-descriptor==1.4.5
# cryptography==43.0.1
# Django==5.0.9
# django-allauth==65.0.1
# django-appconf==1.0.6
# django-celery-beat==2.7.0
# django-compressor==4.5.1
# django-cors-headers==4.4.0
# django-crispy-forms==2.3
# django-debug-toolbar==4.4.6
# django-environ==0.11.2
# django-extensions==3.2.3
# django-model-utils==5.0.0
# django-redis==5.4.0
# django-timezone-field==7.0
# djangorestframework==3.15.2
# djangorestframework-simplejwt==5.3.1
# drf-spectacular==0.27.2
# fido2==1.1.3
# gunicorn==23.0.0
# h11==0.14.0
# httptools==0.6.1
# idna==3.10
# inflection==0.5.1
# jsonschema==4.23.0
# jsonschema-specifications==2023.12.1
# kombu==5.4.2
# packaging==24.1
# pillow==10.4.0
# prompt_toolkit==3.0.47
# psycopg2-binary==2.9.9
# pycparser==2.22
# PyJWT==2.9.0
# pypng==0.20220715.0
# python-crontab==3.2.0
# python-dateutil==2.9.0.post0
# python-dotenv==1.0.1
# python-slugify==8.0.4
# PyYAML==6.0.2
# qrcode==7.4.2
# rcssmin==1.1.2
# redis==5.0.8
# referencing==0.35.1
# rjsmin==1.2.2
# rpds-py==0.20.0
# six==1.16.0
# sniffio==1.3.1
# sqlparse==0.5.1
# text-unidecode==1.3
# typing_extensions==4.12.2
# tzdata==2024.2
# uritemplate==4.1.1
# uvicorn==0.30.6
# uvicorn-worker==0.2.0
# vine==5.1.0
# watchfiles==0.24.0
# wcwidth==0.2.13
# websockets==13.1
# whitenoise==6.7.0
# google-cloud-storage==2.13.0
# django-storages==1.14.2
# google-api-python-client>=2.0.0
# google-auth>=2.35.0
# google-auth-oauthlib>=0.4.0
# google-auth-httplib2>=0.1.0
# google-api-core


amqp==5.2.0
anyio==4.6.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asgiref==3.8.1
attrs==24.2.0
billiard==4.2.1
cachetools==5.5.0
celery==5.4.0
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
crispy-bootstrap5==2024.2
cron-descriptor==1.4.5
cryptography==43.0.1
Django==5.0.9
django-allauth==65.0.1
django-appconf==1.0.6
django-celery-beat==2.7.0
django-compressor==4.5.1
django-cors-headers==4.4.0
django-crispy-forms==2.3
django-debug-toolbar==4.4.6
django-environ==0.11.2
django-extensions==3.2.3
django-model-utils==5.0.0
django-redis==5.4.0
django-storages==1.14.4
django-timezone-field==7.0
djangorestframework==3.15.2
djangorestframework-simplejwt==5.3.1
drf-spectacular==0.27.2
fido2==1.1.3
google-api-core==2.20.0
google-auth==2.35.0
google-cloud-core==2.4.1
google-cloud-storage==2.18.2
google-crc32c==1.6.0
google-resumable-media==2.7.2
google-cloud-translate==3.11.1
googleapis-common-protos==1.65.0
setuptools==68.2.2
gunicorn==23.0.0
h11==0.14.0
httptools==0.6.1
idna==3.10
inflection==0.5.1
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
kombu==5.4.2
packaging==24.1
pillow==10.4.0
prompt_toolkit==3.0.47
proto-plus==1.24.0
protobuf==5.28.2
psycopg2-binary==2.9.9
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
PyJWT==2.9.0
pypng==0.20220715.0
python-crontab==3.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-slugify==8.0.4
PyYAML==6.0.2
qrcode==7.4.2
rcssmin==1.1.2
redis==5.0.8
referencing==0.35.1
requests==2.32.3
rjsmin==1.2.2
rpds-py==0.20.0
rsa==4.9
six==1.16.0
sniffio==1.3.1
sqlparse==0.5.1
text-unidecode==1.3
typing_extensions==4.12.2
tzdata==2024.2
uritemplate==4.1.1
urllib3==2.2.3
uvicorn==0.30.6
uvicorn-worker==0.2.0
vine==5.1.0
watchfiles==0.24.0
wcwidth==0.2.13
websockets==13.1
whitenoise==6.7.0
langchain-core==0.1.27
langchain-google-genai==0.0.11
langchain-google-community==0.0.27
langchain-anthropic==0.1.4
langchain-community==0.0.27
