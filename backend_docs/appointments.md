# Appointments Management

## Overview

The Appointments Management system is a comprehensive solution for handling both manual events and doctor bookings within the Ravid AI Healthcare Platform. It provides flexible scheduling capabilities, conflict detection, and integration with external calendar systems.

## Core Features

1. **Dual Appointment Types**
   - Manual Events: Personal or administrative events
   - Doctor Bookings: Structured appointments with healthcare providers

2. **Flexible Scheduling**
   - Multiple appointment modes (in-person, video call, phone call)
   - Support for all-day events
   - Conflict detection and prevention
   - Integration with Google Calendar

3. **Doctor Availability Management**
   - Recurring availability patterns
   - Override capabilities for special schedules
   - Validation against doctor's available slots
   - Support for multiple recurrence types (daily, weekly, monthly, yearly)

4. **Appointment Status Tracking**
   - Pending
   - Confirmed
   - Canceled
   - Completed

## Data Models

### Appointment

```python
class Appointment(BaseModel):
    creator = models.ForeignKey(CustomUser, related_name='created_appointments')
    patient = models.ForeignKey(CustomUser, related_name='appointments_as_patient')
    doctor = models.ForeignKey(CustomUser, related_name='appointments_as_doctor')
    clinic = models.ForeignKey(Clinic, related_name='appointments')
    enterprise = models.ForeignKey(Enterprise, related_name='appointments')
    title = models.CharField(max_length=255)
    appointment_type = models.CharField(choices=APPOINTMENT_TYPE_CHOICES)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    status = models.CharField(choices=APPOINTMENT_STATUS_CHOICES)
    mode = models.CharField(choices=APPOINTMENT_MODE_CHOICES)
    meeting_link = models.URLField()
    notes = models.TextField()
    qr_code = models.CharField(max_length=255)
    google_event_id = models.CharField(max_length=255)
```

### AppointmentAttachment

```python
class AppointmentAttachment(BaseModel):
    appointment = models.ForeignKey(Appointment, related_name='attachments')
    file = models.ForeignKey(UploadedFile, related_name='appointment_attachments')
    description = models.CharField(max_length=255)
```

## Appointment Types

1. **Manual Events**
   - Personal or administrative events
   - No doctor assignment required
   - Flexible scheduling without availability constraints
   - Suitable for meetings, reminders, or personal events

2. **Doctor Bookings**
   - Structured appointments with healthcare providers
   - Requires both patient and doctor assignment
   - Validates against doctor's availability
   - Supports multiple consultation modes

## Appointment Modes

1. **In-Person**
   - Traditional face-to-face consultations
   - Location tracking
   - QR code generation for check-in

2. **Video Call**
   - Virtual consultations via Google Meet
   - Automatic meeting link generation
   - Integration with Google Calendar
   - Secure video conferencing
   - Meeting details synced with calendar invites

3. **Phone Call**
   - Telephonic consultations
   - No physical or virtual location required

## Validation Rules

1. **Time Validation**
   - End time must be after start time
   - Appointments cannot overlap for the same doctor
   - Must fall within doctor's available slots

2. **Doctor Availability**
   - Checks regular availability patterns
   - Considers override schedules
   - Validates against recurring slots

3. **Mode-specific Validation**
   - Video call appointments require meeting links
   - In-person appointments require location information

## Integration Features

1. **Google Calendar & Meet Integration**
   - Bi-directional synchronization
   - Event ID tracking
   - Automatic updates
   - Automatic Meet link generation for video calls
   - Meeting details included in calendar invites
   - Secure video conferencing

2. **QR Code Generation**
   - Unique identifiers for appointments
   - Quick check-in capabilities
   - Secure access verification

## File Attachments

The system supports file attachments for appointments:
- Document uploads
- Medical records
- Prescriptions
- Test results
- Custom descriptions for attachments

## Security and Privacy

1. **Access Control**
   - Role-based permissions
   - Creator, patient, and doctor access rights
   - Clinic and enterprise-level restrictions

2. **Data Protection**
   - Secure file storage
   - Encrypted meeting links
   - Privacy-compliant information handling

## API Endpoints

1. **Appointment Management**
   - `POST /api/appointments/` - Create new appointment
   - `GET /api/appointments/{id}/` - Retrieve appointment details
   - `PUT /api/appointments/{id}/` - Update appointment
   - `DELETE /api/appointments/{id}/` - Cancel appointment

2. **Availability Management**
   - `GET /api/appointments/availability/` - Check doctor availability
   - `POST /api/appointments/availability/` - Set availability
   - `PUT /api/appointments/availability/{id}/` - Update availability

3. **Attachment Management**
   - `POST /api/appointments/{id}/attachments/` - Add attachment
   - `GET /api/appointments/{id}/attachments/` - List attachments
   - `DELETE /api/appointments/{id}/attachments/{attachment_id}/` - Remove attachment

## Best Practices

1. **Scheduling**
   - Book appointments during regular business hours
   - Consider time zones for virtual appointments
   - Allow sufficient buffer time between appointments

2. **Documentation**
   - Attach relevant medical records
   - Include clear notes and instructions
   - Maintain appointment history

3. **Communication**
   - Send timely reminders
   - Provide clear instructions for virtual appointments
   - Handle cancellations professionally

## Error Handling

1. **Common Errors**
   - Time slot conflicts
   - Invalid availability
   - Missing required fields
   - Invalid mode configuration

2. **Resolution**
   - Automatic conflict detection
   - Clear error messages
   - Alternative slot suggestions
   - Graceful fallback options 