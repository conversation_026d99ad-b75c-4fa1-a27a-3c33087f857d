# Billing Module Documentation

## Overview

The Billing module is responsible for managing payments, subscriptions, and financial transactions within the Ravid AI Healthcare Platform. It integrates with Stripe to provide a comprehensive payment processing system for both one-time services and recurring subscriptions, as well as enterprise solutions.

## Core Components

### Product Management

The module maps platform services, subscription plans, and solutions to Stripe products for payment processing.

#### Product Model
```python
class Product(BaseModel):
    """
    Represents a Stripe Product for SaaS platform services
    Maps to your Service, SubscriptionPlan, and Solution models from content_management
    """
    stripe_product_id = models.CharField(max_length=255, unique=True)
    name = models.CharField(max_length=255)
    content_id = models.UUIDField(unique=True, null=True, blank=True)
    description = models.TextField(blank=True)
    active = models.BooleanField(default=True)
    metadata = models.JSONField(default=dict, blank=True)
    product_type = models.CharField(
        max_length=50,
        choices=[
            ('service', 'Service'),
            ('subscription', 'Subscription'),
            ('solution', 'Solution')
        ]
    )
    service = models.ForeignKey(Service, on_delete=models.SET_NULL, null=True, blank=True, related_name='stripe_products')
    subscription_plan = models.ForeignKey(SubscriptionPlan, on_delete=models.SET_NULL, null=True, blank=True, related_name='stripe_products')
    solution = models.ForeignKey(Solution, on_delete=models.SET_NULL, null=True, blank=True, related_name='stripe_products')
```

Key features:
- Mapping to Stripe products
- Support for different product types (service, subscription, solution)
- Validation to ensure correct relationships
- Metadata storage for additional information

### Price Management

The module manages prices for products in Stripe.

#### Price Model
```python
class Price(BaseModel):
    """
    Represents a Stripe Price for SaaS platform services
    """
    stripe_price_id = models.CharField(max_length=255, unique=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='prices')
    active = models.BooleanField(default=True)
    currency = models.CharField(max_length=3, default='usd')
    unit_amount = models.IntegerField(help_text="Amount in cents")
    recurring = models.JSONField(null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Mapping to Stripe prices
- Support for different currencies
- Support for recurring and one-time prices
- Metadata storage for additional information

### Customer Management

The module manages customer information in Stripe.

#### Customer Model
```python
class Customer(BaseModel):
    """
    Represents a Stripe Customer
    """
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='billing_customer')
    stripe_customer_id = models.CharField(max_length=255, unique=True)
    email = models.EmailField()
    name = models.CharField(max_length=255, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Mapping to Stripe customers
- Association with platform users
- Metadata storage for additional information

### Service Payment Management

The module tracks payments for one-time services.

#### Service Payment Model
```python
class ServicePayment(BaseModel):
    """
    Tracks payments for one-time services
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='service_payments')
    service = models.ForeignKey('content_management.Service', on_delete=models.CASCADE, related_name='payments')
    amount = models.IntegerField(help_text="Amount in cents")
    currency = models.CharField(max_length=3, default='usd')
    status = models.CharField(
        max_length=50,
        choices=[
            ('pending', 'Pending'),
            ('completed', 'Completed'),
            ('failed', 'Failed'),
            ('refunded', 'Refunded')
        ]
    )
    stripe_payment_intent_id = models.CharField(max_length=255, unique=True)
    metadata = models.JSONField(default=dict, blank=True)
    dna_analysis = models.ForeignKey(DNAAnalysis, on_delete=models.SET_NULL, null=True, blank=True, related_name='service_payments')
```

Key features:
- Tracking of payment status
- Association with users and services
- Support for different currencies
- Integration with DNA analysis

### Service Access Management

The module tracks which users have access to which services.

#### Service Access Model
```python
class ServiceAccess(BaseModel):
    """
    Tracks which users have access to which services and their status
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='service_access')
    service = models.ForeignKey('content_management.Service', on_delete=models.CASCADE, related_name='user_access')
    payment = models.ForeignKey(ServicePayment, on_delete=models.CASCADE, related_name='access_records')
    status = models.CharField(
        max_length=50,
        choices=[
            ('active', 'Active'),
            ('expired', 'Expired'),
            ('revoked', 'Revoked')
        ]
    )
    access_granted_at = models.DateTimeField(auto_now_add=True)
    last_accessed_at = models.DateTimeField(auto_now=True)
    access_expires_at = models.DateTimeField(null=True, blank=True)
    analysis_access_data = models.JSONField(default=dict, blank=True, help_text="Specific access data for analysis app")
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Tracking of access status
- Association with users, services, and payments
- Expiration date management
- Last accessed tracking
- Specific access data for analysis app

### Subscription Management

The module manages subscriptions for recurring services.

#### Subscription Model
```python
class Subscription(BaseModel):
    """
    Represents a Stripe Subscription for SaaS platform services
    """
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='subscriptions')
    stripe_subscription_id = models.CharField(max_length=255, unique=True)
    price = models.ForeignKey(Price, on_delete=models.CASCADE, related_name='subscriptions')
    status = models.CharField(max_length=50)
    current_period_start = models.DateTimeField()
    current_period_end = models.DateTimeField()
    cancel_at_period_end = models.BooleanField(default=False)
    canceled_at = models.DateTimeField(null=True, blank=True)
    trial_end = models.DateTimeField(null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Mapping to Stripe subscriptions
- Tracking of subscription status
- Period management
- Cancellation tracking
- Trial period support

### Subscription Access Management

The module tracks which users have access to which subscription plans.

#### Subscription Access Model
```python
class SubscriptionAccess(BaseModel):
    """
    Tracks which users have access to which subscription plans and their status
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='subscription_access')
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='access_records')
    status = models.CharField(
        max_length=50,
        choices=[
            ('active', 'Active'),
            ('past_due', 'Past Due'),
            ('canceled', 'Canceled'),
            ('expired', 'Expired')
        ]
    )
    access_granted_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Tracking of access status
- Association with users and subscriptions
- Access granted tracking

### Enterprise Account Management

The module manages Stripe Connect accounts for enterprises.

#### Enterprise Account Model
```python
class EnterpriseAccount(BaseModel):
    """
    Represents a Stripe Connect account for enterprises
    """
    enterprise = models.OneToOneField('enterprise.Enterprise', on_delete=models.CASCADE, related_name='stripe_account')
    stripe_account_id = models.CharField(max_length=255, unique=True)
    charges_enabled = models.BooleanField(default=False)
    payouts_enabled = models.BooleanField(default=False)
    details_submitted = models.BooleanField(default=False)
    requirements = models.JSONField(default=dict, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Mapping to Stripe Connect accounts
- Tracking of account status
- Requirements tracking
- Metadata storage for additional information

### Enterprise Service Management

The module manages services offered by enterprises.

#### Enterprise Service Model
```python
class EnterpriseService(BaseModel):
    """
    Represents a service offered by an enterprise
    """
    enterprise = models.ForeignKey('enterprise.Enterprise', on_delete=models.CASCADE, related_name='billing_services')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Price in dollars")
    stripe_price_id = models.CharField(max_length=255, unique=True)
    active = models.BooleanField(default=True)
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Association with enterprises
- Price management
- Active/Inactive status management
- Metadata storage for additional information

### Enterprise Payment Management

The module manages payments from personal users to enterprises.

#### Enterprise Payment Model
```python
class EnterprisePayment(BaseModel):
    """
    Represents payments from personal users to enterprises
    """
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='enterprise_payments')
    enterprise = models.ForeignKey('enterprise.Enterprise', on_delete=models.CASCADE, related_name='payments')
    service = models.ForeignKey(EnterpriseService, on_delete=models.CASCADE, related_name='payments')
    amount = models.IntegerField(help_text="Amount in cents")
    platform_fee = models.ForeignKey(PlatformFee, on_delete=models.SET_NULL, null=True)
    platform_fee_amount = models.IntegerField(help_text="Platform fee amount in cents")
    status = models.CharField(max_length=50)
    stripe_payment_intent_id = models.CharField(max_length=255, unique=True)
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Tracking of payment status
- Association with customers, enterprises, and services
- Platform fee management
- Metadata storage for additional information

### Platform Fee Management

The module manages platform fees for enterprise payments.

#### Platform Fee Model
```python
class PlatformFee(BaseModel):
    """
    Represents platform fees for enterprise payments
    """
    percentage = models.DecimalField(max_digits=5, decimal_places=2, help_text="Platform fee percentage")
    fixed_amount = models.IntegerField(default=0, help_text="Fixed platform fee in cents")
    active = models.BooleanField(default=True)
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Percentage-based fee management
- Fixed amount fee management
- Active/Inactive status management
- Metadata storage for additional information

### Solution Access Management

The module tracks which enterprises have access to which solutions.

#### Solution Access Model
```python
class SolutionAccess(BaseModel):
    """
    Tracks which enterprises have access to which solutions and their status
    """
    enterprise = models.ForeignKey('enterprise.Enterprise', on_delete=models.CASCADE, related_name='solution_access')
    solution = models.ForeignKey('content_management.Solution', on_delete=models.CASCADE, related_name='enterprise_access')
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='solution_access')
    status = models.CharField(
        max_length=50,
        choices=[
            ('active', 'Active'),
            ('past_due', 'Past Due'),
            ('canceled', 'Canceled'),
            ('expired', 'Expired')
        ]
    )
    access_granted_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Tracking of access status
- Association with enterprises, solutions, and subscriptions
- Access granted tracking

### User Payment Profile Management

The module manages user payment profile settings.

#### User Payment Profile Model
```python
class UserPaymentProfile(BaseModel):
    """
    Represents a user's payment profile settings
    """
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payment_profile')
    stripe_account_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    charges_enabled = models.BooleanField(default=False)
    payouts_enabled = models.BooleanField(default=False)
    details_submitted = models.BooleanField(default=False)
    
    # Donation settings
    accept_donations = models.BooleanField(default=False)
    donation_message = models.TextField(blank=True)
    minimum_donation = models.IntegerField(default=100, help_text="Minimum donation amount in cents")
    suggested_donation_amounts = models.JSONField(default=list, help_text="List of suggested donation amounts in cents")
    
    # Verification status
    is_verified = models.BooleanField(default=False)
    verification_date = models.DateTimeField(null=True, blank=True)
```

Key features:
- Association with users
- Stripe Connect account management
- Donation settings management
- Verification status tracking

### User Transfer Management

The module manages money transfers between users.

#### User Transfer Model
```python
class UserTransfer(BaseModel):
    """
    Represents money transfers between users
    """
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='sent_transfers')
    receiver = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='received_transfers')
    amount = models.IntegerField(help_text="Amount in cents")
    currency = models.CharField(max_length=3, default='usd')
    message = models.TextField(blank=True)
    transfer_type = models.CharField(
        max_length=50,
        choices=[
            ('donation', 'Donation'),
            ('payment', 'Payment'),
            ('transfer', 'Transfer')
        ]
    )
    platform_fee = models.ForeignKey(PlatformFee, on_delete=models.SET_NULL, null=True)
    platform_fee_amount = models.IntegerField(help_text="Platform fee amount in cents")
    status = models.CharField(
        max_length=50,
        choices=[
            ('pending', 'Pending'),
            ('processing', 'Processing'),
            ('completed', 'Completed'),
            ('failed', 'Failed'),
            ('refunded', 'Refunded')
        ]
    )
    stripe_payment_intent_id = models.CharField(max_length=255, unique=True)
    metadata = models.JSONField(default=dict, blank=True)
```

Key features:
- Tracking of transfer status
- Association with senders and receivers
- Support for different transfer types
- Platform fee management
- Metadata storage for additional information

## Payment Processing Flow

### One-Time Service Payment

1. **Service Selection**
   - User selects a service from the content management module
   - System retrieves service details and pricing

2. **Payment Processing**
   - System creates a payment intent in Stripe
   - User completes payment through Stripe Checkout
   - System receives webhook notification from Stripe

3. **Access Granting**
   - System creates a service payment record
   - System creates a service access record
   - User gains access to the service

### Subscription Payment

1. **Subscription Plan Selection**
   - User selects a subscription plan from the content management module
   - System retrieves plan details and pricing

2. **Subscription Creation**
   - System creates a subscription in Stripe
   - User completes payment through Stripe Checkout
   - System receives webhook notification from Stripe

3. **Access Granting**
   - System creates a subscription record
   - System creates a subscription access record
   - User gains access to the subscription

### Enterprise Payment

1. **Enterprise Service Selection**
   - User selects an enterprise service
   - System retrieves service details and pricing

2. **Payment Processing**
   - System creates a payment intent in Stripe
   - System applies platform fee
   - User completes payment through Stripe Checkout
   - System receives webhook notification from Stripe

3. **Payment Distribution**
   - System transfers payment to enterprise (minus platform fee)
   - System records payment details

## API Endpoints

### Product Management
- `/api/billing/products/` - CRUD operations for products
- `/api/billing/products/active/` - List active products
- `/api/billing/products/sync/` - Sync products with Stripe

### Price Management
- `/api/billing/prices/` - CRUD operations for prices
- `/api/billing/prices/active/` - List active prices
- `/api/billing/prices/sync/` - Sync prices with Stripe

### Customer Management
- `/api/billing/customers/` - CRUD operations for customers
- `/api/billing/customers/sync/` - Sync customers with Stripe

### Service Payment Management
- `/api/billing/service-payments/` - List service payments
- `/api/billing/service-payments/create/` - Create service payment
- `/api/billing/service-payments/refund/` - Refund service payment

### Service Access Management
- `/api/billing/service-access/` - List service access
- `/api/billing/service-access/check/` - Check service access

### Subscription Management
- `/api/billing/subscriptions/` - CRUD operations for subscriptions
- `/api/billing/subscriptions/cancel/` - Cancel subscription
- `/api/billing/subscriptions/reactivate/` - Reactivate subscription

### Subscription Access Management
- `/api/billing/subscription-access/` - List subscription access
- `/api/billing/subscription-access/check/` - Check subscription access

### Enterprise Account Management
- `/api/billing/enterprise-accounts/` - CRUD operations for enterprise accounts
- `/api/billing/enterprise-accounts/connect/` - Connect enterprise account with Stripe

### Enterprise Service Management
- `/api/billing/enterprise-services/` - CRUD operations for enterprise services
- `/api/billing/enterprise-services/active/` - List active enterprise services

### Enterprise Payment Management
- `/api/billing/enterprise-payments/` - List enterprise payments
- `/api/billing/enterprise-payments/create/` - Create enterprise payment
- `/api/billing/enterprise-payments/refund/` - Refund enterprise payment

### Platform Fee Management
- `/api/billing/platform-fees/` - CRUD operations for platform fees
- `/api/billing/platform-fees/active/` - List active platform fees

### Solution Access Management
- `/api/billing/solution-access/` - List solution access
- `/api/billing/solution-access/check/` - Check solution access

### User Payment Profile Management
- `/api/billing/payment-profiles/` - CRUD operations for payment profiles
- `/api/billing/payment-profiles/connect/` - Connect payment profile with Stripe

### User Transfer Management
- `/api/billing/transfers/` - List transfers
- `/api/billing/transfers/create/` - Create transfer
- `/api/billing/transfers/refund/` - Refund transfer

## Integration with Content Management Module

The Billing module integrates with the Content Management module to provide a complete SaaS platform:

1. **Product Mapping**
   - Services, subscription plans, and solutions are mapped to Stripe products
   - Prices are synchronized with Stripe prices

2. **Access Management**
   - Service access is tracked for one-time purchases
   - Subscription access is tracked for recurring subscriptions
   - Solution access is tracked for enterprise solutions

3. **Payment Processing**
   - Payments are processed through Stripe
   - Discounts and promotions are applied at checkout

## Best Practices

1. **Payment Processing**
   - Use Stripe Checkout for secure payment processing
   - Implement webhook handling for payment status updates
   - Handle failed payments gracefully
   - Provide clear error messages

2. **Subscription Management**
   - Implement subscription lifecycle management
   - Handle subscription cancellations and reactivations
   - Process subscription renewals automatically
   - Send subscription status notifications

3. **Enterprise Management**
   - Implement Stripe Connect for enterprise payments
   - Handle platform fee calculations
   - Process payouts to enterprises
   - Track enterprise account status

4. **Security**
   - Implement proper authentication and authorization
   - Secure sensitive payment information
   - Follow PCI compliance guidelines
   - Implement fraud detection 