# Roles Module Documentation

## Overview

The Roles module implements a comprehensive role-based access control (RBAC) system for the Ravid AI Healthcare Platform. It extends Django's default permission system with custom roles, permissions, and enterprise-specific access controls.

## Core Components

### Custom Permission Model

```python
class CustomPermission(models.Model):
    name = models.CharField(max_length=100, unique=True)
    codename = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    category = models.CharField(max_length=50)  # e.g., 'saas', 'enterprise', 'sharing'
```

Key features:
- Unique permission names and codenames
- Categorized permissions
- Detailed permission descriptions

### Role Model

```python
class Role(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    custom_permissions = models.ManyToManyField(CustomPermission)
    is_saas_admin = models.BooleanField(default=False)
    is_enterprise_admin = models.BooleanField(default=False)
    is_enterprise_role = models.<PERSON><PERSON>anField(default=False)
    enterprise = models.ForeignKey('enterprise.Enterprise', null=True, blank=True, on_delete=models.CASCADE)
    group = models.OneToOneField(Group, on_delete=models.CASCADE, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    priority_level = models.IntegerField(default=1)
```

Features:
- Role hierarchy support
- Enterprise-specific roles
- Integration with Django's Group system
- Priority-based access control
- Active/Inactive status management

### Access Token Management

```python
class AccessToken(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    token = models.CharField(max_length=255, unique=True)
    is_active = models.BooleanField(default=True)
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

Features:
- UUID-based token generation
- Token expiration management
- Active/Inactive status tracking
- Timestamp tracking

## Default Roles

The system includes several pre-defined roles:

1. **SaaS Admin**
   - Full system access
   - User management
   - Role management
   - Enterprise management

2. **Enterprise Admin**
   - Enterprise-level administration
   - User management within enterprise
   - Role management within enterprise

3. **Enterprise User**
   - Regular enterprise access
   - Profile management
   - Data access based on permissions

## Role Management

### Role Creation
```python
@classmethod
def create_default_roles(cls):
    roles = {
        'SaaS Admin': {
            'description': 'System administrator with full access',
            'is_saas_admin': True,
            'permissions': ['manage_users', 'manage_roles', 'manage_enterprises']
        },
        'Enterprise Admin': {
            'description': 'Enterprise administrator with full enterprise access',
            'is_enterprise_admin': True,
            'is_enterprise_role': True,
            'permissions': ['manage_enterprise_users', 'manage_enterprise_roles']
        },
        'Enterprise User': {
            'description': 'Regular enterprise user',
            'is_enterprise_role': True,
            'permissions': ['view_enterprise_data', 'edit_own_profile']
        }
    }
```

### Permission Assignment
- Automatic permission assignment based on role
- Custom permission creation
- Permission inheritance
- Enterprise-specific permissions

## Authentication and Authorization

### Token-based Authentication
- JWT token generation
- Token validation
- Token refresh mechanism
- Token revocation

### Permission Checking
```python
def has_permission(self, user, permission):
    if user.is_superuser:
        return True
    if user.role and user.role.is_active:
        return user.role.custom_permissions.filter(codename=permission).exists()
    return False
```

## Enterprise Integration

### Enterprise-specific Roles
- Role scoping to enterprises
- Enterprise admin privileges
- Enterprise user management
- Enterprise permission management

### Role Hierarchy
- Priority-based access control
- Role inheritance
- Permission inheritance
- Enterprise role isolation

## API Endpoints

### Role Management
- `/api/roles/` - CRUD operations for roles
- `/api/roles/permissions/` - Manage permissions
- `/api/roles/assign/` - Assign roles to users
- `/api/roles/enterprise/` - Manage enterprise roles

### Permission Management
- `/api/permissions/` - List available permissions
- `/api/permissions/check/` - Check user permissions
- `/api/permissions/assign/` - Assign permissions to roles

### Token Management
- `/api/tokens/create/` - Create access token
- `/api/tokens/refresh/` - Refresh access token
- `/api/tokens/revoke/` - Revoke access token

## Security Considerations

1. **Token Security**
   - Secure token generation
   - Token expiration
   - Token revocation
   - Token refresh mechanism

2. **Permission Security**
   - Granular permission control
   - Permission inheritance rules
   - Enterprise isolation
   - Role hierarchy enforcement

3. **Access Control**
   - Role-based access control
   - Permission-based access control
   - Enterprise-based access control
   - Resource-based access control

## Best Practices

1. **Role Design**
   - Principle of least privilege
   - Role hierarchy design
   - Permission granularity
   - Enterprise isolation

2. **Token Management**
   - Secure token storage
   - Token rotation
   - Token validation
   - Token revocation

3. **Permission Management**
   - Regular permission audits
   - Permission documentation
   - Permission testing
   - Permission monitoring

## Integration Points

1. **User System**
   - Integration with CustomUser model
   - Role assignment
   - Permission checking
   - Token management

2. **Enterprise System**
   - Enterprise role management
   - Enterprise permission management
   - Enterprise user management
   - Enterprise isolation

3. **Authentication System**
   - Token generation
   - Token validation
   - Token refresh
   - Token revocation 