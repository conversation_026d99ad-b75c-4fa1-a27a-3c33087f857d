# Accounts Module Documentation

## Overview

The Accounts module is a core component of the Ravid AI Healthcare Platform that handles user management, authentication, and profile information. It extends Django's default user model with custom functionality and additional healthcare-specific features.

## Core Components

### CustomUser Model

The `CustomUser` model extends Django's `AbstractBaseUser` and `PermissionsMixin` to provide a flexible user management system:

```python
class CustomUser(AbstractBaseUser, PermissionsMixin):
    id = models.CharField(primary_key=True, max_length=10, editable=False)
    email = models.EmailField(unique=True)
    second_email = models.EmailField(blank=True)
    first_name = models.Char<PERSON>ield(max_length=30, blank=True)
    last_name = models.Char<PERSON>ield(max_length=30, blank=True)
    middle_name = models.CharField(max_length=30, blank=True)
    role = models.ForeignKey('roles.Role', on_delete=models.SET_NULL, null=True, blank=True)
    # ... other fields
```

Key features:
- Custom ID generation using `get_unique_custom_id`
- Email-based authentication
- Role-based access control
- Support for multiple email addresses
- Profile image handling
- Verification status tracking

### User Profile Management

#### Emergency Information
```python
class EmergencyContact(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    contact_name = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=15)
    email = models.EmailField()
    relationship = models.CharField(max_length=100)
    type = models.CharField(max_length=100)

class EmergencyMedical(BaseModel):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    allergies = ArrayField(models.CharField(max_length=200))
    blood_type = models.CharField(max_length=10, choices=BLOOD_TYPE_CHOICES)
    emergency_medications = ArrayField(models.CharField(max_length=200))
    critical_information = models.TextField()
    past_admissions = models.TextField()
```

#### Medical Team
```python
class UserMedicalPractitioner(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    email = models.EmailField()
    contact_number = models.CharField(max_length=20)
    affiliation = models.CharField(max_length=255)
    role = models.CharField(max_length=100)
```

#### Insurance Information
```python
class UserInsurance(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    provider = models.CharField(max_length=100)
    policy_number = models.CharField(max_length=100)
    type = models.CharField(max_length=100, choices=INSURANCE_TYPES)
    start_date = models.DateField()
    end_date = models.DateField()
    group_number = models.CharField(max_length=100)
    policy_holder_name = models.CharField(max_length=100)
    dependent_information = models.TextField()
    deductable_amount = models.DecimalField(max_digits=10, decimal_places=2)
    copay_amount = models.DecimalField(max_digits=10, decimal_places=2)
```

### Account Sharing System

The account sharing feature allows users to grant access to their account to other users, such as family members, caregivers, or healthcare providers.

#### Shared Access Model
```python
class SharedAccess(BaseModel):
    owner = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='shared_access_owner')
    shared_with = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='shared_access_with')
    access_level = models.CharField(max_length=50, choices=ACCESS_LEVEL_CHOICES)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

#### Access Level Permissions
```python
ACCESS_LEVEL_CHOICES = [
    ('view_only', 'View Only'),
    ('limited', 'Limited Access'),
    ('full', 'Full Access'),
    ('custom', 'Custom Access')
]

class SharedAccessPermission(BaseModel):
    shared_access = models.ForeignKey(SharedAccess, on_delete=models.CASCADE)
    module = models.CharField(max_length=100)  # e.g., 'appointments', 'diagnosis', 'prescriptions'
    can_view = models.BooleanField(default=False)
    can_edit = models.BooleanField(default=False)
    can_delete = models.BooleanField(default=False)
    can_share = models.BooleanField(default=False)
```

#### Sharing Invitation System
```python
class SharingInvitation(BaseModel):
    owner = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='invitations_sent')
    email = models.EmailField()
    access_level = models.CharField(max_length=50, choices=ACCESS_LEVEL_CHOICES)
    token = models.UUIDField(default=uuid.uuid4, unique=True)
    expires_at = models.DateTimeField()
    is_accepted = models.BooleanField(default=False)
    accepted_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
```

### Verification System

#### User Verification
```python
class UserVerificationAttempt(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    status = models.CharField(max_length=100, choices=USER_VERIFICATION_STATUS_CHOICES)
    file_paths = ArrayField(models.TextField())
    rejected_reason = models.TextField()
```

#### Document Management
```python
class SupportingDocs(BaseModel):
    name = models.TextField()
    description = models.TextField()
    file = models.OneToOneField(UploadedFile, on_delete=models.CASCADE)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    type = models.CharField(max_length=100, choices=SUPPORTING_DOCS_TYPES)

class VerificationDoc(BaseModel):
    name = models.TextField()
    description = models.TextField()
    file = models.OneToOneField(UploadedFile, on_delete=models.CASCADE)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    type = models.CharField(max_length=100, choices=VERIFICATION_DOC_TYPES)
    attempt = models.ForeignKey(UserVerificationAttempt, on_delete=models.CASCADE)
```

### Temporary User Management

```python
class TemporaryUser(models.Model):
    verification_token = models.UUIDField(primary_key=True)
    email = models.EmailField()
    name = models.CharField(max_length=255)
    password = models.CharField(max_length=128)
    role = models.CharField(max_length=100)
    is_clinic_signup = models.BooleanField(default=False)
    is_enterprise_signup = models.BooleanField(default=False)
```

## Authentication Flow

1. **User Registration**
   - Create temporary user with verification token
   - Send verification email
   - Upon verification, create permanent user account

2. **Email Verification**
   - Generate verification token
   - Send verification email
   - Verify email upon token confirmation

3. **Phone Verification**
   - Generate OTP
   - Send OTP via SMS
   - Verify phone number upon OTP confirmation

4. **ID Verification**
   - Upload verification documents
   - Submit verification attempt
   - Admin review and approval

## Security Features

1. **Password Management**
   - Secure password hashing
   - Password reset functionality
   - Password change verification

2. **Token Management**
   - UUID-based tokens for verification
   - Token expiration
   - Secure token storage

3. **Access Control**
   - Role-based permissions
   - Group-based access
   - Custom permission system

## API Endpoints

### Authentication
- `/api/auth/register/` - User registration
- `/api/auth/login/` - User login
- `/api/auth/logout/` - User logout
- `/api/auth/verify-email/` - Email verification
- `/api/auth/verify-phone/` - Phone verification

### Profile Management
- `/api/profile/` - Get/Update user profile
- `/api/profile/emergency/` - Manage emergency contacts
- `/api/profile/medical/` - Manage medical information
- `/api/profile/insurance/` - Manage insurance information

### Verification
- `/api/verification/submit/` - Submit verification documents
- `/api/verification/status/` - Check verification status
- `/api/verification/documents/` - Manage verification documents

### Account Sharing
- `/api/sharing/invite/` - Send sharing invitation
- `/api/sharing/accept/` - Accept sharing invitation
- `/api/sharing/revoke/` - Revoke shared access
- `/api/sharing/list/` - List all shared access
- `/api/sharing/permissions/` - Manage sharing permissions

## Best Practices

1. **Data Privacy**
   - Encrypt sensitive information
   - Implement data retention policies
   - Follow HIPAA compliance guidelines

2. **Error Handling**
   - Proper validation of user input
   - Secure error messages
   - Logging of security events

3. **Performance**
   - Efficient database queries
   - Proper indexing
   - Caching strategies

4. **Account Sharing**
   - Implement time-limited access
   - Provide granular permission controls
   - Enable easy revocation of access
   - Audit shared access regularly

## Integration Points

1. **Role System**
   - Integration with custom role management
   - Permission inheritance
   - Role-based access control

2. **File Storage**
   - Integration with Google Cloud Storage
   - Secure file upload/download
   - File type validation

3. **Email System**
   - Integration with email service
   - Template-based emails
   - Email verification flow

4. **Notification System**
   - Sharing invitation notifications
   - Access revocation notifications
   - Permission change notifications 