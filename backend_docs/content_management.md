# Content Management Module

## Overview
The Content Management module is a core component of the Ravid AI Healthcare Platform that manages services, subscription plans, solutions, and promotional content. It provides both administrative and public interfaces for managing and accessing platform content.

## Core Components

### 1. Services Management
The module manages different types of healthcare services with support for pricing, discounts, and promotions.

#### Service Model
```python
class Service(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    service_type = models.CharField(max_length=10, choices=SERVICE_TYPES)
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
    
    # Discount fields
    has_discount = models.BooleanField(default=False)
    discount_percentage = models.IntegerField(null=True, blank=True)
    discount_start_date = models.DateTimeField(null=True, blank=True)
    discount_end_date = models.DateTimeField(null=True, blank=True)
    
    # Promotions
    active_promotions = models.ManyToManyField('ServicePromotion', blank=True)
```

#### Service Promotion Model
```python
class ServicePromotion(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField()
    discount_percentage = models.IntegerField()
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=True)
```

### 2. Subscription Plans
Manages different subscription tiers and their features.

#### Subscription Plan Model
```python
class SubscriptionPlan(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    billing_cycle = models.CharField(max_length=10, choices=BILLING_CYCLES)
    features = models.JSONField()
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
    
    # Discount fields
    has_discount = models.BooleanField(default=False)
    discount_percentage = models.IntegerField(null=True, blank=True)
    discount_start_date = models.DateTimeField(null=True, blank=True)
    discount_end_date = models.DateTimeField(null=True, blank=True)
```

### 3. Solutions
Manages enterprise and clinic solutions with their specific features and pricing.

#### Solution Model
```python
class Solution(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField()
    solution_type = models.CharField(max_length=20, choices=SOLUTION_TYPES)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    features = models.JSONField()
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
```

### 4. Tab Alerts
Manages alerts and notifications for different platform tabs.

#### Tab Alert Model
```python
class TabAlert(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    message = models.TextField()
    tab_type = models.CharField(max_length=20, choices=TAB_TYPES)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
```

## API Endpoints

### Admin Endpoints
1. Services Management
   - `GET /admin/services/` - List all services
   - `POST /admin/services/` - Create new service
   - `PUT /admin/services/{id}/` - Update service
   - `DELETE /admin/services/{id}/` - Delete service
   - `POST /admin/services/bulk-order/` - Update service order

2. Subscription Plans
   - `GET /admin/subscription-plans/` - List all plans
   - `POST /admin/subscription-plans/` - Create new plan
   - `PUT /admin/subscription-plans/{id}/` - Update plan
   - `DELETE /admin/subscription-plans/{id}/` - Delete plan
   - `POST /admin/subscription-plans/bulk-order/` - Update plan order

3. Solutions
   - `GET /admin/solutions/` - List all solutions
   - `POST /admin/solutions/` - Create new solution
   - `PUT /admin/solutions/{id}/` - Update solution
   - `DELETE /admin/solutions/{id}/` - Delete solution
   - `POST /admin/solutions/bulk-order/` - Update solution order

4. Tab Alerts
   - `GET /admin/tab-alerts/` - List all alerts
   - `POST /admin/tab-alerts/` - Create new alert
   - `PUT /admin/tab-alerts/{id}/` - Update alert
   - `DELETE /admin/tab-alerts/{id}/` - Delete alert

### Public Endpoints
1. Services
   - `GET /public/services/` - List active services
   - `GET /public/services/{id}/` - Get service details

2. Subscription Plans
   - `GET /public/subscription-plans/` - List active plans
   - `GET /public/subscription-plans/{id}/` - Get plan details

3. Solutions
   - `GET /public/solutions/` - List active solutions
   - `GET /public/solutions/{id}/` - Get solution details

4. Tab Alerts
   - `GET /public/tab-alerts/` - List active alerts
   - `GET /public/tab-alerts/{id}/` - Get alert details

## Features

### 1. Discount Management
- Time-based discounts for services and subscription plans
- Percentage-based discount calculations
- Automatic discount activation/deactivation
- Support for multiple active promotions

### 2. Order Management
- Custom ordering for services, plans, and solutions
- Bulk order updates
- Order persistence across sessions

### 3. Stripe Integration
- Automatic price synchronization with Stripe
- Support for different billing cycles
- Handling of promotional pricing

### 4. Content Validation
- Date range validation for discounts and promotions
- Percentage validation for discounts
- Required field validation
- Active/inactive state management

## Best Practices

### 1. Content Management
- Use bulk operations for order updates
- Validate date ranges before saving
- Handle timezone-aware datetime objects
- Maintain consistent pricing across platforms

### 2. Performance
- Use appropriate indexing for frequently queried fields
- Implement caching for public endpoints
- Optimize bulk operations
- Use efficient query patterns

### 3. Security
- Implement proper access control for admin endpoints
- Validate user permissions
- Sanitize user input
- Protect sensitive pricing information

## Integration Points

### 1. Billing System
- Stripe integration for payment processing
- Price synchronization
- Discount application
- Subscription management

### 2. User Management
- Role-based access control
- User subscription tracking
- Service access management

### 3. Notification System
- Tab alert notifications
- Promotional notifications
- Price change notifications

## Error Handling

### 1. Validation Errors
- Invalid date ranges
- Invalid discount percentages
- Missing required fields
- Invalid service types

### 2. Business Logic Errors
- Overlapping promotions
- Invalid price updates
- Unauthorized access attempts
- Invalid order updates

## Future Enhancements

### 1. Content Management
- Advanced promotion types
- A/B testing support
- Content versioning
- Multi-language support

### 2. Analytics
- Usage tracking
- Promotion effectiveness
- Content performance metrics
- User engagement analytics

### 3. Integration
- Additional payment providers
- External content sources
- Marketing automation
- CRM integration 