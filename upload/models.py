from django.db import models
from django.utils import timezone
from django.conf import settings

class UploadedFile(models.Model):
    file = models.FileField(upload_to="uploaded_files/", max_length=1024)
    filename = models.CharField(max_length=255)
    uploaded_at = models.DateTimeField(default=timezone.now)
    file_url = models.CharField(max_length=1024, blank=True)
    file_type = models.CharField(blank=True, max_length=255)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='uploads')
    file_size = models.BigIntegerField(null=True, blank=True, help_text='Size of the file in bytes')
    
    def __str__(self):
        return f"{self.user.email} - {self.filename}"
