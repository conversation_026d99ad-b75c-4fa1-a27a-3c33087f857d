from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.core.files.storage import default_storage
import uuid
from .models import UploadedFile
import logging

logger = logging.getLogger(__name__)

class FileUploadView(APIView):
    permission_classes = [IsAuthenticated]
    upload_path = "uploaded_files/"

    def upload_file(self, file, user,directory = None):
        """Helper method to handle file upload and database creation"""
        try:
            # Generate a unique filename
            unique_filename = f"{uuid.uuid4()}_{file.name}"
            if directory:
                unique_filename = f"{directory}/{unique_filename}"
            
            # Use the class attribute for the path
            file_path = f"{self.upload_path}{unique_filename}"
            
            # Upload file using django-storages
            saved_path = default_storage.save(file_path, file)
            file_url = default_storage.url(saved_path)

            # Create and return database record
            return UploadedFile.objects.create(
                file=file_path,
                filename=file.name, 
                user=user,
                file_url=file_url,
                file_type=file.content_type
            )
        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            return e;

    def post(self, request):
        try:
            file = request.FILES.get('file')
            if not file:
                return Response(
                    {'error': 'No file provided'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            uploaded_file = self.upload_file(file, request.user)
            return Response({
                'message': 'File uploaded successfully',
                'filename': uploaded_file.filename,
                'file_path': uploaded_file.file.name,
                'url': uploaded_file.file_url,
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_signed_url(self, file_path):
        """
        Generate a signed URL for the given file path
        Args:
            file_path: The path to the file in storage
            expiration: URL expiration time in seconds (default: 1 hour)
        Returns:
            Signed URL string
        """
        try:
            return default_storage.url(file_path)
        except Exception as e:
            logger.error(f"Error generating signed URL: {e}")
            return None

    def delete_file(self, file_path):
        try:
            default_storage.delete(file_path)
        except Exception as e:
            logger.error(f"Error deleting file: {e}")
            return None
