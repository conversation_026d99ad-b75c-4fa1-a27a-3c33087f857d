#!/usr/bin/env python
"""
Debug script to identify the exact Stripe error
"""
import os
import django
import sys

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

import stripe
import logging
from django.conf import settings
from django.contrib.auth import get_user_model
from billing.services import StripeService

# Set up logging to see all details
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

CustomUser = get_user_model()

def test_direct_stripe_call():
    """Test direct Stripe API call without retry mechanism"""
    print("🔍 Testing Direct Stripe API Call...")
    
    try:
        # Get a test user
        user = CustomUser.objects.first()
        if not user:
            print("❌ No users found")
            return False
        
        print(f"✓ Using user: {user.email}")
        
        # Configure Stripe directly
        stripe.api_key = settings.STRIPE_SECRET_KEY
        stripe.api_version = "2023-10-16"
        
        print(f"✓ Stripe API Key: {settings.STRIPE_SECRET_KEY[:12]}...")
        
        # Prepare customer data
        customer_data = {
            'email': user.email,
            'name': f"{user.first_name} {user.last_name}".strip() or user.email,
            'metadata': {'user_id': str(user.id)}
        }
        
        print(f"✓ Customer data: {customer_data}")
        
        # Try direct Stripe call
        print("🚀 Making direct Stripe API call...")
        customer = stripe.Customer.create(**customer_data)
        
        print(f"✅ SUCCESS! Customer created: {customer.id}")
        print(f"   Email: {customer.email}")
        print(f"   Name: {customer.name}")
        
        return True
        
    except stripe.error.AuthenticationError as e:
        print(f"❌ AUTHENTICATION ERROR: {str(e)}")
        print("   → Check your Stripe API key")
        return False
        
    except stripe.error.InvalidRequestError as e:
        print(f"❌ INVALID REQUEST ERROR: {str(e)}")
        print("   → Check the data being sent to Stripe")
        return False
        
    except stripe.error.APIConnectionError as e:
        print(f"❌ CONNECTION ERROR: {str(e)}")
        print("   → Check network connectivity to Stripe")
        return False
        
    except stripe.error.RateLimitError as e:
        print(f"❌ RATE LIMIT ERROR: {str(e)}")
        print("   → Too many requests to Stripe API")
        return False
        
    except stripe.error.StripeError as e:
        print(f"❌ STRIPE ERROR: {str(e)}")
        print(f"   Type: {type(e).__name__}")
        if hasattr(e, 'code'):
            print(f"   Code: {e.code}")
        if hasattr(e, 'param'):
            print(f"   Param: {e.param}")
        return False
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {str(e)}")
        print(f"   Type: {type(e).__name__}")
        return False

def test_stripe_service():
    """Test using the StripeService"""
    print("\n🔍 Testing StripeService...")
    
    try:
        user = CustomUser.objects.first()
        if not user:
            print("❌ No users found")
            return False
        
        print(f"✓ Using user: {user.email}")
        
        # Try using StripeService
        print("🚀 Using StripeService.create_or_update_customer...")
        customer = StripeService.create_or_update_customer(user)
        
        print(f"✅ SUCCESS! Customer created via StripeService")
        print(f"   Customer ID: {customer.id}")
        print(f"   Stripe ID: {customer.stripe_customer_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ STRIPE SERVICE ERROR: {str(e)}")
        print(f"   Type: {type(e).__name__}")
        
        # Try to extract more details
        if hasattr(e, '__cause__'):
            print(f"   Cause: {e.__cause__}")
        
        if "RetryError" in str(e):
            print("   → This is a RetryError - the underlying Stripe call failed multiple times")
        
        return False

def check_environment():
    """Check environment configuration"""
    print("🔍 Checking Environment...")
    
    # Check Stripe keys
    if not hasattr(settings, 'STRIPE_SECRET_KEY'):
        print("❌ STRIPE_SECRET_KEY not found in settings")
        return False
    
    if not settings.STRIPE_SECRET_KEY:
        print("❌ STRIPE_SECRET_KEY is empty")
        return False
    
    print(f"✅ STRIPE_SECRET_KEY: {settings.STRIPE_SECRET_KEY[:12]}...")
    
    if settings.STRIPE_SECRET_KEY.startswith('sk_test_'):
        print("✅ Using test mode")
    elif settings.STRIPE_SECRET_KEY.startswith('sk_live_'):
        print("⚠️  Using live mode")
    else:
        print("❌ Invalid API key format")
        return False
    
    return True

def main():
    """Run debug tests"""
    print("🚀 Starting Stripe Error Debug\n")
    
    if not check_environment():
        print("❌ Environment check failed")
        return False
    
    print("\n" + "="*50)
    success1 = test_direct_stripe_call()
    
    print("\n" + "="*50)
    success2 = test_stripe_service()
    
    print("\n" + "="*50)
    if success1 and success2:
        print("🎉 Both tests passed!")
    elif success1:
        print("⚠️  Direct call works, but StripeService has issues")
    else:
        print("❌ Direct Stripe call failed - check configuration")
    
    return success1 or success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
