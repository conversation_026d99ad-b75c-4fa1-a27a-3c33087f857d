from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from accounts.views.otp_verify import Veri<PERSON>OT<PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView

from accounts.views.insurance import UserInsuranceViewSet
from accounts.views.message_me import MessageMeView
from accounts.views.request_access import GenerateTokenView, VerifyTokenView
from .views import (
    CustomOAuthCallback, 
    CustomOAuthLogin,
    EmailChangeView,
    EmergencyInformationView, 
    RegisterAPI, 
    LoginAPI, 
    LogoutView, 
    PasswordResetView, 
    PasswordResetConfirmView,
    AutoLoginView,
    VerifyEmailChangeView,
    AccountDeletionView,
    UserMedicalPractitionerViewSet,
    RequestAccessView,
    GrantAccessView,
    UserMedicalPractitionerViewSet,
    ProfileCategoryViewSet,
    IDVerificationAttemptView,
    IDVerificationAdminViewSet,
    PhoneVerificationRequestView,
    VerifyPhoneView,
    NoteViewSet,
    SupportingDocsViewSet,
    ChangeSecondEmailView,
    VerifySecondEmailView,
    InvitationRegisterAPI,
    # templateview
    )
from accounts.views.business_card import BusinessCardViewSet
from accounts.views.base import check_custom_url_availability, get_profile_by_identifier

router = DefaultRouter()
router.register(r'medical-practitioners', UserMedicalPractitionerViewSet, basename='medical_practitioners')
router.register(r'profile-categories', ProfileCategoryViewSet, basename='profile_categories')
router.register(r'admin/id-verifications', IDVerificationAdminViewSet)
router.register(r'notes', NoteViewSet, basename='notes')
router.register(r'supporting-docs', SupportingDocsViewSet, basename='supporting_docs')
router.register(r'insurance', UserInsuranceViewSet, basename='insurance')
router.register(r'id-verifications', IDVerificationAttemptView, basename='id_verification_attempts')
router.register(r'business-card', BusinessCardViewSet, basename='business_card')
urlpatterns = [
    path("", include(router.urls)),
    path("register/", RegisterAPI.as_view(), name="register"),
    path("register/invitation/", InvitationRegisterAPI.as_view(), name="invitation_register"),
    path("login/", LoginAPI.as_view(), name="login"),
    path("logout/", LogoutView.as_view(), name="logout"),
    path("auto-login/", AutoLoginView.as_view(), name="auto_login"),
    # path("template/", templateview.as_view(), name="template"),
    path('password-reset/', PasswordResetView.as_view(), name='password_reset'),
    path('reset-password/<str:uidb64>/<str:token>/', PasswordResetConfirmView.as_view(), name='password_reset_confirm'),
   
    path('sso/login/<str:provider>/', CustomOAuthLogin.as_view(), name='custom_oauth_login'),

    path('delete-account/', AccountDeletionView.as_view(), name='delete_account'),
    path('sso/callback/', CustomOAuthCallback.as_view(), name='custom_callback'),

    path('email-change/', EmailChangeView.as_view(), name='email_change'),
    path('verify-email-change/<uuid:token>/', VerifyEmailChangeView.as_view(), name='verify_email_change'),

    path('request-access/', RequestAccessView.as_view(), name='request_access'),
    path('grant-access/', GrantAccessView.as_view(), name='grant_access'),

    path('emergency-information/', EmergencyInformationView.as_view(), name='emergency_information'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

]

urlpatterns += [
    path('request-phone-verification/', PhoneVerificationRequestView.as_view(), name='phone_verification_request'),
    path('verify-phone/', VerifyPhoneView.as_view(), name='verify_phone'),
]

urlpatterns += [
    path('generate-token/', GenerateTokenView.as_view(), name='generate_token'),
    path('verify-token/', VerifyTokenView.as_view(), name='verify_token'),
]

urlpatterns += [
    path('change-second-email/', ChangeSecondEmailView.as_view(), name='change_second_email'),
    path('verify-second-email/<uuid:token>/', VerifySecondEmailView.as_view(), name='verify_second_email'),
]

urlpatterns += [
    path('message/', MessageMeView.as_view(), name='message'),
]

urlpatterns += [
    path('verify-otp/', VerifyOTPAPI.as_view(), name='verify_otp'),
]

# NEW: Custom URL management endpoints
urlpatterns += [
    path('check-custom-url/', check_custom_url_availability, name='check_custom_url'),
    path('profile/<str:identifier>/', get_profile_by_identifier, name='profile_by_identifier'),
]