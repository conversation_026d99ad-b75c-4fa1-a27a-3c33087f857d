# Accounts App Documentation

Welcome to the comprehensive documentation for the Django REST Framework implementation in the accounts app. This documentation covers the complete architecture, implementation patterns, and usage examples for the user management, authentication, and healthcare profile system.

## 📚 Documentation Overview

### 1. [DRF Architecture Guide](./DRF_ARCHITECTURE_GUIDE.md)
**Comprehensive architectural overview of the Django REST Framework implementation**

- **Basic Overview**: High-level architecture and core components
- **Models Architecture**: Detailed model relationships and business logic
- **Serializers**: Dynamic serializer patterns and validation strategies
- **ViewSets and Views**: Custom viewsets with user-based permissions
- **API Endpoints**: Complete endpoint documentation with examples
- **Authentication and Permissions**: Multi-layered security implementation
- **CRUD Operations**: Detailed CRUD patterns with examples
- **Custom Features**: Advanced features like custom URL usernames and verification system
- **Request/Response Flow**: Step-by-step processing flows
- **Business Logic**: Core business rules and validation patterns

### 2. [Code Examples](./DRF_CODE_EXAMPLES.md)
**Practical code examples demonstrating real implementation patterns**

- **Serializer Examples**: Complex validation and custom field handling
- **ViewSet Examples**: User-scoped permissions and custom actions
- **Model Method Examples**: Business logic and relationship patterns
- **Authentication Examples**: Custom authentication backends and token handling
- **Validation Examples**: Cross-field validation and business rules

### 3. [API Quick Reference](./API_QUICK_REFERENCE.md)
**Quick reference guide for all API endpoints**

- **Complete endpoint list** with HTTP methods and authentication requirements
- **Request/response examples** for all major operations
- **Query parameters** and filtering options
- **Error handling** and status codes
- **Rate limiting** and custom headers

### 4. [Frontend API Guide](./FRONTEND_API_GUIDE.md)
**Frontend integration guide for user management and authentication**

- User registration and authentication flow
- Profile management operations
- Emergency information handling
- Error handling strategies
- Integration examples

### 5. [Authentication Guide](./AUTHENTICATION_GUIDE.md)
**Comprehensive authentication and security documentation**

- JWT authentication implementation
- OAuth integration patterns
- Email verification system
- Phone verification system

## 🏗️ Architecture Highlights

### Core Design Principles

1. **User-Centric Design**
   - Comprehensive user profile management
   - Healthcare-specific information handling
   - Multi-verification system (email, phone, ID)
   - Flexible authentication options

2. **Security First**
   - JWT-based authentication
   - Multi-factor verification system
   - Custom URL username masking for privacy
   - Secure file upload and storage

3. **Healthcare Integration**
   - Emergency contact management
   - Medical practitioner relationships
   - Insurance information handling
   - Medical document management

4. **Performance Optimization**
   - Query optimization with select_related and prefetch_related
   - Background task processing for heavy operations
   - Caching strategies for frequently accessed data
   - Efficient pagination and filtering

### Key Features

#### 🔐 Authentication & Authorization
- **JWT Authentication**: Primary authentication method with refresh tokens
- **OAuth Integration**: Google OAuth for seamless login
- **Email Verification**: Secure email verification with token-based system
- **Phone Verification**: OTP-based phone number verification
- **Multi-Factor Authentication**: Combined email and phone verification

#### 👤 User Management
- **Custom User Model**: Extended Django user model with healthcare features
- **Profile Management**: Comprehensive user profile with medical information
- **Custom URL Usernames**: Privacy-friendly profile URLs (e.g., john.doe.123456)
- **Temporary User System**: Secure registration flow with email verification
- **Account Management**: Password reset, email change, account deletion

#### 🏥 Healthcare Features
- **Emergency Information**: Emergency contacts and medical information
- **Medical Practitioners**: User's healthcare provider relationships
- **Insurance Management**: Health insurance information and documents
- **Medical Documents**: Secure document upload and management
- **Business Cards**: Professional healthcare provider cards

#### 📋 Profile Categories
- **Flexible Categories**: User-defined profile categories
- **Custom Content**: JSON-based flexible content storage
- **Visibility Control**: Granular privacy settings
- **Category Management**: Full CRUD operations for profile categories

#### 📄 Document Management
- **ID Verification**: Government ID verification system
- **Supporting Documents**: Medical and legal document storage
- **File Upload**: Secure file upload with validation
- **Document Types**: Categorized document management

#### 💬 Communication
- **Messaging System**: User-to-user messaging
- **Notes Management**: Personal notes and reminders
- **Access Sharing**: Secure profile access sharing
- **Notification System**: Email and SMS notifications

## 🚀 Getting Started

### For Developers

1. **Start with the [Architecture Guide](./DRF_ARCHITECTURE_GUIDE.md)** to understand the overall system design
2. **Review [Code Examples](./DRF_CODE_EXAMPLES.md)** for implementation patterns
3. **Use the [API Reference](./API_QUICK_REFERENCE.md)** for endpoint details
4. **Check existing tests** in the `tests/` directory for usage examples

### For Frontend Developers

1. **Read the [Frontend API Guide](./FRONTEND_API_GUIDE.md)** for integration patterns
2. **Use the [API Reference](./API_QUICK_REFERENCE.md)** for endpoint specifications
3. **Review authentication** requirements and token handling
4. **Check error handling** patterns for robust integration

### For API Consumers

1. **Start with [API Quick Reference](./API_QUICK_REFERENCE.md)** for endpoint overview
2. **Review authentication** requirements and user context
3. **Understand user data** privacy and security patterns
4. **Check error responses** for proper error handling

## 🔧 Development Patterns

### Common Implementation Patterns

#### 1. User-Scoped Queries
```python
def get_queryset(self):
    return Model.objects.filter(user=self.request.user)
```

#### 2. Custom URL Username Generation
```python
def generate_custom_url_username(self):
    email_prefix = self.email.split('@')[0].lower()
    email_prefix = re.sub(r'[^a-z0-9]', '', email_prefix)
    random_suffix = f"{random.randint(100000, 999999)}"
    return f"{email_prefix}.{random_suffix}"
```

#### 3. Email Verification Flow
```python
def send_verification_email(self, temp_user):
    verification_link = f"{settings.SITE_URL}/api/verify-email/{temp_user.verification_token}/"
    send_email(
        to_email=temp_user.email,
        subject="Verify Your Email",
        html_content=html_content
    )
```

#### 4. Rate Limiting Implementation
```python
def _check_rate_limit(self, user_id):
    cache_key = f"email_change_attempts_user_{user_id}"
    attempts = cache.get(cache_key, {"count": 0, "timestamp": now()})
    if attempts["count"] > MAX_ATTEMPTS:
        raise ValidationError("Too many attempts")
```

## 🧪 Testing Strategy

### Test Coverage Areas

1. **Unit Tests**
   - Model validation logic
   - Serializer field validation
   - Business logic methods
   - Utility functions

2. **Integration Tests**
   - API endpoint functionality
   - Authentication flows
   - Permission checking
   - Database interactions

3. **End-to-End Tests**
   - Complete registration flow
   - Email verification process
   - Profile management operations
   - Document upload scenarios

### Running Tests

```bash
# Run all accounts tests
python manage.py test accounts

# Run specific test categories
python manage.py test accounts.tests.test_custom_url
python manage.py test accounts.tests.test_authentication
python manage.py test accounts.tests.test_profile_management

# Run with coverage
coverage run --source='.' manage.py test accounts
coverage report
```

## 📊 Performance Considerations

### Database Optimization
- **Indexes**: Strategic indexing on frequently queried fields
- **Query Optimization**: Use of select_related and prefetch_related
- **Pagination**: Efficient pagination for large datasets
- **Connection Pooling**: Database connection optimization

### Caching Strategy
- **User Sessions**: Cache user session data
- **Profile Information**: Cache frequently accessed profile data
- **Custom URL Mappings**: Cache username-to-ID mappings
- **API Responses**: Cache frequently accessed data

### Background Processing
- **Email Delivery**: Asynchronous email sending
- **File Processing**: Background image and document processing
- **Data Synchronization**: External system integration
- **Audit Logging**: Asynchronous activity logging

## 🔍 Monitoring and Debugging

### Logging Strategy
- **Request/Response Logging**: API interaction tracking
- **Error Logging**: Comprehensive error capture
- **Performance Logging**: Slow query identification
- **Business Logic Logging**: Important business events

### Debugging Tools
- **Django Debug Toolbar**: Development debugging
- **API Documentation**: Auto-generated API docs
- **Test Coverage Reports**: Code coverage analysis
- **Performance Profiling**: Query and response time analysis

## 🤝 Contributing

### Code Standards
- Follow Django and DRF best practices
- Maintain comprehensive test coverage
- Document new features and changes
- Use type hints where appropriate

### Documentation Updates
- Update relevant documentation for new features
- Include code examples for complex implementations
- Maintain API reference accuracy
- Update architectural diagrams as needed

## 📞 Support

For questions about the accounts app implementation:

1. **Check the documentation** in this directory first
2. **Review existing tests** for usage examples
3. **Check the codebase** for implementation details
4. **Consult the team** for architecture decisions

## 📋 Core Models Overview

### User Management System
- **CustomUser**: Extended Django user model with healthcare-specific features
- **TemporaryUser**: Temporary user storage during registration and verification
- **PendingEmailChange**: Email change verification system
- **PendingPhoneVerification**: Phone number verification with OTP
- **UserVerificationAttempt**: ID verification attempt tracking
- **VerificationDoc**: Document storage for ID verification

### Healthcare Information
- **EmergencyContact**: Emergency contact information management
- **EmergencyMedical**: Medical emergency information (allergies, medications, blood type)
- **UserMedicalPractitioner**: Healthcare provider relationships
- **UserInsurance**: Health insurance information and documents
- **BusinessCard**: Professional healthcare provider business cards

### Profile & Content Management
- **ProfileCategory**: User-defined profile categories
- **ProfileCategoryContent**: Flexible JSON-based content storage
- **Note**: Personal notes and reminders
- **SupportingDocs**: Medical and legal document storage
- **Message**: User-to-user messaging system

## 🔄 API Workflow Examples

### User Registration Flow
1. **Registration Request**: User submits registration data
2. **Temporary User Creation**: Create temporary user with verification token
3. **Email Verification**: Send verification email with token
4. **Email Confirmation**: User clicks verification link
5. **Account Activation**: Convert temporary user to permanent account
6. **Role Assignment**: Assign appropriate user role

### Authentication Flow
1. **Login Request**: User submits email and password
2. **Credential Validation**: Verify user credentials
3. **Token Generation**: Generate JWT access and refresh tokens
4. **User Context**: Return user information with tokens
5. **Token Refresh**: Refresh expired access tokens
6. **Logout**: Invalidate tokens and clear session

### Profile Management Flow
1. **Profile Retrieval**: Fetch user profile information
2. **Data Validation**: Validate profile updates
3. **File Processing**: Handle image and document uploads
4. **Emergency Information**: Manage emergency contacts and medical data
5. **Privacy Settings**: Configure profile visibility
6. **Audit Trail**: Log profile changes

### Verification System Flow
1. **ID Verification Request**: User submits verification documents
2. **Document Upload**: Secure file upload and storage
3. **Admin Review**: Administrative verification review
4. **Status Update**: Update verification status
5. **User Notification**: Notify user of verification result
6. **Access Control**: Enable verified user features

## 🛡️ Security Features

### Authentication Security
- **JWT Tokens**: Secure token-based authentication
- **Token Refresh**: Automatic token refresh mechanism
- **Password Hashing**: Secure password storage with Django's built-in hashing
- **Rate Limiting**: Protection against brute force attacks

### Data Protection
- **Input Validation**: Comprehensive data validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Output sanitization
- **CSRF Protection**: Cross-site request forgery prevention

### Privacy Features
- **Custom URL Usernames**: Hide internal user IDs from public URLs
- **Profile Visibility**: Granular privacy controls
- **Data Encryption**: Sensitive data encryption at rest
- **Secure File Storage**: Protected file access with signed URLs

### Verification Security
- **Email Verification**: Token-based email verification
- **Phone Verification**: OTP-based phone verification
- **ID Verification**: Document-based identity verification
- **Multi-Factor Authentication**: Combined verification methods

## 👤 User Management Features

### Registration & Authentication
- **Multiple Registration Types**: Standard, clinic, enterprise, mobile signup
- **Email Verification**: Secure email verification with tokens
- **Phone Verification**: SMS-based OTP verification
- **OAuth Integration**: Google OAuth for seamless login
- **Password Management**: Secure password reset and change

### Profile Management
- **Comprehensive Profiles**: Rich user profile with healthcare information
- **Custom URL Usernames**: SEO-friendly and privacy-protecting URLs
- **Profile Pictures**: Secure image upload and management
- **Multiple Emails**: Primary and secondary email management
- **Contact Information**: Phone numbers with verification

### Healthcare Information
- **Emergency Contacts**: Multiple emergency contact management
- **Medical Information**: Allergies, medications, blood type, medical history
- **Healthcare Providers**: Medical practitioner relationships
- **Insurance Information**: Health insurance details and documents
- **Medical Documents**: Secure medical document storage

### Professional Features
- **Business Cards**: Professional healthcare provider cards
- **Credentials**: Professional credentials and certifications
- **Affiliations**: Professional affiliations and memberships
- **Specializations**: Medical specializations and expertise

## 🔧 Advanced Features

### Custom URL System
- **Auto-Generation**: Automatic custom URL username generation
- **Format**: email_prefix.6_random_digits (e.g., john.123456)
- **Validation**: Comprehensive URL validation and uniqueness checking
- **Dual Lookup**: Support for both custom URLs and internal IDs
- **SEO-Friendly**: Human-readable URLs for better user experience

### Document Management
- **File Upload**: Secure file upload with validation
- **Document Types**: Categorized document management
- **File Storage**: Google Cloud Storage integration
- **Access Control**: Secure file access with signed URLs
- **Version Control**: Document version tracking

### Messaging System
- **User-to-User Messaging**: Direct messaging between users
- **Message Threading**: Organized message conversations
- **Notification Integration**: Email notifications for messages
- **Privacy Controls**: Message privacy and blocking features

### Profile Categories
- **Flexible Structure**: User-defined profile categories
- **JSON Content**: Flexible content storage with JSON fields
- **Visibility Control**: Category-level privacy settings
- **Dynamic Management**: Runtime category creation and modification

## 🌐 API Architecture

### RESTful Design
- **Resource-Based URLs**: Clear, resource-oriented endpoint structure
- **HTTP Methods**: Proper HTTP method usage for operations
- **Status Codes**: Appropriate HTTP status code responses
- **Content Negotiation**: JSON-based request/response handling

### User Context
- **User Scoping**: All operations scoped to authenticated user
- **Permission Checking**: User-based permission validation
- **Data Isolation**: User data isolation and privacy protection
- **Access Control**: Granular access control per resource

### Error Handling
- **Consistent Responses**: Standardized error response format
- **Validation Errors**: Detailed field-level validation errors
- **Authentication Errors**: Clear authentication failure messages
- **Business Logic Errors**: Meaningful business rule violation messages

## 📧 Email & Communication

### Email System
- **Verification Emails**: Account verification email delivery
- **Password Reset**: Secure password reset email flow
- **Notification Emails**: System notification delivery
- **Template System**: HTML email templates with branding

### OTP System
- **SMS Integration**: SMS-based OTP delivery
- **Email OTP**: Email-based OTP for mobile signup
- **Rate Limiting**: OTP request rate limiting
- **Expiration Handling**: Automatic OTP expiration

### Notification System
- **Real-time Notifications**: WebSocket-based notifications
- **Email Notifications**: Asynchronous email delivery
- **SMS Notifications**: SMS notification integration
- **Preference Management**: User notification preferences

---

This documentation provides a comprehensive guide to understanding and working with the Django REST Framework implementation in the accounts app. The architecture demonstrates advanced user management patterns while maintaining security, performance, and extensibility.
