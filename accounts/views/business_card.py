from rest_framework import viewsets, permissions, status
from rest_framework.permissions import Is<PERSON><PERSON><PERSON><PERSON>ted, AllowAny
from accounts.models import BusinessCard
from accounts.serializers.serializer_business_card import BusinessCardSerializer
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

class BusinessCardPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        if view.action in ['retrieve', 'list']:
            return True  # AllowAny
        return request.user and request.user.is_authenticated

class BusinessCardViewSet(viewsets.ModelViewSet):
    queryset = BusinessCard.objects.all()
    serializer_class = BusinessCardSerializer
    permission_classes = [BusinessCardPermission]

    def get_queryset(self):
        user_id = self.request.query_params.get('user_id')
        if user_id:
            return BusinessCard.objects.filter(user_id=user_id)
        return super().get_queryset()

    def list(self, request, *args, **kwargs):
        user_id = request.query_params.get('user_id')
        if user_id:
            obj = BusinessCard.objects.filter(user_id=user_id).first()
            if not obj:
                return Response({'detail': 'Not found'}, status=status.HTTP_404_NOT_FOUND)
            serializer = self.get_serializer(obj)
            return Response(serializer.data)
        return super().list(request, *args, **kwargs)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def perform_update(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['post'], url_path='upsert', permission_classes=[IsAuthenticated])
    def upsert(self, request):
        # Remove any extra fields that are not model fields
        data = request.data.copy()
        try:
            instance = BusinessCard.objects.get(user=request.user)
            serializer = self.get_serializer(instance, data=data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save(user=request.user)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except BusinessCard.DoesNotExist:
            serializer = self.get_serializer(data=data)
            serializer.is_valid(raise_exception=True)
            serializer.save(user=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED) 
