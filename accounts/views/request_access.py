import logging
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status

from accounts.models import CustomUser, EmergencyContact, EmergencyMedical, UserInsurance, UserMedicalPractitioner
from common.generate_token import generate_token, verify_token
from roles.gcp_utils import get_private_profile_picture_signed_url
from roles.models import Profile
from upload.views import FileUploadView

logger = logging.getLogger(__name__)
class RequestAccessView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            user = request.user

            requested_user_id = request.data.get('requested_user_id')
            if not requested_user_id:
                return Response({"error": "requested_user_id is required"}, status=status.HTTP_400_BAD_REQUEST)
            requested_user = CustomUser.objects.get(id=requested_user_id)
            if not requested_user:
                return Response({"error": "Requested user not found"}, status=status.HTTP_404_NOT_FOUND)
            
            subject = "Access To Your Account Requested"
            html_content = render_to_string('account/your_account_access_is_requested.html', {
                'requester_email': request.user.email,
            })
            plain_content = strip_tags(html_content)
            
            try:
                send_mail(
                    subject=subject,
                    message=plain_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[requested_user.email],
                    html_message=html_content,
                    fail_silently=False,
                )
            except Exception as e:
                logger.error(f"Failed to send request access email to requested user: {str(e)}")
                return Response({"error": "Failed to send request access email"},
                              status=status.HTTP_503_SERVICE_UNAVAILABLE)
            
            subject = "Request Access"
            html_content = render_to_string('account/request_access.html', {
                'user': user,
            })
            plain_content = strip_tags(html_content)
            
            try:
                send_mail(
                    subject=subject,
                    message=plain_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    html_message=html_content,
                    fail_silently=False,
                )
                return Response({"message": "Access request sent successfully"}, status=status.HTTP_200_OK)
            except Exception as e:
                logger.error(f"Failed to send request access email: {str(e)}")
                return Response({"error": "Failed to send request access email"},
                              status=status.HTTP_503_SERVICE_UNAVAILABLE)
            
            # TODO: add logic to request access to the user later

        except Exception as e:
            logger.error(f"Error requesting access for user {request.user.email}: {str(e)}")
            return Response({"error": "Failed to request access"},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
class GenerateTokenView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        token = generate_token({"user_id": user.id})
        return Response({"token": token}, status=status.HTTP_200_OK)
    
class VerifyTokenView(APIView):
    permission_classes = [AllowAny]
    file_upload_view = FileUploadView()

    def post(self, request):
        token = request.data.get('token')
        if not token:
            return Response({"error": "Token is required"}, status=status.HTTP_400_BAD_REQUEST)
        try:
            user_id = verify_token(token)['user_id']
            user = CustomUser.objects.get(id=user_id)
            profile = Profile.objects.get(user=user)
            emergency_contact = EmergencyContact.objects.filter(user=user).values(
                'id', 'contact_name', 'email', 'phone_number', 'relationship', 'type', 'created_at', 'updated_at'
            )
            emergency_medical = EmergencyMedical.objects.filter(user=user).values(
                'id', 'allergies', 'emergency_medications',
                'blood_type', 'critical_information', 'past_admissions', 'created_at', 'updated_at'
            ).first()

            medical_practitioners = UserMedicalPractitioner.objects.filter(user=user).values()
            insurances = UserInsurance.objects.filter(user=user).values(
                'id', 'provider', 'policy_number', 'type', 'start_date', 'end_date', 'policy_holder_name', 'dependent_information', 'deductable_amount', 'copay_amount', 'additional_document__file', 'group_number'
            )
            for insurance in insurances:
                insurance['additional_document'] = self.file_upload_view.get_signed_url(insurance['additional_document__file'])
                del insurance['additional_document__file']

            result = {
                "basic_information": {
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "middle_name": user.middle_name,
                    "title": profile.title,
                    "email": user.email,
                    "second_email": user.second_email,
                    "phone_number": profile.mobile,
                    "dob": profile.dob,
                    "private_profile_picture": get_private_profile_picture_signed_url(profile.private_profile_picture, user.id) if profile.private_profile_picture else None
                },
                "address_information": {
                    "address": profile.address,
                    "city": profile.city,
                    "state": profile.state,
                    "country": profile.country,
                    "zipcode": profile.zipcode,
                },
                "medical_information": {
                    "genome_tested": profile.genome_tested,
                    "gender": profile.gender,
                    "blood_group": profile.blood_group,
                    "digital_blood": profile.digital_blood,
                },
                "emergency_information": {
                    "contact_information": list(emergency_contact),
                    "medical_information": emergency_medical,
                },
                "medical_team": list(medical_practitioners),
                "insurances": list(insurances),
            }
            return Response({"message": "Token is valid", "user": result}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": f"Error verifying token {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)