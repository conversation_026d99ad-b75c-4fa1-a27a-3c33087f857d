from django.forms import ValidationError
from accounts.models import UserInsurance
from accounts.serializer import UserInsuranceSerializer
from rest_framework.viewsets import ModelViewSet
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from upload.views import FileUploadView

class UserInsuranceViewSet(ModelViewSet):
    serializer_class = UserInsuranceSerializer
    queryset = UserInsurance.objects.all()
    file_upload_view = FileUploadView()
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = UserInsurance.objects.filter(user=self.request.user).select_related('additional_document').order_by('-created_at')
        return queryset
    
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset().values('id', 'provider', 'policy_number', 'type', 'start_date', 'end_date', 'policy_holder_name', 'dependent_information', 'deductable_amount', 'copay_amount', 'additional_document__file', 'group_number')
        for item in queryset:
            if item['additional_document__file']:
                item['additional_document'] = self.file_upload_view.get_signed_url(item['additional_document__file'])
                del item['additional_document__file']
            else:
                item['additional_document'] = None 
        return Response(queryset)
    
    def create(self, request, *args, **kwargs):
        try:
            file = request.FILES.get('additional_document')
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            if not file:
                serializer.save(user=request.user)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            
            uploaded_file = self.file_upload_view.upload_file(file=file, user=request.user)
            if not uploaded_file:
                raise ValidationError("File upload failed")
            serializer.save(user=request.user, additional_document=uploaded_file)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
    def update(self, request, *args, **kwargs):
        
        instance = self.get_object()
        try:
            file = request.FILES.get('additional_document')
            serializer = self.get_serializer(instance, data=request.data)
            serializer.is_valid(raise_exception=True)
            if file:
                uploaded_file = self.file_upload_view.upload_file(file=file, user=request.user)
                serializer.save(user=request.user, additional_document=uploaded_file)
            else:
                serializer.save(update_fields=['provider', 'policy_number', 'type', 'start_date', 'end_date', 'policy_holder_name', 'dependent_information', 'customer_service_phone', 'website', 'deductable_amount', 'copay_amount', 'mode_of_payment', 'payment_due_date'])
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
