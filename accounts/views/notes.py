from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from accounts.models import Note
from accounts.serializer import NoteSerializer
from rest_framework.response import Response
from rest_framework import status
from django.db import transaction
from config.pagination_utils import paginate_queryset


class NoteViewSet(viewsets.ModelViewSet):
    queryset = Note.objects.all()
    serializer_class = NoteSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = Note.objects.filter(user=self.request.user).order_by('-created_at')
        page = self.request.query_params.get('page', 1)
        page_size = self.request.query_params.get('page_size', 10)
        return paginate_queryset(queryset, page, page_size)
    
    def create(self, request, *args, **kwargs):
        data = request.data
        
        if not isinstance(data, list):
            return Response(
                {"message": "Request body must be an array"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            with transaction.atomic():
                # Separate update and create operations
                to_update = []
                to_create = []
                
                # Validate all notes first
                for item in data:
                    if 'id' in item:
                        instance = Note.objects.filter(
                            id=item['id'],
                            user=self.request.user 
                        ).first()
                        if not instance:
                            return Response(
                                {"message": f"Note with id {item['id']} does not exist"},
                                status=status.HTTP_404_NOT_FOUND
                            )
                        to_update.append((instance, item))
                    else:
                        to_create.append(item)

                # Bulk create new notes
                serializer = self.get_serializer(data=to_create, many=True)
                serializer.is_valid(raise_exception=True)
                created_notes = serializer.save(user=self.request.user)
                
                # Update existing notes
                updated_notes = []
                for instance, item in to_update:
                    serializer = self.get_serializer(instance, data=item, partial=True)
                    serializer.is_valid(raise_exception=True)
                    updated_notes.append(serializer.save(user=self.request.user))

                # Combine results
                all_notes = created_notes + updated_notes
                
                return Response(
                    self.get_serializer(all_notes, many=True).data,
                    status=status.HTTP_201_CREATED
                )
                
        except Exception as e:
            return Response(
                {"message": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def delete(self, request, *args, **kwargs):
        try:
            with transaction.atomic():
                # Get query params
                note_ids = self.request.query_params.getlist('id')
                
                if note_ids:
                    # Check if all notes exist in one query
                    existing_notes = Note.objects.filter(
                        id__in=note_ids,
                        user=self.request.user
                    )
                    
                    if existing_notes.count() != len(note_ids):
                        # Find which note ID doesn't exist
                        existing_ids = set(str(note.id) for note in existing_notes)
                        missing_id = next(id for id in note_ids if id not in existing_ids)
                        return Response(
                            {"message": f"Note with ID {missing_id} does not exist"},
                            status=status.HTTP_404_NOT_FOUND
                        )
                    
                    # Bulk delete the notes
                    existing_notes.delete()
                    
                    return Response(
                        {"message": "Notes deleted successfully"},
                        status=status.HTTP_200_OK
                    )
                else:
                    # Delete all notes for the user
                    Note.objects.filter(user=self.request.user).delete()
                    return Response(
                        {"message": "All notes deleted successfully"},
                        status=status.HTTP_200_OK
                    )
                    
        except Exception as e:
            # Handle any exceptions while ensuring rollback
            return Response(
                {"message": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )