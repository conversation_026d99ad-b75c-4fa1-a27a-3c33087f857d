from accounts.models import CustomUser, Message
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

class MessageMeView(APIView):
    def post(self, request):
        message = request.data.get('message', "")
        receiver_id = request.data.get('receiver_id')

        if message == "" or len(message) > 1000:
            return Response({"error": "Message is required and must be less than 1000 characters"}, status=status.HTTP_400_BAD_REQUEST)

        receiver = CustomUser.objects.get(id=receiver_id)
        email = receiver.second_email if receiver.second_email else receiver.email

        Message.objects.create(
            message=message,
            receiver=receiver,
            sender=request.user)
        
        # Send email with reset link
        subject = "New Message from R.A.V.I.D user"
        html_content = render_to_string('account/new_message.html', {
            'sender': request.user.first_name + " " + request.user.last_name,
            'message': message,
            'home_url': settings.SITE_URL
        })
        plain_content = strip_tags(html_content)
        
        try:
            send_mail(
                subject=subject,
                message=plain_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                html_message=html_content,
                fail_silently=False,
            )
            return Response({"message": "Message sent successfully"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)