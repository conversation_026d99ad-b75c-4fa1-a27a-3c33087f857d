from django.db import transaction
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework import serializers
from django.http import Http404
import logging

from accounts.models import EmergencyContact, EmergencyMedical
from accounts.serializer import EmergencyInformationSerializer

logger = logging.getLogger(__name__)

class EmergencyInformationView(APIView):
    permission_classes = [IsAuthenticated]
    
    # GET request to retrieve emergency contacts and medical information for the authenticated user
    def get(self, request):
        try:
            emergency_data = self._get_emergency_data(request.user)
            return Response(emergency_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error retrieving emergency information: {str(e)}")
            return Response({
                "error": "Failed to retrieve emergency information"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    # POST request to create emergency contacts and medical information for the authenticated user
    def _handle_contact(self, user, contact):
        contact_id = contact.get('id')
        if contact_id:
            contact_obj = EmergencyContact.objects.get(id=contact_id, user=user)
            for field, value in contact.items():
                if field != 'id':
                    setattr(contact_obj, field, value)
            contact_obj.save()
            return contact_obj
        return EmergencyContact.objects.create(user=user, **contact)

    def _handle_medical(self, user, medical_data):
        medical_id = medical_data.get('id')
        if medical_id:
            medical_obj = EmergencyMedical.objects.get(id=medical_id, user=user)
            for field, value in medical_data.items():
                if field != 'id':
                    setattr(medical_obj, field, value)
            medical_obj.save()
        else:
            if EmergencyMedical.objects.filter(user=user).exists():
                raise serializers.ValidationError("Medical information already exists")
            EmergencyMedical.objects.create(user=user, **medical_data)

    def post(self, request):
        serializer = EmergencyInformationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            with transaction.atomic():
                contacts = []
                for contact in serializer.validated_data.get('contact_information', []):
                    contacts.append(self._handle_contact(request.user, contact))

                if medical_data := serializer.validated_data.get('medical_information'):
                    self._handle_medical(request.user, medical_data)

                return Response({
                    "message": "Emergency information handled successfully",
                    "data": self._get_emergency_data(request.user)
                }, status=status.HTTP_200_OK)

        except EmergencyContact.DoesNotExist:
            return Response({"error": "Contact not found"}, status=status.HTTP_404_NOT_FOUND)
        except EmergencyMedical.DoesNotExist:
            return Response({"error": "Medical record not found"}, status=status.HTTP_404_NOT_FOUND)
        except serializers.ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error handling emergency information: {str(e)}")
            return Response({"error": "Failed to handle emergency information"},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    # PUT request to update emergency contacts and medical information for the authenticated user
    def _update_contact(self, user, contact_data):
        contact_id = contact_data.pop('id', None)
        if not contact_id:
            return
            
        contact_obj = EmergencyContact.objects.get(id=contact_id, user=user)
        for field, value in contact_data.items():
            setattr(contact_obj, field, value)
        contact_obj.save()

    def _update_medical(self, user, medical_data):
        if not medical_data:
            return
            
        medical_id = medical_data.pop('id', None)
        medical_record = EmergencyMedical.objects.get(
            id=medical_id if medical_id else user.emergency_medical.id,
            user=user
        )
        
        for field, value in medical_data.items():
            setattr(medical_record, field, value)
        medical_record.save()

    def put(self, request):
        serializer = EmergencyInformationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            with transaction.atomic():
                # Update emergency contacts
                for contact in serializer.validated_data.get('contact_information', []):
                    self._update_contact(request.user, contact)
                
                # Update medical information
                self._update_medical(request.user, serializer.validated_data.get('medical_information', {}))

                return Response({
                    "message": "Emergency information updated successfully",
                    "data": self._get_emergency_data(request.user)
                }, status=status.HTTP_200_OK)

        except EmergencyContact.DoesNotExist:
            return Response({"error": "Contact not found"}, status=status.HTTP_404_NOT_FOUND)
        except EmergencyMedical.DoesNotExist:
            return Response({"error": "Medical record not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error updating emergency information: {str(e)}")
            return Response({"error": "Failed to update"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_emergency_data(self, user):
        contacts = EmergencyContact.objects.filter(user=user).values(
            'id', 'contact_name', 'email', 'phone_number', 'relationship', 'type', 'created_at', 'updated_at'
        )
        
        medical = EmergencyMedical.objects.filter(user=user).values(
            'id', 'allergies', 'emergency_medications',
            'blood_type', 'critical_information', 'past_admissions', 'created_at', 'updated_at'
        ).first()

        return {
            "contact_information": list(contacts),
            "medical_information": medical
        }

    # DELETE request to delete emergency contacts and medical information for the authenticated user
    def _delete_contacts(self, user, delete_ids=None):
        contacts = EmergencyContact.objects.filter(user=user)
        if delete_ids:
            contacts = contacts.filter(id__in=delete_ids)
            found_ids = set(str(x) for x in contacts.values_list('id', flat=True))
            missing_ids = set(delete_ids) - found_ids
            
            if missing_ids:
                raise Http404(f"No contacts found with ids {list(missing_ids)}")
                
            deleted_count = contacts.count()
            contacts.delete()
            return f"{deleted_count} emergency contacts deleted successfully"
        
        if not contacts.exists():
            raise Http404("No emergency contacts found")
        contacts.delete()
        return "All emergency contact information deleted successfully"

    def _delete_medical(self, user, delete_ids=None):
        medical = EmergencyMedical.objects.filter(user=user).first()
        if not medical:
            raise Http404("No medical information found")
            
        if delete_ids and str(medical.id) not in delete_ids:
            raise Http404(f"Medical record with id {delete_ids[0]} not found")
            
        medical.delete()
        return "All emergency medical information deleted successfully"

    def delete(self, request):
        user = request.user
        delete_type = request.query_params.get('type')
        delete_ids = request.query_params.getlist('id')

        try:
            with transaction.atomic():
                if delete_type == 'contact':
                    message = self._delete_contacts(user, delete_ids)
                elif delete_type == 'medical':
                    message = self._delete_medical(user, delete_ids)
                else:
                    EmergencyContact.objects.filter(user=user).delete()
                    EmergencyMedical.objects.filter(user=user).delete()
                    message = "All emergency information deleted successfully"

                return Response({"message": message}, status=status.HTTP_200_OK)

        except Http404 as e:
            return Response({"error": str(e)}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error deleting emergency information: {str(e)}")
            return Response(
                {"error": "Failed to delete emergency information"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
