from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import AllowAny
from accounts.serializer import VerifyOTPSerializer
from accounts.views.base import get_tokens_for_user

class VerifyOTPAPI(APIView):
    permission_classes = [AllowAny]
    renderer_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON>]

    def post(self, request):
        serializer = VerifyOTPSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            tokens = get_tokens_for_user(user)
            return Response({
                'status': 200,
                'message': 'OTP verified, account activated.',
                'access': tokens['access'],
                'refresh': tokens['refresh'],
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'is_active': user.is_active,
                    'is_email_verified': user.is_email_verified,
                }
            }, status=status.HTTP_200_OK)
        return Response({'status': 400, 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
        
