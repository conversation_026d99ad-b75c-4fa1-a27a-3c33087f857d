from django.utils import timezone
import random
from django.conf import settings
from rest_framework.permissions import IsAuthenticated
from common import send_sms
from config.pagination_utils import paginate_queryset
from upload.views import FileUploadView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.permissions import IsAdminUser
from django.shortcuts import get_object_or_404
from accounts.models import PendingPhoneVerification, UserVerificationAttempt, VerificationDoc  # Adjust this import based on your User model location
from rest_framework import viewsets
from rest_framework.decorators import action
from accounts.serializer import PhoneVerificationRequestSerializer, UserVerificationAttemptSerializer, VerificationDocSerializer
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail
import logging
from rest_framework.throttling import UserRateThrottle
logger = logging.getLogger(__name__)
class IDVerificationAttemptView(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    file_upload_view = FileUploadView()
    serializer_class = VerificationDocSerializer

    upload_path = "id_documents/"

    def get_serializer(self, *args, **kwargs):
        return VerificationDocSerializer(*args, **kwargs)

    def list(self, request):
        user = request.user
        docs = VerificationDoc.objects.filter(user=user).select_related('file').values('id', 'name', 'description', 'file__file', 'type')
        for doc in docs:
            if doc['file__file']:
                doc['file_url'] = self.file_upload_view.get_signed_url(doc['file__file'])
        return Response(docs)

    
    def create(self, request):

        if request.user.is_id_verified:
            return Response(
                {'error': 'User ID is already verified'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # pending_attempt = UserVerificationAttempt.objects.filter(user=request.user, status='pending').last()
        try:
            pending_attempt = UserVerificationAttempt.objects.get(user=request.user, status='pending')
        except UserVerificationAttempt.DoesNotExist:
            pending_attempt = UserVerificationAttempt.objects.create(user=request.user, status='pending')
        
        files = request.FILES.getlist('files')
        type = request.data.get('type', 'national_id')
        uploaded_files = []
        for file in files:
           uploaded_file = self.file_upload_view.upload_file(file, request.user)
           serializer = self.get_serializer(data={'name': file.name, 'description': '', 'type': type})
           serializer.is_valid(raise_exception=True)
           serializer.save(user=request.user, file=uploaded_file, attempt=pending_attempt)
           uploaded_files.append(serializer.data)
        
        return Response({
            'message': 'ID verification attempt created successfully',
            'uploaded_files': uploaded_files
        })
    
    def destroy(self, request, pk=None):
        verification_doc = VerificationDoc.objects.filter(id=pk, user=request.user).select_related('file').first()
        if verification_doc is None:
            return Response({'error': 'Verification doc not found'}, status=status.HTTP_404_NOT_FOUND)
        self.file_upload_view.delete_file(verification_doc.file.file.name)
        verification_doc.delete()
        return Response({'message': 'Verification doc removed successfully'})

class IDVerificationAdminViewSet(viewsets.ModelViewSet, FileUploadView):
    # #permission_classes = [IsAdminUser]
    queryset = UserVerificationAttempt.objects.all()
    serializer_class = UserVerificationAttemptSerializer

    def list(self, request):
        queryset = UserVerificationAttempt.objects.all().select_related('user').order_by('-created_at')
        page = self.request.query_params.get('page', 1)
        page_size = self.request.query_params.get('page_size', 10)
        attempts = paginate_queryset(queryset, page, page_size)
        for attempt in attempts:
            verification_docs = VerificationDoc.objects.filter(attempt=attempt)
            if verification_docs.exists():  
                file_type = verification_docs.first().type
                attempt.file_type = file_type
        serializer = self.get_serializer(attempts, many=True)
        return Response(serializer.data)
    
    def retrieve(self, request, pk=None):
        attempt = get_object_or_404(UserVerificationAttempt, id=pk)
        # get verification docs related to the attempt
        verification_docs = VerificationDoc.objects.filter(attempt=attempt)
        file_urls = [self.get_signed_url(file.file.file.name) for file in verification_docs]
        file_type = verification_docs.first().type if verification_docs.exists() else None

        return Response({
            'attempt_id': attempt.id,
            'status': attempt.status,
            'file_paths': attempt.file_paths,
            'urls': file_urls,
            'file_type': file_type,
        })

    def update(self, request, pk=None):

        attempt = get_object_or_404(UserVerificationAttempt, id=pk)

        action = request.data.get('action')
        rejection_reason = request.data.get('rejection_reason')
        user = attempt.user
        
        if action not in ['approve', 'reject']:
            return Response(
                {'error': 'Invalid action. Use "approve" or "reject"'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if action == 'approve':
            user.is_id_verified = True
            # user.id_verification_image = attempt.file_paths[0]
            attempt.status = 'approved'
            subject = "ID Verification Approved"
            html_content = render_to_string('account/id_verification_approved.html', {
                'user': user,
                'home_url': settings.SITE_URL,
            })
            plain_content = strip_tags(html_content)
            try:
                send_mail(
                    subject=subject,
                    message=plain_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    html_message=html_content,
                    fail_silently=False,
                )
            except Exception as e:
                logger.error(f"Error sending ID verification approved email: {str(e)}")
        else:
            user.is_id_verified = False
            attempt.status = 'rejected'
            if rejection_reason is not None:
                attempt.rejection_reason = rejection_reason
            subject = "ID Verification Rejected"
            html_content = render_to_string('account/id_verification_rejected.html', {
                'user': user,
                'rejection_reason': rejection_reason or 'No reason provided',
            })
            plain_content = strip_tags(html_content)
            try:
                send_mail(
                    subject=subject,
                    message=plain_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    html_message=html_content,
                    fail_silently=False,
                )
            except Exception as e:
                logger.error(f"Error sending ID verification rejected email: {str(e)}")
                            
        user.save()
        attempt.save()
        
        return Response({
            'user_id': user.id,
            'verification_status': user.is_id_verified,
            'message': f'ID verification {action}d successfully'
        })

class PhoneVerificationRequestView(APIView):
    permission_classes = [IsAuthenticated]
    throttle_classes = [UserRateThrottle]

    def post(self, request):
        user = request.user
        serialized = PhoneVerificationRequestSerializer(data=request.data)
        if not serialized.is_valid():
            return Response(serialized.errors, status=status.HTTP_400_BAD_REQUEST)
        phone_number = serialized.data.get('phone_number')
 
        pending_verification = PendingPhoneVerification.objects.filter(user=user, phone_number=phone_number).first()
        if pending_verification:
            pending_verification.created_at = timezone.now()
            pending_verification.otp = random.randint(100000, 999999)
            pending_verification.save()
        else:
            pending_verification = PendingPhoneVerification.objects.create(user=user, phone_number=phone_number, otp=random.randint(100000, 999999))

        send_sms.send_sms(phone_number, f"Your OTP to verify your phone number with R.A.V.I.D is {pending_verification.otp}")
        return Response({'message': 'Verification code sent'})


class VerifyPhoneView(APIView):
    permission_classes = [IsAuthenticated]
    throttle_classes = [UserRateThrottle]

    def post(self, request):
        user = request.user
        serialized = PhoneVerificationRequestSerializer(data=request.data)
        if not serialized.is_valid():
            return Response(serialized.errors, status=status.HTTP_400_BAD_REQUEST)
        otp = serialized.data.get('otp')
        if otp is None:
            return Response({'error': 'OTP is required'}, status=status.HTTP_400_BAD_REQUEST)

        phone_number = serialized.data.get('phone_number')
        try:
            pending_verification = PendingPhoneVerification.objects.get(user=user, phone_number=phone_number)
        except PendingPhoneVerification.DoesNotExist:
            return Response({'error': 'Pending verification not found'}, status=status.HTTP_400_BAD_REQUEST)

        if pending_verification.is_expired():
            pending_verification.delete()
            return Response({'error': 'Verification code expired'}, status=status.HTTP_400_BAD_REQUEST)

        if pending_verification.otp != otp:
            return Response({'error': 'Invalid OTP'}, status=status.HTTP_400_BAD_REQUEST)

        user.phone_number = phone_number
        user.is_phone_verified = True
        user.save()
        pending_verification.delete()
        return Response({'message': 'Phone number verified successfully'})

