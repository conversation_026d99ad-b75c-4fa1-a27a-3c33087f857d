from django.forms import ValidationError
from rest_framework.viewsets import ModelViewSet
from rest_framework.response import Response
from rest_framework import status
from accounts.models import SupportingDocs
from accounts.serializer import SupportingDocsSerializer, validate_files
from upload.views import FileUploadView
from django.db import transaction
from config.pagination_utils import paginate_queryset

class SupportingDocsViewSet(ModelViewSet):
    serializer_class = SupportingDocsSerializer
    queryset = SupportingDocs.objects.all()

    def get_queryset(self):
        queryset = SupportingDocs.objects.filter(user=self.request.user).select_related('file').order_by('-created_at')
        page = self.request.query_params.get('page', 1)
        page_size = self.request.query_params.get('page_size', 10)
        paginated_queryset = paginate_queryset(queryset, page, page_size)
        return paginated_queryset

    def create(self, request, *args, **kwargs):
        try:
            # Extract files from the request
            files = request.FILES.getlist('file')  # Ensure `file` matches your form field name
            if not files:
                raise ValidationError("No files uploaded.")
            
            validated_files = validate_files(files)
            if isinstance(validated_files, dict) and "error" in validated_files:
                raise ValidationError(validated_files["error"])

            records = []  # Store serialized data for response
            file_upload_view = FileUploadView()
            
            with transaction.atomic():
                # Process each file and create corresponding records
                for i, file in enumerate(files):
                    # Save the uploaded file first
                    uploaded_file = file_upload_view.upload_file(
                        file=file,
                        user=request.user
                    )
                    
                    if not uploaded_file:
                        raise ValidationError("File upload failed")

                    # Create supporting docs record with uploaded file
                    try:
                        data = request.data.copy()
                        # Use the type from request data for each file index
                        type_key = f'type_{i}'
                        if type_key in request.data:
                            data['type'] = request.data[type_key]
                            
                        serializer = self.get_serializer(data=data)
                        if not serializer.is_valid():
                            raise ValidationError(serializer.errors)
                        doc = serializer.save(user=request.user, file=uploaded_file, name='_'.join(file.name.split('_')[1:]))
                    except Exception as e:
                        # Delete the uploaded file if supporting docs creation fails
                        uploaded_file.delete()
                        raise ValidationError(f"Failed to create supporting docs record: {str(e)}")

                    # Add the serialized record to the response list
                    records.append(SupportingDocsSerializer(doc).data)

            # Return a success response with all created records
            return Response(records, status=status.HTTP_201_CREATED)

        except ValidationError as ve:
            return Response({'error': str(ve)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    def delete(self, request, *args, **kwargs):
        try:
            with transaction.atomic():
                # Get query params
                doc_ids = self.request.query_params.getlist('id')
                
                if doc_ids:
                    # Check if all supporting docs exist in one query
                    existing_docs = SupportingDocs.objects.filter(
                        id__in=doc_ids,
                        user=self.request.user
                    ).select_related('file')
                    
                    if existing_docs.count() != len(doc_ids):
                        # Find which doc ID doesn't exist
                        existing_ids = set(str(doc.id) for doc in existing_docs)
                        missing_id = next(id for id in doc_ids if id not in existing_ids)
                        return Response(
                            {"message": f"Supporting document with ID {missing_id} does not exist"},
                            status=status.HTTP_404_NOT_FOUND
                        )
                    
                    # Delete associated files first
                    for doc in existing_docs:
                        if doc.file:
                            doc.file.delete()
                    
                    # Then delete the supporting docs
                    existing_docs.delete()
                    
                    return Response(
                        {"message": "Selected supporting documents and associated files deleted successfully"},
                        status=status.HTTP_200_OK
                    )
                else:
                    # Get all supporting docs with their files
                    docs = SupportingDocs.objects.filter(
                        user=self.request.user
                    ).select_related('file')
                    
                    # Delete associated files first
                    for doc in docs:
                        if doc.file:
                            doc.file.delete()
                            
                    # Then delete all supporting docs
                    docs.delete()
                    
                    return Response(
                        {"message": "All supporting documents and associated files deleted successfully"},
                        status=status.HTTP_200_OK
                    )
                    
        except Exception as e:
            # Handle any exceptions while ensuring rollback
            return Response(
                {"message": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )