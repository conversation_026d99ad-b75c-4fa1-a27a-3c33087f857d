from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated, AllowAny
from roles.permissions import HasProfileAccess
from roles.serializers.profile import UserProfileSerializer
from accounts.helpers import get_user_by_identifier_or_404, validate_custom_url_username, generate_custom_url_suggestions

class ProfileViewSet(viewsets.ModelViewSet):
    serializer_class = UserProfileSerializer
    permission_classes = [HasProfileAccess]
    
    def get_queryset(self):
        # Only return the user's own profile
        return [self.request.user]
    
    def get_object(self):
        """Override to support lookup by custom_url_username or ID"""
        lookup_value = self.kwargs.get('pk')
        
        if lookup_value:
            # Try to get user by identifier (custom_url_username or ID)
            user = get_user_by_identifier_or_404(lookup_value)
            return getattr(user, 'profile', None)
        
        # Default to current user's profile
        return getattr(self.request.user, 'profile', None)
        
    @action(detail=True, methods=['get'])
    def full_profile(self, request, pk=None):
        """
        Get full profile information including sensitive data.
        Requires either:
        1. User accessing their own profile
        2. Valid access token with view_profile permission
        """
        user = self.get_object()
        serializer = self.get_serializer(user)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated])
    def check_custom_url(self, request):
        """Check if custom URL username is available"""
        custom_url_username = request.data.get('custom_url_username', '').strip()
        
        if not custom_url_username:
            return Response({
                'error': 'Custom URL username is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        is_valid, message = validate_custom_url_username(
            custom_url_username, 
            exclude_user=request.user
        )
        
        return Response({
            'available': is_valid,
            'message': message,
            'suggested': generate_custom_url_suggestions(custom_url_username) if not is_valid else []
        })
    
    @action(detail=False, methods=['patch'], permission_classes=[IsAuthenticated])
    def update_custom_url(self, request):
        """Update user's custom URL username"""
        custom_url_username = request.data.get('custom_url_username', '').strip()
        
        if not custom_url_username:
            return Response({
                'error': 'Custom URL username is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate custom URL
        is_valid, message = validate_custom_url_username(
            custom_url_username, 
            exclude_user=request.user
        )
        
        if not is_valid:
            return Response({
                'error': message,
                'suggested': generate_custom_url_suggestions(custom_url_username)
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Update user's custom URL
        request.user.custom_url_username = custom_url_username
        request.user.save()
        
        return Response({
            'message': 'Custom URL updated successfully',
            'custom_url_username': custom_url_username
        })
    
    @action(detail=False, methods=['get'], permission_classes=[AllowAny])
    def by_identifier(self, request):
        """Get profile by custom_url_username or ID (public endpoint)"""
        identifier = request.query_params.get('identifier')
        
        if not identifier:
            return Response({
                'error': 'Identifier (custom_url_username or ID) is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        user = get_user_by_identifier_or_404(identifier)
        profile = getattr(user, 'profile', None)
        
        if not profile:
            return Response({
                'error': 'Profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check privacy settings for public access
        if not request.user.is_authenticated or request.user != user:
            if not getattr(profile, 'is_public_profile', False):
                return Response({
                    'error': 'Profile is private'
                }, status=status.HTTP_403_FORBIDDEN)
        
        serializer = self.get_serializer(profile)
        data = serializer.data
        
        # Remove sensitive fields for public view
        if not request.user.is_authenticated or request.user != user:
            sensitive_fields = ['email', 'second_email', 'phone_number', 'homephone', 
                              'address', 'zipcode', 'is_email_verified', 'is_phone_verified']
            for field in sensitive_fields:
                data.pop(field, None)
        
        return Response(data)