from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from accounts.serializer import GrantAccessSerializer
import logging

logger = logging.getLogger(__name__)
class GrantAccessView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = GrantAccessSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            email = serializer.validated_data['email']

            if email == request.user.email:
                return Response({"error": "Cannot grant access to yourself"}, status=status.HTTP_400_BAD_REQUEST)
            
            subject = "Access Granted"
            html_content = render_to_string('account/grant_access.html', {
                'email': email,
            })
            plain_content = strip_tags(html_content)
            
            try:
                send_mail(
                    subject=subject,
                    message=plain_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[email],
                    html_message=html_content,
                    fail_silently=False,
                )
                return Response({"message": "Access granted successfully"}, status=status.HTTP_200_OK)
            except Exception as e:
                logger.error(f"Failed to send grant access email: {str(e)}")
                return Response({"error": "Failed to send grant access email"}, 
                              status=status.HTTP_503_SERVICE_UNAVAILABLE)
            
            # TODO: add logic to grant access to the user later
                          
        except Exception as e:
            logger.error(f"Error granting access for email { email }: {str(e)}")
            return Response({"error": "Failed to grant access"},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)
