from rest_framework.permissions import IsAuthenticated
from rest_framework import viewsets, serializers, status
from rest_framework.response import Response

from accounts.serializer import UserMedicalPractitionerSerializer
from ..models import UserMedicalPractitioner
import logging

logger = logging.getLogger(__name__)

class UserMedicalPractitionerViewSet(viewsets.ModelViewSet):
    queryset = UserMedicalPractitioner.objects.all()
    serializer_class = UserMedicalPractitionerSerializer
    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        try:
            user = request.user
            serializer = self.get_serializer(data=request.data)
            serializer.validate_user(user)
            serializer.is_valid(raise_exception=True)
            serializer.save(user=user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except serializers.ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating medical practitioner: {str(e)}")
            return Response(
                {'error': 'Failed to create medical practitioner'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        try:
            self.perform_destroy(instance)
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            logger.error(f"Error deleting medical practitioner: {str(e)}")
            return Response(
                {'error': 'Failed to delete medical practitioner'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def list(self, request, *args, **kwargs):
        try:
            medical_practitioners = UserMedicalPractitioner.objects.filter(user=request.user)
            serializer = self.get_serializer(medical_practitioners, many=True)
            # Transform serialized data into a dictionary with role as the key
            practitioners_by_role = {}
            for practitioner in serializer.data:
                role = practitioner["role"]
                practitioners_by_role[role] = practitioner
        
            return Response(practitioners_by_role)
        except Exception as e:
            logger.error(f"Error listing medical practitioners: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve medical practitioners'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        try:
            serializer = self.get_serializer(instance, data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            return Response(serializer.data)
        except serializers.ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error updating medical practitioner: {str(e)}")
            return Response(
                {'error': 'Failed to update medical practitioner'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        try:
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            return Response(serializer.data)
        except serializers.ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error partially updating medical practitioner: {str(e)}")
            return Response(
                {'error': 'Failed to update medical practitioner'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
