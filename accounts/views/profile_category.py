from django.http import Http404
from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from ..models import ProfileCategory, ProfileCategoryContent
from ..serializer import ProfileCategoryContentSerializer, ProfileCategorySerializer
from rest_framework.decorators import action
from django.db import transaction
import logging
from config.pagination_utils import paginate_queryset

logger = logging.getLogger(__name__)

class ProfileCategoryViewSet(viewsets.ModelViewSet):
    serializer_class = ProfileCategorySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return ProfileCategory.objects.filter(user=self.request.user)
    
    def get_serializer(self, *args, **kwargs):
        if self.action == 'content':
            return ProfileCategoryContentSerializer(*args, **kwargs)
        return super().get_serializer(*args, **kwargs)

    def get_object(self):
        try:
            category = ProfileCategory.objects.prefetch_related('profilecategorycontent_set').get(
                id=self.kwargs['pk'],
                user=self.request.user
            )
            # Add contents to the category object
            category.contents = category.profilecategorycontent_set.all().values('id', 'content')
            return category
        except ProfileCategory.DoesNotExist:
            raise Http404("Profile category not found")

    def list(self, request, *args, **kwargs):
        try:
            page = self.request.query_params.get('page', 1)
            page_size = self.request.query_params.get('page_size', 10)
            category_id = self.request.query_params.get('id', None)
            
            # Get all categories with their related content
            queryset = ProfileCategory.objects.filter(
                user=self.request.user,
                hidden=False  # Only get non-hidden categories
            ).prefetch_related(
                'profilecategorycontent_set'  # Get all related content
            ).order_by('created_at')
            
            # Filter by ID if provided
            if category_id:
                queryset = queryset.filter(id=category_id)
            
            # Filter by name if provided
            name = self.request.query_params.get('name', None)
            if name:
                queryset = queryset.filter(name__icontains=name)
                
            paginated_queryset = paginate_queryset(queryset, page, page_size)
            serializer = self.get_serializer(paginated_queryset, many=True)
            
            # Add contents to each category in response
            response_data = serializer.data
            for category in response_data:
                category_contents = ProfileCategoryContent.objects.filter(
                    profile_category_id=category['id']
                ).values('id', 'content')
                category['contents'] = [
                    {'id': content['id'], 'content': content['content']} 
                    for content in category_contents
                ]
                
            return Response(response_data)
            
        except Exception as e:
            logger.error(f"Error listing profile categories: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve profile categories'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def create(self, request, *args, **kwargs):
        # Set default hidden value if not provided
        if 'hidden' not in request.data:
            request.data['hidden'] = False
            
        # Pop contents before serializer validation since it's not a model field
        contents = request.data.pop('contents', [])
        
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            with transaction.atomic():
                # Create profile category
                profile_category = serializer.save(user=request.user)
                
                # Create profile category contents if provided
                if contents:
                    for content in contents:
                        ProfileCategoryContent.objects.create(
                            profile_category=profile_category, 
                            content=content
                        )
                        
                # Add contents back to serializer data
                serializer_data = serializer.data
                serializer_data['contents'] = contents
                
                return Response(serializer_data, status=status.HTTP_201_CREATED)
                
        except Exception as e:
            logger.error(f"Failed to create profile category: {str(e)}")
            return Response(
                {"error": "Failed to create profile category"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        
        # Set default hidden value if not provided
        if 'hidden' not in request.data:
            request.data['hidden'] = False
            
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            with transaction.atomic():
                # Create profile category
                profile_category = serializer.save(user=request.user)
                contents = request.data.pop('contents', [])
                
                # Create profile category contents if provided
                if contents:
                    for content in contents:
                        _id = content.get('id', None)
                        content_body = content.get('content', None)
                        ProfileCategoryContent.objects.filter(id=_id).update(
                            profile_category=profile_category, 
                            content=content_body
                        )
                        
                # Add contents back to serializer data
                serializer_data = serializer.data
                serializer_data['contents'] = contents
                return Response(serializer_data, status=status.HTTP_200_OK)
            # # Update category fields
            # serializer.save()
            # return Response(serializer.data)
                
        except Exception as e:
            logger.error(f"Failed to update profile category: {str(e)}")
            return Response(
                {"error": "Failed to update profile category"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def destroy(self, request, *args, **kwargs):
        try:
            # Retrieve the object using the pk from the URL
            instance = ProfileCategory.objects.get(pk=kwargs['pk'], user=request.user)
            self.perform_destroy(instance)
            return Response({"message": "Profile category deleted successfully"}, status=status.HTTP_200_OK)
        except ProfileCategory.DoesNotExist:
            return Response(
                {"error": "Profile category not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Failed to delete profile category: {str(e)}")
            return Response(
                {"error": "Failed to delete profile category"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    def _create_content(self, category, contents):
        if not isinstance(contents, list):
            return Response(
                {"error": "Request data must be a list of content objects"},
                status=status.HTTP_400_BAD_REQUEST
            )

        created_contents = [
            ProfileCategoryContent.objects.create(
                profile_category=category,
                content=content
            ) for content in contents
        ]
        serializer = ProfileCategoryContentSerializer(created_contents, many=True)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def _update_content(self, category, content_id, data):
        try:
            content = ProfileCategoryContent.objects.get(
                id=content_id,
                profile_category=category
            )
            
            # Get existing content data
            existing_content = content.content
            
            # If existing_content is not a dict, initialize it
            if not isinstance(existing_content, dict):
                existing_content = {}
                
            # Update or add new key-value pairs from request data
            for key, value in data.items():
                existing_content[key] = value
                
            # Create serializer with updated content
            serializer = ProfileCategoryContentSerializer(
                content,
                data={'content': existing_content},
                partial=True
            )
            
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        except ProfileCategoryContent.DoesNotExist:
            return Response(
                {"error": "Content not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error updating content: {str(e)}")
            return Response(
                {"error": "Failed to update content"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _delete_content(self, category):
        content_ids = self.request.query_params.getlist('id')
        if not content_ids:
            # Delete all content for this category if no IDs provided
            deleted_count = ProfileCategoryContent.objects.filter(
                profile_category=category
            ).delete()[0]
        else:
            # Delete specific content items if IDs are provided
            deleted_count = ProfileCategoryContent.objects.filter(
                id__in=content_ids,
                profile_category=category
            ).delete()[0]
        if not deleted_count:
            return Response(
                {"error": "No matching content found"},
                status=status.HTTP_404_NOT_FOUND
            ) 
        return Response(
            {"message": f"Successfully deleted {deleted_count} content items"},
            status=status.HTTP_200_OK
        )

    
    def _get_content_object(self, category, content_id=None):
        try:
            if content_id:
                return ProfileCategoryContent.objects.get(id=content_id, profile_category=category)
            return ProfileCategoryContent.objects.filter(profile_category=category).order_by('created_at')
        except ProfileCategoryContent.DoesNotExist:
            raise Http404("Content not found")
    
    def _get_content(self, category, content_id=None):
        try:
            if content_id:
                # Get single object if content_id provided
                content = self._get_content_object(category, content_id)
                serializer = ProfileCategoryContentSerializer(content)
                return Response(serializer.data)
            
            # Get paginated list if no content_id
            contents = self._get_content_object(category)
            page = self.request.query_params.get('page', 1)
            page_size = self.request.query_params.get('page_size', 10)
            paginated_contents = paginate_queryset(contents, page, page_size)
            serializer = ProfileCategoryContentSerializer(paginated_contents, many=True)
            return Response(serializer.data)
        except Http404:
            return Response(
                {"error": "Content not found"},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['get', 'post', 'put', 'delete'], url_path='contents(?:/(?P<content_id>[^/.]+))?')
    def content(self, request, *args, **kwargs):
        try:
            category = ProfileCategory.objects.get(id=kwargs.get('pk'), user=request.user)
            content_id = kwargs.get('content_id')

            if request.method == 'GET':
                return self._get_content(category, content_id)
            elif request.method == 'POST':
                return self._create_content(category, request.data)
            elif request.method == 'PUT':
                if not content_id:
                    return Response(
                        {"error": "Content ID is required"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                return self._update_content(category, content_id, request.data)
            elif request.method == 'DELETE':
                return self._delete_content(category)

        except (ProfileCategory.DoesNotExist, ProfileCategoryContent.DoesNotExist) as e:
            error_msg = "Category not found or access denied" if isinstance(e, ProfileCategory.DoesNotExist) else "Content not found"
            return Response({"error": error_msg}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error handling content: {str(e)}")
            return Response(
                {"error": "Failed to process content request"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _update_category(self,user , serializer: ProfileCategorySerializer, contents=None):

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        category =serializer.save(user=user)

        # Create profile category contents if provided
        if contents:
            for content in contents:
                content_body = content.get('content', None)
                ProfileCategoryContent.objects.create(
                    profile_category=category, 
                    content=content_body
                )
        
        return Response(serializer.data)
    
    @action(detail=False, methods=['put'])
    def all(self, request, *args, **kwargs):
        # Same as update but for all categories
        request_categories = request.data.get('categories', [])
        ProfileCategory.objects.filter(user=request.user).delete()
        for category in request_categories:
            # Set default hidden value if not provided
            if 'hidden' not in category:
                category['hidden'] = False
            contents = category.pop('contents', [])
            # instance = ProfileCategory.objects.get(id=category['id'], user=request.user)
            serializer: ProfileCategorySerializer = self.get_serializer(data=category, partial=True)
            self._update_category(request.user, serializer, contents)
        return Response({"message": "Categories updated successfully"},status=status.HTTP_200_OK)