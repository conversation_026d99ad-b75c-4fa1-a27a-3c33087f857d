from rest_framework import serializers

from upload.views import FileUploadView
from .constant import MAXIMUM_FILE_COUNT, MAXIMUM_FILE_SIZE, MAXIMUM_TOTAL_FILE_SIZE, ALLOWED_TYPES, MAXIMUM_FILE_SIZE_MB, MAXIMUM_TOTAL_FILE_SIZE_MB

file_upload_view = FileUploadView()

def validate_files(files):
    # Check number of files in current request
    # TODO: dynamic max file count based on user tier
    if len(files) > MAXIMUM_FILE_COUNT:
        return {"error": f"Maximum {MAXIMUM_FILE_COUNT} files allowed per upload"}
    
    if len(files) == 0:
        return {"error": "No files uploaded"}

    total_size = 0
    allowed_types = ALLOWED_TYPES

    for file in files:
        # Check individual file size (2MB limit)
        if file.size > MAXIMUM_FILE_SIZE:  # 2MB in bytes
            return {"error": f"File {file.name} exceeds {MAXIMUM_FILE_SIZE_MB}MB limit"}

        # Validate file type
        if file.content_type not in allowed_types:
            return {"error": f"Invalid file type for {file.name}. Allowed types: PDF, DOC, PNG, JPG, JPEG, MOV"}

        total_size += file.size

    # Check total upload size
    # TODO: dynamic max total file size based on user tier
    if total_size > MAXIMUM_TOTAL_FILE_SIZE:  # 10MB in bytes
        return {"error": f"Free tier limit: Total upload size cannot exceed {MAXIMUM_TOTAL_FILE_SIZE_MB}MB"}

    return files

def get_file_data(obj):
        if not obj.file:
            return None
        return {
            'id': obj.file.id,
            'name': obj.file.filename,
            'url': file_upload_view.get_signed_url(str(obj.file.file)),
            'type': obj.file.file_type
        }

import re
import uuid
import random
from django.shortcuts import get_object_or_404
from django.http import Http404
from .models import CustomUser

def get_user_by_identifier(identifier):
    """
    Get user by custom_url_username or ID
    Facebook-style lookup: try custom_url_username first, then ID
    """
    try:
        # Try custom_url_username first
        return CustomUser.objects.get(custom_url_username=identifier)
    except CustomUser.DoesNotExist:
        try:
            # Fallback to ID
            return CustomUser.objects.get(id=identifier)
        except CustomUser.DoesNotExist:
            return None

def get_user_by_identifier_or_404(identifier):
    """Get user by identifier or raise 404"""
    user = get_user_by_identifier(identifier)
    if not user:
        raise Http404("User not found")
    return user

def validate_custom_url_username(custom_url_username, exclude_user=None):
    """Validate custom_url_username format and availability"""
    
    # Format validation
    if not re.match(r'^[a-zA-Z0-9._-]+$', custom_url_username):
        return False, "Custom URL can only contain letters, numbers, dots, underscores, and hyphens"
    
    if len(custom_url_username) < 3 or len(custom_url_username) > 50:
        return False, "Custom URL must be between 3 and 50 characters"
    
    # Reserved words
    reserved = ['admin', 'api', 'www', 'mail', 'support', 'help', 'about', 'contact', 'user', 'profile', 
                'settings', 'dashboard', 'login', 'signup', 'register', 'auth', 'oauth', 'callback']
    if custom_url_username.lower() in reserved:
        return False, "This custom URL is not available"
    
    # Uniqueness check
    query = CustomUser.objects.filter(custom_url_username=custom_url_username)
    if exclude_user:
        query = query.exclude(id=exclude_user.id)
    
    if query.exists():
        return False, "This custom URL is already taken"
    
    return True, "Custom URL is available"

def generate_custom_url_suggestions(base_custom_url):
    """Generate alternative custom URL suggestions"""
    suggestions = []
    
    # Remove any existing numbers at the end
    base_clean = re.sub(r'\.\d+$', '', base_custom_url)
    
    for i in range(5):  # Generate 5 suggestions
        suggestion = f"{base_clean}.{random.randint(100, 9999)}"
        if validate_custom_url_username(suggestion)[0]:
            suggestions.append(suggestion)
        
        # If we can't find valid suggestions, try with different patterns
        if len(suggestions) < 3:
            suggestion = f"{base_clean}{random.randint(10, 99)}"
            if validate_custom_url_username(suggestion)[0]:
                suggestions.append(suggestion)
    
    return list(set(suggestions))[:3]  # Return max 3 unique suggestions