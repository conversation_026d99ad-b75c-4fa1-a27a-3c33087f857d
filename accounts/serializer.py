from datetime import timedelta, timezone
from functools import cache
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth import authenticate
from .models import CustomUser, SupportingDocs, UserInsurance, UserMedicalPractitioner, UserVerificationAttempt, ProfileCategory, ProfileCategoryContent, UserMedicalPractitioner, Note, VerificationDoc
from .fields import Base64ImageField
from rest_framework import serializers
from django.core.mail import send_mail
from django.conf import settings
from django.urls import reverse
from django.contrib.sites.shortcuts import get_current_site
from .models import TemporaryUser, CustomUser
import logging
from django.contrib.auth import authenticate
from .send_email import send_email
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from roles.models import Role
from django.utils.timezone import now
from django.core.cache import cache
from .constant import BLOOD_TYPE_CHOICES, EMAIL_CHANGE_MAX_ATTEMPTS, EMAIL_CHANGE_WINDOW_SECONDS
from .helpers import get_file_data, validate_files
import random


logger = logging.getLogger(__name__)

class RegisterSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    is_clinic_signup = serializers.BooleanField(required=False)
    is_enterprise_signup = serializers.BooleanField(required=False)
    is_mobile_signup = serializers.BooleanField(required=False, default=False)
    # name = serializers.CharField(required=False)  # Add this line
    # role = serializers.CharField(required=False)

    class Meta:
        model = TemporaryUser
        fields = ['email', 'password', 'is_clinic_signup', 'is_enterprise_signup', 'is_mobile_signup']
    def validate(self, data):
        logger.info(f"Validating data: {data}")
        return super().validate(data)

    def validate_email(self, value):
        logger.info(f"Validating email: {value}")
        if CustomUser.objects.filter(email=value).exists():
            raise serializers.ValidationError("This email is already verified and in use.")
        return value

    # def validate_role(self, value):
    #     logger.info(f"Validating role: {value}")
    #     available_roles = list(Role.objects.values_list('name', flat=True))
    #     logger.info(f"Available roles: {available_roles}")
    #     if not Role.objects.filter(name=value).exists():
    #         raise serializers.ValidationError(f"Invalid role specified. Available roles are: {', '.join(available_roles)}")
    #     return value

    def create(self, validated_data):
        logger.info(f"Creating user with data: {validated_data}")
        email = validated_data['email']
        password = validated_data['password']
        is_clinic_signup = validated_data['is_clinic_signup']
        is_enterprise_signup = validated_data['is_enterprise_signup']
        is_mobile_signup = validated_data['is_mobile_signup']
        # name = validated_data['name']  # Get the name
        # role = validated_data['role']
        
        # Check if a CustomUser with this email exists but is not verified
        existing_user = CustomUser.objects.filter(email=email, is_email_verified=False).first()
        if existing_user:
            # Update the existing user's password
            existing_user.set_password(password)
            # existing_user.name = name  # Set the name
            # existing_user.assign_role(role)
            existing_user.save()
            return existing_user
        
        # Check if a TemporaryUser with this email already exists
        temp_user = TemporaryUser.objects.filter(email=email).first()
        if temp_user:
            # Update the existing temporary user's password
            temp_user.password = password
            # temp_user.name = name  # Set the name
            # temp_user.role = role
            temp_user.save()
        else:
            # Create a new TemporaryUser
            temp_user = TemporaryUser.objects.create(
                email=email, 
                password=password,
                is_clinic_signup=is_clinic_signup,
                is_enterprise_signup=is_enterprise_signup,
                is_mobile_signup=is_mobile_signup,
                # name=name, # Set the name
                # role=role

            )
        if is_mobile_signup:
            self.send_otp_email(temp_user)
        else:
            self.send_verification_email(temp_user)
        return temp_user
    
    
    def send_verification_email(self, temp_user):
        verification_link = f"{settings.SITE_URL}/api/verify-email/{temp_user.verification_token}/"
        subject = "Welcome to R.A.V.I.D. - Please Verify Your Email"
        
        context = {
            'verification_link': verification_link,
            'user_email': temp_user.email,
            # 'user_name': temp_user.name
        }
        
        html_content = render_to_string('account/verification_email.html', context)
        plain_content = strip_tags(html_content)
        
        try:
            send_email(
                to_email=temp_user.email,
                subject=subject,
                html_content=html_content,
                plain_content=plain_content
            )
            logger.info(f"Verification email sent to {temp_user.email}")
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            raise serializers.ValidationError("Failed to send verification email")

    def send_otp_email(self, temp_user):
        # Sinh OTP 6 số
        otp = f"{random.randint(100000, 999999)}"
        # Lưu vào Redis (cache), key: otp:<email>, expiry 10 phút
        cache.set(f"otp:{temp_user.email}", otp, timeout=600)
        subject = "R.A.V.I.D. - Your OTP Code"
        context = {
            'otp_code': otp,
            'user_email': temp_user.email,
        }
        # Dùng template giống verify email, chỉ khác nội dung là mã OTP
        html_content = render_to_string('account/otp_email.html', context)
        plain_content = strip_tags(html_content)
        try:
            send_email(
                to_email=temp_user.email,
                subject=subject,
                html_content=html_content,
                plain_content=plain_content
            )
            logger.info(f"OTP email sent to {temp_user.email}")
        except Exception as e:
            logger.error(f"Failed to send OTP email: {str(e)}")
            raise serializers.ValidationError("Failed to send OTP email")



class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    # name = serializers.CharField(read_only=True)  # Add this line
    token = serializers.CharField(read_only=True)
    # role = serializers.CharField(read_only=True)  # Add this line


    def validate(self, data):
        email = data.get("email")
        password = data.get("password")

        # Try to authenticate using email or username
        user = authenticate(
            request=self.context.get("request"), email=email, password=password
        )
        if user is None:
            raise serializers.ValidationError("Invalid email or password")
        if not user.is_email_verified:
            raise serializers.ValidationError("Email is not verified")

        data["user"] = user
        # data["name"] = user.name  # Add the name to the validated data
        # data["role"] = user.role.name if user.role else None  # Add this line
        return data




# Add these new serializers
class PasswordResetSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        User = get_user_model()
        if not User.objects.filter(email=value).exists():
            raise serializers.ValidationError("No user found with this email address.")
        return value

class PasswordResetConfirmSerializer(serializers.Serializer):
    new_password = serializers.CharField(min_length=8, max_length=128)
    token = serializers.CharField()
    uidb64 = serializers.CharField()

    def validate(self, data):
        try:
            uid = urlsafe_base64_decode(data['uidb64']).decode()
            user = CustomUser.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, CustomUser.DoesNotExist):
            raise serializers.ValidationError("Invalid reset link")

        if not default_token_generator.check_token(user, data['token']):
            raise serializers.ValidationError("Invalid reset link")

        data['user'] = user
        return data

class EmailChangeSerializer(serializers.Serializer):
    new_email = serializers.EmailField(required=True)

    def _check_email_in_use(self, email):
        if CustomUser.objects.filter(email=email).exists():
            raise serializers.ValidationError(
                "This email address is already in use. Please try a different one."
            )

    def _format_time(self, seconds):
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def _check_rate_limit(self, user_id):
        cache_key = f"email_change_attempts_user_{user_id}"
        attempts = cache.get(cache_key, {"count": 0, "timestamp": now()})
        time_elapsed = (now() - attempts["timestamp"]).total_seconds()

        if time_elapsed > EMAIL_CHANGE_WINDOW_SECONDS:
            attempts = {"count": 1, "timestamp": now()}
        else:
            attempts["count"] += 1
            if attempts["count"] > EMAIL_CHANGE_MAX_ATTEMPTS:
                time_remaining = EMAIL_CHANGE_WINDOW_SECONDS - time_elapsed
                time_msg = self._format_time(time_remaining)
                raise serializers.ValidationError(
                    f"Too many email change attempts. Please try again in {time_msg}."
                )

        cache.set(cache_key, attempts, timeout=EMAIL_CHANGE_WINDOW_SECONDS)

    def validate_new_email(self, value):
        self._check_email_in_use(value)
        
        request = self.context.get("request")
        if not request or not request.user.is_authenticated:
            raise serializers.ValidationError("You must be authenticated to change email.")

        self._check_rate_limit(request.user.id)
        return value

class EmergencyContactInformationSerializer(serializers.Serializer):
    id = serializers.CharField(required=False)
    contact_name = serializers.CharField(max_length=100, required=False, allow_blank=True)
    email = serializers.EmailField(required=False, allow_blank=True)
    phone_number = serializers.CharField(max_length=20, required=False, allow_blank=True)
    relationship = serializers.CharField(max_length=100, required=False, allow_blank=True)
    type = serializers.CharField(max_length=100, required=False, allow_blank=True)

class EmergencyMedicalInformationSerializer(serializers.Serializer):
    id = serializers.CharField(required=False)
    allergies = serializers.ListField(
        child=serializers.CharField(max_length=200),
        required=False,
        default=list
    )
    emergency_medications = serializers.ListField(
        child=serializers.CharField(max_length=200),
        required=False,
        default=list
    )
    blood_type = serializers.ChoiceField(
        choices=BLOOD_TYPE_CHOICES,
        required=False
    )
    critical_information = serializers.CharField(allow_blank=True, required=False)
    past_admissions = serializers.CharField(allow_blank=True, required=False)

class EmergencyInformationSerializer(serializers.Serializer):
    contact_information = EmergencyContactInformationSerializer(many=True, required=False)
    medical_information = EmergencyMedicalInformationSerializer(required=False)

class UserMedicalPractitionerSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserMedicalPractitioner
        fields = ['id', 'name', 'email', 'contact_number', 'affiliation', 'role']

    def validate_user(self, user):
        if UserMedicalPractitioner.objects.filter(user=user).count() > 6:
            raise serializers.ValidationError("User can only have upto 6 medical practitioners")
        return user

    def validate(self, data):
        return data

class GrantAccessSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)

    def validate_email(self, value):
        if not CustomUser.objects.filter(email=value).exists():
            raise serializers.ValidationError("No user found with this email address.")
        return value

class ProfileCategoryContentSerializer(serializers.ModelSerializer):
    id = serializers.CharField(required=False)
    content = serializers.JSONField(required=False)

    class Meta:
        model = ProfileCategoryContent
        fields = ['id', 'content']

class ProfileCategorySerializer(serializers.ModelSerializer):
    name = serializers.CharField(max_length=1000, required=False)
    description = serializers.CharField(required=False)
    hidden = serializers.BooleanField(required=False)
    contents = serializers.ListField(child=serializers.JSONField(), required=False)

    class Meta:
        model = ProfileCategory
        fields = ['id', 'name', 'description', 'hidden', 'contents']

class UserVerificationAttemptSerializer(serializers.ModelSerializer):
    user_id = serializers.CharField(source='user.id', read_only=True)
    user_name = serializers.CharField(source='user.name', read_only=True)
    user_first_name = serializers.CharField(source='user.first_name', read_only=True)
    user_last_name = serializers.CharField(source='user.last_name', read_only=True)
    file_type = serializers.CharField(read_only=True)

    class Meta:
        model = UserVerificationAttempt
        fields = ['id', 'created_at', 'status', 'file_paths', 'rejected_reason', 'user_id', 'user_first_name', 'user_last_name', 'user_name', 'file_type']
    

class PhoneVerificationRequestSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=20, required=True)
    otp = serializers.CharField(max_length=6, required=False)

class NoteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Note
        fields = ['id', 'title', 'description', 'created_at', 'updated_at']

class SupportingDocsSerializer(serializers.ModelSerializer):
    file_data = serializers.SerializerMethodField()

    class Meta:
        model = SupportingDocs
        fields = ['id', 'name', 'description', 'created_at', 'updated_at', 'file_data', 'type']
        read_only_fields = ['file_data']

    def get_file_data(self, obj):
        return get_file_data(obj)

    def validate_files(self, value):
        return validate_files(value)

class UserInsuranceSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserInsurance
        exclude = ['additional_document', 'user']

class VerificationDocSerializer(serializers.ModelSerializer):
    class Meta:
        model = VerificationDoc
        exclude = ['user', 'file']


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for the CustomUser model.
    Used for general user information representation.
    """
    class Meta:
        model = get_user_model()
        fields = [
            'id', 'email', 'first_name', 'last_name', 'middle_name', 
            'phone_number', 'is_active', 'is_email_verified', 
            'is_phone_verified', 'date_joined', 'last_login',
            'is_clinic_signup', 'is_enterprise_signup'
        ]
        read_only_fields = [
            'id', 'email', 'is_active', 'is_email_verified', 
            'is_phone_verified', 'date_joined', 'last_login'
        ] 

class VerifyOTPSerializer(serializers.Serializer):
    email = serializers.EmailField()
    otp = serializers.CharField(max_length=6)

    def validate(self, data):
        email = data.get('email')
        otp = data.get('otp')
        from django.core.cache import cache
        from .models import TemporaryUser, CustomUser
        temp_user = TemporaryUser.objects.filter(email=email, is_mobile_signup=True).first()
        if not temp_user:
            raise serializers.ValidationError('No pending mobile signup for this email.')
        cache_key = f"otp:{email}"
        cached_otp = cache.get(cache_key)
        if not cached_otp or cached_otp != otp:
            raise serializers.ValidationError('OTP invalid or expired.')
        data['temp_user'] = temp_user
        return data

    def save(self):
        temp_user = self.validated_data['temp_user']
        from .models import CustomUser
        # Tạo hoặc xác thực user
        user = CustomUser.objects.filter(email=temp_user.email).first()
        if user:
            user.is_active = True
            user.is_email_verified = True
            user.save()
        else:
            user = CustomUser.objects.create_user(
                email=temp_user.email,
                password=temp_user.password,
                is_active=True,
                is_email_verified=True
            )
        # Xoá OTP khỏi Redis và xoá TemporaryUser
        from django.core.cache import cache
        cache.delete(f"otp:{temp_user.email}")
        temp_user.delete()
        return user 

class InvitationRegisterSerializer(serializers.ModelSerializer):
    """
    Serializer for invitation-based registration
    Requires a valid invitation code to register
    """
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    invitation_code = serializers.CharField(max_length=50, required=True, write_only=True)
    is_clinic_signup = serializers.BooleanField(required=False, default=False)
    is_enterprise_signup = serializers.BooleanField(required=False, default=False)
    is_mobile_signup = serializers.BooleanField(required=False, default=False)

    class Meta:
        model = TemporaryUser
        fields = ['email', 'password', 'invitation_code', 'is_clinic_signup', 'is_enterprise_signup', 'is_mobile_signup']

    def validate_email(self, value):
        """Validate email is not already in use"""
        logger.info(f"Validating email: {value}")
        if CustomUser.objects.filter(email=value).exists():
            raise serializers.ValidationError("This email is already verified and in use.")
        return value

    def validate_invitation_code(self, value):
        """Validate invitation code exists and is valid"""
        from content_management.models import InvitationCode
        
        value = value.strip().upper()
        logger.info(f"Validating invitation code: {value}")
        
        try:
            invitation = InvitationCode.objects.get(code=value)
        except InvitationCode.DoesNotExist:
            raise serializers.ValidationError("Invalid invitation code")
        
        is_valid, message = invitation.is_valid()
        if not is_valid:
            raise serializers.ValidationError(message)
        
        # Store the invitation object for later use
        self.invitation = invitation
        return value

    def create(self, validated_data):
        """Create temporary user and use invitation code"""
        # Remove invitation_code from validated_data since it's not a field in TemporaryUser
        invitation_code = validated_data.pop('invitation_code')
        
        email = validated_data['email']
        password = validated_data['password']
        is_clinic_signup = validated_data.get('is_clinic_signup', False)
        is_enterprise_signup = validated_data.get('is_enterprise_signup', False)
        is_mobile_signup = validated_data.get('is_mobile_signup', False)
        
        logger.info(f"Creating invitation-based user with data: {validated_data}")
        
        # Check if a CustomUser with this email exists but is not verified
        existing_user = CustomUser.objects.filter(email=email, is_email_verified=False).first()
        if existing_user:
            # Update the existing user's password
            existing_user.set_password(password)
            existing_user.save()
            temp_user = existing_user
        else:
            # Check if a TemporaryUser with this email already exists
            temp_user = TemporaryUser.objects.filter(email=email).first()
            if temp_user:
                # Update the existing temporary user's password
                temp_user.password = password
                temp_user.is_clinic_signup = is_clinic_signup
                temp_user.is_enterprise_signup = is_enterprise_signup
                temp_user.is_mobile_signup = is_mobile_signup
                temp_user.save()
            else:
                # Create a new TemporaryUser
                temp_user = TemporaryUser.objects.create(
                    email=email, 
                    password=password,
                    is_clinic_signup=is_clinic_signup,
                    is_enterprise_signup=is_enterprise_signup,
                    is_mobile_signup=is_mobile_signup,
                )
        
        # Use the invitation code
        invitation = getattr(self, 'invitation', None)
        if invitation:
            invitation.use_code()
            logger.info(f"Invitation code {invitation.code} used successfully")
        
        # Send verification email or OTP
        if is_mobile_signup:
            self.send_otp_email(temp_user)
        else:
            self.send_verification_email(temp_user)
            
        return temp_user
    
    def send_verification_email(self, temp_user):
        """Send verification email (same as original RegisterSerializer)"""
        verification_link = f"{settings.SITE_URL}/api/verify-email/{temp_user.verification_token}/"
        subject = "Welcome to R.A.V.I.D. - Please Verify Your Email"
        
        context = {
            'verification_link': verification_link,
            'user_email': temp_user.email,
        }
        
        html_content = render_to_string('account/verification_email.html', context)
        plain_content = strip_tags(html_content)
        
        try:
            send_email(
                to_email=temp_user.email,
                subject=subject,
                html_content=html_content,
                plain_content=plain_content
            )
            logger.info(f"Verification email sent to {temp_user.email}")
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            raise serializers.ValidationError("Failed to send verification email")

    def send_otp_email(self, temp_user):
        """Send OTP email (same as original RegisterSerializer)"""
        # Sinh OTP 6 số
        otp = f"{random.randint(100000, 999999)}"
        # Lưu vào Redis (cache), key: otp:<email>, expiry 10 phút
        cache.set(f"otp:{temp_user.email}", otp, timeout=600)
        subject = "R.A.V.I.D. - Your OTP Code"
        context = {
            'otp_code': otp,
            'user_email': temp_user.email,
        }
        # Dùng template giống verify email, chỉ khác nội dung là mã OTP
        html_content = render_to_string('account/otp_email.html', context)
        plain_content = strip_tags(html_content)
        try:
            send_email(
                to_email=temp_user.email,
                subject=subject,
                html_content=html_content,
                plain_content=plain_content
            )
            logger.info(f"OTP email sent to {temp_user.email}")
        except Exception as e:
            logger.error(f"Failed to send OTP email: {str(e)}")
            raise serializers.ValidationError("Failed to send OTP email") 