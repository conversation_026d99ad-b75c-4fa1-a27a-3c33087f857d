from django.test import TestCase
from accounts.models import CustomUser
from accounts.helpers import get_user_by_identifier, validate_custom_url_username

class CustomURLTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.user = CustomUser.objects.create_user(
            email='<EMAIL>',
            first_name='<PERSON>',
            last_name='<PERSON><PERSON>',
            password='testpass123'
        )
    
    def test_auto_generate_custom_url_username(self):
        """Test that custom_url_username is auto-generated with email prefix format"""
        self.assertIsNotNone(self.user.custom_url_username)
        self.assertTrue(self.user.custom_url_username.startswith('test.'))
        # Should have 6 digit suffix
        suffix = self.user.custom_url_username.split('.')[-1]
        self.assertEqual(len(suffix), 6)
        self.assertTrue(suffix.isdigit())

    def test_email_prefix_extraction(self):
        """Test custom_url_username generation with different email formats"""
        # Test with complex email
        user2 = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.assertTrue(user2.custom_url_username.startswith('johndoetest.'))

        # Test with short email prefix
        user3 = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.assertTrue(user3.custom_url_username.startswith('userab.'))

        # Test with numeric email prefix
        user4 = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.assertTrue(user4.custom_url_username.startswith('123user.'))
    
    def test_custom_url_validation_valid_cases(self):
        """Test valid custom URL validation"""
        valid_urls = [
            'john.doe.1234',
            'user_123',
            'doctor-smith',
            'test.user.999'
        ]
        
        for url in valid_urls:
            is_valid, message = validate_custom_url_username(url)
            self.assertTrue(is_valid, f"'{url}' should be valid: {message}")
    
    def test_custom_url_validation_invalid_cases(self):
        """Test invalid custom URL validation"""
        invalid_cases = [
            ('ab', 'Too short'),
            ('admin', 'Reserved word'),
            ('user@123', 'Invalid character'),
            ('a' * 51, 'Too long'),
            ('', 'Empty string'),
        ]
        
        for url, reason in invalid_cases:
            is_valid, message = validate_custom_url_username(url)
            self.assertFalse(is_valid, f"'{url}' should be invalid ({reason})")
    
    def test_custom_url_uniqueness(self):
        """Test that duplicate custom URLs are not allowed"""
        custom_url = 'unique.test.123'
        
        # Create user with specific custom URL
        user1 = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        user1.custom_url_username = custom_url
        user1.save()
        
        # Try to validate same URL for different user
        is_valid, message = validate_custom_url_username(custom_url)
        self.assertFalse(is_valid)
        self.assertIn('already taken', message)
    
    def test_get_user_by_identifier_with_custom_url(self):
        """Test getting user by custom_url_username"""
        found_user = get_user_by_identifier(self.user.custom_url_username)
        self.assertEqual(found_user, self.user)
    
    def test_get_user_by_identifier_with_id(self):
        """Test getting user by ID as fallback"""
        found_user = get_user_by_identifier(self.user.id)
        self.assertEqual(found_user, self.user)
    
    def test_get_user_by_identifier_not_found(self):
        """Test getting user with non-existent identifier"""
        found_user = get_user_by_identifier('nonexistent.user.999')
        self.assertIsNone(found_user)
    
    def test_custom_url_update(self):
        """Test updating custom URL"""
        new_url = 'updated.user.456'
        
        # Validate it's available
        is_valid, message = validate_custom_url_username(new_url, exclude_user=self.user)
        self.assertTrue(is_valid)
        
        # Update the URL
        self.user.custom_url_username = new_url
        self.user.save()
        
        # Verify the update
        updated_user = get_user_by_identifier(new_url)
        self.assertEqual(updated_user, self.user)
    
    def test_custom_url_exclude_current_user(self):
        """Test validation excludes current user for updates"""
        current_url = self.user.custom_url_username
        
        # Should be valid when excluding current user
        is_valid, message = validate_custom_url_username(current_url, exclude_user=self.user)
        self.assertTrue(is_valid)
        
        # Should be invalid when not excluding
        is_valid, message = validate_custom_url_username(current_url)
        self.assertFalse(is_valid) 