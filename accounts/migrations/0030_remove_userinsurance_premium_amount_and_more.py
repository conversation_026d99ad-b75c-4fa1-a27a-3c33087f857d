# Generated by Django 5.0.9 on 2025-01-14 12:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0029_customuser_middle_name'),
        ('upload', '0002_uploadedfile_file_type_uploadedfile_file_url'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userinsurance',
            name='premium_amount',
        ),
        migrations.AddField(
            model_name='userinsurance',
            name='copay_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='userinsurance',
            name='deductable_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='userinsurance',
            name='additional_document',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_insurance_additional_document', to='upload.uploadedfile'),
        ),
        migrations.AlterField(
            model_name='userinsurance',
            name='type',
            field=models.CharField(blank=True, choices=[('health', 'Health'), ('dental', 'Dental'), ('vision', 'Vision')], max_length=100),
        ),
    ]
