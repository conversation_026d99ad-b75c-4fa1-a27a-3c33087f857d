# Generated by Django 5.0.9 on 2025-05-12 09:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0042_customuser_google_credentials'),
        ('enterprise', '0006_remove_enterprise_user_enterprise_owner_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='enterprise',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='enterprise.enterprise'),
        ),
    ]
