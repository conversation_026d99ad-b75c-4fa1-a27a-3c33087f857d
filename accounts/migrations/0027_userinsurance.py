# Generated by Django 5.0.9 on 2024-12-24 16:02

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0026_supportingdocs'),
        ('upload', '0002_uploadedfile_file_type_uploadedfile_file_url'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserInsurance',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.CharField(blank=True, max_length=100)),
                ('policy_number', models.CharField(blank=True, max_length=100)),
                ('type', models.CharField(blank=True, choices=[('health', 'Health'), ('death', 'Death'), ('vision', 'Vision')], max_length=100)),
                ('start_date', models.DateField(blank=True, null=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('policy_holder_name', models.CharField(blank=True, max_length=100)),
                ('dependent_information', models.TextField(blank=True)),
                ('customer_service_phone', models.CharField(blank=True, max_length=15)),
                ('website', models.CharField(blank=True, max_length=100)),
                ('premium_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('mode_of_payment', models.CharField(blank=True, choices=[('credit_card', 'Credit Card'), ('debit_card', 'Debit Card'), ('bank_transfer', 'Bank Transfer')], max_length=100)),
                ('payment_due_date', models.DateField(blank=True, null=True)),
                ('additional_document', models.OneToOneField(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_insurance_additional_document', to='upload.uploadedfile')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_insurance', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
