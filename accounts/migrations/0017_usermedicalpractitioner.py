# Generated by Django 5.0.9 on 2024-12-08 08:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0016_emergencycontact_emergencymedical'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserMedicalPractitioner',
            fields=[
                ('id', models.Char<PERSON>ield(editable=False, max_length=10, primary_key=True, serialize=False)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('contact_number', models.CharField(blank=True, max_length=20)),
                ('affiliation', models.CharField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('role', models.Char<PERSON>ield(choices=[('PP', 'Primary Physician'), ('OG', 'Obstetrician and Gynecologist'), ('GN', 'Geneticist'), ('SN', 'Special Needs'), ('DN', 'Dentist'), ('OT', 'Other')], max_length=100)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
