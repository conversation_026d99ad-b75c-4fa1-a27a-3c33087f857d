# Generated by Django 5.0.9 on 2025-02-23 07:52

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0035_customuser_is_enterprise_signup_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userverificationattempt',
            name='file_path',
        ),
        migrations.AddField(
            model_name='userverificationattempt',
            name='file_paths',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.TextField(), blank=True, default=list, size=None),
        ),
    ]
