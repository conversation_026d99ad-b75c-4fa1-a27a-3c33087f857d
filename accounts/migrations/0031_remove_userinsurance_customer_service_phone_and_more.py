# Generated by Django 5.0.9 on 2025-01-15 12:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0030_remove_userinsurance_premium_amount_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userinsurance',
            name='customer_service_phone',
        ),
        migrations.RemoveField(
            model_name='userinsurance',
            name='mode_of_payment',
        ),
        migrations.RemoveField(
            model_name='userinsurance',
            name='payment_due_date',
        ),
        migrations.RemoveField(
            model_name='userinsurance',
            name='website',
        ),
        migrations.AddField(
            model_name='userinsurance',
            name='group_number',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='userinsurance',
            name='type',
            field=models.CharField(blank=True, choices=[('health', 'Health'), ('dental', 'Dental'), ('vision', 'Vision'), ('other', 'Other')], max_length=100),
        ),
    ]
