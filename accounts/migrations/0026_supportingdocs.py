# Generated by Django 5.1.3 on 2024-12-17 15:37

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0025_create_note'),
        ('upload', '0002_uploadedfile_file_type_uploadedfile_file_url'),
    ]

    operations = [
        migrations.CreateModel(
            name='SupportingDocs',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.TextField(blank=True)),
                ('description', models.TextField(blank=True)),
                ('type', models.CharField(blank=True, choices=[('prescription', 'Prescription'), ('diagnosis', 'Diagnosis'), ('other', 'Other')], max_length=100)),
                ('file', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='upload.uploadedfile')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
