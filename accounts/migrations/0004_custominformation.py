# Generated by Django 5.0.9 on 2024-10-11 17:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_profile_username'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomInformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_information', to='accounts.profile')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
