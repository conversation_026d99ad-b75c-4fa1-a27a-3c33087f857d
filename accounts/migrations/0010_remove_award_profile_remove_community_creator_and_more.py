# Generated by Django 5.0.9 on 2024-10-15 17:31

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0009_alter_award_description_alter_award_profile_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='award',
            name='profile',
        ),
        migrations.RemoveField(
            model_name='community',
            name='creator',
        ),
        migrations.RemoveField(
            model_name='community',
            name='members',
        ),
        migrations.RemoveField(
            model_name='credentialdocument',
            name='profile',
        ),
        migrations.RemoveField(
            model_name='custominformation',
            name='profile',
        ),
        migrations.RemoveField(
            model_name='education',
            name='profile',
        ),
        migrations.RemoveField(
            model_name='practicelocation',
            name='profile',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='user',
        ),
        migrations.RemoveField(
            model_name='youtubevideo',
            name='profile',
        ),
        migrations.RemoveField(
            model_name='researchpaper',
            name='profile',
        ),
        migrations.DeleteModel(
            name='ActivityLog',
        ),
        migrations.DeleteModel(
            name='Award',
        ),
        migrations.DeleteModel(
            name='Community',
        ),
        migrations.DeleteModel(
            name='CredentialDocument',
        ),
        migrations.DeleteModel(
            name='CustomInformation',
        ),
        migrations.DeleteModel(
            name='Education',
        ),
        migrations.DeleteModel(
            name='PracticeLocation',
        ),
        migrations.DeleteModel(
            name='YouTubeVideo',
        ),
        migrations.DeleteModel(
            name='Profile',
        ),
        migrations.DeleteModel(
            name='ResearchPaper',
        ),
    ]
