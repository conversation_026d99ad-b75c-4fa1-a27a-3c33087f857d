# Generated by Django 5.0.9 on 2024-12-10 14:33

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0019_alter_usermedicalpractitioner_role'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='id_verification_image',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='is_id_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='UserVerificationAttempt',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], max_length=100)),
                ('file_path', models.TextField()),
                ('rejected_reason', models.TextField(blank=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
