# Generated by Django 5.1.3 on 2024-12-09 15:15

import uuid6
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0017_usermedicalpractitioner'),
    ]

    operations = [
        migrations.RunSQL(
            '''
            DO $$
            BEGIN
                IF EXISTS (SELECT 1 FROM accounts_emergencycontact) THEN
                    DELETE FROM accounts_emergencycontact;
                END IF;
                
                IF EXISTS (SELECT 1 FROM accounts_emergencymedical) THEN
                    DELETE FROM accounts_emergencymedical;
                END IF;
                
                IF EXISTS (SELECT 1 FROM accounts_usermedicalpractitioner) THEN
                    DELETE FROM accounts_usermedicalpractitioner;
                END IF;
            END $$;
            '''
        ),
        migrations.AlterField(
            model_name='emergencycontact',
            name='id',
            field=models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='emergencymedical',
            name='id',
            field=models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='usermedicalpractitioner',
            name='id',
            field=models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False),
        ),
    ]
