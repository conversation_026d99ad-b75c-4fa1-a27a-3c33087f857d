# Generated by Django 5.1.3 on 2024-12-12 12:27

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0021_customuser_phone_number_pendingphoneverification'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProfileCategory',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(blank=True, max_length=1000)),
                ('description', models.TextField(blank=True)),
                ('hidden', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProfileCategoryContent',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content', models.JSONField(blank=True, default=dict)),
                ('profile_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.profilecategory')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
