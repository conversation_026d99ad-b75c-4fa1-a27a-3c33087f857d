# Generated by Django 5.0.9 on 2024-10-15 17:04

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0008_alter_profile_profile_picture'),
        ('roles', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='award',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='award',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='awards_list', to='accounts.profile'),
        ),
        migrations.AlterField(
            model_name='award',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='award',
            name='year',
            field=models.IntegerField(),
        ),
        migrations.AlterField(
            model_name='community',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='community',
            name='members',
            field=models.ManyToManyField(blank=True, related_name='communities', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='community',
            name='name',
            field=models.CharField(max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name='credentialdocument',
            name='document',
            field=models.FileField(upload_to='credential_documents/'),
        ),
        migrations.AlterField(
            model_name='credentialdocument',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credential_documents', to='accounts.profile'),
        ),
        migrations.AlterField(
            model_name='credentialdocument',
            name='uploaded_at',
            field=models.DateTimeField(auto_now_add=True),
        ),
        migrations.AlterField(
            model_name='custominformation',
            name='description',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='custominformation',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_information', to='accounts.profile'),
        ),
        migrations.AlterField(
            model_name='custominformation',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='email',
            field=models.EmailField(max_length=254, unique=True),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='first_name',
            field=models.CharField(blank=True, max_length=30),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='id',
            field=models.CharField(editable=False, max_length=10, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='is_email_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='is_staff',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='last_name',
            field=models.CharField(blank=True, max_length=30),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='name',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='profile_image',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='role',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='roles.role'),
        ),
        migrations.AlterField(
            model_name='education',
            name='degree',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='education',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='education',
            name='end_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='education',
            name='field_of_study',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='education',
            name='institution',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='education',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='education_list', to='accounts.profile'),
        ),
        migrations.AlterField(
            model_name='education',
            name='start_date',
            field=models.DateField(),
        ),
        migrations.AlterField(
            model_name='practicelocation',
            name='address',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='practicelocation',
            name='name',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='practicelocation',
            name='phone',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AlterField(
            model_name='practicelocation',
            name='website',
            field=models.URLField(blank=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='about_me',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='affiliations',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='awards',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='bio',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='credential_submitted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='credential_verification_notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='credentials',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='education',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='is_credentials_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='profile',
            name='locations',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='practices',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='profile_picture',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='research_papers',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='speciality',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='title',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='username',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='youtube_videos',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='researchpaper',
            name='journal',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='researchpaper',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='research_papers_list', to='accounts.profile'),
        ),
        migrations.AlterField(
            model_name='researchpaper',
            name='publication_date',
            field=models.DateField(),
        ),
        migrations.AlterField(
            model_name='researchpaper',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='researchpaper',
            name='url',
            field=models.URLField(blank=True),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='email',
            field=models.EmailField(max_length=254),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='name',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='password',
            field=models.CharField(max_length=128),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='role',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='verification_token',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='youtubevideo',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='youtubevideo',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='youtube_videos_list', to='accounts.profile'),
        ),
        migrations.AlterField(
            model_name='youtubevideo',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='youtubevideo',
            name='url',
            field=models.URLField(),
        ),
    ]
