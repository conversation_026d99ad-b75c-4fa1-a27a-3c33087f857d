# Generated by Django 5.0.9 on 2024-10-12 11:37

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0004_custominformation'),
        ('roles', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='award',
            name='description',
            field=models.TextField(blank=True, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='award',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='awards_list', to='accounts.profile', verbose_name='Profile'),
        ),
        migrations.AlterField(
            model_name='award',
            name='title',
            field=models.CharField(max_length=255, verbose_name='Title'),
        ),
        migrations.AlterField(
            model_name='award',
            name='year',
            field=models.IntegerField(verbose_name='Year'),
        ),
        migrations.AlterField(
            model_name='community',
            name='description',
            field=models.TextField(blank=True, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='community',
            name='members',
            field=models.ManyToManyField(blank=True, related_name='communities', to=settings.AUTH_USER_MODEL, verbose_name='Members'),
        ),
        migrations.AlterField(
            model_name='community',
            name='name',
            field=models.CharField(max_length=100, unique=True, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='credentialdocument',
            name='document',
            field=models.FileField(upload_to='credential_documents/', verbose_name='Document'),
        ),
        migrations.AlterField(
            model_name='credentialdocument',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credential_documents', to='accounts.profile', verbose_name='Profile'),
        ),
        migrations.AlterField(
            model_name='credentialdocument',
            name='uploaded_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Uploaded At'),
        ),
        migrations.AlterField(
            model_name='custominformation',
            name='description',
            field=models.TextField(verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='custominformation',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_information', to='accounts.profile', verbose_name='Profile'),
        ),
        migrations.AlterField(
            model_name='custominformation',
            name='title',
            field=models.CharField(max_length=255, verbose_name='Title'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='email',
            field=models.EmailField(max_length=254, unique=True, verbose_name='Email'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='first_name',
            field=models.CharField(blank=True, max_length=30, verbose_name='First Name'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='id',
            field=models.CharField(editable=False, max_length=10, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='Is Active'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='is_email_verified',
            field=models.BooleanField(default=False, verbose_name='Is Email Verified'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='is_staff',
            field=models.BooleanField(default=False, verbose_name='Is Staff'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='last_name',
            field=models.CharField(blank=True, max_length=30, verbose_name='Last Name'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='name',
            field=models.CharField(blank=True, max_length=255, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='profile_image',
            field=models.TextField(blank=True, verbose_name='Profile Image'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='role',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='roles.role', verbose_name='Role'),
        ),
        migrations.AlterField(
            model_name='education',
            name='degree',
            field=models.CharField(max_length=255, verbose_name='Degree'),
        ),
        migrations.AlterField(
            model_name='education',
            name='description',
            field=models.TextField(blank=True, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='education',
            name='end_date',
            field=models.DateField(blank=True, null=True, verbose_name='End Date'),
        ),
        migrations.AlterField(
            model_name='education',
            name='field_of_study',
            field=models.CharField(max_length=255, verbose_name='Field of Study'),
        ),
        migrations.AlterField(
            model_name='education',
            name='institution',
            field=models.CharField(max_length=255, verbose_name='Institution'),
        ),
        migrations.AlterField(
            model_name='education',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='education_list', to='accounts.profile', verbose_name='Profile'),
        ),
        migrations.AlterField(
            model_name='education',
            name='start_date',
            field=models.DateField(verbose_name='Start Date'),
        ),
        migrations.AlterField(
            model_name='practicelocation',
            name='address',
            field=models.TextField(verbose_name='Address'),
        ),
        migrations.AlterField(
            model_name='practicelocation',
            name='name',
            field=models.CharField(max_length=255, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='practicelocation',
            name='phone',
            field=models.CharField(blank=True, max_length=20, verbose_name='Phone'),
        ),
        migrations.AlterField(
            model_name='practicelocation',
            name='website',
            field=models.URLField(blank=True, verbose_name='Website'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='about_me',
            field=models.TextField(blank=True, null=True, verbose_name='About Me'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='affiliations',
            field=models.TextField(blank=True, null=True, verbose_name='Affiliations'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='awards',
            field=models.TextField(blank=True, null=True, verbose_name='Awards'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='bio',
            field=models.TextField(blank=True, null=True, verbose_name='Bio'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='credential_submitted_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Credential Submitted At'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='credential_verification_notes',
            field=models.TextField(blank=True, null=True, verbose_name='Credential Verification Notes'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='credentials',
            field=models.TextField(blank=True, null=True, verbose_name='Credentials'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='education',
            field=models.TextField(blank=True, null=True, verbose_name='Education'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='is_credentials_verified',
            field=models.BooleanField(default=False, verbose_name='Is Credentials Verified'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='locations',
            field=models.TextField(blank=True, null=True, verbose_name='Locations'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='practices',
            field=models.TextField(blank=True, null=True, verbose_name='Practices'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='profile_picture',
            field=models.ImageField(blank=True, null=True, upload_to='profile_pictures/', verbose_name='Profile Picture'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='research_papers',
            field=models.TextField(blank=True, null=True, verbose_name='Research Papers'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='speciality',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Speciality'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='title',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Title'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='username',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Username'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='youtube_videos',
            field=models.TextField(blank=True, null=True, verbose_name='YouTube Videos'),
        ),
        migrations.AlterField(
            model_name='researchpaper',
            name='journal',
            field=models.CharField(max_length=255, verbose_name='Journal'),
        ),
        migrations.AlterField(
            model_name='researchpaper',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='research_papers_list', to='accounts.profile', verbose_name='Profile'),
        ),
        migrations.AlterField(
            model_name='researchpaper',
            name='publication_date',
            field=models.DateField(verbose_name='Publication Date'),
        ),
        migrations.AlterField(
            model_name='researchpaper',
            name='title',
            field=models.CharField(max_length=255, verbose_name='Title'),
        ),
        migrations.AlterField(
            model_name='researchpaper',
            name='url',
            field=models.URLField(blank=True, verbose_name='URL'),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='Created At'),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='email',
            field=models.EmailField(max_length=254, verbose_name='Email'),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='name',
            field=models.CharField(blank=True, max_length=255, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='password',
            field=models.CharField(max_length=128, verbose_name='Password'),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='role',
            field=models.CharField(max_length=100, verbose_name='Role'),
        ),
        migrations.AlterField(
            model_name='temporaryuser',
            name='verification_token',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='Verification Token'),
        ),
        migrations.AlterField(
            model_name='youtubevideo',
            name='description',
            field=models.TextField(blank=True, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='youtubevideo',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='youtube_videos_list', to='accounts.profile', verbose_name='Profile'),
        ),
        migrations.AlterField(
            model_name='youtubevideo',
            name='title',
            field=models.CharField(max_length=255, verbose_name='Title'),
        ),
        migrations.AlterField(
            model_name='youtubevideo',
            name='url',
            field=models.URLField(verbose_name='URL'),
        ),
    ]
