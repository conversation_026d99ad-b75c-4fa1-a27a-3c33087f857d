import random
import string

def generate_custom_id():
    # Greek lowercase letters
    # greek_letters = 'αβγδεζηθικλμνξοπρστυφχψω'

    special_chars = '012345673246249832789'
    all_chars = special_chars
    
    # Generate 8 characters (to make total length 10 including 'RA')
    chars = ''.join(random.choices(all_chars, k=10))
    
    return chars
    # return f"RA{chars}"

def get_unique_custom_id(model):
    while True:
        custom_id = generate_custom_id()
        if not model.objects.filter(id=custom_id).exists():
            return custom_id