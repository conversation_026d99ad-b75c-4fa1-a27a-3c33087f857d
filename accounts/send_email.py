from django.core.mail import send_mail
import logging

import environ

env = environ.Env()
environ.Env.read_env()

logger = logging.getLogger(__name__)

def send_email(to_email, subject, html_content, plain_content):
    try:
        from_email = env('RELAYHOST_USERNAME')

        send_mail(
            subject=subject,
            message=plain_content,
            from_email=None,
            recipient_list=[to_email],
            html_message=html_content,
            fail_silently=False,
        )
        logger.info(f"Email sent successfully to {to_email}")
    except Exception as e:
        logger.error(f"Failed to send email: {str(e)}")
        raise
