# Accounts/models.py
import uuid
from django.db import models
from django.contrib.auth.models import (
    AbstractBaseUser,
    BaseUserManager,
    PermissionsMixin,
    Permission,
    Group,
)
from django.utils import timezone
import logging
import base64
from django.core.files.base import ContentFile

from roles.models.role import Role
from .uuid_utils import get_unique_custom_id
from django.utils.translation import gettext_lazy as _
from storages.backends.gcloud import GoogleCloudStorage
from .constant import EMAIL_CHANGE_TOKEN_EXPIRY_TIME, BLOOD_TYPE_CHOICES, INSURANCE_TYPES, MODE_OF_PAYMENT, SUPPORTING_DOCS_TYPES, USER_VERIFICATION_STATUS_CHOICES, PHONE_VERIFICATION_TOKEN_EXPIRY_TIME, VERIFICATION_DOC_TYPES
from django.contrib.postgres.fields import Array<PERSON>ield
from config.models import BaseModel
from upload.models import UploadedFile

logger = logging.getLogger(__name__)

class CustomUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(email, password, **extra_fields)


class CustomUser(AbstractBaseUser, PermissionsMixin):
    # id = models.UUIDField(default=uuid.uuid4, primary_key=True, editable=False)
    is_id_verified = models.BooleanField(default=False)
    id_verification_image = models.TextField(blank=True)
    id = models.CharField(primary_key=True, max_length=10, editable=False)
    
    # NEW: Custom URL username field
    custom_url_username = models.CharField(
        max_length=50,
        unique=True,
        null=True,
        blank=True,
        db_index=True,
        help_text="Unique username for profile URL (e.g., john.doe.1234)"
    )
    
    email = models.EmailField(unique=True)
    second_email = models.EmailField(blank=True)
    first_name = models.CharField(max_length=30, blank=True)
    last_name = models.CharField(max_length=30, blank=True)
    middle_name = models.CharField(max_length=30, blank=True)
    name = models.CharField(max_length=255, blank=True)
    profile_image = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_email_verified = models.BooleanField(default=False)
    role = models.ForeignKey('roles.Role', on_delete=models.SET_NULL, null=True, blank=True)
    is_clinic_signup = models.BooleanField(default=False)
    is_enterprise_signup = models.BooleanField(default=False)
    enterprise = models.ForeignKey('enterprise.Enterprise', null=True, blank=True, on_delete=models.SET_NULL, related_name='users')
    
    phone_number = models.CharField(max_length=15, blank=True)
    is_phone_verified = models.BooleanField(default=False)
    paid_for_verification = models.BooleanField(default=False)

    date_joined = models.DateTimeField(default=timezone.now)
    objects = CustomUserManager()

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    groups = models.ManyToManyField(Group, related_name="customuser_set", blank=True)
    user_permissions = models.ManyToManyField(
        Permission,
        verbose_name=_('user permissions'),
        blank=True,
        help_text=_('Specific permissions for this user.'),
        related_name="customuser_set",
        related_query_name="customuser",
    )

    google_credentials = models.JSONField(
        null=True,
        blank=True,
        help_text="Stores Google OAuth credentials for calendar integration"
    )

    def __str__(self):
        return self.email
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip() or self.email
    
    def save(self, *args, **kwargs):
        if not self.id:
            self.id = get_unique_custom_id(CustomUser)
        
        # Auto-generate custom_url_username if not set
        if not self.custom_url_username:
            self.custom_url_username = self.generate_custom_url_username()
            
        super().save(*args, **kwargs)
    
    def generate_custom_url_username(self):
        """Generate username: email_prefix.6_random_digits (e.g., john.123456)"""
        import re
        import random

        # Extract email prefix (part before @)
        if self.email:
            email_prefix = self.email.split('@')[0].lower()
            # Remove special characters, keep only alphanumeric
            email_prefix = re.sub(r'[^a-z0-9]', '', email_prefix)
            # Ensure minimum length
            if len(email_prefix) < 3:
                email_prefix = f"user{email_prefix}"
        else:
            email_prefix = f"user{self.id}" if self.id else "user"

        # Add 6 random numbers
        for _ in range(100):  # Max 100 attempts
            random_suffix = f"{random.randint(100000, 999999)}"
            candidate = f"{email_prefix}.{random_suffix}"

            if not CustomUser.objects.filter(custom_url_username=candidate).exists():
                return candidate

        # Fallback with UUID
        return f"{email_prefix}.{uuid.uuid4().hex[:6]}"

    def assign_role(self, role_name):
        try:
            role = Role.objects.get(name=role_name)
            self.role = role
            self.save()
            self.groups.clear()
            if role.group:
                self.groups.add(role.group)
            else:
                logger.warning(f"Role {role_name} has no associated group")
            self.user_permissions.set(role.permissions.all())
        except Role.DoesNotExist:
            logger.error(f"Role '{role_name}' does not exist")
            raise ValueError(f"Role '{role_name}' does not exist")

class TemporaryUser(models.Model):
    verification_token = models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True)
    email = models.EmailField()  # This won't be unique
    name = models.CharField(max_length=255, blank=True)
    password = models.CharField(max_length=128)
    created_at = models.DateTimeField(default=timezone.now)
    role = models.CharField(max_length=100)  # Add this field
    is_clinic_signup = models.BooleanField(default=False)
    is_enterprise_signup = models.BooleanField(default=False)
    is_mobile_signup = models.BooleanField(default=False)
    
    def __str__(self):
        return f"Token: {self.verification_token}"

class PendingEmailChange(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    new_email = models.EmailField() # This won't be unique
    token = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    created_at = models.DateTimeField(default=timezone.now)

    def is_expired(self):
        time_elapsed = timezone.now() - self.created_at
        return time_elapsed.total_seconds() > EMAIL_CHANGE_TOKEN_EXPIRY_TIME

class EmergencyContact(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE) #Can create multiple emergency contacts for a user
    contact_name = models.CharField(max_length=100, blank=True)
    phone_number = models.CharField(max_length=15, blank=True)
    email = models.EmailField(blank=True)
    relationship = models.CharField(max_length=100, blank=True)
    type = models.CharField(max_length=100, blank=True)

    def save(self, *args, **kwargs):
        if not self.id or self.id == '':
            self.id = get_unique_custom_id(EmergencyContact)
        super().save(*args, **kwargs)

class EmergencyMedical(BaseModel):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name='emergency_medical') # One user has one emergency medical
    allergies = ArrayField(
        models.CharField(max_length=200),
        blank=True,
        default=list
    )
    critical_information = models.TextField(blank=True)
    blood_type = models.CharField(
        max_length=10,
        blank=True,
        choices=BLOOD_TYPE_CHOICES
    )
    emergency_medications = ArrayField(
        models.CharField(max_length=200),
        blank=True,
        default=list
    )

    past_admissions = models.TextField(blank=True)

    def save(self, *args, **kwargs):
        if not self.id or self.id == '':
            self.id = get_unique_custom_id(EmergencyMedical)
        super().save(*args, **kwargs)
class UserMedicalPractitioner(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    email = models.EmailField(blank=True)
    contact_number = models.CharField(max_length=20, blank=True)
    affiliation = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    role = models.CharField(max_length=100)
    
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.role} - {self.name} - {self.affiliation}"

class ProfileCategory(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    name = models.CharField(blank=True, max_length=1000)
    description = models.TextField(blank=True)
    hidden = models.BooleanField(default=False)

class ProfileCategoryContent(BaseModel):
    profile_category = models.ForeignKey(ProfileCategory, on_delete=models.CASCADE)
    content = models.JSONField(blank=True, default=dict)

class UserVerificationAttempt(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    status = models.CharField(max_length=100, choices=USER_VERIFICATION_STATUS_CHOICES)
    file_paths = ArrayField(models.TextField(), blank=True, default=list)
    rejected_reason = models.TextField(blank=True)


    def save(self, *args, **kwargs):
        # if user idVerified is True, then should not allowed to make another verification attempt
        if not self.pk and self.user.is_id_verified:
            raise ValueError("User ID is already verified")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.email} - {self.id} - {self.status}"

class PendingPhoneVerification(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    phone_number = models.CharField(max_length=15, blank=True)
    otp = models.CharField(max_length=6, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def is_expired(self):
        time_elapsed = timezone.now() - self.created_at
        return time_elapsed.total_seconds() > PHONE_VERIFICATION_TOKEN_EXPIRY_TIME

class Note(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    title = models.TextField(blank=True)
    description = models.TextField(blank=True)

class SupportingDocs(BaseModel):
    name = models.TextField(blank=True)
    description = models.TextField(blank=True)
    file = models.OneToOneField(UploadedFile, on_delete=models.CASCADE)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    type = models.CharField(max_length=100, blank=True, choices=SUPPORTING_DOCS_TYPES)
class VerificationDoc(BaseModel):
    name = models.TextField(blank=True)
    description = models.TextField(blank=True)
    file = models.OneToOneField(UploadedFile, on_delete=models.CASCADE)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    type = models.CharField(max_length=100, blank=True, choices=VERIFICATION_DOC_TYPES)
    attempt = models.ForeignKey(UserVerificationAttempt, on_delete=models.CASCADE, related_name='verification_docs', null=True, blank=True)
class UserInsurance(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='user_insurance')
    provider = models.CharField(max_length=100, blank=True)
    policy_number = models.CharField(max_length=100, blank=True)
    type = models.CharField(max_length=100, blank=True, choices=INSURANCE_TYPES)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    group_number = models.CharField(max_length=100, blank=True)
    policy_holder_name = models.CharField(max_length=100, blank=True)
    dependent_information = models.TextField(blank=True)
    deductable_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    copay_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    additional_document = models.OneToOneField(UploadedFile, on_delete=models.CASCADE, blank=True, null=True, related_name='user_insurance_additional_document')
    

class Message(BaseModel):
    message = models.TextField(blank=True)
    receiver = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='received_messages')
    sender = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sent_messages')

    def __str__(self):
        return f"{self.sender.email} - {self.receiver.email} - {self.id}"

class BusinessCard(models.Model):
    user = models.OneToOneField('CustomUser', on_delete=models.CASCADE, related_name='business_card')
    first_name = models.CharField(max_length=100, blank=True)
    middle_name = models.CharField(max_length=100, blank=True)
    last_name = models.CharField(max_length=100, blank=True)
    professional_title = models.CharField(max_length=255, blank=True)
    address = models.CharField(max_length=255, blank=True)
    phone = models.CharField(max_length=30, blank=True)
    email = models.EmailField(blank=True)
    affiliation = models.CharField(max_length=255, blank=True)
    additional_information = models.TextField(blank=True)
    url = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return f"BusinessCard({self.user_id})"