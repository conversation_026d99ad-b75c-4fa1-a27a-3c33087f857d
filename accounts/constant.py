EMAIL_CHANGE_TOKEN_EXPIRY_TIME = 24 * 60 * 60  # 24 hours in seconds
EMAIL_CHANGE_MAX_ATTEMPTS = 3
EMAIL_CHANGE_WINDOW_HOURS = 6
EMAIL_CHANGE_WINDOW_SECONDS = EMAIL_CHANGE_WINDOW_HOURS * 60 * 60

PHONE_VERIFICATION_TOKEN_EXPIRY_TIME = 60 * 5 # 5 minutes

BLOOD_TYPE_CHOICES = [
  ('A+', 'A+'),
  ('A-', 'A-'),
  ('B+', 'B+'),
  ('B-', 'B-'),
  ('AB+', 'AB+'),
  ('AB-', 'AB-'),
  ('O+', 'O+'),
  ('O-', 'O-'),
  ('NA', 'NA')
]

USER_VERIFICATION_STATUS_CHOICES = [
    ("pending", "Pending"),
    ("approved", "Approved"),
    ("rejected", "Rejected"),
]

MAXIMUM_FILE_SIZE_MB = 2
MAXIMUM_FILE_SIZE = MAXIMUM_FILE_SIZE_MB * 1024 * 1024 # 2MB in bytes
MAXIMUM_FILE_COUNT = 5
MAXIMUM_TOTAL_FILE_SIZE_MB = 10
MAXIMUM_TOTAL_FILE_SIZE = MAXIMUM_TOTAL_FILE_SIZE_MB * 1024 * 1024 # 10MB in bytes
ALLOWED_TYPES= [
  'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'image/jpeg', 'image/png', 'image/jpg', 'video/quicktime'
]

SUPPORTING_DOCS_TYPES = [
    ('prescription', 'Prescription'),
    ('diagnosis', 'Diagnosis'),
    ('other', 'Other')
]

VERIFICATION_DOC_TYPES = [
  ('national_id', 'National ID'),
  ('passport', 'Passport'),
  ('driving_license', 'Driving License'),
  ('voter_id', 'Voter ID'),
  ('residence_permit', 'Residence Permit'),
  ('other', 'Other')
]

INSURANCE_TYPES = [
    ('health', 'Health'),
    ('dental', 'Dental'),
    ('vision', 'Vision'),
    ('other', 'Other')
]
MODE_OF_PAYMENT = [
    ('credit_card', 'Credit Card'),
    ('debit_card', 'Debit Card'),
    ('bank_transfer', 'Bank Transfer'),
]