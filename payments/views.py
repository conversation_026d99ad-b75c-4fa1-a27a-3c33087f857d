from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.views.decorators.csrf import csrf_exempt
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
import stripe
import logging
import warnings
from django.views.decorators.http import require_POST
from django.http import HttpResponse
from django.utils import timezone

from payments.models import StripeCustomer, Price, Transaction, UserSubscription
from payments.tasks import handle_payment_webhook

logger = logging.getLogger(__name__)

# DEPRECATION WARNING: This app is deprecated
warnings.warn(
    "The 'payments' app views are deprecated and will be removed in a future version. "
    "Please use the 'billing' app views instead. "
    "Migration guide: https://docs.ravid.cloud/migration/payments-to-billing",
    DeprecationWarning,
    stacklevel=2
)

stripe.api_key = settings.STRIPE_SECRET_KEY

@api_view(['GET'])
@login_required
def get_user_subscription(request):
    """
    DEPRECATED: Use billing app endpoints instead.
    GET /api/billing/subscriptions/
    """
    warnings.warn(
        "get_user_subscription is deprecated. Use GET /api/billing/subscriptions/ instead.",
        DeprecationWarning,
        stacklevel=2
    )
    user_subscriptions = UserSubscription.objects.filter(user=request.user).select_related('price')
    return Response({'user_subscriptions': user_subscriptions.values()}, status=200)


@api_view(['POST'])
@login_required
def create_checkout_session(request):
    """
    DEPRECATED: Use billing app endpoints instead.
    POST /api/billing/checkout/
    """
    warnings.warn(
        "create_checkout_session is deprecated. Use POST /api/billing/checkout/ instead.",
        DeprecationWarning,
        stacklevel=2
    )
    try:
        price_id = request.data.get('price_id')
        redirect_url = request.data.get('redirect_url', '/')
        if not price_id:
            return Response({'error': 'Price ID (price_id) is required'}, status=400)
        try:
            stripe_customer = StripeCustomer.objects.get(user=request.user)
        except StripeCustomer.DoesNotExist:
            stripe_customer = stripe.Customer.create(name=f'{request.user.first_name} {request.user.last_name}', email=request.user.email)
            stripe_customer = StripeCustomer.objects.create(user=request.user, stripe_customer_id=stripe_customer.id)
        quantity = 1
        price = Price.objects.get(id=price_id)

        # Check if user has already owned this subscription
        if UserSubscription.objects.filter(user=request.user, price=price, status='active').exists():
            return Response({'error': 'User already has this subscription'}, status=400)

        stripe_price = stripe.Price.retrieve(price.stripe_price_id)
        if not stripe_price:
            return Response({'error': 'Price not found'}, status=404)
        checkout_session = stripe.checkout.Session.create(
            line_items=[{
                'price': stripe_price.id,
                'quantity': quantity,
            }],
            client_reference_id=request.user.id,
            customer=stripe_customer.stripe_customer_id,
            mode='subscription' if stripe_price.recurring else 'payment',
            success_url=settings.SITE_URL + '/payment-success?user_id=' + str(request.user.id) + '&redirect_url=' + redirect_url,
            cancel_url=settings.SITE_URL + '/payment-cancel?user_id=' + str(request.user.id) + '&redirect_url=' + redirect_url,
            saved_payment_method_options={
                'payment_method_save': 'enabled'
            },
        )

        return Response({'url': checkout_session.url}, status=200)
    except Exception as e:
        return Response({'error': str(e)}, status=400)

@api_view(['GET'])
def get_prices(request):
    """
    DEPRECATED: Use content_management app endpoints instead.
    GET /api/content/public/services/
    GET /api/content/public/subscription-plans/
    """
    warnings.warn(
        "get_prices is deprecated. Use content_management app endpoints instead.",
        DeprecationWarning,
        stacklevel=2
    )
    prices = Price.objects.exclude(short_name='').order_by('index')
    return Response({'prices': prices.values()}, status=200)

@api_view(['GET'])
@staff_member_required
def sync_prices(request):
    """
    DEPRECATED: Use billing app management commands instead.
    python manage.py sync_products
    """
    warnings.warn(
        "sync_prices is deprecated. Use 'python manage.py sync_products' instead.",
        DeprecationWarning,
        stacklevel=2
    )
    # list all prices
    prices = stripe.Price.list(limit=100)
    # Upsert prices
    for price in prices:
        product = stripe.Product.retrieve(price.product)
        if product.active == False:
            continue
        Price.objects.update_or_create(
            stripe_price_id=price.id,
            defaults={
                'active': price.active,
                'updated_at': timezone.now(),
                'name': product.name,
                'description': product.description,
                'price': price.unit_amount / 100,
            }
        )
    return Response({'prices': prices}, status=200)

@api_view(['GET'])
@login_required
def get_billing_history(request):
    """
    DEPRECATED: Use billing app endpoints instead.
    GET /api/billing/payments/
    """
    warnings.warn(
        "get_billing_history is deprecated. Use GET /api/billing/payments/ instead.",
        DeprecationWarning,
        stacklevel=2
    )
    transactions = Transaction.objects.filter(user=request.user).order_by('-created_at')
    return Response({'transactions': transactions.values()}, status=200)

@require_POST
@csrf_exempt
@permission_classes([AllowAny])
def webhook_received(request):
    """
    DEPRECATED: Use billing app webhook endpoint instead.
    POST /api/billing/webhook/
    """
    warnings.warn(
        "webhook_received is deprecated. Use POST /api/billing/webhook/ instead.",
        DeprecationWarning,
        stacklevel=2
    )
    try:
        logger.info("Received webhook request")
        logger.debug(f"Headers: {dict(request.headers)}")

        payload = request.body
        sig_header = request.headers.get('stripe-signature')

        if not sig_header:
            logger.error("No Stripe signature found in headers")
            return HttpResponse(status=400)

        # Try production webhook secret first
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_TEST_WEBHOOK_SECRET_BILLING
            )
            logger.info("Successfully verified webhook with production secret")
        except stripe.error.SignatureVerificationError:
            # If production secret fails, try test secret
            try:
                event = stripe.Webhook.construct_event(
                    payload, sig_header, settings.STRIPE_TEST_WEBHOOK_SECRET
                )
                logger.info("Successfully verified webhook with test secret")
            except stripe.error.SignatureVerificationError as e:
                logger.error(f"Invalid signature for both production and test: {str(e)}")
                return HttpResponse(status=400)

        logger.info(f"Webhook verified: {event.type} - {event.id}")
        handle_payment_webhook.delay(event.type, event.data.object)

        return HttpResponse(status=200)
    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}")
        return HttpResponse(status=500)



