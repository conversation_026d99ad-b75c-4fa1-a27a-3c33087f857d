from django.db import models
import warnings

from accounts.models import CustomUser
from config.models import BaseModel

# DEPRECATION WARNING: This app is deprecated
warnings.warn(
    "The 'payments' app is deprecated and will be removed in a future version. "
    "Please use the 'billing' app instead. "
    "Migration guide: https://docs.ravid.cloud/migration/payments-to-billing",
    DeprecationWarning,
    stacklevel=2
)

class StripeCustomer(BaseModel):
    """
    DEPRECATED: Use billing.models.Customer instead.
    This model will be removed in a future version.
    """
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name='stripe_customer')
    stripe_customer_id = models.CharField(max_length=255, blank=True)

    def __init__(self, *args, **kwargs):
        warnings.warn(
            "StripeCustomer is deprecated. Use billing.models.Customer instead.",
            DeprecationWarning,
            stacklevel=2
        )
        super().__init__(*args, **kwargs)

# Mapping data from stripe price (reorcurring - subscription) to our database
class Price(BaseModel):
    """
    DEPRECATED: Use billing.models.Price instead.
    This model will be removed in a future version.

    Mapping data from stripe price (reorcurring - subscription) to our database
    Equal to stripe price
    """

    stripe_price_id = models.CharField(max_length=255, blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    name = models.CharField(max_length=255, blank=True)
    description = models.TextField(blank=True)
    active = models.BooleanField(default=True)
    short_name = models.CharField(max_length=255, blank=True)
    index = models.IntegerField(default=0)

    def __init__(self, *args, **kwargs):
        warnings.warn(
            "payments.models.Price is deprecated. Use billing.models.Price instead.",
            DeprecationWarning,
            stacklevel=2
        )
        super().__init__(*args, **kwargs)

class UserSubscription(BaseModel):
    """
    DEPRECATED: Use billing.models.Subscription instead.
    This model will be removed in a future version.

    User subscription to a price of stripe
    """
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='user_subscription')
    price = models.ForeignKey(Price, on_delete=models.CASCADE, related_name='user_subscription')
    status = models.CharField(max_length=255, blank=True)
    stripe_subscription_id = models.CharField(max_length=255, blank=True)
    renewal_date = models.DateTimeField(blank=True, null=True)

    def __init__(self, *args, **kwargs):
        warnings.warn(
            "UserSubscription is deprecated. Use billing.models.Subscription instead.",
            DeprecationWarning,
            stacklevel=2
        )
        super().__init__(*args, **kwargs)

class Transaction(BaseModel):
    """
    DEPRECATED: Use billing.models.ServicePayment or billing.models.EnterprisePayment instead.
    This model will be removed in a future version.
    """
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='transaction')
    stripe_transaction_id = models.CharField(max_length=255, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=255, blank=True)
    payload = models.JSONField(blank=True, null=True)

    def __init__(self, *args, **kwargs):
        warnings.warn(
            "Transaction is deprecated. Use billing.models.ServicePayment or billing.models.EnterprisePayment instead.",
            DeprecationWarning,
            stacklevel=2
        )
        super().__init__(*args, **kwargs)


class WebhookEvent(BaseModel):
    """
    DEPRECATED: Webhook events are now handled directly in billing app.
    This model will be removed in a future version.

    Storing webhook events for future cross-referencing
    """
    event_type = models.CharField(max_length=255, blank=True)
    payload = models.JSONField(blank=True, null=True)

    def __init__(self, *args, **kwargs):
        warnings.warn(
            "WebhookEvent is deprecated. Webhook events are now handled directly in billing app.",
            DeprecationWarning,
            stacklevel=2
        )
        super().__init__(*args, **kwargs)

