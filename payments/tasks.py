import logging
import time
import warnings

from celery import shared_task
from django.conf import settings
import stripe

from django.utils import timezone

from accounts.models import CustomUser
from analysis.constant import ANALYSIS_RUN_STATUS
from analysis.models.base import AnalysisRun
from payments.models import WebhookEvent, Price, UserSubscription, Transaction, StripeCustomer

logger = logging.getLogger(__name__)

# DEPRECATION WARNING: This app is deprecated
warnings.warn(
    "The 'payments' app tasks are deprecated and will be removed in a future version. "
    "Please use the 'billing' app webhook handling instead. "
    "Migration guide: https://docs.ravid.cloud/migration/payments-to-billing",
    DeprecationWarning,
    stacklevel=2
)

from django.http import HttpResponse

@shared_task()
def handle_payment_webhook(event_type, data):
    """
    DEPRECATED: Use billing app webhook handling instead.
    Webhook events are now handled directly in billing.views.stripe_webhook
    """
    warnings.warn(
        "handle_payment_webhook task is deprecated. Use billing app webhook handling instead.",
        DeprecationWarning,
        stacklevel=2
    )
    try:

        logger.info(f"Handling webhook {event_type} of {data.get('id')}")

        # Storing for future cross-referencing
        WebhookEvent.objects.create(
            event_type=event_type,
            payload=data,
        )

        # Handling webhook from stripe
        # Case 1: New price created
        if event_type == 'price.created':
            product = stripe.Product.retrieve(data['product'])
            Price.objects.create(
                stripe_price_id=data['id'],
                active=data['active'],
                name=product.name,
                description=product.description,
                price=data['unit_amount'] / 100,
            )

        # Case 2: Price deleted or archived
        if event_type == 'price.deleted' or (event_type == 'price.updated' and not data.get('active', True)):
            Price.objects.filter(stripe_price_id=data['id']).update(
                active=False,
                updated_at=timezone.now(),
            )

        # Case 3: Price updated
        if event_type == 'price.updated':
            Price.objects.filter(stripe_price_id=data['id']).update(
                active=data['active'],
                price=data['unit_amount'] / 100,
                updated_at=timezone.now(),
            )

        # Case 4: User checkout session completed
        if event_type == 'checkout.session.completed':
            try:
                user = CustomUser.objects.get(id=data['client_reference_id'])
            except CustomUser.DoesNotExist:
                logger.error(f"checkout.session.completed: User {data['client_reference_id']} does not exist")
                return False

            if data['mode'] == 'subscription':
                stripe_subscription_id = data['subscription']
                stripe_subscription = stripe.Subscription.retrieve(stripe_subscription_id)

                price = Price.objects.get(stripe_price_id=stripe_subscription['items']['data'][0]['price']['id'])
                if price.short_name == 'profile-verification':
                    user.paid_for_verification = True
                    user.save()

                # Storing into UserSubscription
                UserSubscription.objects.create(
                    user=user,
                    status=stripe_subscription['status'],
                    stripe_subscription_id=stripe_subscription_id,
                    price=price,
                )
            elif data['mode'] == 'payment':
                # get line-items
                session = stripe.checkout.Session.retrieve(data['id'], expand=['line_items'])
                line_items = session.line_items
                for line_item in line_items.data:
                    price = Price.objects.get(stripe_price_id=line_item.price.id)
                    AnalysisRun.objects.create(
                        user=user,
                        price=price,
                        status=ANALYSIS_RUN_STATUS["PENDING"],
                        stripe_session_id=data['id'],
                    )

        # Case: Invoice payment succeeded
        if event_type == 'invoice.payment_succeeded':
            # Storing into Transaction
            try:
                customer = StripeCustomer.objects.get(stripe_customer_id=data['customer'])
            except StripeCustomer.DoesNotExist:
                logger.error(f"invoice.payment_succeeded: Stripe customer {data['customer']} does not exist")
                return False
            Transaction.objects.create(
                user=customer.user,
                stripe_transaction_id=data['id'],
                amount=data['amount_due'] / 100,
                status='success',
                payload=data,
            )
            # Renewal of subscription
            if data['billing_reason'] == 'subscription_cycle':
                subscription = UserSubscription.objects.get(stripe_subscription_id=data['subscription'])
                subscription.renewal_date = timezone.now()
                subscription.save()

        # Case 5: Invoice payment failed
        if event_type == 'invoice.payment_failed':
            # Storing into Transaction
            try:
                customer = StripeCustomer.objects.get(stripe_customer_id=data['customer'])
            except StripeCustomer.DoesNotExist:
                logger.error(f"invoice.payment_failed: Stripe customer {data['customer']} does not exist")
                return False
            Transaction.objects.create(
                user=customer.user,
                stripe_transaction_id=data['id'],
                amount=data['amount_due'] / 100,
                status='failed',
                payload=data,
            )

            # Cancelling subscription
            if data['billing_reason'] == 'subscription_cycle':
                subscription = UserSubscription.objects.get(stripe_subscription_id=data['subscription'])
                subscription.status = 'cancelled'
                subscription.save()

        if event_type == 'customer.subscription.deleted':
            try:
                subscription = UserSubscription.objects.get(stripe_subscription_id=data['id'])
                subscription.status = 'cancelled'
                subscription.save()
            except UserSubscription.DoesNotExist:
                logger.error(f"customer.subscription.deleted: User subscription {data['id']} does not exist")
                return False


        # default return ok
        return True
    except ValueError as e:
        logger.error(f"Error constructing event: {e}")
        return False
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"Error verifying signature: {e}")
        return False
    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        return False
