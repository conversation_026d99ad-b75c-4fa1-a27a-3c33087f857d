# ⚠️ DEPRECATED: Payments App

## 🚨 Important Notice

**This app is deprecated and will be removed in a future version.**

Please migrate to the **`billing`** app for all payment-related functionality.

## 📋 Migration Guide

### Models Migration

| Deprecated (payments) | New (billing) | Notes |
|----------------------|---------------|-------|
| `StripeCustomer` | `Customer` | Use `billing.models.Customer` |
| `Price` | `Price` | Use `billing.models.Price` (better structure) |
| `UserSubscription` | `Subscription` | Use `billing.models.Subscription` |
| `Transaction` | `ServicePayment` / `EnterprisePayment` | Use appropriate payment model |
| `WebhookEvent` | N/A | Handled directly in billing app |

### API Endpoints Migration

| Deprecated (payments) | New (billing/content) | Notes |
|----------------------|----------------------|-------|
| `GET /api/payments/get-prices/` | `GET /api/content/public/services/` | Use content_management |
| `POST /api/payments/create-checkout-session/` | `POST /api/billing/checkout/` | Use billing app |
| `GET /api/payments/user-subscription/` | `GET /api/billing/subscriptions/` | Use billing app |
| `GET /api/payments/billing-history/` | `GET /api/billing/payments/` | Use billing app |
| `POST /api/payments/webhook/` | `POST /api/billing/webhook/` | Use billing app |
| `GET /api/payments/sync-prices/` | `python manage.py sync_products` | Use management command |

### Code Migration Examples

#### Before (Deprecated)
```python
from payments.models import StripeCustomer, Price, UserSubscription

# Get customer
customer = StripeCustomer.objects.get(user=user)

# Get prices
prices = Price.objects.filter(active=True)

# Get subscriptions
subscriptions = UserSubscription.objects.filter(user=user)
```

#### After (Recommended)
```python
from billing.models import Customer, Price, Subscription

# Get customer
customer = Customer.objects.get(user=user)

# Get prices (via products)
prices = Price.objects.filter(active=True, product__active=True)

# Get subscriptions
subscriptions = Subscription.objects.filter(customer__user=user)
```

## 🔄 Migration Steps

1. **Update imports** from `payments.models` to `billing.models`
2. **Update API calls** to use billing endpoints
3. **Update related_name references**:
   - `user.stripe_customer` → `user.billing_customer`
   - `user.user_subscription` → `user.subscription_access`
4. **Test thoroughly** before removing payments app usage
5. **Run data migration** (when available)

## 📚 Documentation

- [Billing App Documentation](../billing/README.md)
- [Content Management Documentation](../backend_docs/content_management.md)
- [Migration Guide](https://docs.ravid.cloud/migration/payments-to-billing) *(coming soon)*

## ⏰ Timeline

- **Current**: Deprecation warnings added
- **Next Release**: Migration tools provided
- **Future Release**: Payments app removed

## 🆘 Support

If you need help with migration, please:

1. Check the billing app documentation
2. Review the migration examples above
3. Contact the development team

---

**⚠️ All new development should use the `billing` app instead of this deprecated `payments` app.**
