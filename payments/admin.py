from django.contrib import admin

from payments.models import Price, UserSubscription

# Register your models here.
# admin.site.register(Price)
admin.site.register(UserSubscription)


@admin.register(Price)
class PriceAdmin(admin.ModelAdmin):
    list_display = ('name', 'stripe_price_id', 'price', 'active')
    list_filter = ('active',)
    search_fields = ('name', 'stripe_price_id')
    list_editable = ('active',)
    list_per_page = 20
