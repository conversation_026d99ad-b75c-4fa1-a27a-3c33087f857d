from django.urls import path
from .views import create_checkout_session, get_billing_history, get_prices, get_user_subscription,  webhook_received, sync_prices

# ⚠️ DEPRECATED: These URLs are deprecated and will be removed in a future version.
# Please use the billing app URLs instead:
# - /api/billing/checkout/ instead of create-checkout-session/
# - /api/content/public/services/ instead of get-prices/
# - /api/billing/webhook/ instead of webhook/
# - python manage.py sync_products instead of sync-prices/
# - /api/billing/payments/ instead of billing-history/
# - /api/billing/subscriptions/ instead of user-subscription/

urlpatterns = [
    path('create-checkout-session/', create_checkout_session, name='create_checkout_session'),  # DEPRECATED
    path('get-prices/', get_prices, name='get_prices'),  # DEPRECATED
    path('webhook/', webhook_received, name='webhook'),  # DEPRECATED
    path('sync-prices/', sync_prices, name='sync_prices'),  # DEPRECATED
    path('billing-history/', get_billing_history, name='billing_history'),  # DEPRECATED
    path('user-subscription/', get_user_subscription, name='user_subscription'),  # DEPRECATED
]
