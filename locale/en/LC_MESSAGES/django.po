# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-12 16:18+0530\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: .\config\settings\base.py:37
msgid "English"
msgstr ""

#: .\config\settings\base.py:38
msgid "French"
msgstr ""

#: .\config\settings\base.py:39
msgid "Portuguese"
msgstr ""

#: .\config\settings\base.py:40
msgid "Vietnamese"
msgstr ""

#: .\config\settings\base.py:41
msgid "Arabic"
msgstr ""

#: .\config\settings\base.py:42
msgid "Thai"
msgstr ""

#: .\ravid_communities\templates\allauth\layouts\entrance.html:8
#: .\ravid_communities\templates\base.html:102
msgid "Sign In"
msgstr ""

#: .\ravid_communities\templates\base.html:87
msgid "My Profile"
msgstr ""

#: .\ravid_communities\templates\base.html:91
msgid "Sign Out"
msgstr ""

#: .\ravid_communities\templates\base.html:97
msgid "Sign Up"
msgstr ""

#: .\ravid_communities\users\admin.py:24
msgid "Personal info"
msgstr ""

#: .\ravid_communities\users\admin.py:26
msgid "Permissions"
msgstr ""

#: .\ravid_communities\users\admin.py:37
msgid "Important dates"
msgstr ""

#: .\ravid_communities\users\apps.py:9
msgid "Users"
msgstr ""

#: .\ravid_communities\users\forms.py:27
#: .\ravid_communities\users\tests\test_forms.py:35
msgid "This email has already been taken."
msgstr ""

#: .\ravid_communities\users\models.py:21
msgid "Name of User"
msgstr ""

#: .\ravid_communities\users\models.py:24
msgid "email address"
msgstr ""

#: .\ravid_communities\users\tests\test_views.py:72
#: .\ravid_communities\users\views.py:25
msgid "Information successfully updated"
msgstr ""
