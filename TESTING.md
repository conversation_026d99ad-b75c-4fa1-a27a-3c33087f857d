# 🧪 Testing Guide for Ravid Communities Backend

## Quick Start

### 1. Check Environment
```bash
# Check for port conflicts and Docker status
make check-ports
```

### 2. Run Tests
```bash
# Quick test (uses dev environment if running, otherwise starts test environment)
make test-quick

# Full test suite
make test

# Unit tests only (fast)
make test-unit

# Integration tests
make test-integration

# Tests with coverage report
make test-coverage
```

### 3. Development Testing (Faster)
```bash
# If development environment is already running
make test-dev-fast      # Quick unit tests
make test-dev-unit      # All unit tests
make test-dev           # All tests in dev environment
```

## 🚀 Testing Commands Reference

### Environment Management
```bash
make check-ports        # Check for port conflicts
make test-setup         # Setup test environment
make clean-test         # Clean up test containers
make clean-test-all     # Clean up everything including images
```

### Test Execution
```bash
# Full test suite
make test               # All tests with full setup
make test-unit          # Unit tests only
make test-integration   # Integration tests only
make test-e2e          # End-to-end tests only
make test-coverage      # Tests with coverage report
make test-performance   # Performance tests only
make test-watch         # Watch mode for development

# Development environment testing (faster)
make test-dev           # All tests in dev environment
make test-dev-unit      # Unit tests in dev environment
make test-dev-fast      # Fast unit tests with fail-fast
make test-quick         # Smart quick testing

# Specific test categories
make test-billing           # All billing tests
make test-billing-services  # Service layer tests only
make test-billing-models    # Model tests only
make test-billing-api       # API tests only
```

### Specific Test Execution
```bash
# Run specific test file
make test-file FILE=billing/tests/unit/services/test_payment_service.py

# Run specific test method
make test-method METHOD=billing/tests/unit/services/test_payment_service.py::TestPaymentService::test_process_checkout_success

# Debug specific test
make test-debug TEST=billing/tests/unit/services/test_payment_service.py::TestPaymentService::test_process_checkout_success
```

### CI/CD
```bash
make ci-test            # Full CI/CD test pipeline
make test-report        # Generate comprehensive test reports
```

## 🐛 Troubleshooting

### Port Conflicts
If you get port conflict errors:

1. **Check what's using the ports:**
   ```bash
   make check-ports
   ```

2. **Clean up test environment:**
   ```bash
   make clean-test
   ```

3. **If development environment is running:**
   ```bash
   # Use development environment for testing (faster)
   make test-dev-fast
   ```

4. **Manual port checking:**
   ```bash
   # Check specific port
   lsof -i :5434  # Test database port
   lsof -i :6381  # Test Redis port
   
   # Kill process using port
   kill -9 $(lsof -t -i:5434)
   ```

### Common Issues

#### "Port already allocated"
```bash
# Solution 1: Use development environment
make test-dev-fast

# Solution 2: Clean up and retry
make clean-test
make test

# Solution 3: Check and kill conflicting processes
make check-ports
```

#### "Database connection failed"
```bash
# Wait for database to be ready
make test-setup

# Check database status
docker compose -f docker-compose-test.yaml logs test-db
```

#### "Tests not found"
```bash
# Make sure you're in the project root directory
pwd  # Should show /path/to/ravid-server

# Check test files exist
ls billing/tests/
```

### Docker Issues

#### "Docker not running"
```bash
# Start Docker Desktop or Docker daemon
sudo systemctl start docker  # Linux
# or start Docker Desktop app
```

#### "Permission denied"
```bash
# Make scripts executable
chmod +x scripts/*.sh

# Or run with sudo if needed
sudo make test
```

## 📊 Test Types & Performance

### Test Categories
- **Unit Tests (60%)**: Fast, isolated tests (~1 second each)
- **Integration Tests (30%)**: API and database tests (~5 seconds each)
- **E2E Tests (10%)**: Complete workflow tests (~30 seconds each)

### Performance Expectations
- **Unit Tests**: < 30 seconds total
- **Integration Tests**: < 2 minutes total
- **Full Test Suite**: < 3 minutes total
- **Coverage Report**: < 30 seconds additional

### Optimization Tips
1. **Use development environment for quick testing:**
   ```bash
   make test-dev-fast  # Fastest option
   ```

2. **Run only what you need:**
   ```bash
   make test-unit      # Skip integration tests
   ```

3. **Use fail-fast for debugging:**
   ```bash
   make test-dev-fast  # Stops on first failure
   ```

## 🔧 Development Workflow

### TDD Workflow
```bash
# 1. Write failing test
# 2. Run quick test to see failure
make test-quick

# 3. Implement feature
# 4. Run tests until they pass
make test-dev-fast

# 5. Run full test suite before commit
make test-coverage
```

### Pre-commit Testing
```bash
# Quick check before commit
make test-quick

# Full check before push
make test-coverage
make lint
```

### Debugging Tests
```bash
# Run with verbose output
make test-dev-unit -v

# Debug specific test with PDB
make test-debug TEST=path/to/test.py::TestClass::test_method

# View test logs
docker compose -f docker-compose-test.yaml logs test-web
```

## 📈 Coverage Reports

### Generate Coverage
```bash
make test-coverage
```

### View Coverage
```bash
# HTML report (detailed)
open htmlcov/index.html

# Terminal report
make test-coverage  # Shows coverage in terminal

# XML report (for CI/CD)
# Generated at coverage.xml
```

### Coverage Goals
- **Overall**: 90%+
- **Services**: 95%+
- **Models**: 90%+
- **Views**: 85%+

## 🎯 Best Practices

### Writing Tests
1. **Use descriptive test names**
2. **Follow AAA pattern** (Arrange, Act, Assert)
3. **Use factories for test data**
4. **Mock external dependencies**
5. **Test edge cases and error conditions**

### Running Tests
1. **Start with quick tests** (`make test-quick`)
2. **Use development environment** for iteration
3. **Run full suite** before commits
4. **Check coverage** regularly
5. **Clean up** test environment when done

### Performance
1. **Use unit tests** for fast feedback
2. **Limit integration tests** to critical paths
3. **Mock external services** (Stripe, email, etc.)
4. **Use in-memory database** for tests
5. **Run tests in parallel** when possible

## 🚨 Emergency Commands

### Quick Fixes
```bash
# Everything is broken, start fresh
make clean-test-all
make test-setup
make test

# Just want to run one test quickly
make test-dev-fast

# Need to see what's wrong
make check-ports
docker compose -f docker-compose-test.yaml logs
```

### Reset Everything
```bash
# Nuclear option - reset everything
make clean
docker system prune -a -f
make dev  # Restart development environment
make test-setup  # Setup test environment
```

## 📞 Getting Help

### Check Status
```bash
make check-ports    # Environment status
make ps            # Container status
docker ps          # All containers
```

### Logs
```bash
# Test logs
docker compose -f docker-compose-test.yaml logs test-web

# Development logs
make log-dev name=web

# Database logs
docker compose -f docker-compose-test.yaml logs test-db
```

### Common Solutions
1. **Port conflicts**: Use `make test-dev-fast`
2. **Slow tests**: Use `make test-unit`
3. **Database issues**: Run `make test-setup`
4. **Docker issues**: Restart Docker and run `make clean`
5. **Permission issues**: Run `chmod +x scripts/*.sh`
