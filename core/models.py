from django.db import models
from django.utils import timezone
from config.models import BaseModel
from .managers import SoftDeleteManager

class SoftDeleteModel(BaseModel):
    """
    An abstract base class model that provides soft delete functionality
    """
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)

    objects = SoftDeleteManager()
    all_objects = models.Manager()

    def delete(self, using=None, keep_parents=False):
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self):
        super().delete()

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        if not self.is_deleted:
            self.deleted_at = None
        super().save(*args, **kwargs) 