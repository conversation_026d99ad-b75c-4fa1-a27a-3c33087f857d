#!/bin/bash

# Script to configure CORS for Google Cloud Storage bucket
# Usage: ./scripts/setup_gcs_cors.sh [BUCKET_NAME]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get bucket name from argument or environment variable
BUCKET_NAME=${1:-$GCS_BUCKET_NAME}

if [ -z "$BUCKET_NAME" ]; then
    print_error "Bucket name not provided!"
    echo "Usage: $0 [BUCKET_NAME]"
    echo "Or set GCS_BUCKET_NAME environment variable"
    exit 1
fi

print_status "🔧 Setting up CORS for Google Cloud Storage bucket: $BUCKET_NAME"
echo "=" * 60

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    print_error "gcloud CLI is not installed!"
    echo "Please install it from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if gsutil is available
if ! command -v gsutil &> /dev/null; then
    print_error "gsutil is not available!"
    echo "Please ensure Google Cloud SDK is properly installed"
    exit 1
fi

# Check authentication
print_status "Checking authentication..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
    print_error "Not authenticated with Google Cloud!"
    echo "Please run: gcloud auth login"
    exit 1
fi

ACTIVE_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1)
print_success "Authenticated as: $ACTIVE_ACCOUNT"

# Check if bucket exists
print_status "Checking if bucket exists..."
if ! gsutil ls -b gs://$BUCKET_NAME > /dev/null 2>&1; then
    print_error "Bucket gs://$BUCKET_NAME does not exist or you don't have access!"
    exit 1
fi
print_success "Bucket gs://$BUCKET_NAME found"

# Get current CORS configuration
print_status "Checking current CORS configuration..."
CURRENT_CORS=$(gsutil cors get gs://$BUCKET_NAME 2>/dev/null || echo "[]")
if [ "$CURRENT_CORS" != "[]" ] && [ "$CURRENT_CORS" != "" ]; then
    print_warning "Bucket already has CORS configuration:"
    echo "$CURRENT_CORS"
    echo
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled"
        exit 0
    fi
fi

# Apply CORS configuration
print_status "Applying CORS configuration..."
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CORS_CONFIG_FILE="$SCRIPT_DIR/cors-config.json"

if [ ! -f "$CORS_CONFIG_FILE" ]; then
    print_error "CORS configuration file not found: $CORS_CONFIG_FILE"
    exit 1
fi

if gsutil cors set "$CORS_CONFIG_FILE" gs://$BUCKET_NAME; then
    print_success "CORS configuration applied successfully!"
else
    print_error "Failed to apply CORS configuration"
    exit 1
fi

# Verify the configuration
print_status "Verifying CORS configuration..."
NEW_CORS=$(gsutil cors get gs://$BUCKET_NAME)
echo "Current CORS configuration:"
echo "$NEW_CORS"

print_success "🎉 CORS configuration completed successfully!"
echo
echo "Next steps:"
echo "1. Restart your Django development server"
echo "2. Test file uploads from your frontend application"
echo "3. Check browser developer tools for any remaining CORS errors"
echo
echo "To verify the configuration later, run:"
echo "  gsutil cors get gs://$BUCKET_NAME"
