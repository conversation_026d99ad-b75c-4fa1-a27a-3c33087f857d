#!/usr/bin/env python
"""
Script to check for existing appointments with conflicting payment methods.
This script identifies appointments that have both insurance=True and direct_payment=True.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.base')
django.setup()

from appointments.models import Appointment


def check_payment_conflicts():
    """Check for appointments with conflicting payment methods"""
    print("Checking for appointments with conflicting payment methods...")
    print("=" * 60)
    
    # Find appointments with both insurance and direct_payment set to True
    conflicting_appointments = Appointment.objects.filter(
        insurance=True,
        direct_payment=True
    )
    
    count = conflicting_appointments.count()
    
    if count == 0:
        print("✅ No conflicting appointments found!")
        print("All appointments have valid payment method configurations.")
    else:
        print(f"⚠️  Found {count} appointment(s) with conflicting payment methods:")
        print()
        
        for appointment in conflicting_appointments:
            print(f"Appointment ID: {appointment.id}")
            print(f"  - Title: {appointment.title}")
            print(f"  - Type: {appointment.appointment_type}")
            print(f"  - Patient: {appointment.patient}")
            print(f"  - Doctor: {appointment.doctor}")
            print(f"  - Start Time: {appointment.start_time}")
            print(f"  - Insurance: {appointment.insurance}")
            print(f"  - Direct Payment: {appointment.direct_payment}")
            print(f"  - Created: {appointment.created_at}")
            print("-" * 40)
        
        print("\n📋 Recommended Actions:")
        print("1. Review each conflicting appointment manually")
        print("2. Update payment method to use either insurance OR direct payment")
        print("3. Consider the business context for each appointment")
        print("4. Run the migration after fixing the data conflicts")
    
    print("\n" + "=" * 60)
    return count


def suggest_fixes():
    """Suggest potential fixes for conflicting appointments"""
    print("\n🔧 Potential Fix Strategies:")
    print("1. Set insurance=False for appointments that should use direct payment")
    print("2. Set direct_payment=False for appointments that should use insurance")
    print("3. Contact the patient/doctor to clarify the intended payment method")
    print("\nExample Django shell commands to fix:")
    print("# To set direct payment only:")
    print("Appointment.objects.filter(id=<appointment_id>).update(insurance=False)")
    print("\n# To set insurance only:")
    print("Appointment.objects.filter(id=<appointment_id>).update(direct_payment=False)")


if __name__ == "__main__":
    try:
        conflict_count = check_payment_conflicts()
        
        if conflict_count > 0:
            suggest_fixes()
            print(f"\n⚠️  Please resolve {conflict_count} conflict(s) before running the migration.")
            sys.exit(1)
        else:
            print("\n✅ Ready to run the migration!")
            sys.exit(0)
            
    except Exception as e:
        print(f"❌ Error checking payment conflicts: {str(e)}")
        sys.exit(1)
