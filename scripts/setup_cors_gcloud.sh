#!/bin/bash

# Script to setup CORS using gcloud CLI inside Docker container
# This script can be run inside the Docker container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get bucket name from environment or argument
BUCKET_NAME=${1:-${GCS_BUCKET_NAME:-"file-upload-ravid"}}

print_status "🔧 Setting up CORS for bucket: $BUCKET_NAME"
echo "=" * 50

# Check if running inside container
if [ ! -f /.dockerenv ]; then
    print_warning "This script is designed to run inside Docker container"
    print_status "Run: make shell, then ./scripts/setup_cors_gcloud.sh"
fi

# Install gcloud CLI if not present
if ! command -v gcloud &> /dev/null; then
    print_status "Installing Google Cloud CLI..."
    
    # Update package list
    apt-get update -qq
    
    # Install dependencies
    apt-get install -y -qq curl apt-transport-https ca-certificates gnupg
    
    # Add Google Cloud SDK repository
    echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
    
    # Import Google Cloud public key
    curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
    
    # Update and install
    apt-get update -qq
    apt-get install -y -qq google-cloud-cli
    
    print_success "Google Cloud CLI installed"
else
    print_success "Google Cloud CLI already available"
fi

# Authenticate using service account
if [ -f "/app/credentials/credentials.json" ]; then
    print_status "Authenticating with service account..."
    gcloud auth activate-service-account --key-file=/app/credentials/credentials.json
    
    # Set project
    gcloud config set project macro-thinker-436514-p5
    
    print_success "Authenticated with service account"
else
    print_error "Service account credentials not found at /app/credentials/credentials.json"
    print_status "You may need to authenticate manually:"
    print_status "  gcloud auth login"
    exit 1
fi

# Check if bucket exists
print_status "Checking bucket access..."
if gsutil ls -b gs://$BUCKET_NAME > /dev/null 2>&1; then
    print_success "Bucket gs://$BUCKET_NAME is accessible"
else
    print_error "Cannot access bucket gs://$BUCKET_NAME"
    print_status "Please check:"
    print_status "  1. Bucket name is correct"
    print_status "  2. Service account has proper permissions"
    exit 1
fi

# Check current CORS configuration
print_status "Checking current CORS configuration..."
CURRENT_CORS=$(gsutil cors get gs://$BUCKET_NAME 2>/dev/null || echo "[]")
if [ "$CURRENT_CORS" != "[]" ] && [ "$CURRENT_CORS" != "" ]; then
    print_warning "Bucket already has CORS configuration:"
    echo "$CURRENT_CORS"
fi

# Apply CORS configuration
print_status "Applying CORS configuration..."
CORS_CONFIG_FILE="/app/scripts/cors-config.json"

if [ ! -f "$CORS_CONFIG_FILE" ]; then
    print_error "CORS configuration file not found: $CORS_CONFIG_FILE"
    exit 1
fi

# Try to apply CORS configuration
if gsutil cors set "$CORS_CONFIG_FILE" gs://$BUCKET_NAME; then
    print_success "CORS configuration applied successfully!"
else
    print_error "Failed to apply CORS configuration"
    print_status "This might be due to insufficient permissions."
    print_status "The service account needs 'Storage Admin' role or 'storage.buckets.update' permission."
    exit 1
fi

# Verify configuration
print_status "Verifying CORS configuration..."
NEW_CORS=$(gsutil cors get gs://$BUCKET_NAME)
echo "Current CORS configuration:"
echo "$NEW_CORS"

print_success "🎉 CORS configuration completed successfully!"
echo
echo "Next steps:"
echo "1. Restart your Django development server"
echo "2. Test file uploads from your frontend application"
echo "3. Check browser developer tools for any remaining CORS errors"
