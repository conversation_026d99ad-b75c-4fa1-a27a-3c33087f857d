#!/bin/bash

# Test cleanup script for billing module
# This script cleans up test environment and containers

set -e  # Exit on any error

echo "🧹 Cleaning up test environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Use docker compose or docker-compose based on availability
if docker compose version > /dev/null 2>&1; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

print_status "Using: $DOCKER_COMPOSE"

# Stop and remove test containers
print_status "Stopping test containers..."
$DOCKER_COMPOSE -f docker-compose-test.yaml down --remove-orphans --volumes > /dev/null 2>&1 || true

print_success "Test containers stopped ✓"

# Remove test images if requested
if [ "$1" = "--images" ]; then
    print_status "Removing test images..."
    
    # Get test image names
    TEST_IMAGES=$($DOCKER_COMPOSE -f docker-compose-test.yaml config --services | xargs -I {} echo "ravid-server_{}")
    
    for image in $TEST_IMAGES; do
        if docker images -q "$image" > /dev/null 2>&1; then
            docker rmi "$image" > /dev/null 2>&1 || true
            print_status "Removed image: $image"
        fi
    done
    
    print_success "Test images removed ✓"
fi

# Clean up test volumes
print_status "Cleaning up test volumes..."
docker volume prune -f > /dev/null 2>&1 || true

print_success "Test volumes cleaned ✓"

# Clean up test networks
print_status "Cleaning up test networks..."
docker network prune -f > /dev/null 2>&1 || true

print_success "Test networks cleaned ✓"

# Remove test reports and coverage files if requested
if [ "$1" = "--all" ] || [ "$2" = "--all" ]; then
    print_status "Removing test reports and coverage files..."
    
    rm -rf test-reports/ > /dev/null 2>&1 || true
    rm -rf htmlcov/ > /dev/null 2>&1 || true
    rm -rf .coverage > /dev/null 2>&1 || true
    rm -rf .pytest_cache/ > /dev/null 2>&1 || true
    
    print_success "Test reports and coverage files removed ✓"
fi

print_success "🎉 Test environment cleanup completed!"

echo ""
print_status "Usage:"
print_status "  ./scripts/test-cleanup.sh           # Basic cleanup"
print_status "  ./scripts/test-cleanup.sh --images  # Also remove test images"
print_status "  ./scripts/test-cleanup.sh --all     # Remove everything including reports"
