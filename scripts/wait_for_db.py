#!/usr/bin/env python3
"""
Wait for database to be ready script.
This script waits for the database to be available before proceeding.
"""
import os
import sys
import time
import psycopg
from psycopg import OperationalError

def wait_for_db(max_attempts=30, delay=2):
    """
    Wait for database to be ready.
    
    Args:
        max_attempts (int): Maximum number of connection attempts
        delay (int): Delay between attempts in seconds
    
    Returns:
        bool: True if database is ready, False otherwise
    """
    # Get database connection parameters from environment
    db_config = {
        'host': os.getenv('POSTGRES_HOST', 'test-db'),
        'port': os.getenv('POSTGRES_PORT', '5432'),
        'dbname': os.getenv('POSTGRES_DB', 'test_ravid'),
        'user': os.getenv('POSTGRES_USER', 'test_user'),
        'password': os.getenv('POSTGRES_PASSWORD', 'test_pass'),
    }
    
    print(f"Waiting for database at {db_config['host']}:{db_config['port']}...")
    
    for attempt in range(1, max_attempts + 1):
        try:
            # Try to connect to the database
            with psycopg.connect(**db_config) as conn:
                with conn.cursor() as cur:
                    cur.execute('SELECT 1')
                    result = cur.fetchone()
                    if result:
                        print(f"✅ Database is ready! (attempt {attempt}/{max_attempts})")
                        return True
        except OperationalError as e:
            print(f"⏳ Database not ready yet (attempt {attempt}/{max_attempts}): {e}")
            if attempt < max_attempts:
                time.sleep(delay)
            else:
                print(f"❌ Database failed to become ready after {max_attempts} attempts")
                return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False
    
    return False

def main():
    """Main function."""
    if not wait_for_db():
        print("❌ Failed to connect to database")
        sys.exit(1)
    
    print("🎉 Database is ready!")
    sys.exit(0)

if __name__ == '__main__':
    main()
