#!/bin/bash

# Test setup script for billing module
# This script prepares the environment for running tests

set -e  # Exit on any error

echo "🧪 Setting up test environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running ✓"

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1 && ! docker compose version > /dev/null 2>&1; then
    print_error "Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

# Use docker compose or docker-compose based on availability
if docker compose version > /dev/null 2>&1; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

print_status "Using: $DOCKER_COMPOSE"

# Clean up any existing test containers
print_status "Cleaning up existing test containers..."
$DOCKER_COMPOSE -f docker-compose-test.yaml down --remove-orphans > /dev/null 2>&1 || true

# Check for port conflicts
print_status "Checking for port conflicts..."

# Check if port 5434 is in use
if lsof -Pi :5434 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_warning "Port 5434 is already in use. This might cause conflicts."
    print_status "Attempting to find and stop conflicting services..."
    
    # Try to stop any existing test containers
    $DOCKER_COMPOSE -f docker-compose-test.yaml down --remove-orphans > /dev/null 2>&1 || true
    
    # Wait a moment for cleanup
    sleep 2
    
    if lsof -Pi :5434 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_error "Port 5434 is still in use. Please stop the conflicting service manually."
        print_status "You can find what's using the port with: lsof -i :5434"
        exit 1
    fi
fi

# Check if port 6381 is in use
if lsof -Pi :6381 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_warning "Port 6381 is already in use. This might cause conflicts."
    print_status "Attempting to find and stop conflicting services..."
    
    # Try to stop any existing test containers
    $DOCKER_COMPOSE -f docker-compose-test.yaml down --remove-orphans > /dev/null 2>&1 || true
    
    # Wait a moment for cleanup
    sleep 2
    
    if lsof -Pi :6381 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_error "Port 6381 is still in use. Please stop the conflicting service manually."
        print_status "You can find what's using the port with: lsof -i :6381"
        exit 1
    fi
fi

print_success "Port check completed ✓"

# Create necessary directories
print_status "Creating test directories..."
mkdir -p test-reports
mkdir -p htmlcov

print_success "Test directories created ✓"

# Build test images if needed
print_status "Building test images..."
$DOCKER_COMPOSE -f docker-compose-test.yaml build --quiet

print_success "Test images built ✓"

# Start test database and redis
print_status "Starting test database and Redis..."
$DOCKER_COMPOSE -f docker-compose-test.yaml up -d test-db test-redis

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 5

# Check if services are healthy
print_status "Checking service health..."

# Check database
if $DOCKER_COMPOSE -f docker-compose-test.yaml exec -T test-db pg_isready -U test_user > /dev/null 2>&1; then
    print_success "Test database is ready ✓"
else
    print_error "Test database is not ready"
    $DOCKER_COMPOSE -f docker-compose-test.yaml logs test-db
    exit 1
fi

# Check Redis
if $DOCKER_COMPOSE -f docker-compose-test.yaml exec -T test-redis redis-cli ping > /dev/null 2>&1; then
    print_success "Test Redis is ready ✓"
else
    print_error "Test Redis is not ready"
    $DOCKER_COMPOSE -f docker-compose-test.yaml logs test-redis
    exit 1
fi

print_success "🎉 Test environment is ready!"
print_status "You can now run tests with:"
print_status "  make test           # Run all tests"
print_status "  make test-unit      # Run unit tests only"
print_status "  make test-coverage  # Run tests with coverage"

echo ""
print_status "To clean up test environment when done:"
print_status "  make clean-test"
