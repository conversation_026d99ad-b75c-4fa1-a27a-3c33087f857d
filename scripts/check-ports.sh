#!/bin/bash

# Port conflict checker for test environment
# This script checks for port conflicts before running tests

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Ports used by test environment
TEST_PORTS=(5434 6381)
DEV_PORTS=(5432 6380 8000 25)

echo "🔍 Checking port conflicts..."

# Function to check if port is in use
check_port() {
    local port=$1
    local service_name=$2
    
    if command -v lsof > /dev/null 2>&1; then
        # Use lsof if available
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            local pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
            local process=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
            return 0  # Port is in use
        fi
    elif command -v netstat > /dev/null 2>&1; then
        # Use netstat as fallback
        if netstat -ln | grep ":$port " >/dev/null 2>&1; then
            return 0  # Port is in use
        fi
    else
        print_warning "Neither lsof nor netstat available. Cannot check port $port"
        return 2  # Cannot check
    fi
    
    return 1  # Port is free
}

# Function to get process info for port
get_port_info() {
    local port=$1
    
    if command -v lsof > /dev/null 2>&1; then
        local pid=$(lsof -Pi :$port -sTCP:LISTEN -t 2>/dev/null)
        if [ -n "$pid" ]; then
            local process=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
            local cmd=$(ps -p $pid -o args= 2>/dev/null || echo "unknown command")
            echo "PID: $pid, Process: $process, Command: $cmd"
        fi
    fi
}

# Check development environment ports
print_status "Checking development environment ports..."
dev_conflicts=0

for port in "${DEV_PORTS[@]}"; do
    case $port in
        5432)
            service_name="PostgreSQL (dev)"
            ;;
        6380)
            service_name="Redis (dev)"
            ;;
        8000)
            service_name="Django (dev)"
            ;;
        25)
            service_name="Postfix (dev)"
            ;;
        *)
            service_name="Unknown service"
            ;;
    esac
    
    check_port $port "$service_name"
    case $? in
        0)
            print_status "Port $port ($service_name) is in use - this is expected for dev environment"
            ;;
        1)
            print_warning "Port $port ($service_name) is not in use - dev environment might not be running"
            ;;
        2)
            print_warning "Cannot check port $port"
            ;;
    esac
done

echo ""

# Check test environment ports
print_status "Checking test environment ports..."
test_conflicts=0

for port in "${TEST_PORTS[@]}"; do
    case $port in
        5434)
            service_name="PostgreSQL (test)"
            ;;
        6381)
            service_name="Redis (test)"
            ;;
        *)
            service_name="Unknown test service"
            ;;
    esac
    
    check_port $port "$service_name"
    case $? in
        0)
            print_error "Port $port ($service_name) is already in use!"
            print_status "Process info: $(get_port_info $port)"
            test_conflicts=$((test_conflicts + 1))
            ;;
        1)
            print_success "Port $port ($service_name) is available ✓"
            ;;
        2)
            print_warning "Cannot check port $port"
            ;;
    esac
done

echo ""

# Summary and recommendations
if [ $test_conflicts -eq 0 ]; then
    print_success "🎉 No port conflicts detected! Test environment can start safely."
    echo ""
    print_status "You can now run:"
    print_status "  make test           # Run all tests"
    print_status "  make test-unit      # Run unit tests only"
    print_status "  make test-coverage  # Run tests with coverage"
else
    print_error "❌ Found $test_conflicts port conflict(s)!"
    echo ""
    print_status "To resolve conflicts:"
    print_status "1. Stop conflicting services manually"
    print_status "2. Or run: make clean-test"
    print_status "3. Or change ports in docker-compose-test.yaml"
    echo ""
    print_status "To find what's using a port:"
    print_status "  lsof -i :PORT_NUMBER"
    echo ""
    print_status "To kill a process using a port:"
    print_status "  kill -9 \$(lsof -t -i:PORT_NUMBER)"
    
    exit 1
fi

# Check Docker
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

print_status "Docker is running ✓"

# Check Docker Compose
if docker compose version > /dev/null 2>&1; then
    print_status "Docker Compose is available ✓"
elif command -v docker-compose > /dev/null 2>&1; then
    print_status "Docker Compose (legacy) is available ✓"
else
    print_error "Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

print_success "✅ Environment check completed successfully!"
