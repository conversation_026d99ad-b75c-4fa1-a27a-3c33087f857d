#!/usr/bin/env python3
"""
Script to configure CORS settings for Google Cloud Storage bucket.
This script sets up CORS to allow frontend applications to upload files directly to GCS.
"""

import os
import sys
import django
from google.cloud import storage
from google.oauth2 import service_account

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from django.conf import settings


def configure_bucket_cors():
    """Configure CORS settings for the Google Cloud Storage bucket."""
    
    try:
        # Initialize the storage client
        client = storage.Client(
            project=settings.GS_PROJECT_ID,
            credentials=settings.GS_CREDENTIALS
        )
        
        # Get the bucket
        bucket = client.bucket(settings.GS_BUCKET_NAME)
        
        # Define CORS configuration
        cors_configuration = [
            {
                "origin": [
                    "http://localhost:3000",
                    "http://localhost:3001",
                    "https://localhost:3000", 
                    "https://*.ravid.cloud",
                    "https://test.ravid.cloud",
                    "https://test.in.ravid.cloud"
                ],
                "method": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"],
                "responseHeader": [
                    "Content-Type",
                    "Content-Length",
                    "Content-Range",
                    "Content-Encoding",
                    "Date",
                    "ETag",
                    "Server",
                    "Transfer-Encoding",
                    "x-goog-generation",
                    "x-goog-metageneration",
                    "x-goog-storage-class",
                    "x-goog-stored-content-encoding",
                    "x-goog-stored-content-length",
                    "x-goog-hash",
                    "x-goog-resumable"
                ],
                "maxAgeSeconds": 3600
            }
        ]
        
        # Apply CORS configuration to the bucket
        bucket.cors = cors_configuration
        bucket.patch()
        
        print(f"✅ CORS configuration successfully applied to bucket: {settings.GS_BUCKET_NAME}")
        print("CORS Configuration:")
        for i, cors_rule in enumerate(cors_configuration, 1):
            print(f"  Rule {i}:")
            print(f"    Origins: {cors_rule['origin']}")
            print(f"    Methods: {cors_rule['method']}")
            print(f"    Max Age: {cors_rule['maxAgeSeconds']} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ Error configuring CORS: {str(e)}")
        return False


def verify_cors_configuration():
    """Verify the current CORS configuration of the bucket."""
    
    try:
        # Initialize the storage client
        client = storage.Client(
            project=settings.GS_PROJECT_ID,
            credentials=settings.GS_CREDENTIALS
        )
        
        # Get the bucket
        bucket = client.bucket(settings.GS_BUCKET_NAME)
        
        # Reload bucket to get current configuration
        bucket.reload()
        
        print(f"\n📋 Current CORS configuration for bucket: {settings.GS_BUCKET_NAME}")
        
        if bucket.cors:
            for i, cors_rule in enumerate(bucket.cors, 1):
                print(f"  Rule {i}:")
                print(f"    Origins: {cors_rule.get('origin', [])}")
                print(f"    Methods: {cors_rule.get('method', [])}")
                print(f"    Response Headers: {cors_rule.get('responseHeader', [])}")
                print(f"    Max Age: {cors_rule.get('maxAgeSeconds', 'Not set')} seconds")
                print()
        else:
            print("  No CORS configuration found.")
            
        return True
        
    except Exception as e:
        print(f"❌ Error verifying CORS configuration: {str(e)}")
        return False


if __name__ == "__main__":
    print("🔧 Google Cloud Storage CORS Configuration Tool")
    print("=" * 50)
    
    # Verify current configuration
    print("1. Checking current CORS configuration...")
    verify_cors_configuration()
    
    # Configure CORS
    print("\n2. Applying new CORS configuration...")
    success = configure_bucket_cors()
    
    if success:
        print("\n3. Verifying updated configuration...")
        verify_cors_configuration()
        
        print("\n🎉 CORS configuration completed successfully!")
        print("\nNext steps:")
        print("1. Restart your Django development server")
        print("2. Test file uploads from your frontend application")
        print("3. Check browser developer tools for any remaining CORS errors")
    else:
        print("\n❌ CORS configuration failed. Please check the error messages above.")
        sys.exit(1)
