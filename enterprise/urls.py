from django.urls import include, path

from enterprise.api.views.admin_email import RequestAdminView
from enterprise.api.views.admin_info import AdminInfoViewSet
from enterprise.api.views.base import AdminPromotionView, EnterpriseDetailView, emailtemp, remove_enterprise_logo
from enterprise.api.views.enterprise_doctor_actions import EnterpriseDoctorActionsViewSet
from enterprise.api.views.enterprise_member import EnterpriseMemberViewSet
from enterprise.api.views.enterprise_solution import EnterpriseSolutionViewSet
from enterprise.api.views.enterprise_users import EnterpriseUsersView
from enterprise.api.views.invitation import AcceptInvitationView, InviteUserToEnterpriseView

from rest_framework.routers import DefaultRouter
router = DefaultRouter()
router.register(r'admin', AdminInfoViewSet, basename='enterprise-admin')
router.register(r'doctor', EnterpriseDoctorActionsViewSet, basename='enterprise-doctor')
router.register(r'solutions', EnterpriseSolutionViewSet, basename='enterprise-solution')
router.register(r'members', EnterpriseMemberViewSet, basename='enterprise-member')

urlpatterns = [
    path('', include(router.urls)),
    path('profile/', EnterpriseDetailView.as_view(), name='enterprise'),
    path('profile/<str:euid>/', EnterpriseDetailView.as_view(), name='enterprise-detail'),
    path('promote-admin/', AdminPromotionView.as_view(), name='promote-admin'),
    path('request-admin/', RequestAdminView.as_view(), name='request-admin'),
    path('emailtemp/', emailtemp, name='emailtemp'),
    path('remove-logo/', remove_enterprise_logo, name='remove-logo'),
    # Invitation endpoints
    path('<int:enterprise_id>/invite/', InviteUserToEnterpriseView.as_view(), name='invite-user-to-enterprise'),
    path('invite/accept/', AcceptInvitationView.as_view(), name='accept-invitation'),
    # List users of an enterprise
    path('<int:enterprise_id>/users/', EnterpriseUsersView.as_view(), name='enterprise-users'),
]
