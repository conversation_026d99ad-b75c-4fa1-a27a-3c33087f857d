import stripe
from django.conf import settings
from django.utils import timezone
from django.core.exceptions import ValidationError
from .models import Enterprise, EnterpriseMember, EnterpriseInvitation
from billing.models import SolutionAccess, SolutionUserAccess, Customer, Price
from billing.services import StripeService
from content_management.models import Solution
import logging
from accounts.send_email import send_email
from django.utils.crypto import get_random_string
from django.urls import reverse
from django.contrib.auth import get_user_model
User = get_user_model()

logger = logging.getLogger(__name__)

class EnterpriseSolutionService:
    @staticmethod
    def create_enterprise_solution(enterprise_id, solution_id, owner_id):
        """
        Create a new enterprise solution
        """
        try:
            enterprise = Enterprise.objects.get(id=enterprise_id)
            solution = Solution.objects.get(id=solution_id)
            
            # Create subscription
            subscription = StripeService.create_subscription(
                customer=enterprise.customer,
                price=solution.price,
                quantity=1  # Start with 1 seat
            )
            
            # Create solution access
            solution_access = SolutionAccess.objects.create(
                enterprise=enterprise,
                solution=solution,
                subscription=subscription,
                total_seats=1,
                used_seats=1,
                status='active'
            )
            
            # Create owner access
            SolutionUserAccess.objects.create(
                solution_access=solution_access,
                user_id=owner_id,
                role='owner'  # Set owner role for the first user
            )
            
            return solution_access
            
        except Enterprise.DoesNotExist:
            raise ValidationError("Enterprise not found")
        except Solution.DoesNotExist:
            raise ValidationError("Solution not found")
        except Exception as e:
            logger.error(f"Error creating enterprise solution: {str(e)}")
            raise ValidationError(f"Error creating enterprise solution: {str(e)}")

    @staticmethod
    def add_users(solution_access_id, user_ids):
        """
        Add users to an enterprise solution
        """
        try:
            solution_access = SolutionAccess.objects.get(id=solution_access_id)
            
            # Calculate new total seats needed
            new_total_seats = solution_access.used_seats + len(user_ids)
            
            if new_total_seats > solution_access.total_seats:
                # Update subscription with new quantity
                stripe.Subscription.modify(
                    solution_access.subscription.stripe_subscription_id,
                    items=[{
                        'id': solution_access.subscription.stripe_subscription_item_id,
                        'quantity': new_total_seats
                    }]
                )
                
                # Update solution access
                solution_access.total_seats = new_total_seats
                solution_access.save()
            
            # Create user access records
            for user_id in user_ids:
                SolutionUserAccess.objects.create(
                    solution_access=solution_access,
                    user_id=user_id,
                    role='member'  # Default role for new users
                )
            
            # Update used seats
            solution_access.used_seats = new_total_seats
            solution_access.save()
            
            return solution_access
            
        except SolutionAccess.DoesNotExist:
            raise ValidationError("Solution access not found")
        except Exception as e:
            logger.error(f"Error adding users: {str(e)}")
            raise ValidationError(f"Error adding users: {str(e)}")

    @staticmethod
    def remove_users(solution_access_id, user_ids):
        """
        Remove users from enterprise solution
        """
        try:
            logger.debug(f"Attempting to remove users {user_ids} from solution access {solution_access_id}")
            
            solution_access = SolutionAccess.objects.get(id=solution_access_id)
            current_users = solution_access.used_seats
            removed_users_count = len(user_ids)
            
            logger.debug(f"Current users: {current_users}, removing {removed_users_count} users")
            
            # Update solution access
            logger.debug(f"Updating solution access used_seats from {current_users} to {current_users - removed_users_count}")
            solution_access.used_seats = current_users - removed_users_count
            solution_access.save()
            
            # Remove users
            logger.debug(f"Revoking access for users: {user_ids}")
            SolutionUserAccess.objects.filter(
                solution_access=solution_access,
                user_id__in=user_ids
            ).update(status='revoked', revoked_at=timezone.now())
            
            logger.debug("Successfully removed users from solution")
            return solution_access
            
        except SolutionAccess.DoesNotExist:
            logger.error(f"Solution access {solution_access_id} not found")
            raise ValidationError("Solution access not found")
        except Exception as e:
            logger.error(f"Error removing users from solution: {str(e)}")
            raise ValidationError(f"Error removing users: {str(e)}")

    @staticmethod
    def cancel_solution(solution_access_id):
        """
        Cancel enterprise solution
        """
        try:
            solution_access = SolutionAccess.objects.get(id=solution_access_id)
            
            # Cancel Stripe subscription
            stripe.Subscription.delete(solution_access.subscription.stripe_subscription_id)
            
            # Update solution access
            solution_access.status = 'canceled'
            solution_access.save()
            
            # Deactivate all users
            SolutionUserAccess.objects.filter(
                solution_access=solution_access
            ).update(status='revoked', revoked_at=timezone.now())
            
            return solution_access
            
        except SolutionAccess.DoesNotExist:
            raise ValidationError("Solution access not found")
        except Exception as e:
            logger.error(f"Error canceling solution: {str(e)}")
            raise ValidationError(f"Error canceling solution: {str(e)}")

    @staticmethod
    def get_solution_users(solution_access_id):
        """
        Get all active users for a solution
        """
        try:
            solution_access = SolutionAccess.objects.get(id=solution_access_id)
            return SolutionUserAccess.objects.filter(
                solution_access=solution_access,
                status='active'
            )
        except SolutionAccess.DoesNotExist:
            raise ValidationError("Solution access not found")

    @staticmethod
    def get_enterprise_solutions(enterprise_id):
        """
        Get all solutions for an enterprise
        """
        try:
            enterprise = Enterprise.objects.get(id=enterprise_id)
            return SolutionAccess.objects.filter(enterprise=enterprise)
        except Enterprise.DoesNotExist:
            raise ValidationError("Enterprise not found")

class EnterpriseInvitationService:
    @staticmethod
    def invite_user_to_solution(email, enterprise, solution_access, invited_by, expires_in_days=7):
        # Generate unique token
        token = get_random_string(48)
        expires_at = timezone.now() + timezone.timedelta(days=expires_in_days)
        invitation = EnterpriseInvitation.objects.create(
            email=email,
            enterprise=enterprise,
            solution_access=solution_access,
            token=token,
            status="pending",
            invited_by=invited_by,
            expires_at=expires_at
        )
        # Build invite link
        invite_url = f"{settings.FRONTEND_URL}/enterprise/invite/accept/?token={token}"
        owner_name = enterprise.owner.get_full_name() if enterprise.owner and hasattr(enterprise.owner, 'get_full_name') and enterprise.owner.get_full_name() else (enterprise.owner.email if enterprise.owner and hasattr(enterprise.owner, 'email') else 'Owner')
        enterprise_display_name = enterprise.name if enterprise.name else "enterprise solution"
        subject = f"You're invited to join {enterprise_display_name} on RAVID"
        html_content = f"""
            <p>Hello,</p>
            <p>You have been invited by <b>{owner_name}</b> to be a part of their <b>\"{enterprise_display_name}\"</b>.</p>
            <p>Click <a href='{invite_url}'>here</a> to accept the invitation.</p>
            <p>This link will expire in {expires_in_days} days.</p>
        """
        plain_content = f"Hello,\nYou have been invited by {owner_name} to be a part of their '{enterprise_display_name}'. Visit: {invite_url}\nThis link will expire in {expires_in_days} days."
        send_email(email, subject, html_content, plain_content)
        return invitation

    @staticmethod
    def accept_invitation(token, user):
        logger.info(f"Attempting to accept invitation with token {token} for user {user.id}")
        try:
            invitation = EnterpriseInvitation.objects.get(token=token, status="pending")
            logger.debug(f"Found pending invitation {invitation.id} for email {invitation.email}")
        except EnterpriseInvitation.DoesNotExist:
            logger.warning(f"Invalid or expired invitation token: {token}")
            raise ValidationError("Invalid or expired invitation token.")

        # Only allow the invited user to accept
        if user.email.lower() != invitation.email.lower():
            logger.warning(f"User {user.email} is not the invited email {invitation.email}")
            raise ValidationError("You are not authorized to accept this invitation.")

        if invitation.expires_at < timezone.now():
            logger.info(f"Invitation {invitation.id} has expired")
            invitation.status = "expired"
            invitation.save()
            raise ValidationError("This invitation has expired.")

        # Link user to enterprise/solution
        logger.info(f"Accepting invitation {invitation.id} for user {user.id}")
        invitation.status = "accepted"
        invitation.accepted_at = timezone.now()
        invitation.accepted_user = user
        invitation.save()

        # Add user to solution access if provided
        if invitation.solution_access:
            logger.info(f"Adding user {user.id} to solution access {invitation.solution_access.id}")
            solution_user_access, created = SolutionUserAccess.objects.get_or_create(
                solution_access=invitation.solution_access,
                user=user,
                defaults={"role": "member", "status": "active"}
            )
            logger.debug(f"Solution user access {'created' if created else 'already existed'}")

        # Add user as EnterpriseMember if not already
        logger.info(f"Adding user {user.id} as member of enterprise {invitation.enterprise.id}")
        member, created = EnterpriseMember.objects.get_or_create(
            enterprise=invitation.enterprise,
            user=user,
            defaults={"role": "member", "is_active": True}
        )
        logger.debug(f"Enterprise member {'created' if created else 'already existed'}")

        logger.info(f"Successfully accepted invitation {invitation.id}")
        return invitation