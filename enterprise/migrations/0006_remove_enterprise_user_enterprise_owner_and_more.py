# Generated by Django 5.0.9 on 2025-05-08 14:33

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('enterprise', '0005_alter_enterprise_user'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='enterprise',
            name='user',
        ),
        migrations.AddField(
            model_name='enterprise',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='owned_enterprises', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enterprise',
            name='stripe_customer_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.CreateModel(
            name='EnterpriseMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.<PERSON>r<PERSON><PERSON>(choices=[('admin', 'Administrator'), ('manager', 'Manager'), ('member', 'Member')], default='member', max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('enterprise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='members', to='enterprise.enterprise')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enterprise_memberships', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('enterprise', 'user')},
            },
        ),
    ]
