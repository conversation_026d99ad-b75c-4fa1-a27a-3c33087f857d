# Generated by Django 5.0.9 on 2025-05-16 15:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('billing', '0013_servicepayment_services'),
        ('enterprise', '0006_remove_enterprise_user_enterprise_owner_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EnterpriseInvitation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('token', models.CharField(max_length=64, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('accepted', 'Accepted'), ('expired', 'Expired'), ('revoked', 'Revoked')], default='pending', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('accepted_at', models.DateTimeField(blank=True, null=True)),
                ('accepted_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='accepted_enterprise_invitations', to=settings.AUTH_USER_MODEL)),
                ('enterprise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='enterprise.enterprise')),
                ('invited_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_enterprise_invitations', to=settings.AUTH_USER_MODEL)),
                ('solution_access', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='billing.solutionaccess')),
            ],
            options={
                'indexes': [models.Index(fields=['email', 'enterprise', 'status'], name='enterprise__email_79197a_idx'), models.Index(fields=['token'], name='enterprise__token_42ab0a_idx')],
                'unique_together': {('email', 'enterprise', 'solution_access', 'status')},
            },
        ),
    ]
