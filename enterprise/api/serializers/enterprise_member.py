from rest_framework import serializers
from enterprise.models import EnterpriseMember
from accounts.serializer import UserSerializer

class EnterpriseMemberSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    user_id = serializers.PrimaryKeyRelatedField(queryset=EnterpriseMember._meta.get_field('user').related_model.objects.all(), source='user', write_only=True)

    class Meta:
        model = EnterpriseMember
        fields = ['id', 'user', 'user_id', 'role', 'is_active', 'joined_at']
        read_only_fields = ['id', 'joined_at', 'user'] 