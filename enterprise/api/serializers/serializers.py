from venv import logger
from rest_framework import serializers
from ...models import (
    Enterprise,
    Location, 
    Service, 
    EnterpriseDoctor, 
    OperatingHours, 
    EnterpriseDepartment, 
    EnterpriseDepartmentDoctor,
    EnterpriseImage,
    EnterpriseMember,
    EnterpriseInvitation
)
from billing.models import SolutionAccess, SolutionUserAccess
from content_management.models import Solution
from accounts.serializer import UserSerializer
from ...admin import EnterpriseAdmin
from django.contrib.auth import get_user_model

User = get_user_model()

class EnterpriseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Enterprise
        fields = ['id', 'name', 'about']
        read_only_fields = ['id']
        
class OperatingHoursSerializer(serializers.ModelSerializer):
    open_time = serializers.TimeField(format='%I:%M %p', input_formats=['%I:%M %p', '%H:%M'])
    close_time = serializers.TimeField(format='%I:%M %p', input_formats=['%I:%M %p', '%H:%M'])

    class Meta:
        model = OperatingHours
        fields = ['id', 'day_of_week', 'open_time', 'close_time']
        read_only_fields = ['id']

    def validate(self, data):
        open_time = data.get('open_time')
        close_time = data.get('close_time')
        if open_time and close_time and open_time >= close_time:
            raise serializers.ValidationError("Close time must be after open time.")
        return data


class LocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = ['id', 'address', 'city', 'state', 'zip_code', 'country']


class EnterpriseImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = EnterpriseImage
        fields = ['id', 'image', 'uploaded_at']

class ServiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Service
        fields = ['id', 'name', 'description']


class EnterpriseDoctorSerializer(serializers.ModelSerializer):
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    
    class Meta:
        model = EnterpriseDoctor
        fields = ['id', 'doctor', 'doctor_name']
        read_only_fields = ['id']


class EnterpriseDepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = EnterpriseDepartment
        fields = ['id', 'name', 'description']
        read_only_fields = ['id']
        
class EnterpriseDepartmentDoctorSerializer(serializers.ModelSerializer):
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    class Meta:
        model = EnterpriseDepartmentDoctor
        fields = ['id', 'department', 'doctor', 'doctor_name']
        read_only_fields = ['id']

class EnterpriseMemberSerializer(serializers.ModelSerializer):
    user_email = serializers.EmailField(source='user.email', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = EnterpriseMember
        fields = ['id', 'user', 'user_email', 'user_name', 'role', 'is_active', 'joined_at']
        read_only_fields = ['id', 'joined_at']

class SolutionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Solution
        fields = ['id', 'name', 'description', 'solution_type', 'features', 'price']

class SolutionUserAccessSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = SolutionUserAccess
        fields = ['id', 'user', 'status', 'assigned_at', 'revoked_at']

class SolutionAccessSerializer(serializers.ModelSerializer):
    solution = SolutionSerializer(read_only=True)
    user_access = SolutionUserAccessSerializer(many=True, read_only=True)
    
    class Meta:
        model = SolutionAccess
        fields = ['id', 'solution', 'status', 'total_seats', 'used_seats', 
                 'available_seats', 'user_access', 'access_granted_at']

class EnterpriseDetailSerializer(serializers.ModelSerializer):
    admin_email = serializers.EmailField(source='owner.email', read_only=True)
    contact_number = serializers.CharField(required=False,allow_blank=True)
    logo = serializers.CharField(required=False, read_only=True,allow_null=True, allow_blank=True)
    additional_email = serializers.EmailField(required=False,allow_blank=True)
    language_spoken = serializers.CharField(required=False,allow_blank=True)
    insurance_accepted = serializers.BooleanField(required=False,allow_null=True )
    location = LocationSerializer(required=False,allow_null=True)
    services = ServiceSerializer(many=True, required=False,allow_null=True)
    is_enterprise_public = serializers.BooleanField(required=False,allow_null=True)
    doctors = EnterpriseDoctorSerializer(many=True, required=False,allow_null=True)
    operating_hours = OperatingHoursSerializer(many=True, required=False,allow_null=True)
    departments = EnterpriseDepartmentSerializer(many=True, required=False,allow_null=True)
    enterprise_department_doctors = EnterpriseDepartmentDoctorSerializer(many=True, required=False,allow_null=True)
    enterprise_images = EnterpriseImageSerializer(many=True, required=False,allow_null=True)
    members = EnterpriseMemberSerializer(many=True, read_only=True)
    solution_access = SolutionAccessSerializer(many=True, read_only=True)

    logo_file = serializers.ImageField(required=False,allow_null=True)
    identification_number = serializers.CharField(required=False,allow_blank=True,allow_null=True)
    state_of_incorporation = serializers.CharField(required=False,allow_blank=True,allow_null=True)
    date_of_incorporation = serializers.DateField(required=False,allow_null=True)
    website = serializers.CharField(required=False,allow_blank=True,allow_null=True)

    class Meta:
        model = Enterprise
        fields = ['id', 'euid', 'admin_email', 'name', 'about', 'contact_number', 'additional_email', 'language_spoken',
                  'insurance_accepted', 'logo', 'location', 'services', 'doctors', 'operating_hours',
                  'departments', 'enterprise_department_doctors', 'enterprise_images', 'is_enterprise_public',
                  'logo_file', 'identification_number', 'state_of_incorporation', 'date_of_incorporation', 'website',
                  'members', 'solution_access'
                  ]
        read_only_fields = ['id', 'euid']

    def update(self, instance, validated_data):
        user_data = validated_data.pop('owner', {})
        
        user = instance.owner
        for attr, value in user_data.items():
            setattr(user, attr, value)
        user.save()

        nested_fields = {
            'location': LocationSerializer,
            'services': ServiceSerializer,
            'doctors': EnterpriseDoctorSerializer,
            'operating_hours': OperatingHoursSerializer,
            'departments': EnterpriseDepartmentSerializer,
            'enterprise_department_doctors': EnterpriseDepartmentDoctorSerializer,
        }

        for field, serializer_class in nested_fields.items():
            if field in validated_data:
                self.update_nested_data(instance, field, validated_data.pop(field), serializer_class)

        # Handle enterprise_images separately
        if 'enterprise_images' in validated_data:
            enterprise_images_data = validated_data.pop('enterprise_images')
            for image_data in enterprise_images_data:
                EnterpriseImage.objects.create(enterprise=instance, **image_data)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance
    
    def update_nested_data(self, instance, field_name, data, serializer_class):
        if field_name == 'location':
            # Handle one-to-one relationship (location)
            if hasattr(instance, 'location') and instance.location:
                # Update existing location
                location_serializer = serializer_class(instance.location, data=data, partial=True)
            else:
                # Create new location
                location_serializer = serializer_class(data=data)
            
            if location_serializer.is_valid():
                location = location_serializer.save(enterprise=instance)
                instance.location = location
                instance.save()
        elif field_name in ['services', 'doctors', 'operating_hours', 'departments', 'enterprise_department_doctors']:
            # Handle many-to-many relationships (services and doctors)
            related_manager = getattr(instance, field_name)
            related_manager.all().delete()
            for item in data:
                serializer = serializer_class(data=item)
                if serializer.is_valid():
                    serializer.save(enterprise=instance)

    def to_representation(self, instance):
        return super().to_representation(instance)

class AdminInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = EnterpriseAdmin
        fields = ['id', 'name', 'email', 'phone_number', 'position']
        read_only_fields = ['id']


class EnterpriseDoctorActionsSerializer(serializers.ModelSerializer):
    doctor_id = serializers.CharField(write_only=True)
    
    class Meta:
        model = EnterpriseDoctor
        fields = ('doctor_id',)

class EnterpriseSolutionSerializer(serializers.ModelSerializer):
    solution_name = serializers.CharField(source='solution.name', read_only=True)
    solution_description = serializers.CharField(source='solution.description', read_only=True)
    active_users_count = serializers.SerializerMethodField()
    
    class Meta:
        model = SolutionAccess
        fields = [
            'id', 'enterprise', 'solution', 'solution_name', 'solution_description',
            'total_seats', 'used_seats', 'active_users_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['stripe_subscription_id']
    
    def get_active_users_count(self, obj):
        return obj.users.filter(is_active=True).count()

class CreateEnterpriseSolutionSerializer(serializers.Serializer):
    enterprise_id = serializers.IntegerField()
    solution_id = serializers.IntegerField()
    
    def validate(self, data):
        try:
            enterprise = Enterprise.objects.get(id=data['enterprise_id'])
            solution = Solution.objects.get(id=data['solution_id'])
            
            # Check if enterprise already has this solution
            if SolutionAccess.objects.filter(enterprise=enterprise, solution=solution).exists():
                raise serializers.ValidationError("Enterprise already has this solution")
            
            return data
        except Enterprise.DoesNotExist:
            raise serializers.ValidationError("Enterprise not found")
        except Solution.DoesNotExist:
            raise serializers.ValidationError("Solution not found")

class AddUsersToSolutionSerializer(serializers.Serializer):
    user_ids = serializers.ListField(
        child=serializers.CharField(),
        min_length=1
    )
    
    def validate(self, data):
        logger.debug(f"Validating AddUsersToSolutionSerializer with data: {data}")
        solution_access_id = self.context.get('solution_access_id')
        logger.debug(f"Solution access ID from context: {solution_access_id}")
        
        try:
            solution_access = SolutionAccess.objects.get(id=solution_access_id)
            logger.debug(f"Found solution access: {solution_access}")
            
            # Convert emails to user IDs
            user_ids = []
            for identifier in data['user_ids']:
                try:
                    # Try to find user by ID first
                    user = User.objects.get(id=identifier)
                except User.DoesNotExist:
                    try:
                        # If not found by ID, try to find by email
                        user = User.objects.get(email=identifier)
                    except User.DoesNotExist:
                        raise serializers.ValidationError(f"User not found with ID or email: {identifier}")
                user_ids.append(str(user.id))
            
            # Update the user_ids with the resolved IDs
            data['user_ids'] = user_ids
            
            # Check if adding these users would exceed the subscription limit
            current_users = solution_access.used_seats
            new_users_count = len(user_ids)
            logger.debug(f"Current users: {current_users}, New users to add: {new_users_count}, Total seats: {solution_access.total_seats}")
            
            if current_users + new_users_count > solution_access.total_seats:
                logger.debug("Validation failed: Would exceed subscription limit")
                raise serializers.ValidationError(
                    f"Adding {new_users_count} users would exceed the subscription limit of {solution_access.total_seats}"
                )
            
            logger.debug("Validation successful")
            return data
        except SolutionAccess.DoesNotExist:
            logger.error(f"Solution access not found for ID: {solution_access_id}")
            raise serializers.ValidationError("Solution access not found")

class UpgradeToEnterpriseSerializer(serializers.Serializer):
    solution_id = serializers.UUIDField()
    redirect_url = serializers.CharField(required=False, allow_blank=True)
    enterprise_name = serializers.CharField(max_length=255, required=False)
    enterprise_email = serializers.EmailField(required=False)
    enterprise_phone = serializers.CharField(max_length=20, required=False)

    def validate_enterprise_address(self, value):
        required_fields = ['street', 'city', 'state', 'country', 'zip_code']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"Address must include {field}")
        return value

    def validate_solution_id(self, value):
        try:
            solution = Solution.objects.get(id=value)
            if not solution.is_active:
                raise serializers.ValidationError("Solution is not active")
            return value
        except Solution.DoesNotExist:
            raise serializers.ValidationError("Solution not found")

class EnterpriseInvitationSerializer(serializers.ModelSerializer):
    invited_by_email = serializers.EmailField(source="invited_by.email", read_only=True)
    accepted_user_email = serializers.EmailField(source="accepted_user.email", read_only=True)
    enterprise_name = serializers.CharField(source="enterprise.name", read_only=True)

    class Meta:
        model = EnterpriseInvitation
        fields = [
            "id", "email", "enterprise", "enterprise_name", "solution_access", "token", "status",
            "invited_by", "invited_by_email", "created_at", "expires_at", "accepted_at", "accepted_user", "accepted_user_email"
        ]
        read_only_fields = ["id", "token", "status", "invited_by", "created_at", "accepted_at", "accepted_user"]

    def validate(self, data):
        # Prevent duplicate pending invites for the same email/enterprise/solution
        email = data.get("email")
        enterprise = data.get("enterprise")
        solution_access = data.get("solution_access")
        if EnterpriseInvitation.objects.filter(
            email=email,
            enterprise=enterprise,
            solution_access=solution_access,
            status="pending"
        ).exists():
            raise serializers.ValidationError("A pending invitation for this user already exists.")
        return data