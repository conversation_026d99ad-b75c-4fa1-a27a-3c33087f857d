from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from enterprise.models import Enterprise, EnterpriseDoctor
from enterprise.api.serializers.serializers import EnterpriseDoctorActionsSerializer
from django.contrib.auth import get_user_model
from django.db.models import Q
from config.pagination_utils import paginate_queryset

class EnterpriseDoctorActionsViewSet(viewsets.ModelViewSet):
    serializer_class = EnterpriseDoctorActionsSerializer
    queryset = EnterpriseDoctor.objects.all()
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        enterprise = Enterprise.objects.get(user=self.request.user)
        queryset = EnterpriseDoctor.objects.filter(enterprise=enterprise)
        return queryset

    def list(self, request, *args, **kwargs):
        try:
            enterprise = Enterprise.objects.get(user=request.user)
            queryset = EnterpriseDoctor.objects\
                       .filter(enterprise=enterprise)\
                       .select_related('doctor', 'doctor__profile', 'doctor__role')

            specialty = request.query_params.get('specialty', None)
            search_text = request.query_params.get('search_text', None)

            if specialty:
                queryset = queryset.filter(
                    doctor__profile__speciality=specialty
                )

            if search_text:
                queryset = queryset.filter(
                    Q(doctor__name__icontains=search_text) |
                    Q(doctor__first_name__icontains=search_text) |
                    Q(doctor__last_name__icontains=search_text) |
                    Q(doctor__profile__speciality__icontains=search_text)
                )

            # Convert to values() after all filters are applied
            queryset = queryset.values(
                'doctor__id',
                'doctor__email',
                'doctor__name', 
                'doctor__first_name',
                'doctor__last_name',
                'doctor__is_id_verified',
                'doctor__profile__id',
                'doctor__profile__speciality'
            )

            page = request.query_params.get('page', 1)
            page_size = request.query_params.get('page_size', 10)
            paginated_queryset = paginate_queryset(queryset, page, page_size)
            
            # Transform the data to match serializer format
            formatted_data = []
            for item in paginated_queryset:
                formatted_data.append({
                    'doctor_id': item['doctor__id'],
                    'email': item['doctor__email'],
                    'name': item['doctor__name'],
                    'first_name': item['doctor__first_name'],
                    'last_name': item['doctor__last_name'],
                    'profile_id': item['doctor__profile__id'],
                    'specialty': item['doctor__profile__speciality'],
                    'is_id_verified': item['doctor__is_id_verified']
                })
            
            return Response(formatted_data)

        except Enterprise.DoesNotExist:
            return Response(
                {"error": "Enterprise not found for this user"},
                status=status.HTTP_404_NOT_FOUND
            )

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        doctor_id = serializer.validated_data.get('doctor_id')
        
        try:
            enterprise = Enterprise.objects.get(user=request.user)
            doctor = get_user_model().objects.get(id=doctor_id)
            
            EnterpriseDoctor.objects.create(
                enterprise=enterprise,
                doctor=doctor
            )
            
            return Response(
                {"message": "Doctor added successfully"}, 
                status=status.HTTP_201_CREATED
            )
            
        except get_user_model().DoesNotExist:
            return Response(
                {"error": "User not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Enterprise.DoesNotExist:
            return Response(
                {"error": "Enterprise not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )

    def destroy(self, request, *args, **kwargs):
        try:
            # Check if doctor exists
            doctor_id = kwargs['pk']
            try:
                get_user_model().objects.get(id=doctor_id)
            except get_user_model().DoesNotExist:
                return Response(
                    {"error": "Doctor not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

            enterprise = Enterprise.objects.get(user=request.user)
            
            # Check if doctor belongs to enterprise
            if not EnterpriseDoctor.objects.filter(enterprise=enterprise, doctor_id=doctor_id).exists():
                return Response(
                    {"error": "Doctor does not belong to this enterprise"},
                    status=status.HTTP_404_NOT_FOUND
                )

            enterprise_doctor = EnterpriseDoctor.objects.get(
                enterprise=enterprise,
                doctor_id=doctor_id
            )
            enterprise_doctor.delete()
            return Response(
                {"message": "Doctor removed successfully"},
                status=status.HTTP_200_OK
            )
        except Enterprise.DoesNotExist:
            return Response(
                {"error": "Enterprise not found"},
                status=status.HTTP_404_NOT_FOUND
            )
