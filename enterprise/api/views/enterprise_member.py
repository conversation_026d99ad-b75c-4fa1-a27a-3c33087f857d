from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from enterprise.models import Enterprise, EnterpriseMember
from billing.models import SolutionUserAccess, SolutionAccess
from enterprise.api.serializers.enterprise_member import EnterpriseMemberSerializer
from rest_framework.exceptions import PermissionDenied
from django.db import transaction

class IsEnterpriseOwner(IsAuthenticated):
    def has_permission(self, request, view):
        enterprise_id = request.query_params.get('enterprise_id') or request.data.get('enterprise')
        if not enterprise_id:
            return False
        enterprise = get_object_or_404(Enterprise, id=enterprise_id)
        return request.user.is_authenticated and enterprise.owner == request.user

class EnterpriseMemberViewSet(viewsets.ModelViewSet):
    serializer_class = EnterpriseMemberSerializer
    permission_classes = [IsEnterpriseOwner]
    queryset = EnterpriseMember.objects.all()

    def get_queryset(self):
        enterprise_id = self.request.query_params.get('enterprise_id')
        if not enterprise_id:
            return EnterpriseMember.objects.none()
        enterprise = get_object_or_404(Enterprise, id=enterprise_id)
        if enterprise.owner != self.request.user:
            raise PermissionDenied('Only enterprise owner can view members.')
        return EnterpriseMember.objects.filter(enterprise=enterprise)

    def perform_create(self, serializer):
        enterprise = get_object_or_404(Enterprise, id=self.request.data.get('enterprise'))
        if enterprise.owner != self.request.user:
            raise PermissionDenied('Only enterprise owner can add members.')
        serializer.save(enterprise=enterprise)

    @transaction.atomic
    def perform_destroy(self, instance):
        # Revoke all SolutionUserAccess for this user in this enterprise
        solution_accesses = SolutionAccess.objects.filter(enterprise=instance.enterprise)
        SolutionUserAccess.objects.filter(solution_access__in=solution_accesses, user=instance.user, status='active').update(status='revoked')
        instance.delete()

    def perform_update(self, serializer):
        enterprise = get_object_or_404(Enterprise, id=self.request.data.get('enterprise', serializer.instance.enterprise.id))
        if enterprise.owner != self.request.user:
            raise PermissionDenied('Only enterprise owner can update members.')
        serializer.save() 