from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from django.shortcuts import get_object_or_404
from enterprise.models import Enterprise
from billing.models import SolutionAccess, SolutionUserAccess
from enterprise.api.serializers.serializers import SolutionUserAccessSerializer

class EnterpriseUsersView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, enterprise_id):
        enterprise = get_object_or_404(Enterprise, id=enterprise_id)
        solution_access = SolutionAccess.objects.filter(
            enterprise=enterprise,
            status='active'
        ).first()
        if not solution_access:
            return Response({'error': 'No active solution for this enterprise.'}, status=status.HTTP_404_NOT_FOUND)
        users = SolutionUserAccess.objects.filter(solution_access=solution_access, status='active')
        serializer = SolutionUserAccessSerializer(users, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK) 