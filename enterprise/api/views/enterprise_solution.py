from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.conf import settings
import json
from enterprise.api.serializers.serializers import AddUsersToSolutionSerializer, CreateEnterpriseSolutionSerializer, SolutionAccessSerializer, SolutionUserAccessSerializer, UpgradeToEnterpriseSerializer
from enterprise.services import EnterpriseSolutionService
import stripe
from django.contrib.auth import get_user_model

from .base import EnterpriseViewSet
from ...models import Enterprise, Location

from billing.models import SolutionAccess, SolutionUserAccess, Customer, Price
from billing.services import StripeService
from content_management.models import Solution

CustomUser = get_user_model()

class EnterpriseSolutionViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def get_enterprise(self):
        return get_object_or_404(Enterprise, id=self.kwargs['enterprise_pk'])

    def retrieve(self, request, pk=None):
        """Get details of a specific enterprise solution"""
        try:
            solution_access = SolutionAccess.objects.get(id=pk)
            return Response(
                SolutionAccessSerializer(solution_access).data,
                status=status.HTTP_200_OK
            )
        except SolutionAccess.DoesNotExist:
            return Response(
                {'error': 'Solution access not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def create_solution(self, request, enterprise_pk=None):
        """Create a new enterprise solution"""
        serializer = CreateEnterpriseSolutionSerializer(data=request.data)
        if serializer.is_valid():
            try:
                solution_access = EnterpriseSolutionService.create_enterprise_solution(
                    enterprise_id=enterprise_pk,
                    solution_id=serializer.validated_data['solution_id'],
                    owner_id=request.user.id
                )
                return Response(
                    SolutionAccessSerializer(solution_access).data,
                    status=status.HTTP_201_CREATED
                )
            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def add_users(self, request, enterprise_pk=None, pk=None):
        """Add users to an enterprise solution"""
        serializer = AddUsersToSolutionSerializer(
            data=request.data,
            context={'solution_access_id': pk}
        )
        if serializer.is_valid():
            try:
                solution_access = EnterpriseSolutionService.add_users(
                    solution_access_id=pk,
                    user_ids=serializer.validated_data['user_ids']
                )
                return Response(
                    SolutionAccessSerializer(solution_access).data,
                    status=status.HTTP_200_OK
                )
            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def remove_users(self, request, enterprise_pk=None, pk=None):
        """Remove users from an enterprise solution"""
        serializer = AddUsersToSolutionSerializer(
            data=request.data,
            context={'solution_access_id': pk}
        )
        if serializer.is_valid():
            try:
                solution_access = EnterpriseSolutionService.remove_users(
                    solution_access_id=pk,
                    user_ids=serializer.validated_data['user_ids']
                )
                return Response(
                    SolutionAccessSerializer(solution_access).data,
                    status=status.HTTP_200_OK
                )
            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def cancel(self, request, enterprise_pk=None, pk=None):
        """Cancel an enterprise solution"""
        try:
            solution_access = EnterpriseSolutionService.cancel_solution(
                solution_access_id=pk
            )
            return Response(
                SolutionAccessSerializer(solution_access).data,
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def users(self, request, enterprise_pk=None, pk=None):
        """Get all users for an enterprise solution"""
        try:
            users = EnterpriseSolutionService.get_solution_users(
                solution_access_id=pk
            )
            return Response(
                SolutionUserAccessSerializer(users, many=True).data,
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def list(self, request, enterprise_pk=None):
        """List all solutions for an enterprise"""
        try:
            solutions = EnterpriseSolutionService.get_enterprise_solutions(
                enterprise_id=enterprise_pk
            )
            return Response(
                SolutionAccessSerializer(solutions, many=True).data,
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def upgrade_to_enterprise(self, request):
        """
        Upgrade a regular user to enterprise by purchasing an enterprise solution
        """
        serializer = UpgradeToEnterpriseSerializer(data=request.data)
        if serializer.is_valid():
            try:
                # Get solution
                solution = Solution.objects.get(
                    id=serializer.validated_data.get('solution_id', '0196b5ac-5bfc-7926-ae06-f33e58d74ef2')
                )
                # Get current user with all fields
                user = CustomUser.objects.select_related('role').get(id=request.user.id)

                # Check if user already has an enterprise and active solution access
                enterprise = Enterprise.objects.filter(owner=user).first()
                if enterprise:
                    # Check for existing active SolutionAccess
                    existing_access = SolutionAccess.objects.filter(
                        enterprise=enterprise,
                        solution=solution,
                        status='active'
                    ).first()
                    if existing_access:
                        return Response({'message': 'You already have an active subscription to this solution.'}, status=status.HTTP_200_OK)
                # Create or get customer
                try:
                    customer = StripeService.create_or_update_customer(user)
                except Exception as customer_error:
                    return Response({
                        'error': f'Error creating customer: {str(customer_error)}',
                        'details': 'Please check your Stripe configuration and user data'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                # Get price
                price = Price.objects.get(product__solution=solution)
                # Create checkout session
                redirect_url = serializer.validated_data.get('redirect_url', settings.FRONTEND_URL)
                success_url = redirect_url
                cancel_url = redirect_url
                # Get metadata from current user
                metadata = {
                    'solution_id': str(solution.id),
                    'user_id': str(user.id),
                    'enterprise_name': user.name or f"{user.first_name} {user.last_name}".strip(),
                    'enterprise_email': user.email,
                    'enterprise_phone': user.phone_number,
                    'enterprise_address': json.dumps({
                        'street': 'default',
                        'city': 'default',
                        'state': 'default',
                        'country': 'default',
                        'zip_code': 'default'
                    })
                }
                session = StripeService.create_checkout_session(
                    customer=customer,
                    price=price,
                    success_url=success_url,
                    cancel_url=cancel_url,
                    metadata=metadata,
                    mode='subscription'  # Set mode to subscription
                )
                return Response({
                    'checkout_url': session.url
                }, status=status.HTTP_200_OK)
            except Solution.DoesNotExist:
                return Response(
                    {'error': 'Solution not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            except Price.DoesNotExist:
                return Response(
                    {'error': 'Solution price not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def increase_seats(self, request, enterprise_pk=None, pk=None):
        """Increase the number of seats for an enterprise solution"""
        try:
            solution_access = SolutionAccess.objects.get(id=pk)
            new_seats = request.data.get('seats', 0)
            
            if not isinstance(new_seats, int) or new_seats <= 0:
                return Response(
                    {'error': 'Invalid number of seats'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Update Stripe subscription
            stripe.Subscription.modify(
                solution_access.subscription.stripe_subscription_id,
                items=[{
                    'id': solution_access.subscription.stripe_subscription_item_id,
                    'quantity': new_seats
                }]
            )
            
            # Update solution access
            solution_access.total_seats = new_seats
            solution_access.save()
            
            return Response(
                SolutionAccessSerializer(solution_access).data,
                status=status.HTTP_200_OK
            )
            
        except SolutionAccess.DoesNotExist:
            return Response(
                {'error': 'Solution access not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            ) 