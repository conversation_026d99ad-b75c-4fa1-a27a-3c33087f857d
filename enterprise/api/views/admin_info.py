from rest_framework import viewsets, permissions
from enterprise.api.serializers.serializers import AdminInfoSerializer
from enterprise.admin import EnterpriseAdmin
from rest_framework.response import Response
from rest_framework import status
from django.db import transaction
from django.http import Http404
from rest_framework import serializers
from config.pagination_utils import paginate_queryset
from enterprise.models import Enterprise


class AdminInfoViewSet(viewsets.ModelViewSet):
    serializer_class = AdminInfoSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'

    def get_queryset(self):
        enterprise = Enterprise.objects.get(user=self.request.user)
        queryset = EnterpriseAdmin.objects.filter(enterprise=enterprise)
        page = self.request.query_params.get('page', 1)
        page_size = self.request.query_params.get('page_size', 10)
        return paginate_queryset(queryset, page, page_size)

    def _handle_admin_item(self, item, enterprise):
        if 'id' in item:
            try:
                instance = EnterpriseAdmin.objects.get(id=item['id'], enterprise=enterprise)
                serializer = self.get_serializer(instance, data=item, partial=True)
            except EnterpriseAdmin.DoesNotExist:
                raise Http404(f"Admin with id {item['id']} not found")
        else:
            serializer = self.get_serializer(data=item)
            
        if serializer.is_valid():
            serializer.save(enterprise=enterprise)
            return serializer
        raise serializers.ValidationError(serializer.errors)

    def create(self, request, *args, **kwargs):
        try:
            enterprise = Enterprise.objects.get(user=request.user)
        except Enterprise.DoesNotExist:
            return Response({"error": "Enterprise not found for this user"}, status=status.HTTP_404_NOT_FOUND)
            
        if isinstance(request.data, list):
            try:
                with transaction.atomic():
                    admin_serializers = [self._handle_admin_item(item, enterprise) for item in request.data]
                    return Response([s.data for s in admin_serializers], status=status.HTTP_200_OK)
            except Http404 as e:
                return Response({"error": str(e)}, status=status.HTTP_404_NOT_FOUND)
            except serializers.ValidationError as e:
                return Response(e.detail, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
        return super().create(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        try:
            enterprise = Enterprise.objects.get(user=request.user)
        except Enterprise.DoesNotExist:
            return Response({"error": "Enterprise not found for this user"}, status=status.HTTP_404_NOT_FOUND)

        try:
            with transaction.atomic():
                ids = request.query_params.getlist('id')
                if ids:
                    # Delete specific admins
                    admins = EnterpriseAdmin.objects.filter(enterprise=enterprise, id__in=ids)
                    if not admins.exists():
                        return Response({"error": "No matching admins found"}, status=status.HTTP_404_NOT_FOUND)
                    count = admins.count()
                    admins.delete()
                    message = f"{count} admin{'s' if count > 1 else ''} successfully deleted"
                else:
                    # Delete all admins for this enterprise
                    count = EnterpriseAdmin.objects.filter(enterprise=enterprise).count()
                    EnterpriseAdmin.objects.filter(enterprise=enterprise).delete()
                    message = f"All {count} admin{'s' if count > 1 else ''} successfully deleted"
                
            return Response({"message": message}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
