from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from django.shortcuts import get_object_or_404
from billing.models import SolutionAccess
from enterprise.models import Enterprise
from enterprise.api.serializers.serializers import EnterpriseInvitationSerializer
from enterprise.services import EnterpriseInvitationService
from django.contrib.auth import get_user_model

User = get_user_model()

class InviteUserToEnterpriseView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, enterprise_id):
        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email is required.'}, status=status.HTTP_400_BAD_REQUEST)
        enterprise = get_object_or_404(Enterprise, id=enterprise_id)
        # Find the first active solution_access for this enterprise (or None)
        solution_access = SolutionAccess.objects.filter(
            enterprise=enterprise,
            status='active'
        ).first()
        invitation = EnterpriseInvitationService.invite_user_to_solution(
            email=email,
            enterprise=enterprise,
            solution_access=solution_access,
            invited_by=request.user
        )
        serializer = EnterpriseInvitationSerializer(invitation)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

class AcceptInvitationView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        token = request.data.get('token')
        if not token:
            return Response({'error': 'Token is required.'}, status=status.HTTP_400_BAD_REQUEST)
        if not request.user.is_authenticated:
            return Response({'error': 'Authentication required.'}, status=status.HTTP_401_UNAUTHORIZED)
        try:
            invitation = EnterpriseInvitationService.accept_invitation(token, request.user)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        serializer = EnterpriseInvitationSerializer(invitation)
        return Response(serializer.data, status=status.HTTP_200_OK) 