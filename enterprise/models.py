from django.db import models
from django.contrib.auth import get_user_model
from django.conf import settings
from django.core.exceptions import ValidationError
import datetime
import uuid

# Create your models here.
class Enterprise(models.Model):
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='owned_enterprises', null=True, blank=True)
    euid = models.CharField(max_length=10, unique=True, editable=False, null=True)
    name = models.CharField(max_length=255,blank=True,null=True)
    logo = models.CharField(blank=True, null=True)
    about = models.TextField(blank=True,null=True)
    contact_number = models.CharField(max_length=20,blank=True,null=True)
    additional_email = models.EmailField(blank=True,null=True)
    is_enterprise_public = models.BooleanField(default=False)
    insurance_accepted = models.BooleanField(default=False)
    language_spoken = models.Char<PERSON>ield(max_length=100,blank=True,null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    identification_number = models.CharField(max_length=255,blank=True)
    state_of_incorporation = models.CharField(max_length=255,blank=True)
    date_of_incorporation = models.DateField(blank=True,null=True)
    website = models.CharField(max_length=255,blank=True,null=True)
    stripe_customer_id = models.CharField(max_length=255, null=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.euid:
            self.euid = self.generate_euid()
        super().save(*args, **kwargs)

    def generate_euid(self):
        while True:
            euid = uuid.uuid4().hex[:8].upper()
            if not Enterprise.objects.filter(euid=euid).exists():
                return euid

    def __str__(self):
        return f"{self.name} ({self.euid})"

class EnterpriseMember(models.Model):
    ROLE_CHOICES = [
        ('admin', 'Administrator'),
        ('manager', 'Manager'),
        ('member', 'Member')
    ]

    enterprise = models.ForeignKey(Enterprise, on_delete=models.CASCADE, related_name='members')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='enterprise_memberships')
    role = models.CharField(max_length=50, choices=ROLE_CHOICES, default='member')
    is_active = models.BooleanField(default=True)
    joined_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('enterprise', 'user')

    def __str__(self):
        return f"{self.user.email} - {self.enterprise.name} ({self.role})"

class Location(models.Model):
    enterprise = models.OneToOneField(Enterprise, on_delete=models.CASCADE, related_name='location')
    address = models.CharField(max_length=255,blank=True,null=True)
    city = models.CharField(max_length=100,blank=True,null=True)
    state = models.CharField(max_length=100,blank=True,null=True)
    zip_code = models.CharField(max_length=20,blank=True,null=True)
    country = models.CharField(max_length=100,blank=True,null=True)
    
    def __str__(self):
        return f"{self.enterprise.name} - {self.city}, {self.state}"

class Service(models.Model):
    enterprise = models.ForeignKey(Enterprise, on_delete=models.CASCADE, related_name='services')
    name = models.CharField(max_length=255,blank=True,null=True)
    description = models.TextField(blank=True,null=True)
    
    def __str__(self):
        return f"{self.enterprise.name} - {self.name}"

class EnterpriseDoctor(models.Model):
    enterprise = models.ForeignKey(Enterprise, on_delete=models.CASCADE, related_name='doctors')
    doctor = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='enterprise_doctor')
    
    def __str__(self):
        return f"{self.enterprise.name} - {self.doctor.name}"

class OperatingHours(models.Model):
    enterprise = models.ForeignKey(Enterprise, on_delete=models.CASCADE, related_name='operating_hours')
    day_of_week = models.CharField(max_length=100, blank=True, null=True)
    open_time = models.TimeField(blank=True, null=True)
    close_time = models.TimeField(blank=True, null=True)
    
    def __str__(self):
        return f"{self.enterprise.name} - {self.day_of_week} - {self.format_time(self.open_time)} to {self.format_time(self.close_time)}"
    
    def format_time(self, time):
        if time:
            return time.strftime("%I:%M %p")
        return "N/A"
    
    def clean(self):
        if self.open_time and self.close_time and self.open_time >= self.close_time:
            raise ValidationError("Close time must be after open time.")
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

class EnterpriseDepartment(models.Model):
    enterprise = models.ForeignKey(Enterprise, on_delete=models.CASCADE, related_name='departments')
    name = models.CharField(max_length=255,blank=True,null=True)
    description = models.TextField(blank=True,null=True)
    
    def __str__(self):
        return f"{self.enterprise.name} - {self.name}"      

class EnterpriseDepartmentDoctor(models.Model):
    enterprise = models.ForeignKey(Enterprise, on_delete=models.CASCADE, related_name='enterprise_department_doctors', blank=True, null=True)
    department = models.ForeignKey(EnterpriseDepartment, on_delete=models.CASCADE, related_name='doctors')
    doctor = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='enterprise_department_doctor')
    def __str__(self):
        return f"{self.department.enterprise.name} - {self.department.name} - {self.doctor.name}"

class EnterpriseImage(models.Model):
    enterprise = models.ForeignKey(Enterprise, on_delete=models.CASCADE, related_name='enterprise_images')
    image = models.CharField(blank=True, null=True)  # Store the blob name
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.enterprise.name} - {self.image}"

class EnterpriseInvitation(models.Model):
    STATUS_CHOICES = [
        ("pending", "Pending"),
        ("accepted", "Accepted"),
        ("expired", "Expired"),
        ("revoked", "Revoked"),
    ]

    email = models.EmailField()
    enterprise = models.ForeignKey(Enterprise, on_delete=models.CASCADE, related_name="invitations")
    solution_access = models.ForeignKey(
        'billing.SolutionAccess', on_delete=models.CASCADE, related_name="invitations", null=True, blank=True
    )
    token = models.CharField(max_length=64, unique=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default="pending")
    invited_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name="sent_enterprise_invitations")
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    accepted_at = models.DateTimeField(null=True, blank=True)
    accepted_user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name="accepted_enterprise_invitations")

    class Meta:
        indexes = [
            models.Index(fields=["email", "enterprise", "status"]),
            models.Index(fields=["token"]),
        ]
        unique_together = ("email", "enterprise", "solution_access", "status")

    def __str__(self):
        return f"Invite {self.email} to {self.enterprise.name} ({self.status})"