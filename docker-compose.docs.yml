services:
  docs:
    image: ravid_communities_local_docs
    container_name: ravid_communities_local_docs
    build:
      context: .
      dockerfile: ./compose/local/docs/Dockerfile
    env_file:
      - ./.envs/.local/.django
    volumes:
      - ./docs:/docs:z
      - ./config:/app/config:z
      - ./ravid_communities:/app/ravid_communities:z
    ports:
      - '9000:9000'
    command: /start-docs
