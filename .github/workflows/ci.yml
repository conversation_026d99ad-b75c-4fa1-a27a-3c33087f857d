name: <PERSON><PERSON>

# Enable Buildkit and let compose use it to speed up image building
env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

on:
  pull_request:
    branches: ['master', 'main']
    paths-ignore: ['docs/**']

  push:
    branches: ['master', 'main']
    paths-ignore: ['docs/**']

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  linter:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code Repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Run pre-commit
        uses: pre-commit/action@v3.0.1

  # With no caching at all the entire ci process takes 3m to complete!
  pytest:
    runs-on: ubuntu-latest

    services:
      redis:
        image: redis:6
        ports:
          - 6379:6379
      postgres:
        image: postgres:16
        ports:
          - 5432:5432
        env:
          POSTGRES_PASSWORD: postgres

    env:
      CELERY_BROKER_URL: 'redis://localhost:6379/0'
      # postgres://user:password@host:port/database
      DATABASE_URL: 'postgres://postgres:postgres@localhost:5432/postgres'

    steps:
      - name: Checkout Code Repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'
          cache: pip
          cache-dependency-path: |
            requirements/base.txt
            requirements/local.txt

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements/local.txt

      - name: Check DB Migrations
        run: python manage.py makemigrations --check

      - name: Run DB Migrations
        run: python manage.py migrate

      - name: Test with pytest
        run: pytest
