name: Docker Compose Build and Up

on:
  push:
    branches:
      - RC-V.3.9

jobs:
  build-and-up-us:
    name: Build and Up on US Runner
    runs-on: ['self-hosted', 'us', 'test'] # Use the 'us' tag to target the US runner
    steps:
      - name: Pull Latest Code and Checkout Branch
        run: |
          cd /home/<USER>/project-ravid-backend-communities/
          git fetch
          git checkout ${{ github.ref_name }}
          git pull
      
      - name: Docker system prune
        run: |
          docker system prune -f

      - name: Build and Start Docker Compose Services
        run: |
          cd /home/<USER>/project-ravid-backend-communities/
          docker compose -f docker-compose-build.yaml build
          docker compose -f docker-compose-build.yaml up -d --force-recreate
      - name: Check to see if the services are running
        run: |
          cd /home/<USER>/project-ravid-backend-communities/
          docker ps
          for i in {1..10}; do
            sleep 15
            curl --fail http://localhost:8000/api/health-check/ && exit 0
          done
          echo "Django server is not responding"
          exit 1
