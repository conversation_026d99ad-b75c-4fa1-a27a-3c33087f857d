from django.db import models
from .profile import Profile

class PracticeLocation(models.Model):
    profile = models.ForeignKey(Profile, related_name='practice_locations', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    address = models.TextField()
    phone = models.CharField(max_length=20, blank=True)
    website = models.URLField(blank=True)

    def __str__(self):
        return self.name 