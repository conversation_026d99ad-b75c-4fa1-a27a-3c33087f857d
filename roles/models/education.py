from django.db import models
from .profile import Profile

class Education(models.Model):
    profile = models.ForeignKey(Profile, related_name='education_list', on_delete=models.CASCADE)
    institution = models.CharField(max_length=255)
    degree = models.CharField(max_length=255)
    field_of_study = models.CharField(max_length=255)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    description = models.TextField(blank=True)

    def __str__(self):
        return f"{self.degree} in {self.field_of_study} from {self.institution}"

    class Meta:
        ordering = ['-end_date', '-start_date'] 