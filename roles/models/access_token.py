from django.db import models
from django.utils import timezone
import uuid
from django.conf import settings

class AccessToken(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    token = models.CharField(max_length=255, unique=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='access_tokens',
        null=True,
        blank=True,
        help_text="The user this token belongs to (for authentication tokens)"
    )
    granted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='granted_tokens',
        null=True,
        blank=True,
        help_text="The user who granted this token (for shared access)"
    )
    granted_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_tokens',
        null=True,
        blank=True,
        help_text="The user this token was granted to (for shared access)"
    )
    permissions = models.J<PERSON><PERSON><PERSON>(default=dict, help_text="Permissions associated with this token")
    is_active = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_used_at = models.DateTimeField(null=True, blank=True)
    is_impersonation = models.BooleanField(default=False, help_text="Indicates if this token is used for impersonation")

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['user']),
            models.Index(fields=['granted_by', 'granted_to']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        if self.user:
            return f"Token for {self.user.email}"
        return f"Shared token from {self.granted_by} to {self.granted_to}"

    def is_expired(self):
        return timezone.now() > self.expires_at

    def update_last_used(self):
        self.last_used_at = timezone.now()
        self.save(update_fields=['last_used_at'])
        
    def get_user(self):
        """Return the user this token is for"""
        if self.user:
            return self.user
        if self.is_impersonation and self.granted_by:
            return self.granted_by
        return self.granted_to 