from django.db import models
from .profile import Profile

class CredentialDocument(models.Model):
    profile = models.ForeignKey(Profile, related_name='credential_documents', on_delete=models.CASCADE)
    document = models.FileField(upload_to='credential_documents/')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Credential document for {self.profile.user.email}" 