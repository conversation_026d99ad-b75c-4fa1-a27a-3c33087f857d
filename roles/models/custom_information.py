from django.db import models
from .profile import Profile

class CustomInformation(models.Model):
    profile = models.ForeignKey(Profile, related_name='custom_information', on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    description = models.TextField()

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['id']  # Order by id instead of created_at 