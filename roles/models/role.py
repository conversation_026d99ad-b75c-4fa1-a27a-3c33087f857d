from django.db import models
from django.contrib.auth.models import Group
from django.utils import timezone

from .custom_permission import CustomPermission

class Role(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    custom_permissions = models.ManyToManyField(CustomPermission)
    is_saas_admin = models.BooleanField(default=False)
    is_enterprise_admin = models.BooleanField(default=False)
    is_enterprise_role = models.BooleanField(default=False)  # New field for enterprise roles
    enterprise = models.ForeignKey('enterprise.Enterprise', null=True, blank=True, on_delete=models.CASCADE)
    group = models.OneToOneField(Group, on_delete=models.CASCADE, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    priority_level = models.IntegerField(default=1)
    # Override created_at and updated_at to provide default values
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        # Update the updated_at field before saving
        self.updated_at = timezone.now()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    @classmethod
    def create_default_roles(cls):
        roles = {
            'SaaS Admin': {
                'description': 'System administrator with full access',
                'is_saas_admin': True,
                'permissions': ['manage_users', 'manage_roles', 'manage_enterprises']
            },
            'Enterprise Admin': {
                'description': 'Enterprise administrator with full enterprise access',
                'is_enterprise_admin': True,
                'is_enterprise_role': True,
                'permissions': ['manage_enterprise_users', 'manage_enterprise_roles']
            },
            'Enterprise User': {
                'description': 'Regular enterprise user',
                'is_enterprise_role': True,
                'permissions': ['view_enterprise_data', 'edit_own_profile']
            }
        }
        for role_name, role_data in roles.items():
            role, created = cls.objects.get_or_create(
                name=role_name,
                defaults={
                    'description': role_data['description'],
                    'is_saas_admin': role_data.get('is_saas_admin', False),
                    'is_enterprise_admin': role_data.get('is_enterprise_admin', False),
                    'is_enterprise_role': role_data.get('is_enterprise_role', False)
                }
            )
            if created:
                for perm_name in role_data['permissions']:
                    permission, _ = CustomPermission.objects.get_or_create(
                        codename=perm_name,
                        defaults={
                            'name': f'Can {perm_name.replace("_", " ")}',
                            'description': f'Allows {perm_name.replace("_", " ")}',
                            'category': 'enterprise' if 'enterprise' in perm_name else 'saas'
                        }
                    )
                    role.custom_permissions.add(permission) 