from django.db import models
from django.contrib.postgres.fields import ArrayField

class TranslationData(models.Model):
    data = models.JSONField()
    key = models.CharField(max_length=255)
    target_language = models.Char<PERSON>ield(max_length=10)
    exclude_fields = Array<PERSON>ield(models.CharField(max_length=255))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.key} - {self.target_language}" 