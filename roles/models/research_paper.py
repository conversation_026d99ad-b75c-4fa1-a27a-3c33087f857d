from django.db import models
from .profile import Profile

class ResearchPaper(models.Model):
    profile = models.ForeignKey(Profile, related_name='research_papers_list', on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    publication_date = models.DateField()
    journal = models.CharField(max_length=255)
    url = models.URLField(blank=True)

    def __str__(self):
        return self.title 