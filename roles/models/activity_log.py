from django.db import models
from django.conf import settings

class ActivityLog(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    action = models.CharField(max_length=255)
    target = models.CharField(max_length=255)
    details = models.TextField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    role = models.CharField(max_length=100)  # Add this field to store the user's role at the time of the action

    def __str__(self):
        return f"{self.user.email} - {self.action} - {self.timestamp}" 