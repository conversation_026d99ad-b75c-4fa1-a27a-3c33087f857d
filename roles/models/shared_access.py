from django.db import models
from django.conf import settings
from django.utils import timezone
from .access_token import AccessToken

class SharedAccess(models.Model):
    granted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='granted_access',
        null=True,
        blank=True
    )
    granted_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_access',
        null=True,
        blank=True
    )
    permissions = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        # unique_together = ('granted_by', 'granted_to')
        indexes = [
            models.Index(fields=['granted_by', 'granted_to']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"Access from {self.granted_by} to {self.granted_to}"

    def is_expired(self):
        if not self.expires_at:
            return False
        return timezone.now() > self.expires_at 