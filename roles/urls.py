from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views.base import (
    RoleViewSet, 
    AvailableRolesView, 
    CommunityViewSet, 
    UserProfileView, 
    UserProfileAdminView,
    CommunityCreateView,
    CommunityListView,
    CommunityDetailView,
    CommunityMembershipView,
    DoctorCredentialSubmissionView,
    AdminCredentialVerificationView,
    ActivityLogView,
    ProfileToggleView,
    ProfileFieldsVisibilityView,
    get_profile_categories,
    create_first_admin,
    ChangeLanguageView,
)
from .views.access_sharing import ShareAccessView, RevokeAccessView, ListSharedAccessView
from roles.views.shared_access import SharedAccessViewSet
from roles.views.access_token import AccessTokenViewSet
from roles.views.enterprise import EnterpriseViewSet

# Initialize the DefaultRouter for automatic URL routing
router = DefaultRouter()

# Register viewsets with the router to generate URL patterns
# Role management endpoints (create, list, update, delete roles)
router.register(r'roles', RoleViewSet, basename='role')

# Shared access endpoints (manage access sharing between users)
router.register(r'shared-access', SharedAccessViewSet, basename='shared-access')

# Access token endpoints (create, validate, revoke access tokens)
router.register(r'access-tokens', AccessTokenViewSet, basename='access-token')

# Enterprise management endpoints (enterprise-specific functionality)
router.register(r'enterprise', EnterpriseViewSet, basename='enterprise')

urlpatterns = [
    path('', include(router.urls)),
    # Add specific URL pattern for request-access
    path('access-tokens/request-access/', AccessTokenViewSet.as_view({'post': 'request_access'}), name='access-token-request-access'),

    # Language change endpoint
    path('change-language/', ChangeLanguageView.as_view(), name='change-language'),

    # First admin creation endpoint (no authentication required)
    path('admin/create-first-admin/', create_first_admin, name='create-first-admin'),

    # RoleViewSet endpoints
    path('admin/', RoleViewSet.as_view({'get': 'list', 'post': 'create'}), name='role-list'),
    path('admin/<int:pk>/', RoleViewSet.as_view({'get': 'retrieve', 'put': 'update', 'patch': 'partial_update', 'delete': 'destroy'}), name='role-detail'),
    path('admin/assign_role/', RoleViewSet.as_view({'post': 'assign_role'}), name='assign-role'),
    path('admin/create_role/', RoleViewSet.as_view({'post': 'create_role'}), name='create-role'),
    path('admin/available_permissions/', RoleViewSet.as_view({'get': 'available_permissions'}), name='available-permissions'),
    path('admin/check-permissions/', RoleViewSet.as_view({'get': 'check_permissions'}), name='check-permissions'),
    
    # New user management endpoints
    path('admin/users/', RoleViewSet.as_view({'get': 'list_users'}), name='list-users'),
    path('admin/users/bulk-assign-role/', RoleViewSet.as_view({'post': 'bulk_assign_role'}), name='bulk-assign-role'),
    path('admin/users/revoke-role/', RoleViewSet.as_view({'post': 'revoke_role'}), name='revoke-role'),
    path('admin/users/toggle-status/', RoleViewSet.as_view({'post': 'toggle_user_status'}), name='toggle-user-status'),
    path('admin/users/reset-password/', RoleViewSet.as_view({'post': 'reset_user_password'}), name='reset-user-password'),
    path('admin/users/delete/', RoleViewSet.as_view({'delete': 'delete_user'}), name='delete-user'),

    # Enterprise endpoints
    path('enterprise/assign-role/', EnterpriseViewSet.as_view({'post': 'assign_enterprise_role'}), name='assign-enterprise-role'),
    path('enterprise/revoke-role/', EnterpriseViewSet.as_view({'post': 'revoke_enterprise_role'}), name='revoke-enterprise-role'),
    path('enterprise/users/', EnterpriseViewSet.as_view({'get': 'list_enterprise_users'}), name='list-enterprise-users'),
    path('enterprise/roles/', EnterpriseViewSet.as_view({'get': 'list_enterprise_roles'}), name='list-enterprise-roles'),

    # AvailableRolesView endpoints
    path('admin/available/', AvailableRolesView.as_view({'get': 'list'}), name='available-roles'),

    # UserProfileView endpoints
    path('profile/', UserProfileView.as_view(), name='user-profile'),
    path('profile/<str:pk>/', UserProfileView.as_view(), name='user-profile-detail'),
    path('profile/<str:pk>/categories/', get_profile_categories, name='user-profile-categories'),
    path('admin/user-profile/<str:user__email>/', UserProfileAdminView.as_view(), name='admin-user-profile'),
    
    # Profile Fields Visibility endpoints
    path('profile/fields/', ProfileFieldsVisibilityView.as_view(), name='profile-fields-visibility'),
    
    # Profile Toggle endpoints
    path('profile/toggle/', ProfileToggleView.as_view(), name='profile-toggle'),
    
    # Doctor Credential Submission endpoints
    path('doctor/submit-credentials/', DoctorCredentialSubmissionView.as_view(), name='submit-credentials'),
    path('admin/verify-credentials/<str:user__email>/', AdminCredentialVerificationView.as_view(), name='verify-credentials'),
    
    # Activity Log endpoints
    path('admin/activity-logs/', ActivityLogView.as_view(), name='activity-logs'),

    # Community endpoints
    path('communities/', CommunityListView.as_view(), name='community-list'),
    path('communities/create/', CommunityCreateView.as_view(), name='community-create'),
    path('communities/<int:pk>/', CommunityDetailView.as_view(), name='community-detail'),
    path('communities/<int:pk>/join/', CommunityViewSet.as_view({"post": "join_community"}), name='community-join'),
    path('communities/<int:pk>/leave/', CommunityViewSet.as_view({"post": "leave_community"}), name='community-leave'),
    path('communities/search/', CommunityViewSet.as_view({"get": "search_communities"}), name='community-search'),
    path('communities/<int:pk>/membership/', CommunityMembershipView.as_view(), name='community-membership'),
    path('communities/<int:pk>/flag_post/', CommunityViewSet.as_view({"post": "flag_post"}), name='community-flag-post'),
    path('communities/<int:pk>/delete_post/', CommunityViewSet.as_view({"delete": "delete_post"}), name='community-delete-post'),

    # Access Sharing endpoints
    path('shared-access/', ListSharedAccessView.as_view(), name='list-shared-access'),
    path('shared-access/share/', ShareAccessView.as_view(), name='share-access'),
    path('shared-access/revoke/', RevokeAccessView.as_view(), name='revoke-access'),
]





# Usage examples:

# List all roles (requires admin authentication):
# GET http://localhost:8000/api/roles/

# Create a new role (requires admin authentication):
# POST http://localhost:8000/api/roles/
# {
#     "name": "Researcher",
#     "description": "Can access and analyze data"
# }

# Assign Role (requires admin authentication):
# POST http://localhost:8000/api/roles/assign_role/
# {
#     "email": "<EMAIL>",
#     "role_id": 1
# }

# Create Role (requires admin authentication):
# POST http://localhost:8000/api/roles/create_role/
# {
#     "name": "Researcher",
#     "description": "Adding the new Role Researcher"
# }

# List available roles (no authentication required):
# GET http://localhost:8000/api/roles/available/


# This setup will create the following endpoints:
# GET /api/roles/: List all roles (admin only)
# POST /api/roles/: Create a new role (admin only)
# GET /api/roles/{id}/: Retrieve a specific role (admin only)
# PUT /api/roles/{id}/: Update a specific role (admin only)
# PATCH /api/roles/{id}/: Partially update a specific role (admin only)
# DELETE /api/roles/{id}/: Delete a specific role (admin only)
# POST /api/roles/assign_role/: Assign a role to a user (admin only)
# POST /api/roles/create_role/: Create a new role (admin only)
# GET /api/roles/available/: List available roles (public)
# GET /api/roles/available_permissions/  : list available permissions





# 1. Awards:

# List: GET /api/roles/awards/
# Create: POST /api/roles/awards/
# Retrieve: GET /api/roles/awards/<pk>/
# Update: PUT /api/roles/awards/<pk>/
# Partial Update: PATCH /api/roles/awards/<pk>/
# Delete: DELETE /api/roles/awards/<pk>/


# YouTube Videos:

# List: GET /api/roles/youtube-videos/
# Create: POST /api/roles/youtube-videos/
# Retrieve: GET /api/roles/youtube-videos/<pk>/
# Update: PUT /api/roles/youtube-videos/<pk>/
# Partial Update: PATCH /api/roles/youtube-videos/<pk>/
# Delete: DELETE /api/roles/youtube-videos/<pk>/


# Practice Locations:

# List: GET /api/roles/practice-locations/
# Create: POST /api/roles/practice-locations/
# Retrieve: GET /api/roles/practice-locations/<pk>/
# Update: PUT /api/roles/practice-locations/<pk>/
# Partial Update: PATCH /api/roles/practice-locations/<pk>/
# Delete: DELETE /api/roles/practice-locations/<pk>/
