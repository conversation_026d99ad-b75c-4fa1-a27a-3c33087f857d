from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated

from roles.serializers.role import RoleSerializer
from .serializers import FirstAdminUserSerializer
from accounts.models import CustomUser
from .models import Role
from .permissions import IsA<PERSON>ministra<PERSON>
from roles.activity_utils import log_activity
import logging

logger = logging.getLogger(__name__)

class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [IsAdministrator, IsAuthenticated]

    def create(self, request, *args, **kwargs):
        return self.create_role(request)

@api_view(['POST'])
@permission_classes([AllowAny])
def create_first_admin(request):
    """
    Special endpoint to create the first admin user.
    This endpoint should only be used once to create the initial admin user.
    After that, use the regular admin management endpoints.
    """
    # Check if there are any users with admin role
    if CustomUser.objects.filter(role__name='Admin').exists():
        return Response(
            {"error": "Admin users already exist. Use the regular admin management endpoints."},
            status=status.HTTP_400_BAD_REQUEST
        )

    serializer = FirstAdminUserSerializer(data=request.data)
    if serializer.is_valid():
        try:
            # Create the user with email verified
            user = CustomUser.objects.create_user(
                email=serializer.validated_data['email'],
                password=serializer.validated_data['password'],
                first_name=serializer.validated_data.get('first_name', ''),
                last_name=serializer.validated_data.get('last_name', ''),
                is_email_verified=True  # Set email as verified
            )

            # Assign admin role
            admin_role = Role.objects.get(name='Admin')
            user.role = admin_role
            user.save()

            # Log the activity
            log_activity(user, "Created First Admin", f"User: {user.email}", "First admin user created")

            return Response({
                "message": "First admin user created successfully",
                "user": {
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name
                }
            }, status=status.HTTP_201_CREATED)
        except Role.DoesNotExist:
            return Response(
                {"error": "Admin role not found. Please run migrations and create default roles."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)