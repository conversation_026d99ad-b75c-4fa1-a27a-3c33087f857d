from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from ..models import Profile, SharedAccess
from ..serializers import SharedAccessSerializer

class ShareAccessView(generics.CreateAPIView):
    """
    View for sharing access with another user.
    """
    permission_classes = [IsAuthenticated]
    serializer_class = SharedAccessSerializer

    def perform_create(self, serializer):
        shared_with = get_object_or_404(Profile, user__email=self.request.data.get('email'))
        serializer.save(
            shared_by=self.request.user.profile,
            shared_with=shared_with
        )

class RevokeAccessView(generics.DestroyAPIView):
    """
    View for revoking shared access.
    """
    permission_classes = [IsAuthenticated]
    serializer_class = SharedAccessSerializer

    def get_object(self):
        shared_with = get_object_or_404(Profile, user__email=self.request.data.get('email'))
        return get_object_or_404(
            SharedAccess,
            shared_by=self.request.user.profile,
            shared_with=shared_with
        )

class ListSharedAccessView(generics.ListAPIView):
    """
    View for listing all shared access entries.
    """
    permission_classes = [IsAuthenticated]
    serializer_class = SharedAccessSerializer

    def get_queryset(self):
        return SharedAccess.objects.filter(shared_by=self.request.user.profile) 