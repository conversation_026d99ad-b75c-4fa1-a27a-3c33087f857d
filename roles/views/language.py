from django.conf import settings
from django.utils import translation
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

class ChangeLanguageView(APIView):
    """
    View for changing the user's language preference.
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        language = request.data.get('language')
        
        if language and language in [lang[0] for lang in settings.LANGUAGES]:
            translation.activate(language)
            request.session[translation.LANGUAGE_SESSION_KEY] = language
            return Response({'message': 'Language changed successfully'}, status=status.HTTP_200_OK)
        
        return Response(
            {'error': 'Invalid language code'},
            status=status.HTTP_400_BAD_REQUEST
        ) 