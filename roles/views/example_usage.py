from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from roles.decorators import (
    require_saas_admin,
    require_enterprise_admin,
    require_permission,
    require_enterprise_access
)

class SaaSAdminDashboardView(APIView):
    permission_classes = [IsAuthenticated]

    @require_saas_admin
    def get(self, request):
        # Only SaaS admins can access this endpoint
        return Response({
            "message": "Welcome to SaaS Admin Dashboard",
            "stats": {
                "total_users": 100,
                "active_enterprises": 10,
                "total_revenue": 50000
            }
        })

class EnterpriseAdminView(APIView):
    permission_classes = [IsAuthenticated]

    @require_enterprise_admin
    @require_enterprise_access
    def get(self, request, enterprise_id):
        # Only enterprise admins can access this endpoint
        return Response({
            "message": f"Welcome to Enterprise {enterprise_id} Dashboard",
            "enterprise_stats": {
                "total_users": 50,
                "active_projects": 5,
                "total_budget": 25000
            }
        })

class SharedResourceView(APIView):
    permission_classes = [IsAuthenticated]

    @require_permission('view_resource')
    def get(self, request):
        # Users with 'view_resource' permission can access
        return Response({
            "message": "Accessing shared resource",
            "resource_data": {
                "id": 1,
                "name": "Shared Document",
                "content": "This is a shared resource"
            }
        })

class UserProfileView(APIView):
    permission_classes = [IsAuthenticated]

    @require_permission('edit_own_profile')
    def put(self, request):
        # Users with 'edit_own_profile' permission can update their profile
        user = request.user
        user.first_name = request.data.get('first_name', user.first_name)
        user.last_name = request.data.get('last_name', user.last_name)
        user.save()
        
        return Response({
            "message": "Profile updated successfully",
            "user": {
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name
            }
        }) 