from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404

from roles.models import Role
from roles.serializers import RoleSerializer
from roles.permissions import IsEnterpriseAdmin, IsAdministrator
from accounts.models import CustomUser
from accounts.serializer import UserSerializer

class EnterpriseViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing enterprise-related operations.
    Handles CRUD operations for enterprise users and roles.
    """
    permission_classes = [IsAuthenticated, IsEnterpriseAdmin]
    serializer_class = RoleSerializer
    queryset = Role.objects.all()

    @action(detail=False, methods=['post'])
    def assign_enterprise_role(self, request):
        """
        Assign enterprise role to a user
        """
        user_id = request.data.get('user_id')
        role_id = request.data.get('role_id')

        if not user_id or not role_id:
            return Response(
                {'error': 'Both user_id and role_id are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            user = CustomUser.objects.get(id=user_id)
            role = Role.objects.get(id=role_id)

            # Check if role is enterprise role
            if not role.is_enterprise_role:
                return Response(
                    {'error': 'Selected role is not an enterprise role'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create or update user role
            user_role, created = Role.objects.update_or_create(
                user=user,
                defaults={'role': role}
            )

            serializer = RoleSerializer(user_role)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except CustomUser.DoesNotExist:
            return Response(
                {'error': 'CustomUser not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Role.DoesNotExist:
            return Response(
                {'error': 'Role not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'])
    def list_enterprise_users(self, request):
        """
        List all users with enterprise roles
        """
        enterprise_users = CustomUser.objects.filter(
            userrole__role__is_enterprise_role=True
        ).distinct()
        
        serializer = UserSerializer(enterprise_users, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def revoke_enterprise_role(self, request):
        """
        Revoke enterprise role from a user
        """
        user_id = request.data.get('user_id')
        
        if not user_id:
            return Response(
                {'error': 'user_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            user = CustomUser.objects.get(id=user_id)
            user_role = Role.objects.filter(
                user=user,
                role__is_enterprise_role=True
            ).first()

            if user_role:
                user_role.delete()
                return Response(
                    {'message': 'Enterprise role revoked successfully'},
                    status=status.HTTP_200_OK
                )
            else:
                return Response(
                    {'error': 'CustomUser does not have an enterprise role'},
                    status=status.HTTP_404_NOT_FOUND
                )

        except CustomUser.DoesNotExist:
            return Response(
                {'error': 'CustomUser not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'])
    def list_enterprise_roles(self, request):
        """
        List all available enterprise roles
        """
        enterprise_roles = Role.objects.filter(is_enterprise_role=True)
        serializer = RoleSerializer(enterprise_roles, many=True)
        return Response(serializer.data) 