from rest_framework import viewsets, permissions, status, serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from ..models import AccessToken
from ..serializers import AccessTokenSerializer
from django.utils import timezone
from datetime import timedelta
import secrets
from django.db import models
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.shortcuts import get_object_or_404
import logging
from ..models import Profile
from roles.gcp_utils import get_signed_url, get_private_profile_picture_signed_url
from urllib.parse import quote

User = get_user_model()
logger = logging.getLogger(__name__)

class AccessTokenViewSet(viewsets.ModelViewSet):
    queryset = AccessToken.objects.all()
    serializer_class = AccessTokenSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """
        Optionally filter tokens by:
        - ?filter=all (default): tokens granted by or to the user
        - ?filter=only_granted_to: only tokens granted to the user
        - ?filter=only_granted_by: only tokens granted by the user
        """
        filter_type = self.request.query_params.get('filter', 'all')
        if filter_type == 'only_granted_to':
            return AccessToken.objects.filter(granted_to=self.request.user)
        elif filter_type == 'only_granted_by':
            return AccessToken.objects.filter(granted_by=self.request.user)
        # default: all
        return AccessToken.objects.filter(
            models.Q(granted_by=self.request.user) |
            models.Q(granted_to=self.request.user)
        )

    def perform_create(self, serializer):
        # Generate a secure random token
        token = secrets.token_urlsafe(32)
        logger.info(f"Generated new access token for user {self.request.user.email}")
        
        # Set default expiration to 24 hours from now if not specified
        expires_at = serializer.validated_data.get('expires_at')
        if not expires_at:
            expires_at = timezone.now() + timedelta(hours=24)
            logger.info(f"Using default expiration of 24 hours from now: {expires_at}")
        else:
            logger.info(f"Using provided expiration: {expires_at}")
        
        # Get the user to grant access to
        granted_to_id = serializer.validated_data.get('granted_to_id')
        try:
            granted_to = User.objects.get(id=granted_to_id)
            logger.info(f"Granting access to user: {granted_to.email}")
        except User.DoesNotExist:
            logger.error(f"User with ID {granted_to_id} not found")
            raise serializers.ValidationError({"granted_to_id": "User not found"})
        
        # Save with the current user as granted_by
        access_token = serializer.save(
            granted_by=self.request.user,
            granted_to=granted_to,
            token=token,
            expires_at=expires_at,
            is_impersonation=True  # Set impersonation to True for account sharing
        )
        logger.info(f"Created access token {access_token.id} from {self.request.user.email} to {granted_to.email}")
        
        # Send email notification to the user who received the token
        self._send_shared_token_email(access_token)

    def _send_shared_token_email(self, access_token):
        """Send email notification when a token is shared"""
        try:
            logger.info(f"Starting to send shared token email for token {access_token.id}")
            
            # Get the shared user's profile information
            shared_user = access_token.granted_by
            print("\nShared user keys:")
            for key in shared_user.__dict__.keys():
                print(f"  {key}")
            print()
            logger.info(f"Shared user: {shared_user}")
            
            try:
                profile = shared_user.profile
                logger.info(f"Profile: {profile}")
                
                # Get signed URLs for profile pictures
                if profile.profile_picture:
                    full_blob_name = f"UID_{profile.user.id}/profile_picture/{profile.profile_picture}"
                    profile_picture = get_signed_url(full_blob_name, profile.user.id, timedelta(days=7))
                else:
                    profile_picture = None
                    
                if profile.private_profile_picture:
                    try:
                        private_profile_picture = get_private_profile_picture_signed_url(profile.private_profile_picture, profile.user.id)
                    except Exception as e:
                        logger.error(f"Error generating signed URL for private profile picture: {str(e)}")
                        private_profile_picture = None
                else:
                    private_profile_picture = None
                
                # Get user's full name from profile or fallback to CustomUser fields
                user_name = f"{shared_user.first_name} {shared_user.last_name}".strip() or shared_user.email
            except Profile.DoesNotExist:
                profile_picture = None
                private_profile_picture = None
                user_name = f"{shared_user.first_name} {shared_user.last_name}".strip() or shared_user.email
            logger.info(f"Profile picture URL: {profile_picture}")
            logger.info(f"Private profile picture URL: {private_profile_picture}")
            logger.info(f"User name: {user_name}")
            
            # Create URL with user information in query params
            url_params = {
                'shared_token': access_token.token,
                'shared_by_email': shared_user.email,
                'shared_by_name': user_name,
                'shared_by_profile_picture': profile_picture,
                'shared_by_private_profile_picture': private_profile_picture
            }
            logger.info(f"URL params: {url_params}")
            
            url = f"{settings.FRONTEND_URL}/shared?{'&'.join(f'{k}={v}' for k, v in url_params.items() if v)}"
            logger.info(f"Generated URL: {url}")
            
            subject = "Access Token Shared with You"
            template_context = {
                'granted_by_name': f"{access_token.granted_by.first_name} {access_token.granted_by.last_name}".strip() or access_token.granted_by.email,
                'expires_at': access_token.expires_at.strftime("%B %d, %Y at %I:%M %p"),
                'home_url': url,
                'current_year': timezone.now().year,
                'token': access_token.token
            }
            logger.info(f"Template context: {template_context}")
            
            html_content = render_to_string('account/shared_access_token.html', template_context)
            logger.info("HTML content generated")
            
            plain_content = strip_tags(html_content)
            logger.info("Plain text content generated")
            
            logger.info(f"Attempting to send email to {access_token.granted_to.email}")
            send_mail(
                subject=subject,
                message=plain_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[access_token.granted_to.email],
                html_message=html_content,
                fail_silently=False,
            )
            logger.info(f"Successfully sent shared access token email to {access_token.granted_to.email}")
        except Exception as e:
            logger.error(f"Failed to send shared access token email: {str(e)}")
            logger.exception("Full traceback:")

    @action(detail=False, methods=['get'])
    def my_tokens(self, request):
        # Get tokens where user is either granted_by or granted_to
        tokens = AccessToken.objects.filter(
            models.Q(granted_by=request.user) | 
            models.Q(granted_to=request.user)
        )
        serializer = self.get_serializer(tokens, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def revoke_token(self, request, pk=None):
        token = self.get_object()
        # Only the user who granted the token can revoke it
        if token.granted_by != request.user:
            return Response(
                {"error": "You don't have permission to revoke this token"},
                status=status.HTTP_403_FORBIDDEN
            )
        token.is_active = False
        token.save()
        return Response({"status": "token revoked"})

    @action(detail=True, methods=['post'])
    def extend_expiry(self, request, pk=None):
        token = self.get_object()
        # Only the user who granted the token can extend it
        if token.granted_by != request.user:
            return Response(
                {"error": "You don't have permission to extend this token"},
                status=status.HTTP_403_FORBIDDEN
            )
        # Extend token expiry by 24 hours
        token.expires_at = timezone.now() + timedelta(hours=24)
        token.save()
        return Response({"status": "token expiry extended"})
        
    @action(detail=False, methods=['post'])
    def validate(self, request):
        """Validate an access token"""
        token = request.data.get('token')
        if not token:
            return Response(
                {'error': 'Token is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        access_token = get_object_or_404(
            AccessToken,
            token=token,
            is_active=True
        )

        if access_token.expires_at and access_token.expires_at < timezone.now():
            return Response(
                {'error': 'Token has expired'},
                status=status.HTTP_400_BAD_REQUEST
            )

        access_token.update_last_used()
        serializer = self.get_serializer(access_token)
        return Response(serializer.data)

    @action(detail=False, methods=['post'], url_path='request-access')
    def request_access(self, request):
        """Request access to another user's account"""
        email = request.data.get('email')
        if not email:
            return Response(
                {'error': 'Email is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Verify if the requested user exists
            want_to_access_user = User.objects.get(email=email)
            
            # Don't allow requesting access to your own account
            if want_to_access_user == request.user:
                return Response(
                    {'error': 'You cannot request access to your own account'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create URL with user information
            url = f"{settings.FRONTEND_URL}"
            
            # Prepare email context
            template_context = {
                'want_to_access_user': request.user,
                'home_url': url,
                'current_year': timezone.now().year
            }
            
            # Render email template
            html_content = render_to_string('account/request_access_token.html', template_context)
            plain_content = strip_tags(html_content)
            
            # Send email
            subject = "Access Request to Your R.A.V.I.D Account"
            send_mail(
                subject=subject,
                message=plain_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                html_message=html_content,
                fail_silently=False,
            )
            
            logger.info(f"Access request email sent to {email} from {request.user.email}")
            return Response({
                'message': 'Access request email sent successfully',
                'status': 'success'
            })
            
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Failed to send access request email: {str(e)}")
            logger.exception("Full traceback:")
            return Response(
                {'error': 'Failed to send access request email'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 