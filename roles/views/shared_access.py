from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.shortcuts import get_object_or_404
import uuid
from datetime import timedelta

from ..models.shared_access import SharedAccess
from ..serializers.shared_access import SharedAccessSerializer
from ..permissions import IsAdministrator

class SharedAccessViewSet(viewsets.ModelViewSet):
    queryset = SharedAccess.objects.all()
    serializer_class = SharedAccessSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdministrator]

    def get_queryset(self):
        return SharedAccess.objects.filter(granted_by=self.request.user)

    def perform_create(self, serializer):
        serializer.save(granted_by=self.request.user)

    @action(detail=False, methods=['get'])
    def my_shared_access(self, request):
        shared_access = SharedAccess.objects.filter(user=request.user)
        serializer = self.get_serializer(shared_access, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def revoke_access(self, request, pk=None):
        shared_access = self.get_object()
        if shared_access.granted_by != request.user:
            return Response(
                {"error": "You don't have permission to revoke this access"},
                status=status.HTTP_403_FORBIDDEN
            )
        shared_access.is_active = False
        shared_access.save()
        return Response({"status": "access revoked"}) 