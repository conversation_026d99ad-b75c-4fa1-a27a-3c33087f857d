# Generated by Django 5.0.9 on 2025-04-20 13:03

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('enterprise', '0005_alter_enterprise_user'),
        ('roles', '0012_alter_translationdata_key'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('codename', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField()),
                ('category', models.CharField(max_length=50)),
            ],
        ),
        migrations.RemoveField(
            model_name='role',
            name='permissions',
        ),
        migrations.AddField(
            model_name='role',
            name='enterprise',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='enterprise.enterprise'),
        ),
        migrations.AddField(
            model_name='role',
            name='is_enterprise_admin',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='role',
            name='is_saas_admin',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='role',
            name='description',
            field=models.TextField(),
        ),
        migrations.CreateModel(
            name='AccessToken',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('token', models.CharField(max_length=255, unique=True)),
                ('expires_at', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_tokens', to=settings.AUTH_USER_MODEL)),
                ('granted_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_tokens', to=settings.AUTH_USER_MODEL)),
                ('custom_permissions', models.ManyToManyField(to='roles.custompermission')),
            ],
        ),
        migrations.AddField(
            model_name='role',
            name='custom_permissions',
            field=models.ManyToManyField(to='roles.custompermission'),
        ),
        migrations.CreateModel(
            name='SharedAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('shared_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shared_access_given', to='roles.profile')),
                ('shared_with', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shared_access_received', to='roles.profile')),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('shared_by', 'shared_with')},
            },
        ),
    ]
