# Generated by Django 5.0.9 on 2025-04-27 14:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('roles', '0015_role_priority_level'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='accesstoken',
            options={'ordering': ['-created_at']},
        ),
        migrations.AlterModelOptions(
            name='sharedaccess',
            options={},
        ),
        migrations.AlterUniqueTogether(
            name='sharedaccess',
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name='accesstoken',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='accesstoken',
            name='custom_permissions',
        ),
        migrations.AddField(
            model_name='accesstoken',
            name='granted_by',
            field=models.ForeignKey(blank=True, help_text='The user who granted this token (for shared access)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='granted_tokens', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accesstoken',
            name='last_used_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='accesstoken',
            name='permissions',
            field=models.JSONField(default=dict, help_text='Permissions associated with this token'),
        ),
        migrations.AddField(
            model_name='accesstoken',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='accesstoken',
            name='user',
            field=models.ForeignKey(blank=True, help_text='The user this token belongs to (for authentication tokens)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='access_tokens', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='role',
            name='is_enterprise_role',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='sharedaccess',
            name='expires_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='sharedaccess',
            name='granted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='granted_access', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sharedaccess',
            name='granted_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_access', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sharedaccess',
            name='permissions',
            field=models.JSONField(default=dict),
        ),
        migrations.AlterField(
            model_name='accesstoken',
            name='granted_to',
            field=models.ForeignKey(blank=True, help_text='The user this token was granted to (for shared access)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_tokens', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='accesstoken',
            index=models.Index(fields=['token'], name='roles_acces_token_ae359a_idx'),
        ),
        migrations.AddIndex(
            model_name='accesstoken',
            index=models.Index(fields=['user'], name='roles_acces_user_id_0e47f1_idx'),
        ),
        migrations.AddIndex(
            model_name='accesstoken',
            index=models.Index(fields=['granted_by', 'granted_to'], name='roles_acces_granted_7a8a14_idx'),
        ),
        migrations.AddIndex(
            model_name='accesstoken',
            index=models.Index(fields=['is_active'], name='roles_acces_is_acti_73ce29_idx'),
        ),
        migrations.AddIndex(
            model_name='sharedaccess',
            index=models.Index(fields=['granted_by', 'granted_to'], name='roles_share_granted_0f44e5_idx'),
        ),
        migrations.AddIndex(
            model_name='sharedaccess',
            index=models.Index(fields=['is_active'], name='roles_share_is_acti_abebe5_idx'),
        ),
        migrations.RemoveField(
            model_name='sharedaccess',
            name='shared_by',
        ),
        migrations.RemoveField(
            model_name='sharedaccess',
            name='shared_with',
        ),
    ]
