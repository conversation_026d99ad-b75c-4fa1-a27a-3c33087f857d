# Generated by Django 5.0.9 on 2025-06-17 15:51

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('roles', '0019_alter_accesstoken_expires_at'),
    ]

    operations = [
        migrations.AlterField(
            model_name='profile',
            name='username',
            field=models.CharField(blank=True, help_text='Unique username for public profile URLs. Only lowercase letters, numbers, and hyphens allowed.', max_length=50, null=True, unique=False, validators=[django.core.validators.RegexValidator(code='invalid_username', message='Username can only contain lowercase letters, numbers, and hyphens.', regex='^[a-z0-9-]+$'), django.core.validators.RegexValidator(code='invalid_username_format', message='Username must start and end with a letter or number.', regex='^[a-z0-9].*[a-z0-9]$|^[a-z0-9]$')]),
        ),
    ]
