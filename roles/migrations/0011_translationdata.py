# Generated by Django 5.0.9 on 2025-01-12 15:14

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('roles', '0010_alter_profile_preferred_language'),
    ]

    operations = [
        migrations.CreateModel(
            name='TranslationData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.J<PERSON><PERSON>ield()),
                ('key', models.CharField(max_length=255, unique=True)),
                ('target_language', models.CharField(max_length=10)),
                ('exclude_fields', django.contrib.postgres.fields.ArrayField(base_field=models.Char<PERSON>ield(max_length=255), size=None)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
