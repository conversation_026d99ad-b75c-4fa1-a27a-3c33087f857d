# from django.db import models
# from django.contrib.auth.models import Permission as DjangoPermission, Group
# from django.utils import timezone
# import base64
# from django.core.files.base import ContentFile
# import uuid
# from django.conf import settings
# from django.contrib.postgres.fields import ArrayField
# from django.contrib.auth import get_user_model
# # Create your models here.

# User = get_user_model()

# class CustomPermission(models.Model):
#     name = models.CharField(max_length=100, unique=True)
#     codename = models.CharField(max_length=100, unique=True)
#     description = models.TextField()
#     category = models.CharField(max_length=50)  # e.g., 'saas', 'enterprise', 'sharing'

#     def __str__(self):
#         return self.name


# class Role(models.Model):
#     name = models.CharField(max_length=100, unique=True)
#     description = models.TextField()
#     custom_permissions = models.ManyToManyField(CustomPermission)
#     is_saas_admin = models.BooleanField(default=False)
#     is_enterprise_admin = models.BooleanField(default=False)
#     is_enterprise_role = models.BooleanField(default=False)  # New field for enterprise roles
#     enterprise = models.ForeignKey('enterprise.Enterprise', null=True, blank=True, on_delete=models.CASCADE)
#     group = models.OneToOneField(Group, on_delete=models.CASCADE, null=True, blank=True)
#     is_active = models.BooleanField(default=True)
#     priority_level = models.IntegerField(default=1)

#     def __str__(self):
#         return self.name

#     @classmethod
#     def create_default_roles(cls):
#         roles = {
#             'SaaS Admin': {
#                 'description': 'System administrator with full access',
#                 'is_saas_admin': True,
#                 'permissions': ['manage_users', 'manage_roles', 'manage_enterprises']
#             },
#             'Enterprise Admin': {
#                 'description': 'Enterprise administrator with full enterprise access',
#                 'is_enterprise_admin': True,
#                 'is_enterprise_role': True,
#                 'permissions': ['manage_enterprise_users', 'manage_enterprise_roles']
#             },
#             'Enterprise User': {
#                 'description': 'Regular enterprise user',
#                 'is_enterprise_role': True,
#                 'permissions': ['view_enterprise_data', 'edit_own_profile']
#             }
#         }
#         for role_name, role_data in roles.items():
#             role, created = cls.objects.get_or_create(
#                 name=role_name,
#                 defaults={
#                     'description': role_data['description'],
#                     'is_saas_admin': role_data.get('is_saas_admin', False),
#                     'is_enterprise_admin': role_data.get('is_enterprise_admin', False),
#                     'is_enterprise_role': role_data.get('is_enterprise_role', False)
#                 }
#             )
#             if created:
#                 for perm_name in role_data['permissions']:
#                     permission, _ = CustomPermission.objects.get_or_create(
#                         codename=perm_name,
#                         defaults={
#                             'name': f'Can {perm_name.replace("_", " ")}',
#                             'description': f'Allows {perm_name.replace("_", " ")}',
#                             'category': 'enterprise' if 'enterprise' in perm_name else 'saas'
#                         }
#                     )
#                     role.custom_permissions.add(permission)


# class AccessToken(models.Model):
#     id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
#     user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='access_tokens')
#     token = models.CharField(max_length=255, unique=True)
#     is_active = models.BooleanField(default=True)
#     expires_at = models.DateTimeField()
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     def __str__(self):
#         return f"Token for {self.user.email}"

#     def is_expired(self):
#         return timezone.now() > self.expires_at

#     class Meta:
#         ordering = ['-created_at']


# class Profile(models.Model):
#     user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
#     username = models.CharField(max_length=100, blank=True, null=True)
#     clinic = models.ForeignKey('clinic.Clinic', on_delete=models.SET_NULL, null=True, blank=True)
#     bio = models.TextField(blank=True, null=True)   
#     profile_picture = models.CharField(max_length=255, blank=True, null=True) 
#     private_profile_picture = models.CharField(max_length=255, blank=True, null=True)
#     credentials = models.TextField(blank=True, null=True)
#     dob = models.CharField(max_length=10, blank=True, null=True)
#     genome_tested = models.BooleanField(default=False, blank=True, null=True)
#     gender = models.CharField(max_length=10, blank=True, null=True)
#     language = models.CharField(max_length=10, blank=True, null=True)
#     blood_group = models.CharField(max_length=10, blank=True, null=True)
#     digital_blood = models.CharField(max_length=10, blank=True, null=True)
#     address = models.CharField(max_length=255, blank=True, null=True)
#     city = models.CharField(max_length=255, blank=True, null=True)
#     state = models.CharField(max_length=255, blank=True, null=True)
#     zipcode = models.CharField(max_length=20, blank=True, null=True)
#     country = models.CharField(max_length=100, blank=True, null=True)
#     mobile = models.CharField(max_length=20, blank=True, null=True)
#     homephone = models.CharField(max_length=20, blank=True, null=True)
#     # New fields for doctor profile
#     title = models.CharField(max_length=100, blank=True, null=True)
#     education = models.TextField(blank=True, null=True)
#     about_me = models.TextField(blank=True, null=True)
#     practices = models.TextField(blank=True, null=True)
#     affiliations = models.TextField(blank=True, null=True)
#     research_papers = models.TextField(blank=True, null=True)
#     awards = models.TextField(blank=True, null=True)
#     youtube_videos = models.TextField(blank=True, null=True)
#     locations = models.TextField(blank=True, null=True)
#     speciality = models.CharField(max_length=255, blank=True, null=True)
#     is_credentials_verified = models.BooleanField(default=False)
#     credential_verification_notes = models.TextField(blank=True, null=True)
#     credential_submitted_at = models.DateTimeField(null=True, blank=True)
#     preferred_language = models.CharField(max_length=10, choices=settings.LANGUAGES, default=settings.LANGUAGE_CODE) 
    
    
    
#     show_education = models.BooleanField(default=True)
#     show_research_papers = models.BooleanField(default=True)
#     show_awards = models.BooleanField(default=True)
#     show_youtube_videos = models.BooleanField(default=True)
#     show_practice_locations = models.BooleanField(default=True)
#     show_credential_documents = models.BooleanField(default=True)
#     show_custom_information = models.BooleanField(default=True)
    
#     is_public_profile = models.BooleanField(default=False)

#     def save(self, *args, **kwargs):
#         if isinstance(self.profile_picture, str) and self.profile_picture.startswith(
#             "data:image"
#         ):
#             # Split the base64 string and decode it
#             format, imgstr = self.profile_picture.split(";base64,")
#             ext = format.split("/")[-1]  # Extract the file extension
#             image_data = ContentFile(
#                 base64.b64decode(imgstr), name=f"{uuid.uuid4()}.{ext}"
#             )
#             self.profile_picture = image_data

#         super().save(*args, **kwargs)


# class Community(models.Model):
#     name = models.CharField(max_length=100, unique=True)
#     description = models.TextField(blank=True)
#     members = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name="communities", blank=True)
#     creator = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_communities')
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     def __str__(self):
#         return self.name



# class Education(models.Model):
#     profile = models.ForeignKey(Profile, related_name='education_list', on_delete=models.CASCADE)
#     institution = models.CharField(max_length=255)
#     degree = models.CharField(max_length=255)
#     field_of_study = models.CharField(max_length=255)
#     start_date = models.DateField()
#     end_date = models.DateField(null=True, blank=True)
#     description = models.TextField(blank=True)

#     def __str__(self):
#         return f"{self.degree} in {self.field_of_study} from {self.institution}"

#     class Meta:
#         ordering = ['-end_date', '-start_date']


# # New model for doctor's research papers
# class ResearchPaper(models.Model):
#     profile = models.ForeignKey(Profile, related_name='research_papers_list', on_delete=models.CASCADE)
#     title = models.CharField(max_length=255)
#     publication_date = models.DateField()
#     journal = models.CharField(max_length=255)
#     url = models.URLField(blank=True)

#     def __str__(self):
#         return self.title

# # New model for doctor's awards
# class Award(models.Model):
#     profile = models.ForeignKey(Profile, related_name='awards_list', on_delete=models.CASCADE)
#     title = models.CharField(max_length=255)
#     year = models.IntegerField()
#     description = models.TextField(blank=True)

#     def __str__(self):
#         return self.title


# # New model for doctor's YouTube videos
# class YouTubeVideo(models.Model):
#     profile = models.ForeignKey(Profile, related_name='youtube_videos_list', on_delete=models.CASCADE)
#     title = models.CharField(max_length=255)
#     url = models.URLField()
#     description = models.TextField(blank=True)

#     def __str__(self):
#         return self.title

# # New model for doctor's practice locations
# class PracticeLocation(models.Model):
#     profile = models.ForeignKey(Profile, related_name='practice_locations', on_delete=models.CASCADE)
#     name = models.CharField(max_length=255)
#     address = models.TextField()
#     phone = models.CharField(max_length=20, blank=True)
#     website = models.URLField(blank=True)

#     def __str__(self):
#         return self.name



# class CredentialDocument(models.Model):
#     profile = models.ForeignKey(Profile, related_name='credential_documents', on_delete=models.CASCADE)
#     document = models.FileField(upload_to='credential_documents/')
#     uploaded_at = models.DateTimeField(auto_now_add=True)

#     def __str__(self):
#         return f"Credential document for {self.profile.user.email}"



# class CustomInformation(models.Model):
#     profile = models.ForeignKey(Profile, related_name='custom_information', on_delete=models.CASCADE)
#     title = models.CharField(max_length=255)
#     description = models.TextField()

#     def __str__(self):
#         return f"{self.title} - {self.profile.user.email}"

#     class Meta:
#         ordering = ['id']  # Order by id instead of created_at


# class ActivityLog(models.Model):
#     user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
#     action = models.CharField(max_length=255)
#     target = models.CharField(max_length=255)
#     details = models.TextField(blank=True, null=True)
#     timestamp = models.DateTimeField(auto_now_add=True)
#     role = models.CharField(max_length=100)  # Add this field to store the user's role at the time of the action

#     def __str__(self):
#         return f"{self.user.email} ({self.role}) - {self.action} - {self.timestamp}"
    
# class TranslationData(models.Model):
#     data = models.JSONField()
#     key = models.CharField(max_length=255)
#     target_language = models.CharField(max_length=10)
#     exclude_fields = ArrayField(models.CharField(max_length=255))
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

# class SharedAccess(models.Model):
#     """
#     Model for managing shared access between users.
#     """
#     shared_by = models.ForeignKey(
#         'Profile',
#         on_delete=models.CASCADE,
#         related_name='shared_access_given'
#     )
#     shared_with = models.ForeignKey(
#         'Profile',
#         on_delete=models.CASCADE,
#         related_name='shared_access_received'
#     )
#     granted_by = models.ForeignKey(
#         'Profile',
#         on_delete=models.CASCADE,
#         related_name='shared_access_granted',
#         null=True,
#         blank=True
#     )
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)
#     is_active = models.BooleanField(default=True)

#     class Meta:
#         unique_together = ('shared_by', 'shared_with')
#         ordering = ['-created_at']

#     def __str__(self):
#         return f"{self.shared_by} shared with {self.shared_with}"
