from django.core.management.base import BaseCommand
from roles.models import Role, CustomPermission
from accounts.models import CustomUser

class Command(BaseCommand):
    help = 'Creates default roles with associated permissions'

    def handle(self, *args, **kwargs):
        self.stdout.write('Creating default roles and permissions...')

        # Define default roles and their permissions
        default_roles = {
            'Doctor': ['can_post', 'can_comment'],
            'Admin': ['can_manage_users', 'can_manage_roles', 'can_post', 'can_comment'],
            'Moderator': ['can_moderate_posts', 'can_moderate_comments', 'can_comment'],
            'Patient': ['can_comment'],
        }

        for role_name, permissions in default_roles.items():
            role, created = Role.objects.get_or_create(name=role_name)
            if created:
                self.stdout.write(f'Created role: {role_name}')
            else:
                self.stdout.write(f'Role already exists: {role_name}')

            for perm_name in permissions:
                permission, _ = CustomPermission.objects.get_or_create(
                    codename=perm_name,
                    defaults={
                        'name': f'Can {perm_name.replace("_", " ")}',
                        'description': f'Allows {perm_name.replace("_", " ")}',
                        'category': 'saas'
                    }
                )
                role.custom_permissions.add(permission)
                self.stdout.write(f'Added permission {perm_name} to role {role_name}')

        self.stdout.write(self.style.SUCCESS('Successfully created default roles and permissions'))
