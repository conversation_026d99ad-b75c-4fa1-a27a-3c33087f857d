from django.contrib import admin
from .models import Role, ActivityLog, Profile, Community, Education, ResearchPaper, Award, YouTubeVideo, PracticeLocation, CredentialDocument, CustomInformation

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'group')
    filter_horizontal = ('custom_permissions',)
    search_fields = ('name', 'description')



@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'title', 'speciality', 'is_credentials_verified')
    list_filter = ('is_credentials_verified', 'speciality')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'title', 'speciality')

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'User Email'

@admin.register(Community)
class CommunityAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    filter_horizontal = ('members',)
    search_fields = ('name', 'description')

@admin.register(Education)
class EducationAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'institution', 'degree', 'field_of_study', 'start_date', 'end_date')
    list_filter = ('degree', 'field_of_study')
    search_fields = ('profile__user__email', 'institution', 'degree', 'field_of_study')

    def user_email(self, obj):
        return obj.profile.user.email
    user_email.short_description = 'User Email'

@admin.register(ResearchPaper)
class ResearchPaperAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'title', 'publication_date', 'journal')
    list_filter = ('publication_date', 'journal')
    search_fields = ('profile__user__email', 'title', 'journal')

    def user_email(self, obj):
        return obj.profile.user.email
    user_email.short_description = 'User Email'

@admin.register(Award)
class AwardAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'title', 'year')
    list_filter = ('year',)
    search_fields = ('profile__user__email', 'title')

    def user_email(self, obj):
        return obj.profile.user.email
    user_email.short_description = 'User Email'

@admin.register(YouTubeVideo)
class YouTubeVideoAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'title', 'url')
    search_fields = ('profile__user__email', 'title')

    def user_email(self, obj):
        return obj.profile.user.email
    user_email.short_description = 'User Email'

@admin.register(PracticeLocation)
class PracticeLocationAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'name', 'address', 'phone')
    search_fields = ('profile__user__email', 'name', 'address')

    def user_email(self, obj):
        return obj.profile.user.email
    user_email.short_description = 'User Email'

@admin.register(CredentialDocument)
class CredentialDocumentAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'document', 'uploaded_at')
    list_filter = ('uploaded_at',)
    search_fields = ('profile__user__email',)

    def user_email(self, obj):
        return obj.profile.user.email
    user_email.short_description = 'User Email'
    
@admin.register(CustomInformation)
class CustomInformationAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'title')
    search_fields = ('profile__user__email', 'title')

    def user_email(self, obj):
        return obj.profile.user.email
    user_email.short_description = 'User Email'
    
    
    
@admin.register(ActivityLog)
class ActivityLogAdmin(admin.ModelAdmin):
    list_display = ('user_email', 'action', 'target', 'timestamp')
    list_filter = ('timestamp',)
    search_fields = ('user__email', 'action', 'target')

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'User Email'    