from django.core.mail import EmailMultiAlternatives
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags

def send_credential_submission_email(profile, documents):
    user = profile.user
    full_name = user.get_full_name() if hasattr(user, 'get_full_name') else user.email
    subject = "New Doctor Credential Submission"
    
    # Render HTML content
    html_content = render_to_string('roles/credential_submission.html', {
        'full_name': full_name,
        'email': user.email,
    })
    text_content = strip_tags(html_content)
    
    from_email = settings.DEFAULT_FROM_EMAIL
    recipient_list = [admin[1] for admin in settings.ADMINS]  # Assuming ADMINS is set in settings.py
    
    msg = EmailMultiAlternatives(subject, text_content, from_email, recipient_list)
    msg.attach_alternative(html_content, "text/html")
    
    # Attach PDF documents
    for document in documents:
        msg.attach_file(document.document.path)
    
    msg.send()

def send_credential_verification_email(user):
    full_name = user.get_full_name() if hasattr(user, 'get_full_name') else user.email
    subject = "Your credentials have been verified"
    
    # Render HTML content
    html_content = render_to_string('roles/credential_verification.html', {
        'full_name': full_name,
    })
    text_content = strip_tags(html_content)
    
    from_email = settings.DEFAULT_FROM_EMAIL
    recipient_list = [user.email]
    
    msg = EmailMultiAlternatives(subject, text_content, from_email, recipient_list)
    msg.attach_alternative(html_content, "text/html")
    msg.send()