# Roles & Permissions Setup Guide

## Overview
This guide explains how to set up and manage the role-based access control (RBAC) system for the Ravid Healthcare Platform. The roles app provides comprehensive user profile management, authentication, and permission controls.

## 1. Database Migration

First, apply the migrations to set up the roles system:

```bash
make dev  # Start Docker containers
docker compose exec web python manage.py migrate roles
```

## 2. System Architecture

### Core Models

#### 🎭 Role Management
- **Role**: Defines user roles with permissions and enterprise context
- **CustomPermission**: Granular permissions for specific actions
- **Access Control**: Role-based permission checking

#### 👤 Profile System
- **Profile**: Comprehensive user profile with healthcare context
- **Community**: Professional communities and networks
- **Education**: Educational background and certifications
- **Research Papers**: Published research and publications
- **Awards**: Professional recognition and achievements
- **YouTube Videos**: Educational content and presentations
- **Practice Locations**: Medical practice addresses
- **Credential Documents**: Professional certifications and licenses

#### 🔐 Access & Security
- **AccessToken**: Shared access token system for profile sharing
- **SharedAccess**: Profile sharing permissions and controls
- **ActivityLog**: Comprehensive audit trail for all activities
- **Translation**: Multi-language support system

## 3. Default Roles & Permissions

### System Roles

#### 🩺 Healthcare Roles
```python
# Doctor Role
- Full profile management
- Patient consultation access
- Appointment scheduling
- Prescription management
- Community participation

# Patient Role  
- Personal health profile
- Appointment booking
- Access shared medical records
- Basic community participation
```

#### 🏢 Enterprise Roles
```python
# SaaS Admin
- Full system administration
- Enterprise management
- User role assignment
- System monitoring

# Enterprise Admin
- Enterprise user management
- Role assignment within enterprise
- Billing and subscription management
- Analytics and reporting

# Enterprise User
- Enterprise-scoped access
- Limited profile management
- Department-specific permissions
```

### Creating Default Roles

Run the management command to create default roles:

```bash
docker compose exec web python manage.py shell
>>> from roles.models import Role
>>> Role.create_default_roles()
```

## 4. API Endpoints

### Profile Management

#### User Profile CRUD
```
GET /roles/profiles/                    # List profiles
POST /roles/profiles/                   # Create profile
GET /roles/profiles/{id}/               # Get profile details
PUT /roles/profiles/{id}/               # Update profile
PATCH /roles/profiles/{id}/             # Partial update
DELETE /roles/profiles/{id}/            # Delete profile
```

#### Public Profile Access
```
GET /roles/profiles/public/{username}/  # Get public profile by username
GET /roles/profiles/by_username/{username}/  # Get profile by username
```

#### Profile Components
```
# Education Management
GET /roles/profiles/{id}/education/
POST /roles/profiles/{id}/education/

# Research Papers
GET /roles/profiles/{id}/papers/
POST /roles/profiles/{id}/papers/

# Awards & Recognition
GET /roles/profiles/{id}/awards/

# Practice Locations
GET /roles/profiles/{id}/locations/

# Credential Documents
GET /roles/profiles/{id}/credentials/
```

### Role & Permission Management

#### Role Management
```
GET /roles/roles/                       # List all roles
POST /roles/roles/                      # Create new role
GET /roles/roles/{id}/                  # Get role details
PUT /roles/roles/{id}/                  # Update role
DELETE /roles/roles/{id}/               # Delete role
```

#### Permission Management
```
GET /roles/permissions/                 # List permissions
POST /roles/permissions/                # Create permission
GET /roles/permissions/by_category/     # Get permissions by category
```

#### User Role Assignment
```
POST /roles/users/{user_id}/assign_role/    # Assign role to user
DELETE /roles/users/{user_id}/remove_role/  # Remove role from user
GET /roles/users/{user_id}/permissions/     # Get user permissions
```

### Access Sharing System

#### Share Profile Access
```
POST /roles/shared_access/              # Create shared access
GET /roles/shared_access/               # List shared access
DELETE /roles/shared_access/{id}/       # Revoke access
```

#### Access Token Management
```
POST /roles/access_tokens/              # Generate access token
GET /roles/access_tokens/               # List tokens
PUT /roles/access_tokens/{id}/          # Update token
DELETE /roles/access_tokens/{id}/       # Revoke token
```

## 5. Custom Username System

### Public Profile URLs

The system supports SEO-friendly URLs while maintaining privacy:

```python
# Generate username from name
username = generate_unique_username(profile_instance)

# Access patterns
/profile/john-doe-md          # Public username
/api/profiles/by_username/john-doe-md/
```

### Username Rules
- Lowercase letters, numbers, and hyphens only
- Must start and end with alphanumeric character
- Minimum 3 characters
- Auto-generated from first/last name
- Unique across the system
- Reserved words protected

## 6. Permission System Implementation

### Custom Permissions

```python
from roles.models import CustomPermission

# Create custom permission
permission = CustomPermission.objects.create(
    name='Can Schedule Appointments',
    codename='can_schedule_appointments',
    description='Allows scheduling appointments with patients',
    category='healthcare'
)
```

### Role-Based Access Control

```python
from roles.constants import RoleNames

# Check user permissions
def has_appointment_access(user):
    return (
        RoleNames.user_is_doctor(user) or
        RoleNames.user_is_admin(user) or
        user.role.custom_permissions.filter(
            codename='can_schedule_appointments'
        ).exists()
    )
```

### ViewSet Permission Integration

```python
from roles.permissions import CustomPermissionRequired

class AppointmentViewSet(ModelViewSet):
    permission_classes = [
        IsAuthenticated,
        CustomPermissionRequired('can_schedule_appointments')
    ]
```

## 7. Enterprise Management

### Multi-Tenant Support

```python
# Enterprise-specific roles
enterprise_role = Role.objects.create(
    name='Cardiology Department Head',
    description='Head of cardiology department',
    is_enterprise_role=True,
    enterprise=hospital_enterprise,
    priority_level=5
)

# Enterprise user filtering
def get_enterprise_users(enterprise):
    return User.objects.filter(
        role__enterprise=enterprise,
        role__is_enterprise_role=True
    )
```

### Enterprise Admin Setup

```python
# Create enterprise admin
enterprise_admin = Role.objects.create(
    name='Hospital Admin',
    description='Hospital system administrator',
    is_enterprise_admin=True,
    enterprise=hospital_instance
)

# Assign enterprise permissions
admin_permissions = [
    'manage_enterprise_users',
    'manage_enterprise_roles',
    'view_enterprise_analytics'
]

for perm_code in admin_permissions:
    permission = CustomPermission.objects.get(codename=perm_code)
    enterprise_admin.custom_permissions.add(permission)
```

## 8. Profile Visibility Controls

### Privacy Settings

```python
class Profile(models.Model):
    # Visibility controls
    show_education = models.BooleanField(default=True)
    show_research_papers = models.BooleanField(default=True)
    show_awards = models.BooleanField(default=True)
    show_youtube_videos = models.BooleanField(default=True)
    show_practice_locations = models.BooleanField(default=True)
    show_credential_documents = models.BooleanField(default=True)
    show_custom_information = models.BooleanField(default=True)
    is_public_profile = models.BooleanField(default=False)
```

### Public vs Private Data

```python
def get_public_profile_data(profile):
    """Return only publicly visible profile data"""
    data = {
        'username': profile.username,
        'professional_title': profile.professional_title,
        'bio': profile.bio if profile.is_public_profile else None,
        'profile_picture': profile.profile_picture,
    }
    
    # Add sections based on visibility settings
    if profile.show_education:
        data['education'] = profile.education_set.all()
    
    return data
```

## 9. Authentication Integration

### Multi-Backend Authentication

```python
# JWT + Shared Access Token Authentication
class CombinedAuthentication(BaseAuthentication):
    def authenticate(self, request):
        # Try shared access token first
        shared_auth = SharedAccessTokenAuthentication()
        result = shared_auth.authenticate(request)
        if result is not None:
            return result
            
        # Fall back to JWT
        jwt_auth = JWTAuthentication()
        return jwt_auth.authenticate(request)
```

### Access Token Generation

```python
from roles.models import AccessToken

# Generate shared access token
def create_shared_access(profile, granted_by, permissions=None):
    token = AccessToken.objects.create(
        profile=profile,
        granted_by=granted_by,
        permissions=permissions or [],
        expires_at=timezone.now() + timedelta(days=30)
    )
    return token.generate_token()
```

## 10. Activity Logging

### Comprehensive Audit Trail

```python
from roles.models import ActivityLog

# Log user activities
def log_activity(user, action, details=None):
    ActivityLog.objects.create(
        user=user,
        action=action,
        details=details or {},
        ip_address=get_client_ip(request),
        user_agent=request.META.get('HTTP_USER_AGENT', '')
    )

# Usage examples
log_activity(user, 'profile_updated', {'fields': ['bio', 'title']})
log_activity(user, 'appointment_created', {'appointment_id': appointment.id})
log_activity(user, 'access_granted', {'granted_to': other_user.email})
```

## 11. Frontend Integration

### Profile Management

```javascript
// Get user profile
const getProfile = async (username) => {
    const response = await fetch(`/roles/profiles/by_username/${username}/`, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
    return response.json();
};

// Update profile
const updateProfile = async (profileId, data) => {
    const response = await fetch(`/roles/profiles/${profileId}/`, {
        method: 'PATCH',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    });
    return response.json();
};
```

### Role-Based UI Controls

```javascript
// Check user permissions
const hasPermission = (user, permission) => {
    return user.role?.custom_permissions?.some(
        perm => perm.codename === permission
    );
};

// Conditional rendering
const renderAdminPanel = () => {
    if (hasPermission(user, 'can_manage_users')) {
        return <AdminPanel />;
    }
    return null;
};
```

## 12. Testing Strategy

### Model Tests

```python
def test_profile_username_generation():
    user = User.objects.create(
        email='<EMAIL>',
        first_name='John',
        last_name='Doe'
    )
    profile = Profile.objects.create(user=user)
    assert profile.username == 'john-doe'

def test_role_permission_assignment():
    role = Role.objects.create(name='Test Role')
    permission = CustomPermission.objects.create(
        codename='test_permission'
    )
    role.custom_permissions.add(permission)
    assert role.custom_permissions.filter(
        codename='test_permission'
    ).exists()
```

### API Tests

```python
def test_profile_crud_operations(api_client, user):
    # Create profile
    data = {'bio': 'Test bio', 'professional_title': 'MD'}
    response = api_client.post('/roles/profiles/', data)
    assert response.status_code == 201
    
    # Update profile
    profile_id = response.data['id']
    update_data = {'bio': 'Updated bio'}
    response = api_client.patch(f'/roles/profiles/{profile_id}/', update_data)
    assert response.status_code == 200
```

## 13. Troubleshooting

### Common Issues

#### Username Generation Fails
```python
# Check if user has valid name data
if not user.first_name and not user.last_name:
    # Provide fallback username generation
    profile.username = f"user-{user.id}"
```

#### Permission Denied Errors
```python
# Debug permission checking
def debug_user_permissions(user):
    print(f"User: {user.email}")
    print(f"Role: {user.role.name if user.role else 'No role'}")
    if user.role:
        perms = user.role.custom_permissions.all()
        print(f"Permissions: {[p.codename for p in perms]}")
```

#### Enterprise Context Issues
```python
# Ensure enterprise context in queries
def get_enterprise_filtered_queryset(request, queryset):
    if hasattr(request.user, 'role') and request.user.role.is_enterprise_role:
        return queryset.filter(enterprise=request.user.role.enterprise)
    return queryset
```

## 14. Security Best Practices

### Input Validation
- All profile data validated at model and serializer level
- Username sanitization and reserved word checking
- File upload validation for profile images and documents

### Access Control
- Role-based permissions for all sensitive operations
- Enterprise data isolation
- Activity logging for audit compliance

### Data Privacy
- Public/private profile controls
- Selective field visibility
- GDPR compliance for data export/deletion

## 15. Performance Optimizations

### Database Queries
```python
# Optimize profile queries
profiles = Profile.objects.select_related('user', 'role', 'clinic')\
    .prefetch_related('education_set', 'research_papers', 'awards')

# Cache frequently accessed data
@cache_result(timeout=300)
def get_user_permissions(user_id):
    user = User.objects.select_related('role').get(id=user_id)
    return list(user.role.custom_permissions.values_list('codename', flat=True))
```

### API Response Optimization
```python
# Use different serializers for different contexts
class ProfileListSerializer(ModelSerializer):
    # Minimal fields for list view
    class Meta:
        model = Profile
        fields = ['id', 'username', 'professional_title', 'profile_picture']

class ProfileDetailSerializer(ModelSerializer):
    # Full fields for detail view
    class Meta:
        model = Profile
        fields = '__all__'
```

## 16. Next Steps

After setup completion:
1. Configure role-based email notifications
2. Set up enterprise-specific dashboards
3. Implement advanced search and filtering
4. Add real-time activity notifications
5. Configure data analytics and reporting
6. Set up automated compliance reporting

This completes the comprehensive setup guide for the roles and permissions system in the Ravid Healthcare Platform. 