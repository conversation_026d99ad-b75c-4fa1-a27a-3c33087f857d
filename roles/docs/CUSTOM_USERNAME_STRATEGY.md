# Custom Username Strategy for Public APIs

## Overview

Implement custom usernames/handles for public APIs to replace UID exposure, similar to Facebook's approach where frontend uses custom names to call public APIs and responses don't expose actual UIDs.

## Current State Analysis

### Existing Structure
- `CustomUser` model uses custom 10-character ID instead of UUID
- `Profile` model has `username` field (CharField, max_length=100, blank=True, null=True)
- Public APIs currently expose `doctor_id` (UID) in URLs and responses
- Examples: `/api/appointments/available_slots/?doctor_id=ABC123XYZ`

### Problem Statement
- Public URLs expose internal user IDs
- Not user-friendly for sharing profiles
- Security concern: predictable ID structure
- Poor SEO and user experience

## Refined Strategy (After Self-Review)

### Approach: Hybrid Public Identifier System

Instead of modifying existing username field, implement a new **public slug system** that's:
- **Stable**: Won't change frequently
- **Secure**: Doesn't expose internal structure  
- **User-friendly**: Readable and memorable
- **Backward compatible**: Doesn't break existing APIs

### Core Components

#### 1. Database Schema Enhancement

```python
class Profile(models.Model):
    # Existing fields...
    username = models.CharField(max_length=100, blank=True, null=True)  # Keep as-is
    
    # New fields for public access
    public_slug = models.SlugField(
        max_length=50, 
        unique=True, 
        null=True, 
        blank=True,
        help_text="URL-safe identifier for public profile access"
    )
    slug_updated_at = models.DateTimeField(null=True, blank=True)
    allow_public_access = models.BooleanField(default=False)
```

#### 2. Slug Generation Strategy

**Priority Order:**
1. User-provided username (if valid and available)
2. Auto-generated from name: `dr-john-smith`
3. Fallback to hash-based: `doc-a1b2c3d4`

**Validation Rules:**
- Format: `^[a-z0-9-]+$` (lowercase, numbers, hyphens only)
- Length: 3-50 characters
- No consecutive hyphens
- Must start/end with alphanumeric
- Reserved words blocked: `admin`, `api`, `www`, etc.

#### 3. API Architecture

**New Public API Endpoints:**
```
GET /api/public/profiles/{slug}/                    # Public profile
GET /api/public/doctors/{slug}/availability/        # Doctor availability  
GET /api/public/doctors/{slug}/consultation-info/   # Consultation details
```

**Internal Management APIs:**
```
POST /api/users/profile/slug/                       # Set/update slug
GET  /api/users/profile/slug/check/{slug}/          # Check availability
```

**Existing APIs:** Keep unchanged for backward compatibility

#### 4. Response Format Changes

**Before:**
```json
{
  "doctor_id": "ABC123XYZ",
  "name": "Dr. John Smith",
  "available_slots": [...]
}
```

**After (Public APIs only):**
```json
{
  "doctor": "dr-john-smith",
  "name": "Dr. John Smith", 
  "available_slots": [...],
  "profile_url": "/profile/dr-john-smith"
}
```

## Implementation Phases

### Phase 1: Foundation (Week 1-2)
- [ ] Add `public_slug` field to Profile model
- [ ] Create slug generation utilities
- [ ] Write migration to populate existing users
- [ ] Add validation and constraints

### Phase 2: Core APIs (Week 3-4)  
- [ ] Create public profile API endpoint
- [ ] Implement slug management APIs
- [ ] Add slug resolution middleware
- [ ] Create public doctor availability API

### Phase 3: Integration (Week 5-6)
- [ ] Update appointment booking flow
- [ ] Modify serializers for public responses
- [ ] Add frontend URL routing support
- [ ] Implement caching layer

### Phase 4: Migration & Cleanup (Week 7-8)
- [ ] Gradual migration of existing integrations
- [ ] Performance optimization
- [ ] Documentation and testing
- [ ] Monitor and adjust

## Technical Considerations

### Security
- **Rate Limiting**: Limit slug changes (1 per month)
- **Validation**: Strict format enforcement
- **Privacy**: Optional public access flag
- **Reserved Names**: Block system/admin usernames

### Performance
- **Database Index**: Add index on `public_slug` field
- **Caching**: Redis cache for slug → user_id mapping
- **Query Optimization**: Use select_related for profile lookups

### User Experience
- **Slug Suggestions**: Provide alternatives when desired slug is taken
- **Change History**: Track slug changes for audit
- **Fallback URLs**: Support both slug and ID for transition period

## Migration Strategy

### Data Migration
```python
def populate_public_slugs():
    for profile in Profile.objects.filter(public_slug__isnull=True):
        if profile.username:
            slug = validate_and_clean_slug(profile.username)
        else:
            slug = generate_slug_from_name(profile.user)
        
        profile.public_slug = ensure_unique_slug(slug)
        profile.save()
```

### API Transition
1. **Soft Launch**: New APIs available but not promoted
2. **Parallel Running**: Both old and new APIs active
3. **Gradual Migration**: Move integrations one by one
4. **Deprecation**: Mark old APIs as deprecated with timeline
5. **Sunset**: Remove old APIs after sufficient notice

## Risk Mitigation

### Potential Issues
1. **Slug Conflicts**: Multiple users want same slug
2. **Performance Impact**: Additional database lookups
3. **SEO Changes**: URL structure changes affect search rankings
4. **User Confusion**: Multiple ways to access same profile

### Mitigation Strategies
1. **Conflict Resolution**: Automatic numbering (`dr-john-smith-2`)
2. **Performance**: Aggressive caching and database optimization
3. **SEO**: Implement proper redirects and canonical URLs
4. **User Education**: Clear documentation and UI guidance

## Success Metrics

### Technical Metrics
- API response time < 200ms for slug resolution
- Cache hit rate > 95% for slug lookups
- Zero downtime during migration
- Backward compatibility maintained

### User Experience Metrics  
- Profile sharing increase by 30%
- Reduced support tickets about "ugly URLs"
- User adoption of custom slugs > 60%
- SEO improvement in profile discoverability

## Future Enhancements

### Phase 2 Features
- **Vanity URLs**: Custom domains for verified doctors
- **QR Codes**: Generate QR codes with slug URLs
- **Analytics**: Track profile visits via slug
- **Social Integration**: Better sharing on social platforms

### Advanced Features
- **Slug Marketplace**: Premium slug reservation
- **Internationalization**: Unicode slug support
- **Verification Badges**: Verified doctor indicators
- **Custom Branding**: Personalized profile themes

---

**Document Version**: 1.0  
**Last Updated**: 2025-06-17  
**Author**: Development Team  
**Status**: Draft - Pending Review
