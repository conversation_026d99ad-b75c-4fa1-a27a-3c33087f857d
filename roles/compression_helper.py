from PIL import Image
from io import BytesIO
import sys
from django.core.files.uploadedfile import InMemoryUploadedFile
import logging
from PIL import ImageOps
import os

logger = logging.getLogger(__name__)




def compress_image(image):
    try:
        # Open the uploaded image and preserve orientation
        img = Image.open(image)
        
        # Prevent auto-orientation
        img = ImageOps.exif_transpose(img)
        
        # Store original format and extension
        original_format = img.format
        original_extension = os.path.splitext(image.name)[1].lower()
        
        # Set maximum dimensions while maintaining aspect ratio
        max_size = (800, 800)
        img.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # Create output buffer
        output = BytesIO()
        
        # Preserve original format and transparency for PNG
        if original_extension == '.png':
            # Keep PNG format and transparency
            if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                img.save(output, format='PNG', optimize=True)
                extension = 'png'
                mime_type = 'image/png'
            else:
                # Convert to RGBA to ensure transparency support
                img = img.convert('RGBA')
                img.save(output, format='PNG', optimize=True)
                extension = 'png'
                mime_type = 'image/png'
        elif original_format == 'GIF' and getattr(img, 'is_animated', False):
            img.save(output, format='GIF', optimize=True)
            extension = 'gif'
            mime_type = 'image/gif'
        elif original_extension in ['.jpg', '.jpeg']:
            if img.mode != 'RGB':
                img = img.convert('RGB')
            img.save(output, format='JPEG', quality=85, optimize=True)
            extension = original_extension.replace('.', '')
            mime_type = 'image/jpeg'
        else:
            # For other formats, save as JPEG
            if img.mode != 'RGB':
                img = img.convert('RGB')
            img.save(output, format='JPEG', quality=85, optimize=True)
            extension = 'jpg'
            mime_type = 'image/jpeg'
            
        output.seek(0)
        
        # Keep original filename and extension
        original_filename = os.path.splitext(image.name)[0]
        new_filename = f"{original_filename}.{extension}"
        
        return InMemoryUploadedFile(
            output,
            'ImageField',
            new_filename,
            mime_type,
            sys.getsizeof(output),
            None
        )
        
    except Exception as e:
        logger.error(f"Error processing image: {str(e)}")
        raise ValueError(f"Error processing image: {str(e)}")




# # def compress_image(image):
# #     try:
# #         # Open the uploaded image
# #         img = Image.open(image)
        
# #         # Store original format
# #         original_format = img.format
        
# #         # Convert to RGB if image is not in RGB mode
# #         if img.mode not in ('RGB', 'RGBA'):
# #             img = img.convert('RGB')
        
# #         # Set maximum dimensions
# #         max_size = (800, 800)
# #         img.thumbnail(max_size, Image.Resampling.LANCZOS)
        
# #         # Create a BytesIO object
# #         output = BytesIO()
        
# #         # Preserve original format for PNG and WebP to maintain transparency
# #         if original_format in ('PNG', 'WEBP'):
# #             if img.mode == 'RGBA':
# #                 img.save(output, format=original_format, optimize=True)
# #             else:
# #                 img.save(output, format='JPEG', quality=85, optimize=True)
# #         # Preserve GIF animation if present
# #         elif original_format == 'GIF' and getattr(img, 'is_animated', False):
# #             img.save(output, format='GIF', optimize=True)
# #         # Default to JPEG for other formats
# #         else:
# #             img.save(output, format='JPEG', quality=85, optimize=True)
            
# #         output.seek(0)
        
# #         # Set correct extension based on output format
# #         if original_format in ('PNG', 'WEBP') and img.mode == 'RGBA':
# #             extension = original_format.lower()
# #             mime_type = f'image/{extension}'
# #         elif original_format == 'GIF' and getattr(img, 'is_animated', False):
# #             extension = 'gif'
# #             mime_type = 'image/gif'
# #         else:
# #             extension = 'jpg'
# #             mime_type = 'image/jpeg'
        
# #         return InMemoryUploadedFile(
# #             output,
# #             'ImageField',
# #             f"{image.name.split('.')[0]}.{extension}",
# #             mime_type,
# #             sys.getsizeof(output),
# #             None
# #         )
# #     except Exception as e:
# #         logger.error(f"Error processing image: {str(e)}")
# #         raise ValueError(f"Error processing image: {str(e)}")
    
    
    
    
# def compress_image(image):
#     try:
#         # Open the uploaded image and preserve orientation
#         img = Image.open(image)
        
#         # Prevent auto-orientation
#         img = ImageOps.exif_transpose(img)
        
#         # Store original format
#         original_format = img.format
        
#         # Set maximum dimensions while maintaining aspect ratio
#         max_size = (800, 800)
#         img.thumbnail(max_size, Image.Resampling.LANCZOS)
        
#         # Create output buffer
#         output = BytesIO()
        
#         # Handle different image formats
#         if img.mode == 'RGBA':
#             # For RGBA images, either keep PNG/WebP format or convert to RGB for JPEG
#             if original_format in ('PNG', 'WEBP'):
#                 img.save(output, format=original_format, optimize=True)
#                 extension = original_format.lower()
#                 mime_type = f'image/{extension}'
#             else:
#                 # Convert RGBA to RGB with white background
#                 background = Image.new('RGB', img.size, (255, 255, 255))
#                 background.paste(img, mask=img.split()[3])  # Use alpha channel as mask
#                 background.save(output, format='JPEG', quality=85, optimize=True)
#                 extension = 'jpg'
#                 mime_type = 'image/jpeg'
#         elif original_format == 'GIF' and getattr(img, 'is_animated', False):
#             img.save(output, format='GIF', optimize=True)
#             extension = 'gif'
#             mime_type = 'image/gif'
#         else:
#             # For RGB and other modes, save as JPEG
#             if img.mode != 'RGB':
#                 img = img.convert('RGB')
#             img.save(output, format='JPEG', quality=85, optimize=True)
#             extension = 'jpg'
#             mime_type = 'image/jpeg'
            
#         output.seek(0)
        
#         return InMemoryUploadedFile(
#             output,
#             'ImageField',
#             f"{image.name.split('.')[0]}.{extension}",
#             mime_type,
#             sys.getsizeof(output),
#             None
#         )
        
#     except Exception as e:
#         logger.error(f"Error processing image: {str(e)}")
#         raise ValueError(f"Error processing image: {str(e)}")



# def compress_image(image):
#     try:
#         # Open the uploaded image and preserve orientation
#         img = Image.open(image)
        
#         # Prevent auto-orientation
#         img = ImageOps.exif_transpose(img)
        
#         # Store original format and extension
#         original_format = img.format
#         original_extension = os.path.splitext(image.name)[1].lower()
        
#         # Set maximum dimensions while maintaining aspect ratio
#         max_size = (800, 800)
#         img.thumbnail(max_size, Image.Resampling.LANCZOS)
        
#         # Create output buffer
#         output = BytesIO()
        
#         # Always preserve the original formats
#         if original_format in ('PNG', 'WEBP'):
#             # Keep original format for PNG and WebP
#             img.save(output, format=original_format, optimize=True)
#             extension = original_extension.lstrip('.')
#             mime_type = f'image/{extension}'
#         elif original_format == 'GIF' and getattr(img, 'is_animated', False):
#             # Keep GIF format for animated GIFs
#             img.save(output, format='GIF', optimize=True)
#             extension = 'gif'
#             mime_type = 'image/gif'
#         else:
#             # For other formats, save as JPEG
#             if img.mode != 'RGB':
#                 img = img.convert('RGB')
#             img.save(output, format='JPEG', quality=85, optimize=True)
#             extension = 'jpg'
#             mime_type = 'image/jpeg'
            
#         output.seek(0)
        
#         # Keep original filename and extension
#         original_filename = os.path.splitext(image.name)[0]
#         new_filename = f"{original_filename}.{extension}"
        
#         return InMemoryUploadedFile(
#             output,
#             'ImageField',
#             new_filename,
#             mime_type,
#             sys.getsizeof(output),
#             None
#         )
        
#     except Exception as e:
#         logger.error(f"Error processing image: {str(e)}")
#         raise ValueError(f"Error processing image: {str(e)}")

