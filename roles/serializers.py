# from rest_framework import serializers
# from django.contrib.auth.models import Permission
# from .models import CustomPermission, Role, ActivityLog, Profile, Community, Education, ResearchPaper, Award, YouTubeVideo, PracticeLocation, CredentialDocument, CustomInformation, SharedAccess, AccessToken
# from accounts.serializer import Base64ImageField
# from rest_framework import serializers
# import mimetypes
# from django.core.exceptions import ValidationError
# from django.core.files.uploadedfile import UploadedFile
# from django.utils import timezone
# from .send_cred_email import send_credential_submission_email
# from django.core.validators import RegexValidator
# from django.contrib.auth import get_user_model
# from .serializers.permission import PermissionSerializer
# from .serializers.role import (
#     RoleSerializer,
#     RoleCreationSerializer,
#     RoleUpdateSerializer,
#     UserRoleAssignmentSerializer
# )
# from .serializers.profile import (
#     ProfileSerializer,
#     ProfileToggleSerializer,
#     UserProfileSerializer,
#     UserProfileAdminSerializer,
#     ProfileFieldsVisibilitySerializer,
#     EducationSerializer,
#     ResearchPaperSerializer,
#     AwardSerializer,
#     YouTubeVideoSerializer,
#     PracticeLocationSerializer,
#     CustomInformationSerializer,
#     CredentialDocumentSerializer
# )
# from .serializers.community import CommunitySerializer
# from .serializers.credential_document import (
#     CredentialDocumentSerializer,
#     CredentialVerificationSerializer
# )
# from .serializers.activity_log import ActivityLogSerializer
# from .serializers.user import (
#     UserListSerializer,
#     FirstAdminUserSerializer,
#     UserEmailSerializer,
#     UserStatusSerializer
# )
# from .serializers.shared_access import SharedAccessSerializer
# from .serializers.access_token import AccessTokenSerializer
# from .serializers.translation_data import TranslationDataSerializer

# CustomUser = get_user_model()

# __all__ = [
#     'PermissionSerializer',
#     'RoleSerializer',
#     'RoleCreationSerializer',
#     'RoleUpdateSerializer',
#     'UserRoleAssignmentSerializer',
#     'ProfileSerializer',
#     'ProfileToggleSerializer',
#     'UserProfileSerializer',
#     'UserProfileAdminSerializer',
#     'ProfileFieldsVisibilitySerializer',
#     'EducationSerializer',
#     'ResearchPaperSerializer',
#     'AwardSerializer',
#     'YouTubeVideoSerializer',
#     'PracticeLocationSerializer',
#     'CustomInformationSerializer',
#     'CredentialDocumentSerializer',
#     'CommunitySerializer',
#     'CredentialVerificationSerializer',
#     'ActivityLogSerializer',
#     'UserListSerializer',
#     'FirstAdminUserSerializer',
#     'UserEmailSerializer',
#     'UserStatusSerializer',
#     'SharedAccessSerializer',
#     'AccessTokenSerializer',
#     'TranslationDataSerializer'
# ]

# class PermissionSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = CustomPermission
#         fields = ['id', 'name', 'codename', 'description', 'category']

# class RoleSerializer(serializers.ModelSerializer):
#     custom_permissions = serializers.SerializerMethodField()

#     class Meta:
#         model = Role
#         fields = ['id', 'name', 'description', 'custom_permissions', 'is_saas_admin', 'is_enterprise_admin', 'is_active', 'priority_level']

#     def get_custom_permissions(self, obj):
#         return PermissionSerializer(obj.custom_permissions.all(), many=True).data

# class UserRoleAssignmentSerializer(serializers.Serializer):
#     email = serializers.EmailField()
#     # role_id = serializers.IntegerField()

# class RoleCreationSerializer(serializers.ModelSerializer):
#     custom_permissions = serializers.ListField(child=serializers.CharField(), required=False)

#     class Meta:
#         model = Role
#         fields = ['name', 'description', 'custom_permissions', 'is_saas_admin', 'is_enterprise_admin', 'is_active', 'priority_level']

#     def create(self, validated_data):
#         print("Creating role with validated data:", validated_data)
#         permission_names = validated_data.pop('custom_permissions', [])
#         print("Permission names to add:", permission_names)
        
#         role = Role.objects.create(**validated_data)
#         print("Created role:", role)
        
#         # Get the permissions by codename
#         permissions = CustomPermission.objects.filter(codename__in=permission_names)
#         print("Found permissions:", permissions)
        
#         # Add each permission individually to avoid the ManyRelatedManager issue
#         for permission in permissions:
#             print("Adding permission:", permission)
#             role.custom_permissions.add(permission)
            
#         print("Finished creating role with permissions")
#         return role

#     def to_representation(self, instance):
#         return RoleSerializer(instance).data

# class RoleUpdateSerializer(serializers.ModelSerializer):
#     custom_permissions = serializers.ListField(child=serializers.CharField(), required=False)

#     class Meta:
#         model = Role
#         fields = ['name', 'description', 'custom_permissions', 'is_saas_admin', 'is_enterprise_admin', 'is_active', 'priority_level']

#     def update(self, instance, validated_data):
#         permission_names = validated_data.pop('custom_permissions', None)
#         instance = super().update(instance, validated_data)
#         if permission_names is not None:
#             # Get the permissions by codename
#             permissions = CustomPermission.objects.filter(codename__in=permission_names)
#             # Clear existing permissions and set new ones
#             instance.custom_permissions.clear()
#             instance.custom_permissions.add(*permissions)
#         return instance

#     def to_representation(self, instance):
#         return RoleSerializer(instance).data

# class ProfileSerializer(serializers.ModelSerializer):
#     full_name = serializers.ReadOnlyField()
#     profile_picture = Base64ImageField(max_length=None, use_url=True)

#     class Meta:
#         model = Profile
#         fields = ["bio", "profile_picture", "credentials"]
#         read_only_fields = ["user"]
        
# class ProfileToggleSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Profile
#         fields = ['is_public_profile']        

# class CommunitySerializer(serializers.ModelSerializer):
#     creator = serializers.PrimaryKeyRelatedField(read_only=True)
#     class Meta:
#         model = Community
#         fields = ["id", "name", "description", "members"]
#         fields = ["id", "name", "description", "members", "creator", "created_at", "updated_at"]
#         read_only_fields = ["creator", "created_at", "updated_at"]
        
# class ResearchPaperSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = ResearchPaper
#         fields = ['id', 'title', 'publication_date', 'journal', 'url']
        
# class EducationSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Education
#         fields = ['id', 'institution', 'degree', 'field_of_study', 'start_date', 'end_date', 'description']

# class AwardSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Award
#         fields = ['id', 'title', 'year', 'description']

# class YouTubeVideoSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = YouTubeVideo
#         fields = ['id', 'title', 'url', 'description']

# class PracticeLocationSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = PracticeLocation
#         fields = ['id', 'name', 'address', 'phone', 'website']        
       
       

# class CustomInformationSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = CustomInformation
#         fields = ['id', 'title', 'description']
        

# class PDFFileField(serializers.FileField):
#     def to_internal_value(self, data):
#         file = super().to_internal_value(data)
#         if not isinstance(file, UploadedFile):
#             raise ValidationError("Invalid file type")
        
#         # Check file type using mimetypes
#         mime_type, _ = mimetypes.guess_type(file.name)
#         if mime_type != 'application/pdf':
#             raise ValidationError("File must be a PDF")
        
#         # Check file size (e.g., max 5MB)
#         if file.size > 5 * 1024 * 1024:
#             raise ValidationError("File size cannot exceed 5MB")
        
#         return file

        

# class CredentialDocumentSerializer(serializers.ModelSerializer):
#     document = serializers.FileField(max_length=None, use_url=True, required=False)

#     class Meta:
#         model = CredentialDocument
#         fields = ['id', 'document', 'uploaded_at']
#         read_only_fields = ['uploaded_at']

# class UserProfileSerializer(serializers.ModelSerializer):
#     user_id = serializers.IntegerField(source='user.id', read_only=True)
#     email = serializers.EmailField(source='user.email', read_only=True)
#     second_email = serializers.EmailField(source='user.second_email', read_only=True)
#     username = serializers.CharField(
#         # source='user.username', 
#         required=False, 
#         allow_blank=True,
#         validators=[
#             RegexValidator(
#                 regex='^[a-zA-Z0-9]+$',
#                 message=('Username must be alphanumeric.'),
#                 code='invalid_username'
#             ),
#         ]
#     )  
#     clinic_id = serializers.CharField(source='user.clinic.id', required=False, allow_null=True)
#     enterprise_id = serializers.CharField(source='user.enterprise.id', required=False, allow_null=True)
#     enterprise_logo = serializers.CharField(source='user.enterprise.logo', required=False, allow_null=True, default=None)
#     enterprise_website = serializers.CharField(source='user.enterprise.website', required=False, allow_null=True, default=None)
#     cuid = serializers.CharField(source='user.clinic.unique_identifier', required=False, allow_null=True)
#     euid = serializers.CharField(source='user.enterprise.unique_identifier', required=False, allow_null=True)
#     first_name = serializers.CharField(source='user.first_name', required=False, allow_blank=True)
#     last_name = serializers.CharField(source='user.last_name', required=False, allow_blank=True)
#     role = serializers.CharField(source='user.role.name', read_only=True)
#     profile_picture = serializers.ImageField(required=False, allow_null=True)
#     private_profile_picture = serializers.ImageField(required=False, allow_null=True)
#     gender = serializers.CharField(required=False, allow_blank  =True)
#     locations = serializers.CharField(required=False, allow_blank=True)
#     dob = serializers.CharField(required=False, allow_blank=True)
#     genome_tested = serializers.BooleanField(required=False, allow_null=True)
#     language = serializers.CharField(required=False, allow_blank=True)
#     blood_group = serializers.CharField(required=False, allow_blank=True)
#     digital_blood = serializers.CharField(required=False, allow_blank=True)
#     address = serializers.CharField(required=False, allow_blank=True)
#     city = serializers.CharField(required=False, allow_blank=True)
#     state = serializers.CharField(required=False, allow_blank=True)
#     country = serializers.CharField(required=False, allow_blank=True)
#     zipcode = serializers.CharField(required=False, allow_blank=True)
#     mobile = serializers.CharField(required=False, allow_blank=True)
#     homephone = serializers.CharField(required=False, allow_blank=True)
#     is_email_verified = serializers.BooleanField(source='user.is_email_verified', read_only=True)
#     is_credentials_verified = serializers.BooleanField(read_only=True)
#     research_papers = ResearchPaperSerializer(many=True, required=False, source='research_papers_list')
#     awards = AwardSerializer(many=True, required=False, source='awards_list')
#     youtube_videos = YouTubeVideoSerializer(many=True, required=False, source='youtube_videos_list')
#     practice_locations = PracticeLocationSerializer(many=True, required=False)
#     education = EducationSerializer(many=True, required=False, source='education_list')
#     custom_information = CustomInformationSerializer(many=True, required=False)
#     profile_picture_delete = serializers.BooleanField(required=False)
#     is_public_profile = serializers.BooleanField(required=False)
    
#     phone_number = serializers.CharField(source='user.phone_number', required=False, allow_blank=True)
#     is_phone_verified = serializers.BooleanField(source='user.is_phone_verified', required=False)

#     middle_name = serializers.CharField(source='user.middle_name', required=False, allow_blank=True)
#     title = serializers.CharField(required=False, allow_blank=True, allow_null=True)
#     paid_for_verification = serializers.BooleanField(source='user.paid_for_verification', required=False)


#     class Meta:
#         model = Profile
#         fields = [
#             'user_id', 'email', 'second_email', 'username', 'clinic_id', 'enterprise_id', 'cuid','euid', 'first_name', 'last_name', 'role', 'bio','dob', 'genome_tested', 'language', 'profile_picture',
#             'private_profile_picture', 'gender', 'blood_group', 'digital_blood', 'address', 'city', 'state', 'country', 'zipcode', 'mobile', 'homephone',
#             'is_email_verified', 'is_credentials_verified', 'locations', 'title', 'education', 'about_me',
#             'practices', 'affiliations', 'research_papers', 'awards', 'is_public_profile', 'youtube_videos',
#             'speciality', 'practice_locations', 'education', 'custom_information',
#             'profile_picture_delete','show_education', 'show_research_papers', 'show_awards',
#             'show_youtube_videos', 'show_practice_locations',
#             'show_credential_documents', 'show_custom_information',
#             'phone_number', 'is_phone_verified',
#             'middle_name',
#             'paid_for_verification',
#             'enterprise_logo', 'enterprise_website'
#         ]
#         extra_kwargs = {field: {'required': False, 'allow_null': True} for field in fields if field != 'profile_picture_delete' and field != 'private_profile_picture'}
#         read_only_fields = ['user_id','email', 'phone_number', 'is_phone_verified']
#     def validate_username(self, value):
#         if value and not value.isalnum():
#             raise serializers.ValidationError("Username must be alphanumeric.")
#         return value


#     def update(self, instance, validated_data):
        
#         user_data = validated_data.pop('user', {})
#         private_profile_picture = validated_data.pop('private_profile_picture', None)
#         profile_picture_delete = validated_data.pop('profile_picture_delete', False)
            
#         user = instance.user    
#         for attr, value in user_data.items():
#             if attr == 'username':
#                 if value and not value.isalnum():
#                     raise serializers.ValidationError({"username": "Username must be alphanumeric."})
#             setattr(user, attr, value)
#         user.save()
        
#         if 'username' in validated_data:
#             instance.username = validated_data.pop('username')

#         nested_fields = {
#             'research_papers_list': ResearchPaperSerializer,
#             'awards_list': AwardSerializer,
#             'youtube_videos_list': YouTubeVideoSerializer,
#             'practice_locations': PracticeLocationSerializer,
#             'education_list': EducationSerializer
#         }

#         for field, serializer_class in nested_fields.items():
#             if field in validated_data:
#                 self.update_nested_data(instance, field, validated_data.pop(field), serializer_class)

#         custom_information_data = validated_data.pop('custom_information', None)
#         if custom_information_data is not None:
#             instance.custom_information.all().delete()
#             for item in custom_information_data:
#                 CustomInformation.objects.create(profile=instance, **item)

#         for attr, value in validated_data.items():
#             setattr(instance, attr, value)

#         instance.save()
#         return instance

#     def update_nested_data(self, instance, field_name, data, serializer_class):
#         related_manager = getattr(instance, field_name)
#         related_manager.all().delete()
#         for item in data:
#             serializer = serializer_class(data=item)
#             if serializer.is_valid():
#                 serializer.save(profile=instance)

#     def to_representation(self, instance):
#         return super().to_representation(instance)

# class UserProfileAdminSerializer(UserProfileSerializer):
#     is_credentials_verified = serializers.BooleanField(required=False)

#     class Meta(UserProfileSerializer.Meta):
#         fields = UserProfileSerializer.Meta.fields + ['is_credentials_verified']

#     def update(self, instance, validated_data):
#         is_credentials_verified = validated_data.pop('is_credentials_verified', None)
        
#         # Update other fields
#         instance = super().update(instance, validated_data)

#         # Update is_credentials_verified if provided
#         if is_credentials_verified is not None:
#             instance.is_credentials_verified = is_credentials_verified
#             instance.save()

#         return instance    
    
    
# class ProfileFieldsVisibilitySerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Profile
#         fields = [
#             'show_education', 'show_research_papers', 'show_awards',
#             'show_youtube_videos', 'show_practice_locations',
#             'show_credential_documents', 'show_custom_information'
#         ] 

    
# class Base64ImageField(serializers.ImageField):
#     def to_internal_value(self, data):
#         from django.core.files.base import ContentFile
#         import base64
#         import six
#         import uuid
#         import uuid
#         if isinstance(data, six.string_types):
#             if 'data:' in data and ';base64,' in data:
#                 header, data = data.split(';base64,')
#                 header, data = data.split(';base64,')
#             try:
#                 decoded_file = base64.b64decode(data)
#             except TypeError:
#                 self.fail('invalid_image')
#                 self.fail('invalid_image')
#             file_name = str(uuid.uuid4())[:12]
#             file_extension = self.get_file_extension(file_name, decoded_file)
#             complete_file_name = "%s.%s" % (file_name, file_extension, )
#             data = ContentFile(decoded_file, name=complete_file_name)
#             data = ContentFile(decoded_file, name=complete_file_name)
#         return super(Base64ImageField, self).to_internal_value(data)
#         return super(Base64ImageField, self).to_internal_value(data)
#     def get_file_extension(self, file_name, decoded_file):
#         import imghdr
#         extension = imghdr.what(file_name, decoded_file)
#         valid_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp']
#         if extension in valid_extensions:
#             return 'jpg' if extension == 'jpeg' else extension
#         else:
#             return 'jpg'  # Default to jpg if not a recognized image format



# class CredentialVerificationSerializer(serializers.ModelSerializer):
#     full_name = serializers.ReadOnlyField()
#     credential_documents = serializers.ListField(
#         child=PDFFileField(max_length=100000, allow_empty_file=False, use_url=False),
#         write_only=True
#     )

#     class Meta:
#         model = Profile
#         fields = ['id', 'full_name', 'credential_documents', 'is_credentials_verified', 'credential_verification_notes']
#         read_only_fields = ['id', 'full_name', 'is_credentials_verified', 'credential_verification_notes']

#     def validate_credential_documents(self, value):
#         if not value:
#             raise serializers.ValidationError("At least one document must be provided")
#         return value

#     def create(self, validated_data):
#         documents = validated_data.pop('credential_documents', [])
#         profile = self.context['request'].user.profile
        
#         for document in documents:
#             CredentialDocument.objects.create(profile=profile, document=document)
        
#         profile.is_credentials_verified = False
#         profile.credential_submitted_at = timezone.now()
#         profile.save()
#         return profile

#     def to_representation(self, instance):
#         ret = super().to_representation(instance)
#         ret['credential_documents'] = [doc.document.url for doc in instance.credential_documents.all()]
#         return ret


# class ActivityLogSerializer(serializers.ModelSerializer):
#     user_email = serializers.EmailField(source='user.email', read_only=True)
#     class Meta:
#         model = ActivityLog
#         fields = ['id', 'user_email', 'role', 'action', 'target', 'details', 'timestamp']

# class UserListSerializer(serializers.ModelSerializer):
#     role_name = serializers.SerializerMethodField()
#     role_permissions = serializers.SerializerMethodField()
#     is_saas_admin = serializers.SerializerMethodField()
#     is_enterprise_admin = serializers.SerializerMethodField()
#     user_type = serializers.SerializerMethodField()
#     enterprise_info = serializers.SerializerMethodField()
#     clinic_info = serializers.SerializerMethodField()

#     class Meta:
#         model = CustomUser
#         fields = [
#             'id', 'email', 'is_active', 'role_name', 'date_joined',
#             'role_permissions', 'is_saas_admin', 'is_enterprise_admin',
#             'user_type', 'enterprise_info', 'clinic_info'
#         ]
#         read_only_fields = ['id', 'date_joined']

#     def get_role_name(self, obj):
#         return obj.role.name if obj.role else None

#     def get_role_permissions(self, obj):
#         if obj.role:
#             return PermissionSerializer(obj.role.custom_permissions.all(), many=True).data
#         return []

#     def get_is_saas_admin(self, obj):
#         if obj.role:
#             # Check if role has any permission related to managing users, roles, or enterprises
#             admin_permissions = ['can_manage_users', 'can_manage_roles', 'can_manage_enterprises']
#             return obj.role.custom_permissions.filter(codename__in=admin_permissions).exists()
#         return False

#     def get_is_enterprise_admin(self, obj):
#         if obj.role:
#             # Check if role has any permission related to managing enterprise
#             enterprise_permissions = ['can_manage_enterprise_users', 'can_manage_enterprise_roles']
#             return obj.role.custom_permissions.filter(codename__in=enterprise_permissions).exists()
#         return False

#     def get_user_type(self, obj):
#         if obj.is_clinic_signup:
#             return 'clinic'
#         elif obj.is_enterprise_signup:
#             return 'enterprise'
#         return 'regular'

#     def get_enterprise_info(self, obj):
#         if hasattr(obj, 'enterprise') and obj.enterprise:
#             return {
#                 'id': obj.enterprise.id,
#                 'name': obj.enterprise.name,
#                 'unique_identifier': obj.enterprise.unique_identifier
#             }
#         return None

#     def get_clinic_info(self, obj):
#         if hasattr(obj, 'clinic') and obj.clinic:
#             return {
#                 'id': obj.clinic.id,
#                 'name': obj.clinic.name,
#                 'unique_identifier': obj.clinic.unique_identifier
#             }
#         return None

# class BulkRoleAssignmentSerializer(serializers.Serializer):
#     role_id = serializers.IntegerField()
#     user_emails = serializers.ListField(
#         child=serializers.EmailField(),
#         min_length=1
#     )

# class UserStatusSerializer(serializers.Serializer):
#     email = serializers.EmailField()
#     is_active = serializers.BooleanField()

# class UserEmailSerializer(serializers.Serializer):
#     email = serializers.EmailField()

# class FirstAdminUserSerializer(serializers.Serializer):
#     email = serializers.EmailField()
#     password = serializers.CharField(write_only=True, min_length=8)
#     first_name = serializers.CharField(required=False, allow_blank=True)
#     last_name = serializers.CharField(required=False, allow_blank=True)

#     def validate_email(self, value):
#         if CustomUser.objects.filter(email=value).exists():
#             raise serializers.ValidationError("A user with this email already exists.")
#         return value

# class SharedAccessSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = SharedAccess
#         fields = ['id', 'user', 'granted_by', 'permissions', 'created_at', 'updated_at', 'is_active']
#         read_only_fields = ['id', 'created_at', 'updated_at']

# class AccessTokenSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = AccessToken
#         fields = ['id', 'user', 'token', 'is_active', 'expires_at', 'created_at', 'updated_at']
#         read_only_fields = ['token', 'created_at', 'updated_at']

#     def validate_expires_at(self, value):
#         if value <= timezone.now():
#             raise serializers.ValidationError("Expiration date must be in the future")
#         return value

# class EnterpriseRoleSerializer(serializers.ModelSerializer):
#     """
#     Serializer for enterprise roles
#     """
#     class Meta:
#         model = Role
#         fields = ['id', 'name', 'description', 'is_enterprise_role', 'is_enterprise_admin', 'enterprise']
#         read_only_fields = ['id']

# class EnterpriseUserRoleSerializer(serializers.ModelSerializer):
#     """
#     Serializer for enterprise user roles
#     """
#     user = serializers.PrimaryKeyRelatedField(queryset=CustomUser.objects.all())
#     role = serializers.PrimaryKeyRelatedField(queryset=Role.objects.filter(is_enterprise_role=True))

#     class Meta:
#         model = Role
#         fields = ['id', 'user', 'role']
#         read_only_fields = ['id']

#     def validate_role(self, value):
#         if not value.is_enterprise_role:
#             raise serializers.ValidationError("Selected role is not an enterprise role")
#         return value

# class EnterpriseUserSerializer(serializers.ModelSerializer):
#     """
#     Serializer for users with enterprise roles
#     """
#     role = serializers.SerializerMethodField()
#     enterprise = serializers.SerializerMethodField()

#     class Meta:
#         model = CustomUser
#         fields = ['id', 'email', 'first_name', 'last_name', 'role', 'enterprise', 'is_active']
#         read_only_fields = ['id', 'email', 'role', 'enterprise']

#     def get_role(self, obj):
#         user_role = Role.objects.filter(user=obj, role__is_enterprise_role=True).first()
#         if user_role:
#             return {
#                 'id': user_role.role.id,
#                 'name': user_role.role.name,
#                 'is_enterprise_admin': user_role.role.is_enterprise_admin
#             }
#         return None

#     def get_enterprise(self, obj):
#         if hasattr(obj, 'enterprise') and obj.enterprise:
#             return {
#                 'id': obj.enterprise.id,
#                 'name': obj.enterprise.name,
#                 'unique_identifier': obj.enterprise.unique_identifier
#             }
#         return None
