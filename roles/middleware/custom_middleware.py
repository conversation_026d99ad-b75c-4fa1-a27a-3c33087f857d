from django.http import HttpResponseForbidden
from rest_framework_simplejwt.authentication import JW<PERSON>uthentication
from django.urls import resolve


class RoleMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Perform JWT authentication
        jwt_auth = JWTAuthentication()
        try:
            authenticated = jwt_auth.authenticate(request)
            if authenticated:
                user, token = authenticated
                request.user = user
        except:
            pass

        # Check role-based permissions
        if request.user.is_authenticated:
            current_url = resolve(request.path_info).url_name
            # Safely get user role name
            user_role = request.user.role.name if hasattr(request.user, 'role') and request.user.role else None

            # Define your role-based access rules here
            if current_url == 'admin' and user_role != 'Admin':
                return HttpResponseForbidden("You don't have permission to access this page.")

            # Add more role-based checks here
            if current_url == 'community-create' and user_role not in ['Admin', 'Doctor']:
                return HttpResponseForbidden("Only Ad<PERSON> and Doctors can create communities.")

            if current_url == 'community-update' and user_role not in ['Admin', 'Moderator']:
                return HttpResponseForbidden("Only Admin and Moderators can update communities.")

            if current_url == 'community-delete' and user_role != 'Admin':
                return HttpResponseForbidden("Only Admin can delete communities.")

            if current_url == 'flag-post' and user_role not in ['Admin', 'Moderator']:
                return HttpResponseForbidden("Only Admin and Moderators can flag posts.")

            if current_url == 'delete-post' and user_role not in ['Admin', 'Moderator']:
                return HttpResponseForbidden("Only Admin and Moderators can delete posts.")

            if current_url == 'community-membership' and user_role not in ['Admin', 'Doctor', 'Moderator']:
                return HttpResponseForbidden("Only Admin, Doctors, and Moderators can manage community membership.")

        response = self.get_response(request)
        return response
    
    
    
    