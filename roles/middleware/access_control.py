import logging
from django.http import HttpResponseForbidden
from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from django.urls import resolve
from django.utils import timezone
from roles.models import AccessToken
from django.core.cache import cache
from django.conf import settings

# Configure logging
logger = logging.getLogger(__name__)

class AccessControlMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        logger.info("AccessControlMiddleware initialized")

    def __call__(self, request):
        logger.info(f"Processing request for path: {request.path}")

        # Skip middleware for certain paths
        if self._should_skip_middleware(request.path):
            logger.info(f"Skipping middleware for path: {request.path}")
            return self.get_response(request)

        # Get the authorization header
        auth_header = request.headers.get('Authorization', '')
        logger.info(f"Authorization header: {auth_header}")

        if not auth_header.startswith('Bearer '):
            logger.info("No valid Bearer token found, proceeding with request")
            return self.get_response(request)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token}")

        try:
            # First try to find a shared access token
            logger.info("Checking for shared access token")
            access_token = AccessToken.objects.filter(
                token=token,
                is_active=True,
                expires_at__gt=timezone.now()
            ).select_related('granted_to', 'granted_by').first()

            if access_token:
                logger.info(f"Found shared access token for user: {access_token.granted_to}")
                # Get the user this token was granted by, so we can use the permissions of the user who granted the token
                request.user = access_token.granted_to
                
                # Store the user (the one who granted the token) for audit purposes
                request.original_user = access_token.granted_by
                
                # Update last used timestamp
                access_token.update_last_used()
                logger.info("Updated last used timestamp for shared access token")
                return self.get_response(request)

            # If no shared access token, proceed with normal JWT authentication
            logger.info("No shared access token found, attempting JWT authentication")
            try:
                # Use the JWT authentication class to validate the token
                jwt_auth = JWTAuthentication()
                validated_token = jwt_auth.get_validated_token(token)
                user = jwt_auth.get_user(validated_token)
                
                if user:
                    logger.info(f"JWT authentication successful, user: {user}")
                    request.user = user
                    
                    # # Check for role-based permissions
                    # if hasattr(request.user, 'role') and request.user.role:
                    #     # Store permissions in request for easy access
                    #     request.user_permissions = set(p.codename for p in request.user.role.permissions.all())
                    #     logger.info(f"User permissions: {request.user_permissions}")
                        
                    #     # Check URL-based permissions
                    #     if request.path.startswith('/api/admin/'):
                    #         if not request.user.role.name == 'Admin':
                    #             logger.warning(f"Access denied: Admin role required for {request.path}")
                    #             return HttpResponseForbidden('Admin access required')
                    #     elif request.path.startswith('/api/enterprise/'):
                    #         if not request.user.role.name == 'Enterprise':
                    #             logger.warning(f"Access denied: Enterprise role required for {request.path}")
                    #             return HttpResponseForbidden('Enterprise access required')
            except Exception as e:
                logger.error(f"JWT authentication failed: {str(e)}", exc_info=True)
                # If JWT authentication fails, just continue with the request
                pass

        except Exception as e:
            logger.error(f"Error in middleware processing: {str(e)}", exc_info=True)
            # If any error occurs, just continue with the request
            pass

        logger.info("Proceeding with request after middleware processing")
        return self.get_response(request)

    def _should_skip_middleware(self, path):
        # Skip middleware for certain paths
        skip_paths = [
            '/api/auth/',  # Authentication endpoints
            '/api/token/',  # Token endpoints
            '/api/token/refresh/',  # Token refresh endpoints
            '/api/token/verify/',  # Token verify endpoints
            '/admin/',  # Admin interface
            '/static/',  # Static files
            '/media/',  # Media files
            '/favicon.ico',  # Favicon
            '/__debug__/',  # Debug toolbar
        ]
        
        # Check if the path starts with any of the skip paths
        for skip_path in skip_paths:
            if path.startswith(skip_path):
                logger.info(f"Path {path} matches skip path {skip_path}")
                return True
                
        logger.info(f"Path {path} does not match any skip paths")
        return False