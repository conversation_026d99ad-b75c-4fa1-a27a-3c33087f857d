from rest_framework import serializers
from ..models import AccessToken
from django.utils import timezone
from accounts.serializer import UserSerializer
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()

class AccessTokenSerializer(serializers.ModelSerializer):
    granted_by = UserSerializer(read_only=True)
    granted_to = UserSerializer(read_only=True)
    granted_to_id = serializers.CharField(write_only=True)
    is_expired = serializers.SerializerMethodField()
    is_impersonation = serializers.BooleanField(required=False, default=False)

    class Meta:
        model = AccessToken
        fields = ['id', 'token', 'granted_by', 'granted_to', 'granted_to_id', 
                 'permissions', 'is_active', 'created_at', 'updated_at',
                 'expires_at', 'last_used_at', 'is_expired', 'is_impersonation']
        read_only_fields = ['id', 'token', 'granted_by', 'created_at', 'updated_at', 'last_used_at']
    
    def get_is_expired(self, obj):
        return obj.is_expired()
    
    def validate_expires_at(self, value):
        if value <= timezone.now():
            raise serializers.ValidationError("Expiration date must be in the future")
        if value > timezone.now() + timezone.timedelta(days=30):
            raise serializers.ValidationError("Maximum token expiration is 30 days")
        return value
    
    def validate_granted_to_id(self, value):
        # First try to parse as UUID
        try:
            uuid_obj = uuid.UUID(value)
            # Check if user exists with this UUID
            if User.objects.filter(id=uuid_obj).exists():
                return str(uuid_obj)
        except ValueError:
            # Not a valid UUID, try to find user by other identifiers
            pass
        
        # Try to find user by ID (as string)
        user = User.objects.filter(id=value).first()
        if user:
            return str(user.id)
        
        # Try to find user by email
        user = User.objects.filter(email=value).first()
        if user:
            return str(user.id)
        
        # Try to find user by username
        user = User.objects.filter(username=value).first()
        if user:
            return str(user.id)
        
        raise serializers.ValidationError("User not found")
    
    def validate_permissions(self, value):
        if not isinstance(value, list):
            raise serializers.ValidationError("Permissions must be a list")
        
        # Define allowed permissions
        allowed_permissions = {
            'read',
            'write',
            'delete',
            'share',
            'admin'
        }
        
        # Check if all permissions are valid
        invalid_permissions = set(value) - allowed_permissions
        if invalid_permissions:
            raise serializers.ValidationError(
                f"Invalid permissions: {', '.join(invalid_permissions)}. "
                f"Allowed permissions are: {', '.join(allowed_permissions)}"
            )
        
        return value
    
    def validate(self, data):
        # Prevent granting token to self
        if 'granted_to_id' in data:
            try:
                granted_to_id = data['granted_to_id']
                if granted_to_id == str(self.context['request'].user.id):
                    raise serializers.ValidationError({
                        "granted_to_id": "Cannot grant token to yourself"
                    })
            except (ValueError, AttributeError):
                pass  # Let validate_granted_to_id handle this error
        
        return data 