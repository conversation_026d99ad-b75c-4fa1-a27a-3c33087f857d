from rest_framework import serializers
from ..models import EnterpriseRole, EnterpriseUserRole, EnterpriseUser

class EnterpriseRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = EnterpriseRole
        fields = ['id', 'name', 'description', 'permissions', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class EnterpriseUserRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = EnterpriseUserRole
        fields = ['id', 'user', 'role', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class EnterpriseUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = EnterpriseUser
        fields = ['id', 'user', 'enterprise', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at'] 