from rest_framework import serializers
from ..models import CredentialDocument

class CredentialDocumentSerializer(serializers.ModelSerializer):
    class Meta:
        model = CredentialDocument
        fields = ['id', 'user', 'document', 'verified', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class CredentialVerificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = CredentialDocument
        fields = ['id', 'verified', 'verification_notes', 'updated_at']
        read_only_fields = ['id', 'updated_at'] 