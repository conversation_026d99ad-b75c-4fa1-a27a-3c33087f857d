from rest_framework import serializers
from django.utils import timezone
from datetime import timed<PERSON>ta
from accounts.serializer import UserSerializer
import secrets

from ..models import SharedAccess, AccessToken, CustomPermission

class SharedAccessSerializer(serializers.ModelSerializer):
    shared_by_user = UserSerializer(source='shared_by.user', read_only=True)
    shared_with_user = UserSerializer(source='shared_with.user', read_only=True)
    granted_by_user = UserSerializer(source='granted_by.user', read_only=True)
    shared_with_id = serializers.IntegerField(write_only=True, required=False)
    granted_by_id = serializers.IntegerField(write_only=True, required=False)

    class Meta:
        model = SharedAccess
        fields = [
            'id', 'shared_by', 'shared_by_user', 'shared_with', 'shared_with_user', 
            'shared_with_id', 'granted_by', 'granted_by_user', 'granted_by_id',
            'created_at', 'updated_at', 'is_active'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate(self, data):
        if 'shared_by' in data and 'shared_with' in data and data['shared_by'] == data['shared_with']:
            raise serializers.ValidationError("Cannot share access with yourself")
        return data

class AccessTokenSerializer(serializers.ModelSerializer):
    granted_by = UserSerializer(read_only=True)
    granted_to = UserSerializer(read_only=True)
    granted_to_id = serializers.CharField(write_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    permissions = serializers.JSONField(required=False, default=dict)
    is_impersonation = serializers.BooleanField(required=False, default=True)

    class Meta:
        model = AccessToken
        fields = [
            'id', 'token', 'granted_by', 'granted_to', 'granted_to_id',
            'permissions', 'is_active', 'created_at', 'updated_at',
            'expires_at', 'last_used_at', 'is_expired', 'is_impersonation'
        ]
        read_only_fields = ['id', 'token', 'granted_by', 'created_at', 'updated_at', 'last_used_at']
        extra_kwargs = {
            'expires_at': {'required': False}
        }

    def validate_permissions(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Permissions must be a dictionary")
        
        # Get all available permissions
        available_permissions = CustomPermission.objects.all()
        valid_permission_codes = {p.codename for p in available_permissions}
        
        # Validate each permission
        for key, value in value.items():
            if key not in valid_permission_codes:
                raise serializers.ValidationError(f"Invalid permission: {key}")
            if not isinstance(value, bool):
                raise serializers.ValidationError(f"Permission {key} must be a boolean")
        
        return value

    def validate_expires_at(self, value):
        if value and value < timezone.now():
            raise serializers.ValidationError("Expiration date must be in the future")
        return value

    def validate(self, data):
        # Only validate if both granted_by and granted_to are present
        if 'granted_by' in data and 'granted_to' in data and data['granted_by'] == data['granted_to']:
            raise serializers.ValidationError("Cannot grant access to yourself")
        return data

    def create(self, validated_data):
        # Generate a secure random token
        token = secrets.token_urlsafe(32)
        validated_data['token'] = token
        
        # Set default expiration time to 24 hours from now if not provided
        if 'expires_at' not in validated_data:
            validated_data['expires_at'] = timezone.now() + timedelta(hours=24)
            
        return super().create(validated_data) 
