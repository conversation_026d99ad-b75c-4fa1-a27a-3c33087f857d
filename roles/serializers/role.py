from rest_framework import serializers

from accounts.models import CustomUser
from ..models import Role

class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ['id', 'name', 'description', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class RoleCreationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ['name', 'description']

class RoleUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ['name', 'description']

class UserRoleAssignmentSerializer(serializers.Serializer):
    user = serializers.PrimaryKeyRelatedField(queryset=CustomUser.objects.all())
    role = serializers.PrimaryKeyRelatedField(queryset=Role.objects.all()) 