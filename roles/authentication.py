from rest_framework_simplejwt.authentication import <PERSON><PERSON><PERSON><PERSON>entication
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from django.utils import timezone
from .models import AccessToken

class SharedAccessTokenAuthentication(BaseAuthentication):
    def authenticate(self, request):
        # First check for shared_token in query parameters
        print('Checking for shared token in query parameters...')
        token = request.GET.get('shared_token')
        print(f'Token from query params: {token}')
        
        # If no token in query params, check Authorization header
        if not token:
            print('No token in query params, checking Authorization header...')
            auth_header = request.headers.get('Authorization', '')
            print(f'Authorization header: {auth_header}')
            if not auth_header.startswith('Bearer '):
                print('No Bearer token found in Authorization header')
                return None
            token = auth_header.split(' ')[1]
            print(f'Token from Authorization header: {token}')
        
        # Try to find a shared access token
        print('Looking up access token in database...')
        access_token = AccessToken.objects.filter(
            token=token,
            is_active=True,
            expires_at__gt=timezone.now()
        ).select_related('granted_to', 'granted_by').first()
        print(f'Found access token: {access_token}')

        if access_token:
            print('Valid access token found')
            # Get the user this token was granted by
            user = access_token.granted_by
            print(f'Token granted by user: {user}')
            
            # Store the user (the one who granted the token) for audit purposes
            request.granted_to_user = access_token.granted_to
            print(f'Token granted to user: {request.granted_to_user}')
            
            # Update last used timestamp
            access_token.update_last_used()
            print('Updated last used timestamp')
            
            return (user, None)
            
        print('No valid access token found')
        return None

def is_public_whitelisted(request):
    """
    Return True if the current request is for a public (whitelisted) API endpoint.
    Extend this function to add more public endpoints as needed.
    """
    view = getattr(request, 'parser_context', {}).get('view', None)
    # List of (view class, action) tuples that are public
    public_endpoints = [
        ('DoctorAvailabilityViewSet', 'available_slots'),
        # Add more (view_class_name, action) tuples here as needed
    ]
    if view:
        view_class_name = view.__class__.__name__
        action = getattr(view, 'action', None)
        if (view_class_name, action) in public_endpoints:
            return True
    return False

class CustomAuthentication(JWTAuthentication):
    def authenticate(self, request):
        # Bỏ qua xác thực nếu request thuộc endpoint public
        if is_public_whitelisted(request):
            return None  # Skip authentication for public endpoints
        # First try shared access token authentication
        shared_auth = SharedAccessTokenAuthentication()
        result = shared_auth.authenticate(request)
        if result is not None:
            return result
        # If no shared access token, proceed with JWT authentication
        return super().authenticate(request) 