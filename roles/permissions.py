from rest_framework.permissions import BasePermission
from rest_framework import permissions
from .models import AccessToken

class IsAdministrator(BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'role') and request.user.role and request.user.role.name == 'Admin'

class IsDoctor(BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'role') and request.user.role and request.user.role.name == 'Doctor'

class IsModerator(BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'role') and request.user.role and request.user.role.name == 'Moderator'

class IsAdminOrModerator(BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'role') and request.user.role and request.user.role.name in ['Admin', 'Moderrator']

class CanCreateCommunity(BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'role') and request.user.role and request.user.role.name in ['Admin', 'Doctor']

class IsEnterpriseAdmin(BasePermission):
    """
    Permission class to check if user has enterprise admin role
    """
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and 
            hasattr(request.user, 'role') and 
            request.user.role and 
            request.user.role.name == 'Enterprise Admin' and
            request.user.role.is_enterprise_role
        )

class HasProfileAccess(permissions.BasePermission):
    """
    Custom permission to allow users to access their own profile
    or access profiles they have been granted permission to view via access token.
    """
    def has_object_permission(self, request, view, obj):
        # User can always access their own profile
        if request.user == obj:
            return True
            
        # Check if user has a valid access token with view_profile permission
        access_token = request.META.get('HTTP_X_ACCESS_TOKEN')
        if access_token:
            try:
                token = AccessToken.objects.get(
                    token=access_token,
                    is_active=True,
                    granted_to=request.user
                )
                
                # Check if token is expired
                if token.is_expired:
                    return False
                    
                # Check if token has view_profile permission
                permissions = token.permissions
                if isinstance(permissions, dict) and permissions.get('view_profile'):
                    return True
                    
            except AccessToken.DoesNotExist:
                return False
                
        return False
