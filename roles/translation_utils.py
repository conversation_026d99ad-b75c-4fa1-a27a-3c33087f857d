from django.conf import settings
from google.cloud import translate_v2 as translate
from google.oauth2 import service_account
import os
import json
import logging

from roles.models import TranslationData

logger = logging.getLogger(__name__)


class TranslationService:
    def __init__(self):
        self._client = None

    @property
    def client(self):
        if not self._client:
            try:
                credentials_json_path = os.environ.get(
                    'GOOGLE_APPLICATION_CREDENTIALS')
                if not credentials_json_path:
                    raise ValueError(
                        "GOOGLE_APPLICATION_CREDENTIALS not found")

                credentials_info = json.load(open(credentials_json_path,'r'))
                credentials = service_account.Credentials.from_service_account_info(
                    credentials_info)
                self._client = translate.Client(credentials=credentials)
            except Exception as e:
                logger.error(
                    f"Failed to initialize translation client: {str(e)}")
                raise
        return self._client

    def invalidate_data(self, key):
        try:
            logger.info(f"Attempting to invalidate data for key: {key}")
            TranslationData.objects.filter(key=key).delete()
        except Exception as e:
            logger.error(f"Failed to invalidate data: {str(e)}")
            raise

    def translate_value(self, value, target_language):
        if isinstance(value, str) and value.strip():
            try:
                # Call Google Translate API for translation
                result = self.client.translate(
                    value,
                    target_language=target_language
                )

                # The result is a Translation object, access its attributes directly
                detected_language = result['detectedSourceLanguage']
                translated_text = result['translatedText']

                logger.info(f"Detected source language: {detected_language}")

                # Only retry with English source if detection is ambiguous
                if detected_language in ["und", "ceb", "hi-Latn"]:
                    logger.info(
                        f"Ambiguous language detection ({detected_language}), retrying with English as source")
                    result = self.client.translate(
                        value,
                        target_language=target_language,
                        source_language="en"
                    )
                    translated_text = result['translatedText']

                return translated_text
            except Exception as e:
                logger.error(f"Translation error: {str(e)}")
                return value

        return value

    def translate_data(self, key, data, target_language, exclude_fields=None):
        """
        Translate data while excluding specific fields.

        Args:
            data (dict): Data to translate
            target_language (str): Target language code
            exclude_fields (list): Fields to exclude from translation
        """
        exclude_fields = exclude_fields or []
        logger.info(f"Attempting to translate data to {target_language}")

        try:
            try:
                translation_data = TranslationData.objects.get(
                    key=key, target_language=target_language, exclude_fields=exclude_fields)
                print(f'translation_data found in db: {translation_data}')
                cached_data = translation_data.data
                # Reuse exclude_fields from the data
                for field in exclude_fields:
                    if field in data:
                        cached_data[field] = data[field]
                return cached_data
            except TranslationData.DoesNotExist:
                pass

            # Translate all fields in the data except those in exclude_fields
            translated_data = {}
            for k, v in data.items():
                if k not in exclude_fields:
                    translated_data[k] = self.translate_value(
                        v, target_language)
                else:
                    translated_data[k] = v

            TranslationData.objects.create(
                key=key, target_language=target_language, exclude_fields=exclude_fields, data=translated_data)
            return translated_data

        except Exception as e:
            logger.error(f"Translation error: {str(e)}")
            raise

    def get_target_language(self, request):
        """
        Get target language from request headers or query parameters.
        Defaults to settings.LANGUAGE_CODE if not specified.
        """
        # First, check the query parameter
        lang = request.query_params.get('lang')
        if lang and lang in dict(settings.LANGUAGES).keys():
            return lang

        # Then, check the session language
        session_lang = request.session.get(settings.LANGUAGE_SESSION_KEY)
        if session_lang and session_lang in dict(settings.LANGUAGES).keys():
            return session_lang

        # Then, check the user's preferred language
        if request.user.is_authenticated and hasattr(request.user, 'preferred_language'):
            if request.user.preferred_language in dict(settings.LANGUAGES).keys():
                return request.user.preferred_language

        # Finally, fall back to the language from the request or default
        accept_language = request.headers.get('Accept-Language')
        if accept_language:
            # Parse the Accept-Language header
            lang_code = accept_language.split(',')[0].split('-')[0]
            if lang_code in dict(settings.LANGUAGES).keys():
                return lang_code

        logger.info(f"Using default LANGUAGE_CODE: {settings.LANGUAGE_CODE}")
        return settings.LANGUAGE_CODE
