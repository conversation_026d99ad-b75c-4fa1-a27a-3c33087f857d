"""
Constants for the roles app
"""

class RoleNames:
    """Standard role names used throughout the system"""
    DOCTOR = 'Doctor'
    ADMIN = 'Admin'
    MODERATOR = 'Moderator'
    PATIENT = 'Patient'
    SAAS_ADMIN = 'SaaS Admin'
    ENTERPRISE_ADMIN = 'Enterprise Admin'
    ENTERPRISE_USER = 'Enterprise User'
    
    # List of all valid role names
    ALL_ROLES = [
        DOCTOR,
        ADMIN,
        MODERATOR,
        PATIENT,
        SAAS_ADMIN,
        ENTERPRISE_ADMIN,
        ENTERPRISE_USER,
    ]
    
    @classmethod
    def is_valid_role(cls, role_name):
        """Check if a role name is valid"""
        return role_name in cls.ALL_ROLES

    @classmethod
    def user_has_role(cls, user, role_name):
        """Check if user has a specific role"""
        return (
            hasattr(user, 'role') and
            user.role and
            user.role.name == role_name
        )

    @classmethod
    def user_is_doctor(cls, user):
        """Check if user is a doctor"""
        return cls.user_has_role(user, cls.DOCTOR)

    @classmethod
    def user_is_admin(cls, user):
        """Check if user is an admin"""
        return cls.user_has_role(user, cls.ADMIN)

    @classmethod
    def user_is_moderator(cls, user):
        """Check if user is a moderator"""
        return cls.user_has_role(user, cls.MODERATOR)

    @classmethod
    def user_is_patient(cls, user):
        """Check if user is a patient"""
        return cls.user_has_role(user, cls.PATIENT)

    @classmethod
    def user_is_enterprise_admin(cls, user):
        """Check if user is an enterprise admin"""
        return cls.user_has_role(user, cls.ENTERPRISE_ADMIN)

    @classmethod
    def user_is_enterprise_user(cls, user):
        """Check if user is an enterprise user"""
        return cls.user_has_role(user, cls.ENTERPRISE_USER)

    @classmethod
    def user_is_saas_admin(cls, user):
        """Check if user is a SaaS admin"""
        return cls.user_has_role(user, cls.SAAS_ADMIN)

    @classmethod
    def user_has_any_role(cls, user, role_names):
        """Check if user has any of the specified roles"""
        if not (hasattr(user, 'role') and user.role):
            return False
        return user.role.name in role_names

    @classmethod
    def user_can_create_community(cls, user):
        """Check if user can create communities"""
        return cls.user_is_admin(user) or cls.user_is_doctor(user)

    @classmethod
    def user_can_moderate(cls, user):
        """Check if user can moderate content"""
        return cls.user_is_admin(user) or cls.user_is_moderator(user)

    @classmethod
    def user_can_manage_users(cls, user):
        """Check if user can manage other users"""
        return cls.user_is_admin(user)

class PermissionCategories:
    """Permission categories"""
    SAAS = 'saas'
    ENTERPRISE = 'enterprise'
    SHARING = 'sharing'
    HEALTHCARE = 'healthcare'
    
    CHOICES = [
        (SAAS, 'SaaS'),
        (ENTERPRISE, 'Enterprise'),
        (SHARING, 'Sharing'),
        (HEALTHCARE, 'Healthcare'),
    ]

class DefaultPermissions:
    """Default permission codenames"""
    # Basic permissions
    CAN_POST = 'can_post'
    CAN_COMMENT = 'can_comment'
    
    # Admin permissions
    CAN_MANAGE_USERS = 'can_manage_users'
    CAN_MANAGE_ROLES = 'can_manage_roles'
    MANAGE_ENTERPRISES = 'manage_enterprises'
    
    # Moderation permissions
    CAN_MODERATE_POSTS = 'can_moderate_posts'
    CAN_MODERATE_COMMENTS = 'can_moderate_comments'
    
    # Enterprise permissions
    MANAGE_ENTERPRISE_USERS = 'manage_enterprise_users'
    MANAGE_ENTERPRISE_ROLES = 'manage_enterprise_roles'
    VIEW_ENTERPRISE_DATA = 'view_enterprise_data'
    EDIT_OWN_PROFILE = 'edit_own_profile'