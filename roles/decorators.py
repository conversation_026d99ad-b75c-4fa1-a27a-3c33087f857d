from functools import wraps
from rest_framework.response import Response
from rest_framework import status
from django.core.cache import cache

def require_saas_admin(view_func):
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.role.is_saas_admin:
            return Response({"error": "SaaS Admin access required"}, status=status.HTTP_403_FORBIDDEN)
        return view_func(request, *args, **kwargs)
    return wrapper

def require_enterprise_admin(view_func):
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.role.is_enterprise_admin:
            return Response({"error": "Enterprise Admin access required"}, status=status.HTTP_403_FORBIDDEN)
        return view_func(request, *args, **kwargs)
    return wrapper

def require_permission(permission_codename):
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Get user permissions from cache
            cache_key = f'user_permissions_{request.user.id}'
            user_permissions = cache.get(cache_key)
            
            if user_permissions is None:
                user_permissions = set(request.user.role.permissions.values_list('codename', flat=True))
                cache.set(cache_key, user_permissions, 300)  # Cache for 5 minutes

            if permission_codename not in user_permissions:
                return Response(
                    {"error": f"Permission '{permission_codename}' required"}, 
                    status=status.HTTP_403_FORBIDDEN
                )
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def require_enterprise_access(view_func):
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        enterprise_id = kwargs.get('enterprise_id') or request.GET.get('enterprise_id')
        if not enterprise_id:
            return Response(
                {"error": "Enterprise ID required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if not request.user.role.is_enterprise_admin or request.user.role.enterprise_id != enterprise_id:
            return Response(
                {"error": "Enterprise access denied"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        return view_func(request, *args, **kwargs)
    return wrapper 