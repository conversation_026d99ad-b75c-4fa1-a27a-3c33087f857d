# CORS Troubleshooting for File Uploads

## Problem Description

When the frontend team tries to upload files using the signed URLs from `/api/analysis/gs-file-manager/get-resumable-upload-url/`, they encounter CORS (Cross-Origin Resource Sharing) errors in the browser.

## Root Cause

The CORS error occurs because:

1. **The signed URL points directly to Google Cloud Storage** (`storage.googleapis.com`)
2. **<PERSON><PERSON><PERSON> enforces CORS policy** when making requests from `localhost:3000` to `storage.googleapis.com`
3. **Google Cloud Storage bucket lacks CORS configuration** to allow requests from the frontend origin

## Why Postman Works But Browser Doesn't

- **Postman**: Doesn't enforce CORS policies (it's not a browser)
- **Browser**: Enforces CORS policies for security reasons

## Solution Implementation

### 1. Django CORS Configuration ✅

Updated `config/settings/base.py` with comprehensive CORS settings:

```python
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = True  # For development
CORS_ALLOWED_HEADERS = [
    'accept', 'accept-encoding', 'authorization', 'content-type',
    'dnt', 'origin', 'user-agent', 'x-csrftoken', 'x-requested-with',
    'content-length', 'content-range', 'x-goog-resumable'
]
CORS_ALLOWED_METHODS = ['DELETE', 'GET', 'OPTIONS', 'PATCH', 'POST', 'PUT']
```

### 2. Google Cloud Storage CORS Configuration ⚠️

**REQUIRED**: Configure CORS on the GCS bucket to allow direct uploads from the frontend.

#### Option A: Using Django Management Command

```bash
python manage.py configure_gcs_cors
```

#### Option B: Using the Standalone Script

```bash
python scripts/configure_gcs_cors.py
```

#### Option C: Manual Configuration (Google Cloud Console)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to Cloud Storage > Buckets
3. Select your bucket
4. Go to "Permissions" tab
5. Click "Edit CORS configuration"
6. Add the following CORS configuration:

```json
[
  {
    "origin": [
      "http://localhost:3000",
      "https://localhost:3000",
      "https://*.ravid.cloud",
      "https://test.ravid.cloud",
      "https://test.in.ravid.cloud"
    ],
    "method": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"],
    "responseHeader": [
      "Content-Type", "Content-Length", "Content-Range", "Content-Encoding",
      "Date", "ETag", "Server", "Transfer-Encoding", "x-goog-generation",
      "x-goog-metageneration", "x-goog-storage-class", "x-goog-stored-content-encoding",
      "x-goog-stored-content-length", "x-goog-hash", "x-goog-resumable"
    ],
    "maxAgeSeconds": 3600
  }
]
```

## Frontend Implementation Guidelines

### 1. Correct Upload Request

When uploading to the signed URL, ensure:

```javascript
const uploadFile = async (file, uploadUrl, fileType) => {
  const response = await fetch(uploadUrl, {
    method: 'PUT',
    headers: {
      'Content-Type': fileType,  // Must match exactly
    },
    body: file
  });
  
  if (!response.ok) {
    throw new Error(`Upload failed: ${response.status}`);
  }
  
  return response;
};
```

### 2. Error Handling

```javascript
try {
  await uploadFile(file, uploadUrl, fileType);
  console.log('Upload successful');
} catch (error) {
  if (error.message.includes('CORS')) {
    console.error('CORS error - check bucket configuration');
  }
  console.error('Upload failed:', error);
}
```

## Testing Steps

1. **Configure CORS** (run the management command)
2. **Restart Django server**
3. **Test from frontend**:
   - Get upload URL from API
   - Upload file using the signed URL
   - Check browser developer tools for CORS errors

## Common Issues

### Issue: "Access to fetch at 'https://storage.googleapis.com/...' from origin 'http://localhost:3000' has been blocked by CORS policy"

**Solution**: Run the CORS configuration command and restart the server.

### Issue: "Content-Type mismatch"

**Solution**: Ensure the `Content-Type` header in the upload request exactly matches the `file_type` from the API response.

### Issue: "Upload URL expired"

**Solution**: Upload URLs expire after 15 minutes. Get a fresh URL if needed.

## Verification Commands

```bash
# Verify current CORS configuration
python manage.py configure_gcs_cors --verify-only

# Apply CORS configuration
python manage.py configure_gcs_cors

# Check Django CORS settings
python manage.py shell -c "from django.conf import settings; print(settings.CORS_ALLOWED_HEADERS)"
```

## Production Considerations

For production deployment:

1. **Restrict CORS origins** to specific domains instead of using `CORS_ALLOW_ALL_ORIGINS = True`
2. **Use HTTPS** for all origins
3. **Monitor CORS logs** for any issues
4. **Test thoroughly** before deployment

## Contact

If you continue to experience CORS issues after following these steps, please:

1. Check browser developer tools for specific error messages
2. Verify the CORS configuration was applied successfully
3. Ensure you're using the correct Content-Type header
4. Contact the backend team with specific error details
