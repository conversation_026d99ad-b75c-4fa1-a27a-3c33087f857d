-r production.txt

watchdog==4.0.2 # https://github.com/gorakhargosh/watchdog
Werkzeug[watchdog]==3.0.4 # https://github.com/pallets/werkzeug
ipdb==0.13.13  # https://github.com/gotcha/ipdb
psycopg[binary]==3.2.2  # https://github.com/psycopg/psycopg
watchfiles==0.24.0  # https://github.com/samuelcolvin/watchfiles

# Testing
# ------------------------------------------------------------------------------
mypy==1.11.2  # https://github.com/python/mypy
django-stubs[compatible-mypy]==5.0.4  # https://github.com/typeddjango/django-stubs
pytest==8.3.3  # https://github.com/pytest-dev/pytest
pytest-cov==5.0.0  # https://github.com/pytest-dev/pytest-cov
pytest-sugar==1.0.0  # https://github.com/Frozenball/pytest-sugar
djangorestframework-stubs==3.15.1  # https://github.com/typeddjango/djangorestframework-stubs

# Documentation
# ------------------------------------------------------------------------------
sphinx==7.4.7  # https://github.com/sphinx-doc/sphinx
sphinx-autobuild==2024.9.19 # https://github.com/GaretJax/sphinx-autobuild

# Code quality
# ------------------------------------------------------------------------------
ruff==0.6.7  # https://github.com/astral-sh/ruff
coverage==7.6.1  # https://github.com/nedbat/coveragepy
djlint==1.35.2  # https://github.com/Riverside-Healthcare/djLint
pre-commit==3.8.0  # https://github.com/pre-commit/pre-commit

# Django
# ------------------------------------------------------------------------------
factory-boy==3.3.1  # https://github.com/FactoryBoy/factory_boy

django-debug-toolbar==4.4.6  # https://github.com/jazzband/django-debug-toolbar
django-extensions==3.2.3  # https://github.com/django-extensions/django-extensions
django-coverage-plugin==3.1.0  # https://github.com/nedbat/django_coverage_plugin
pytest-django==4.9.0  # https://github.com/pytest-dev/pytest-django



