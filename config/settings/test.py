"""
With these settings, tests run faster.
"""

from .base import *  # noqa: F403
from .base import TEMPLATES
from .base import env

# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#secret-key
SECRET_KEY = env(
    "DJANGO_SECRET_KEY",
    default="xcFjQXGWJxqyM7kLfYbLvXOgHd75lTatqvYEKokNxq0k9x4HLlxSi8FZE0tat6wT",
)
# https://docs.djangoproject.com/en/dev/ref/settings/#test-runner
TEST_RUNNER = "django.test.runner.DiscoverRunner"

# DATABASE
# ------------------------------------------------------------------------------
# Override database settings for testing
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": env("POSTGRES_DB", default="test_ravid"),
        "USER": env("POSTGRES_USER", default="test_user"),
        "PASSWORD": env("POSTGRES_PASSWORD", default="test_pass"),
        "HOST": env("POSTGRES_HOST", default="test-db"),
        "PORT": env("POSTGRES_PORT", default="5432"),
        "ATOMIC_REQUESTS": True,
        "OPTIONS": {
            "connect_timeout": 10,
        },
    }
}

# PASSWORDS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#password-hashers
PASSWORD_HASHERS = ["django.contrib.auth.hashers.MD5PasswordHasher"]

# EMAIL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#email-backend
EMAIL_BACKEND = "django.core.mail.backends.locmem.EmailBackend"

# DEBUGGING FOR TEMPLATES
# ------------------------------------------------------------------------------
TEMPLATES[0]["OPTIONS"]["debug"] = True  # type: ignore[index]

# MEDIA
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#media-url
MEDIA_URL = "http://media.testserver/"

# CACHES
# ------------------------------------------------------------------------------
# Use Redis for testing but with separate database
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": env("REDIS_URL", default="redis://test-redis:6379/1"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "IGNORE_EXCEPTIONS": True,
        }
    }
}

# CELERY
# ------------------------------------------------------------------------------
# Use Redis for Celery in tests
CELERY_BROKER_URL = env("CELERY_BROKER_URL", default="redis://test-redis:6379/0")
CELERY_RESULT_BACKEND = env("CELERY_BACKEND", default="redis://test-redis:6379/0")
CELERY_TASK_ALWAYS_EAGER = True  # Execute tasks synchronously in tests
CELERY_TASK_EAGER_PROPAGATES = True  # Propagate exceptions in tests

# LOGGING
# ------------------------------------------------------------------------------
# Disable logging during tests for better performance
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "null": {
            "class": "logging.NullHandler",
        },
    },
    "root": {
        "handlers": ["null"],
    },
}

# STORAGES
# ------------------------------------------------------------------------------
# Override storage settings for testing - use local filesystem instead of GCS
STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

# Your stuff...
# ------------------------------------------------------------------------------
