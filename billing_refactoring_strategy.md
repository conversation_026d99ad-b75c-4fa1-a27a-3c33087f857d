# Billing App Refactoring Strategy

## Executive Summary

This document outlines a comprehensive refactoring strategy for the billing app without changing any input/output behavior. The current codebase shows signs of rapid development with complex business logic scattered across large files, making it difficult to maintain and extend.

## Current State Analysis

### Code Quality Issues Identified

1. **Monolithic Service Classes**
   - `StripeService` class: 1,663 lines with 50+ methods
   - Mixed responsibilities: payment processing, webhook handling, product sync, customer management
   - Complex webhook handling with nested conditional logic

2. **Large View Files**
   - `views.py`: 2,085 lines with multiple view classes and functions
   - Mixed concerns: checkout sessions, webhooks, DNA analysis, user transfers
   - Inconsistent error handling patterns

3. **Complex Models**
   - 15+ models in single `models.py` file (407 lines)
   - Complex relationships between billing, enterprise, and content models
   - Some models have multiple responsibilities

4. **Code Duplication**
   - Similar customer creation logic in multiple places
   - Repeated error handling patterns
   - Duplicate validation logic across views

5. **Tight Coupling**
   - Direct Stripe API calls scattered throughout the codebase
   - Business logic mixed with API integration logic
   - Hard dependencies between unrelated components

## Refactoring Strategy

### Phase 1: Service Layer Decomposition

#### 1.1 Break Down StripeService into Specialized Services

**Target Structure:**
```
billing/services/
├── __init__.py
├── base.py                    # Base service with common functionality
├── customer_service.py        # Customer management
├── payment_service.py         # Payment processing
├── subscription_service.py    # Subscription management
├── webhook_service.py         # Webhook event handling
├── product_sync_service.py    # Product synchronization
├── enterprise_service.py      # Enterprise-specific operations
└── user_transfer_service.py   # User-to-user transfers
```

**Benefits:**
- Single Responsibility Principle
- Easier testing and maintenance
- Better code organization
- Reduced cognitive load

#### 1.2 Create Service Interfaces

**Abstract Base Classes:**
```python
# billing/services/interfaces.py
from abc import ABC, abstractmethod

class PaymentProcessorInterface(ABC):
    @abstractmethod
    def create_customer(self, user): pass
    
    @abstractmethod
    def create_payment_intent(self, amount, customer): pass

class WebhookHandlerInterface(ABC):
    @abstractmethod
    def handle_event(self, event): pass
```

### Phase 2: View Layer Restructuring

#### 2.1 Organize Views by Domain

**Target Structure:**
```
billing/views/
├── __init__.py
├── base.py                    # Base view classes
├── checkout_views.py          # Checkout session creation
├── webhook_views.py           # Webhook handling
├── payment_views.py           # Payment success/cancel
├── enterprise_views.py        # Enterprise-specific views
├── dna_analysis_views.py      # DNA analysis payments
├── user_transfer_views.py     # User transfer operations
└── billing_viewsets.py        # DRF ViewSets
```

#### 2.2 Extract Common View Mixins

**Common Functionality:**
```python
# billing/views/mixins.py
class CustomerRequiredMixin:
    def get_or_create_customer(self):
        # Common customer creation logic

class RateLimitMixin:
    def check_rate_limit(self, key, timeout=60):
        # Common rate limiting logic

class ErrorHandlingMixin:
    def handle_stripe_error(self, error):
        # Standardized error handling
```

### Phase 3: Model Organization

#### 3.1 Group Related Models

**Target Structure:**
```
billing/models/
├── __init__.py
├── base.py                    # Base model classes
├── customer.py                # Customer, UserPaymentProfile
├── product.py                 # Product, Price
├── payment.py                 # ServicePayment, EnterprisePayment
├── subscription.py            # Subscription, SubscriptionAccess
├── access.py                  # ServiceAccess, SolutionAccess
├── enterprise.py              # EnterpriseAccount, EnterpriseService
├── transfer.py                # UserTransfer
└── usage.py                   # SolutionUsage, SolutionUsageLimit
```

#### 3.2 Extract Model Managers and QuerySets

**Custom Managers:**
```python
# billing/models/managers.py
class ActiveSubscriptionManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(status='active')

class PaymentQuerySet(models.QuerySet):
    def completed(self):
        return self.filter(status='completed')
    
    def for_user(self, user):
        return self.filter(user=user)
```

### Phase 4: Business Logic Extraction

#### 4.1 Create Domain Services

**Business Logic Services:**
```
billing/domain/
├── __init__.py
├── payment_processor.py       # Payment business logic
├── subscription_manager.py    # Subscription business logic
├── access_controller.py       # Access management logic
├── pricing_calculator.py      # Price calculation logic
└── notification_service.py    # Payment notifications
```

#### 4.2 Extract Validation Logic

**Validators:**
```python
# billing/validators.py
class PaymentValidator:
    @staticmethod
    def validate_amount(amount):
        # Amount validation logic
    
    @staticmethod
    def validate_customer_eligibility(customer, service):
        # Customer eligibility logic
```

### Phase 5: Configuration and Constants

#### 5.1 Extract Configuration

**Configuration Management:**
```python
# billing/config.py
class BillingConfig:
    STRIPE_API_VERSION = "2023-10-16"
    DEFAULT_CURRENCY = "usd"
    RATE_LIMIT_TIMEOUT = 60
    
    # Webhook event mappings
    WEBHOOK_HANDLERS = {
        'checkout.session.completed': 'webhook_service.handle_checkout_completed',
        'payment_intent.succeeded': 'webhook_service.handle_payment_succeeded',
        # ...
    }
```

#### 5.2 Extract Constants

**Constants File:**
```python
# billing/constants.py
class PaymentStatus:
    PENDING = 'pending'
    COMPLETED = 'completed'
    FAILED = 'failed'
    REFUNDED = 'refunded'

class SubscriptionStatus:
    ACTIVE = 'active'
    PAST_DUE = 'past_due'
    CANCELED = 'canceled'
    EXPIRED = 'expired'
```

### Phase 6: Error Handling Standardization

#### 6.1 Custom Exception Classes

**Exception Hierarchy:**
```python
# billing/exceptions.py
class BillingError(Exception):
    """Base billing exception"""
    pass

class PaymentError(BillingError):
    """Payment processing errors"""
    pass

class StripeAPIError(BillingError):
    """Stripe API related errors"""
    pass

class CustomerNotFoundError(BillingError):
    """Customer not found errors"""
    pass
```

#### 6.2 Error Handler Decorators

**Error Handling Decorators:**
```python
# billing/decorators.py
def handle_stripe_errors(func):
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error in {func.__name__}: {str(e)}")
            raise StripeAPIError(str(e))
    return wrapper
```

### Phase 7: Testing Infrastructure

#### 7.1 Test Organization

**Test Structure:**
```
billing/tests/
├── __init__.py
├── test_services/
│   ├── test_customer_service.py
│   ├── test_payment_service.py
│   └── test_webhook_service.py
├── test_views/
│   ├── test_checkout_views.py
│   └── test_webhook_views.py
├── test_models/
│   ├── test_customer.py
│   └── test_payment.py
└── fixtures/
    ├── stripe_fixtures.py
    └── model_fixtures.py
```

#### 7.2 Mock and Fixture Infrastructure

**Test Utilities:**
```python
# billing/tests/utils.py
class StripeTestMixin:
    def setUp(self):
        self.stripe_patcher = patch('billing.services.stripe')
        self.mock_stripe = self.stripe_patcher.start()

    def tearDown(self):
        self.stripe_patcher.stop()
```

## Implementation Plan

### Phase 1: Foundation (Week 1-2)
1. Create new directory structure
2. Extract base classes and interfaces
3. Move constants and configuration
4. Set up testing infrastructure

### Phase 2: Service Decomposition (Week 3-4)
1. Extract CustomerService from StripeService
2. Extract PaymentService from StripeService
3. Extract WebhookService from StripeService
4. Update imports and dependencies

### Phase 3: View Restructuring (Week 5-6)
1. Split views.py into domain-specific files
2. Extract common mixins
3. Standardize error handling
4. Update URL configurations

### Phase 4: Model Organization (Week 7-8)
1. Split models.py into logical groups
2. Extract custom managers and querysets
3. Update model imports
4. Migrate any model-specific logic

### Phase 5: Business Logic (Week 9-10)
1. Extract domain services
2. Create validation layer
3. Implement business rule classes
4. Update service dependencies

### Phase 6: Testing and Documentation (Week 11-12)
1. Create comprehensive test suite
2. Update documentation
3. Performance testing
4. Code review and cleanup

## Risk Mitigation

### 1. Backward Compatibility
- Maintain all existing public APIs
- Use deprecation warnings for old imports
- Provide migration guides

### 2. Testing Strategy
- Comprehensive unit tests for each extracted component
- Integration tests for critical workflows
- Regression testing for all existing functionality

### 3. Gradual Migration
- Implement feature flags for new vs old code paths
- Gradual rollout with monitoring
- Easy rollback mechanism

### 4. Documentation
- Update all documentation during refactoring
- Create architecture decision records (ADRs)
- Maintain changelog of all changes

## Success Metrics

### Code Quality Metrics
- Reduce average method length by 50%
- Achieve 90%+ test coverage
- Reduce cyclomatic complexity
- Eliminate code duplication

### Maintainability Metrics
- Faster feature development time
- Reduced bug introduction rate
- Easier onboarding for new developers
- Improved code review efficiency

## Detailed Refactoring Steps

### Step 1: Extract Constants and Configuration

**Create billing/constants.py:**
```python
class PaymentStatus:
    PENDING = 'pending'
    COMPLETED = 'completed'
    FAILED = 'failed'
    REFUNDED = 'refunded'

class SubscriptionStatus:
    ACTIVE = 'active'
    PAST_DUE = 'past_due'
    CANCELED = 'canceled'
    EXPIRED = 'expired'

class ProductType:
    SERVICE = 'service'
    SUBSCRIPTION = 'subscription'
    SOLUTION = 'solution'

class WebhookEvents:
    CHECKOUT_SESSION_COMPLETED = 'checkout.session.completed'
    PAYMENT_INTENT_SUCCEEDED = 'payment_intent.succeeded'
    PAYMENT_INTENT_FAILED = 'payment_intent.payment_failed'
    SUBSCRIPTION_CREATED = 'customer.subscription.created'
    SUBSCRIPTION_UPDATED = 'customer.subscription.updated'
    SUBSCRIPTION_DELETED = 'customer.subscription.deleted'
    INVOICE_PAYMENT_SUCCEEDED = 'invoice.payment_succeeded'
    INVOICE_PAYMENT_FAILED = 'invoice.payment_failed'
```

**Create billing/config.py:**
```python
from django.conf import settings

class BillingConfig:
    STRIPE_API_VERSION = "2023-10-16"
    DEFAULT_CURRENCY = "usd"
    RATE_LIMIT_TIMEOUT = 60
    CHECKOUT_SESSION_TIMEOUT = 60  # seconds

    @classmethod
    def get_stripe_keys(cls):
        return {
            'publishable_key': settings.STRIPE_PUBLISHABLE_KEY,
            'secret_key': settings.STRIPE_SECRET_KEY,
            'webhook_secret': settings.STRIPE_WEBHOOK_SECRET,
        }

    @classmethod
    def get_webhook_handlers(cls):
        from .constants import WebhookEvents
        return {
            WebhookEvents.CHECKOUT_SESSION_COMPLETED: 'webhook_service.handle_checkout_completed',
            WebhookEvents.PAYMENT_INTENT_SUCCEEDED: 'webhook_service.handle_payment_succeeded',
            WebhookEvents.PAYMENT_INTENT_FAILED: 'webhook_service.handle_payment_failed',
            WebhookEvents.SUBSCRIPTION_CREATED: 'webhook_service.handle_subscription_created',
            WebhookEvents.SUBSCRIPTION_UPDATED: 'webhook_service.handle_subscription_updated',
            WebhookEvents.SUBSCRIPTION_DELETED: 'webhook_service.handle_subscription_deleted',
            WebhookEvents.INVOICE_PAYMENT_SUCCEEDED: 'webhook_service.handle_invoice_payment_succeeded',
            WebhookEvents.INVOICE_PAYMENT_FAILED: 'webhook_service.handle_invoice_payment_failed',
        }
```

### Step 2: Create Exception Classes

**Create billing/exceptions.py:**
```python
class BillingError(Exception):
    """Base billing exception"""
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}

class PaymentError(BillingError):
    """Payment processing errors"""
    pass

class StripeAPIError(BillingError):
    """Stripe API related errors"""
    pass

class CustomerNotFoundError(BillingError):
    """Customer not found errors"""
    pass

class InvalidPaymentMethodError(PaymentError):
    """Invalid payment method errors"""
    pass

class InsufficientFundsError(PaymentError):
    """Insufficient funds errors"""
    pass

class SubscriptionError(BillingError):
    """Subscription related errors"""
    pass

class WebhookError(BillingError):
    """Webhook processing errors"""
    pass

class ProductSyncError(BillingError):
    """Product synchronization errors"""
    pass
```

### Step 3: Create Service Base Classes

**Create billing/services/__init__.py:**
```python
from .customer_service import CustomerService
from .payment_service import PaymentService
from .subscription_service import SubscriptionService
from .webhook_service import WebhookService
from .product_sync_service import ProductSyncService
from .enterprise_service import EnterpriseService
from .user_transfer_service import UserTransferService

# Backward compatibility - maintain existing imports
from .base import StripeService

__all__ = [
    'CustomerService',
    'PaymentService',
    'SubscriptionService',
    'WebhookService',
    'ProductSyncService',
    'EnterpriseService',
    'UserTransferService',
    'StripeService',  # For backward compatibility
]
```

**Create billing/services/base.py:**
```python
import stripe
import logging
from django.conf import settings
from tenacity import retry, stop_after_attempt, wait_exponential

from ..config import BillingConfig
from ..exceptions import StripeAPIError

logger = logging.getLogger(__name__)

class BaseStripeService:
    """Base class for all Stripe-related services"""

    def __init__(self):
        stripe.api_key = settings.STRIPE_SECRET_KEY
        stripe.api_version = BillingConfig.STRIPE_API_VERSION

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _make_stripe_request(self, func, *args, **kwargs):
        """Make a Stripe API request with retry logic and error handling"""
        try:
            return func(*args, **kwargs)
        except stripe.error.StripeError as e:
            logger.error(f"Stripe API error: {str(e)}")
            raise StripeAPIError(str(e), error_code=e.code if hasattr(e, 'code') else None)

    def _log_operation(self, operation, details=None):
        """Log service operations for debugging"""
        logger.info(f"Stripe operation: {operation}", extra=details or {})

# Backward compatibility class
class StripeService(BaseStripeService):
    """
    Backward compatibility class that delegates to specialized services
    This allows existing code to continue working while we migrate
    """

    def __init__(self):
        super().__init__()
        # Import here to avoid circular imports
        from .customer_service import CustomerService
        from .payment_service import PaymentService
        from .subscription_service import SubscriptionService
        from .webhook_service import WebhookService
        from .product_sync_service import ProductSyncService
        from .enterprise_service import EnterpriseService

        self.customer_service = CustomerService()
        self.payment_service = PaymentService()
        self.subscription_service = SubscriptionService()
        self.webhook_service = WebhookService()
        self.product_sync_service = ProductSyncService()
        self.enterprise_service = EnterpriseService()

    # Delegate methods to appropriate services
    @classmethod
    def create_or_update_customer(cls, user):
        return cls().customer_service.create_or_update_customer(user)

    @classmethod
    def create_checkout_session(cls, customer, price, success_url, cancel_url, metadata=None, mode='subscription'):
        return cls().payment_service.create_checkout_session(customer, price, success_url, cancel_url, metadata, mode)

    @classmethod
    def handle_webhook_event(cls, event):
        return cls().webhook_service.handle_event(event)

    # Add more delegation methods as needed...
```

### Step 4: Create Customer Service

**Create billing/services/customer_service.py:**
```python
import stripe
import logging
from django.core.exceptions import ValidationError

from .base import BaseStripeService
from ..models import Customer
from ..exceptions import CustomerNotFoundError, StripeAPIError

logger = logging.getLogger(__name__)

class CustomerService(BaseStripeService):
    """Service for managing Stripe customers"""

    def create_or_update_customer(self, user):
        """Create or update a Stripe customer for a user"""
        try:
            # Check if customer already exists
            customer = Customer.objects.filter(user=user).first()

            if customer and customer.stripe_customer_id:
                return self._update_existing_customer(customer, user)
            else:
                return self._create_new_customer(user, customer)

        except Exception as e:
            logger.error(f"Error creating/updating customer for user {user.id}: {str(e)}")
            raise StripeAPIError(f"Error creating customer: {str(e)}")

    def _update_existing_customer(self, customer, user):
        """Update existing customer in Stripe"""
        logger.info(f"Updating existing Stripe customer {customer.stripe_customer_id} for user {user.id}")

        stripe_customer = self._make_stripe_request(
            stripe.Customer.modify,
            customer.stripe_customer_id,
            email=user.email,
            name=self._get_user_display_name(user),
            metadata={'user_id': user.id}
        )

        # Update local customer record
        customer.email = user.email
        customer.name = self._get_user_display_name(user)
        customer.save()

        return customer

    def _create_new_customer(self, user, existing_customer=None):
        """Create new customer in Stripe"""
        logger.info(f"Creating new Stripe customer for user {user.id}")

        stripe_customer = self._make_stripe_request(
            stripe.Customer.create,
            email=user.email,
            name=self._get_user_display_name(user),
            metadata={'user_id': user.id}
        )

        # Create or update customer record
        if existing_customer:
            existing_customer.stripe_customer_id = stripe_customer.id
            existing_customer.email = user.email
            existing_customer.name = self._get_user_display_name(user)
            existing_customer.save()
            customer = existing_customer
        else:
            customer = Customer.objects.create(
                user=user,
                stripe_customer_id=stripe_customer.id,
                email=user.email,
                name=self._get_user_display_name(user)
            )

        logger.info(f"Created Stripe customer {stripe_customer.id} for user {user.id}")
        return customer

    def get_customer_by_user(self, user):
        """Get customer by user, raise exception if not found"""
        try:
            return Customer.objects.get(user=user)
        except Customer.DoesNotExist:
            raise CustomerNotFoundError(f"Customer not found for user {user.id}")

    def get_customer_by_stripe_id(self, stripe_customer_id):
        """Get customer by Stripe ID, raise exception if not found"""
        try:
            return Customer.objects.get(stripe_customer_id=stripe_customer_id)
        except Customer.DoesNotExist:
            raise CustomerNotFoundError(f"Customer not found for Stripe ID {stripe_customer_id}")

    def _get_user_display_name(self, user):
        """Get display name for user"""
        full_name = f"{user.first_name} {user.last_name}".strip()
        return full_name or user.email
```

## Migration Strategy

### Backward Compatibility Approach

1. **Maintain Existing APIs**: All current public methods remain available
2. **Gradual Migration**: New code uses new services, old code continues working
3. **Deprecation Warnings**: Add warnings for old import patterns
4. **Documentation**: Clear migration guides for developers

### Example Migration Pattern

**Before (Current Code):**
```python
from billing.services import StripeService

customer = StripeService.create_or_update_customer(user)
```

**After (New Code):**
```python
from billing.services import CustomerService

customer_service = CustomerService()
customer = customer_service.create_or_update_customer(user)
```

**Backward Compatible (Transition):**
```python
# Old code continues to work
from billing.services import StripeService
customer = StripeService.create_or_update_customer(user)

# But new code is preferred
from billing.services import CustomerService
customer = CustomerService().create_or_update_customer(user)
```

## Testing Strategy

### Unit Testing Approach

1. **Service-Level Tests**: Each service class has comprehensive unit tests
2. **Mock Stripe API**: Use mocks to avoid actual API calls during testing
3. **Integration Tests**: Test service interactions
4. **Regression Tests**: Ensure existing functionality remains intact

### Example Test Structure

```python
# billing/tests/test_services/test_customer_service.py
import pytest
from unittest.mock import patch, MagicMock
from billing.services import CustomerService
from billing.exceptions import CustomerNotFoundError

class TestCustomerService:
    def setUp(self):
        self.service = CustomerService()

    @patch('billing.services.customer_service.stripe.Customer.create')
    def test_create_new_customer(self, mock_create):
        # Test implementation
        pass

    def test_get_customer_not_found(self):
        with pytest.raises(CustomerNotFoundError):
            self.service.get_customer_by_user(non_existent_user)
```

## Performance Considerations

### Optimization Opportunities

1. **Database Query Optimization**: Use select_related and prefetch_related
2. **Caching Strategy**: Cache frequently accessed customer data
3. **Async Operations**: Consider async processing for webhook handling
4. **Connection Pooling**: Optimize database connections

### Monitoring and Metrics

1. **Service Performance**: Track response times for each service
2. **Error Rates**: Monitor exception rates by service type
3. **Stripe API Usage**: Track API call patterns and limits
4. **Database Performance**: Monitor query performance

## Conclusion

This refactoring strategy transforms the billing app from a monolithic structure to a well-organized, maintainable codebase while preserving all existing functionality. The key benefits include:

### Immediate Benefits
- **Improved Code Organization**: Clear separation of concerns
- **Better Testability**: Isolated components with focused responsibilities
- **Enhanced Maintainability**: Smaller, more manageable code units
- **Reduced Complexity**: Elimination of large, complex files

### Long-term Benefits
- **Easier Feature Development**: New features can be added with minimal impact
- **Better Scalability**: Services can be optimized independently
- **Improved Developer Experience**: Easier onboarding and code navigation
- **Enhanced Reliability**: Better error handling and monitoring capabilities

### Success Factors
1. **Gradual Implementation**: Phased approach minimizes risk
2. **Comprehensive Testing**: Ensures functionality preservation
3. **Clear Documentation**: Facilitates team adoption
4. **Monitoring**: Tracks refactoring success and identifies issues

This refactoring will position the billing system for future growth and make it significantly easier to maintain, extend, and scale. The modular architecture will enable the team to work more efficiently and deliver features faster while maintaining high code quality standards.
