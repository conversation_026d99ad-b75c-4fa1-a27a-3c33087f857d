from django.urls import path, include
from .views import FitbitUserAPI, FitbitSubscriberAPI, upload_access_token, get_authorization_url
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register(r'fitbit_user', FitbitUserAPI, basename='fitbit_user')
router.register(r'fitbit_subscriber', FitbitSubscriberAPI,
                basename='fitbit_subscriber')

urlpatterns = [
    path('', include(router.urls)),
    path('get_authorization_url/', get_authorization_url,
         name='get_authorization_url'),
    path('upload_access_token/', upload_access_token, name='upload_access_token'),
]
