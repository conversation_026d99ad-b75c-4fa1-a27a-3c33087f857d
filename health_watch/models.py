from django.db import models
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield

from accounts.models import CustomUser
from config.models import BaseModel

# Create your models here.

class FitbitUser(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    fitbit_user_id = models.CharField(max_length=255)
    access_token = models.CharField(max_length=300)
    refresh_token = models.CharField(max_length=300)
    scope = ArrayField(models.CharField(max_length=255))
    expires_at = models.DateTimeField()
