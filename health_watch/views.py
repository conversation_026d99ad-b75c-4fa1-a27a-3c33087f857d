from datetime import datetime, timedelta, timezone
import logging
from django.conf import settings
from django.shortcuts import redirect, render
from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.decorators import action, api_view, permission_classes
from fitbit import Fitbit

from health_watch.models import FitbitUser
logger = logging.getLogger(__name__)


class FitbitSubscriberAPI(ViewSet):
    """
    Fitbit subscriber API
    Subscriber to receive notifications (webhooks) from Fitbit
    """
    permission_classes = [AllowAny]

    def create(self, request):
        logger.info(f"Fitbit subscriber notification received: {request.data}")
        return Response({"message": "Fitbit subscriber API"})

    def list(self, request):
        verify = request.query_params.get('verify')
        if verify == settings.FITBIT_SUBSCRIBER_VERIFICATION_CODE:
            return Response({"message": "Fitbit subscriber API"}, status=209)
        else:
            return Response({"message": "Invalid verification code"}, status=404)


fitbit = Fitbit(settings.FITBIT_CLIENT_ID, settings.FITBIT_CLIENT_SECRET)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_authorization_url(request):
    url, _ = fitbit.client.authorize_token_url()
    return redirect(url)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_access_token(request):
    print(request.data)
    res = fitbit.client.fetch_access_token(request.data.get('code'))
    print(res)
    expires_at = datetime.fromtimestamp(res.get('expires_at'), tz=timezone.utc)
    print(expires_at)
    if FitbitUser.objects.filter(user=request.user).exists():
        FitbitUser.objects.filter(user=request.user).update(fitbit_user_id=res.get('user_id'), access_token=res.get(
            'access_token'), refresh_token=res.get('refresh_token'), expires_at=expires_at, scope=res.get('scope'))
    else:
        FitbitUser.objects.create(user=request.user, fitbit_user_id=res.get('user_id'), access_token=res.get(
            'access_token'), refresh_token=res.get('refresh_token'), expires_at=expires_at, scope=res.get('scope'))
    return Response({"message": "Fitbit user API"})


class FitbitUserAPI(ViewSet):
    """
    Fitbit user API
    """

    @action(detail=False, methods=['GET'])
    def get_profile(self, request):
        try:
            before_date = request.query_params.get('before_date') or datetime.now(
                timezone.utc).strftime('%Y-%m-%d')
            fitbit_user = FitbitUser.objects.filter(
                user=request.user).values().first()
            if not fitbit_user:
                return Response({"message": "Fitbit user not found", "fitbit_user": None, "profile": None})

            # define a callback function to store the new access token to the database if refresh token is used
            def refresh_cb(access_token):
                logger.info(
                    f"Refreshing access token: {access_token} for user {fitbit_user.get('id')}")
                fitbit_user.update(access_token=access_token)
            fitbit = FitbitExtendedAPI(settings.FITBIT_CLIENT_ID, settings.FITBIT_CLIENT_SECRET, access_token=fitbit_user.get(
                'access_token'), refresh_token=fitbit_user.get('refresh_token'), expires_at=fitbit_user.get('expires_at'), refresh_cb=refresh_cb)
            profile = fitbit.user_profile_get()
            sleep_data = fitbit.get_sleep_data(before_date)
            activity_data = fitbit.get_activity_data(before_date)
            daily_activity_summary = fitbit.get_daily_activity_summary(
                before_date)
            del fitbit_user['access_token']
            del fitbit_user['refresh_token']
            del fitbit_user['expires_at']
            return Response({"message": "Getting fitbit user profile successfully", "fitbit_user": fitbit_user, "profile": profile, "activity_data": activity_data, "sleep_data": sleep_data, "daily_activity_summary": daily_activity_summary})
        except Exception as e:
            logger.error(f"Error getting fitbit user: {e}")
            return Response({"message": "Failed to get fitbit user profile", "fitbit_user": None, "profile": None})

# Extend the Fitbit API to add a method to get sleep data


class FitbitExtendedAPI(Fitbit):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.LASTEST_API_VERSION = '1.2'

    def get_sleep_data(self, before_date):
        user_id = '-'
        url = f"{self.API_ENDPOINT}/{self.LASTEST_API_VERSION}/user/{user_id}/sleep/list.json?beforeDate={before_date}&sort=desc&limit=100&offset=0"
        logger.info(f'fitbit making request: {url}')
        return self.make_request(url)

    def get_activity_data(self, before_date):
        user_id = '-'
        url = f"{self.API_ENDPOINT}/{self.LASTEST_API_VERSION}/user/{user_id}/activities/list.json?beforeDate={before_date}&sort=desc&limit=100&offset=0"
        logger.info(f'fitbit making request: {url}')
        return self.make_request(url)

    def get_daily_activity_summary(self, before_date):
        user_id = '-'
        url = f"{self.API_ENDPOINT}/{self.API_VERSION}/user/{user_id}/activities/date/{before_date}.json"
        logger.info(f'fitbit making request: {url}')
        return self.make_request(url)
