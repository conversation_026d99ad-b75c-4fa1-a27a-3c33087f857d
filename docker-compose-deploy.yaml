version: "3.8"

services:
  db:
    image: postgres:16.4-alpine3.20
    env_file:
      - .env
    ports:
      - "5432:5432"
    volumes:
      - ./init-db:/docker-entrypoint-initdb.d
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ravid_network
    deploy:
      replicas: 1
      restart_policy:
        condition: any

  web:
    image: ravid_communities_backend:latest
    command: >
      bash -c "
              python manage.py migrate &&
              python manage.py create_default_roles &&
              python manage.py runserver 0.0.0.0:8000"
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    env_file:
      - .env
    depends_on:
      - db
      - postfix
    networks:
      - ravid_network
    deploy:
      replicas: 1
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/api/health-check/" ]
      interval: 30s
      timeout: 10s
      retries: 7
      start_period: 30s # Gives the container 30s to start up before first healthcheck

  postfix:
    image: boky/postfix
    env_file:
      - .env
    ports:
      - "25:25"
    volumes:
      - ./postfix/:/etc/postfix/
    networks:
      - ravid_network
    deploy:
      replicas: 1
      restart_policy:
        condition: any

  redis:
    image: redis
    ports:
      - "6380:6379"
    volumes:
      - cache:/data
    networks:
      - ravid_network
    deploy:
      replicas: 1
      restart_policy:
        condition: any

  celery:
    image: ravid_communities_backend:latest
    command: /start_celery
    volumes:
      - .:/app
    env_file:
      - .env
    depends_on:
      - redis
    networks:
      - ravid_network
    deploy:
      replicas: 1
      restart_policy:
        condition: any

networks:
  ravid_network:


volumes:
  postgres_data:
    external: true
  cache:
