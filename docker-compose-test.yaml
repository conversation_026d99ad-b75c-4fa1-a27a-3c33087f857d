services:
  test-db:
    image: postgres:16.4-alpine3.20
    environment:
      POSTGRES_DB: test_ravid
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_pass
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5434:5432"  # Changed from 5433 to avoid conflicts
    tmpfs:
      - /var/lib/postgresql/data  # In-memory DB for faster tests
    command: postgres -c fsync=off -c synchronous_commit=off -c full_page_writes=off
    networks:
      - test_network

  test-redis:
    image: redis:7.4.0-alpine3.20
    ports:
      - "6381:6379"  # Changed from 6380 to avoid conflicts
    command: redis-server --save "" --appendonly no  # Disable persistence for tests
    networks:
      - test_network
    
  test-web:
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env.test
    environment:
      - DJANGO_SETTINGS_MODULE=config.settings.test
      - DATABASE_URL=*******************************************/test_ravid
    volumes:
      - .:/app
      - ./test-reports:/app/test-reports
      - ./htmlcov:/app/htmlcov
    depends_on:
      - test-db
      - test-redis
    networks:
      - test_network
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        python scripts/wait_for_db.py &&
        echo 'Running migrations...' &&
        python manage.py migrate --noinput &&
        echo 'Running tests...' &&
        pytest billing/tests/ -v --cov=billing --cov-report=html --cov-report=term --cov-report=xml --junit-xml=test-reports/junit.xml
      "

  # Service for running specific test types
  test-unit:
    extends: test-web
    command: >
      sh -c "
        python scripts/wait_for_db.py &&
        python manage.py migrate --noinput &&
        pytest billing/tests/unit/ -v --cov=billing.services --cov=billing.models --cov-report=term
      "

  test-integration:
    extends: test-web
    command: >
      sh -c "
        python scripts/wait_for_db.py &&
        python manage.py migrate --noinput &&
        pytest billing/tests/integration/ -v --cov=billing.views --cov-report=term
      "

  test-e2e:
    extends: test-web
    command: >
      sh -c "
        python scripts/wait_for_db.py &&
        python manage.py migrate --noinput &&
        pytest billing/tests/e2e/ -v --tb=short
      "

  # Service for running tests with coverage
  test-coverage:
    extends: test-web
    command: >
      sh -c "
        python scripts/wait_for_db.py &&
        python manage.py migrate --noinput &&
        pytest billing/tests/ --cov=billing --cov-report=html --cov-report=term --cov-report=xml --cov-fail-under=85
      "

  # Service for running performance tests
  test-performance:
    extends: test-web
    command: >
      sh -c "
        python scripts/wait_for_db.py &&
        python manage.py migrate --noinput &&
        pytest billing/tests/ -m performance -v --tb=short
      "

  # Service for running tests in watch mode (for development)
  test-watch:
    extends: test-web
    command: >
      sh -c "
        python scripts/wait_for_db.py &&
        python manage.py migrate --noinput &&
        ptw billing/tests/ -- --testmon -v
      "
    stdin_open: true
    tty: true

networks:
  test_network:
    driver: bridge

volumes:
  test-reports:
  htmlcov:
