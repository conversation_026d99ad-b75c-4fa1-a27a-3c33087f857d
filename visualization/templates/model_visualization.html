<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAVID Platform - Model Relationships</title>
    <script src="https://unpkg.com/cytoscape@3.26.0/dist/cytoscape.min.js"></script>
    <script src="https://unpkg.com/cytoscape-dagre@2.5.0/cytoscape-dagre.js"></script>
    <script src="https://unpkg.com/dagre@0.8.5/dist/dagre.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem;
            text-align: center;
        }

        .controls {
            background: white;
            padding: 1rem;
            border-bottom: 1px solid #ddd;
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        select,
        button,
        input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            background: #3498db;
            color: white;
            cursor: pointer;
            border: none;
        }

        button:hover {
            background: #2980b9;
        }

        .container {
            display: flex;
            height: calc(100vh - 140px);
        }

        #cy {
            flex: 1;
            background: white;
            border-right: 1px solid #ddd;
        }

        .sidebar {
            width: 300px;
            background: white;
            padding: 1rem;
            overflow-y: auto;
            border-left: 1px solid #ddd;
        }

        .model-info {
            display: none;
        }

        .model-info.active {
            display: block;
        }

        .field-list {
            margin-top: 1rem;
        }

        .field-item {
            padding: 0.5rem;
            margin: 0.25rem 0;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .field-type {
            color: #666;
            font-style: italic;
        }

        .field-constraints {
            color: #e74c3c;
            font-size: 0.8rem;
        }

        .legend {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 0.5rem 0;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 0.5rem;
            border-radius: 3px;
        }

        .stats {
            background: #e8f4fd;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 0.25rem 0;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>🏥 RAVID Platform - Database Schema Visualization</h1>
        <p>Interactive model relationships and dependencies</p>
    </div>

    <div class="controls">
        <div class="control-group">
            <label>Layout:</label>
            <select id="layoutSelect">
                <option value="dagre">Hierarchical (Dagre)</option>
                <option value="breadthfirst">Breadth First</option>
                <option value="circle">Circle</option>
                <option value="grid">Grid</option>
                <option value="cose">Force Directed</option>
            </select>
        </div>

        <div class="control-group">
            <label>Filter by App:</label>
            <select id="appFilter">
                <option value="all">All Apps</option>
            </select>
        </div>

        <div class="control-group">
            <label>Search:</label>
            <input type="text" id="searchInput" placeholder="Search models...">
        </div>

        <button onclick="resetView()">Reset View</button>
        <button onclick="exportImage()">Export PNG</button>
        <button onclick="toggleFullscreen()">Fullscreen</button>
    </div>

    <div class="container">
        <div id="cy"></div>

        <div class="sidebar">
            <div class="stats" id="stats">
                <h3>📊 Statistics</h3>
                <div class="stat-item">
                    <span>Total Models:</span>
                    <span id="totalModels">0</span>
                </div>
                <div class="stat-item">
                    <span>Total Relationships:</span>
                    <span id="totalRelationships">0</span>
                </div>
                <div class="stat-item">
                    <span>Apps:</span>
                    <span id="totalApps">0</span>
                </div>
            </div>

            <div id="modelDetails">
                <h3>📋 Model Details</h3>
                <p>Click on a model to see details</p>
            </div>

            <div class="legend">
                <h3>🎨 Legend</h3>
                <div class="legend-item">
                    <div class="legend-color" style="background: #3498db;"></div>
                    <span>Core Models (accounts, roles)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #e74c3c;"></div>
                    <span>Business Models (clinic, enterprise)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #27ae60;"></div>
                    <span>Content Models</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f39c12;"></div>
                    <span>Analysis Models</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #9b59b6;"></div>
                    <span>Billing Models</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let cy;
        let modelData = {};

        // Color scheme for different app types
        const appColors = {
            'accounts': '#3498db',
            'roles': '#3498db',
            'clinic': '#e74c3c',
            'enterprise': '#e74c3c',
            'appointments': '#e74c3c',
            'content_management': '#27ae60',
            'analysis': '#f39c12',
            'ai_analysis': '#f39c12',
            'billing': '#9b59b6',
            'payments': '#9b59b6',
            'health_watch': '#1abc9c',
            'upload': '#95a5a6',
            'common': '#95a5a6'
        };

        // Initialize the visualization
        async function initVisualization() {
            try {
                // Load model data (you'll need to serve this JSON file)
                const response = await fetch('/static/visualization/models_data.json');
                modelData = await response.json();

                initCytoscape();
                populateControls();
                updateStats();

            } catch (error) {
                console.error('Error loading model data:', error);
                document.getElementById('modelDetails').innerHTML =
                    '<p style="color: red;">Error loading model data. Please generate the data first.</p>';
            }
        }

        function initCytoscape() {
            // Prepare nodes and edges for Cytoscape
            const elements = [];

            // Add nodes (models)
            modelData.nodes.forEach(model => {
                elements.push({
                    data: {
                        id: model.id,
                        label: model.name,
                        app: model.app,
                        model: model
                    }
                });
            });

            // Add edges (relationships)
            modelData.edges.forEach(edge => {
                elements.push({
                    data: {
                        id: `${edge.source}-${edge.target}`,
                        source: edge.source,
                        target: edge.target,
                        label: edge.field_name,
                        relationship: edge.relationship_type
                    }
                });
            });

            // Initialize Cytoscape
            cy = cytoscape({
                container: document.getElementById('cy'),
                elements: elements,
                style: getCytoscapeStyle(),
                layout: {
                    name: 'dagre',
                    rankDir: 'TB',
                    spacingFactor: 1.5
                }
            });

            // Add event listeners
            cy.on('tap', 'node', function (evt) {
                const node = evt.target;
                showModelDetails(node.data('model'));
                highlightConnectedNodes(node);
            });

            cy.on('tap', function (evt) {
                if (evt.target === cy) {
                    clearHighlight();
                }
            });
        }

        function getCytoscapeStyle() {
            return [
                {
                    selector: 'node',
                    style: {
                        'background-color': function (ele) {
                            return appColors[ele.data('app')] || '#95a5a6';
                        },
                        'label': 'data(label)',
                        'text-valign': 'center',
                        'text-halign': 'center',
                        'color': 'white',
                        'font-size': '12px',
                        'font-weight': 'bold',
                        'width': '80px',
                        'height': '40px',
                        'shape': 'roundrectangle'
                    }
                },
                {
                    selector: 'edge',
                    style: {
                        'width': 2,
                        'line-color': '#666',
                        'target-arrow-color': '#666',
                        'target-arrow-shape': 'triangle',
                        'curve-style': 'bezier',
                        'label': 'data(label)',
                        'font-size': '10px',
                        'text-rotation': 'autorotate'
                    }
                },
                {
                    selector: '.highlighted',
                    style: {
                        'background-color': '#ff6b6b',
                        'line-color': '#ff6b6b',
                        'target-arrow-color': '#ff6b6b',
                        'width': 4
                    }
                }
            ];
        }

        function showModelDetails(model) {
            const detailsDiv = document.getElementById('modelDetails');

            let fieldsHtml = '';
            model.fields.forEach(field => {
                let constraints = [];
                if (field.primary_key) constraints.push('PK');
                if (field.unique) constraints.push('UNIQUE');
                if (field.null) constraints.push('NULL');
                if (field.blank) constraints.push('BLANK');

                fieldsHtml += `
                    <div class="field-item">
                        <strong>${field.name}</strong>
                        <span class="field-type">${field.type}</span>
                        ${constraints.length > 0 ? `<div class="field-constraints">${constraints.join(', ')}</div>` : ''}
                        ${field.related_model ? `<div style="color: #3498db;">→ ${field.related_model}</div>` : ''}
                    </div>
                `;
            });

            detailsDiv.innerHTML = `
                <h3>📋 ${model.name}</h3>
                <p><strong>App:</strong> ${model.app}</p>
                <p><strong>Table:</strong> ${model.table_name}</p>
                <p><strong>Fields:</strong> ${model.fields.length}</p>
                <div class="field-list">
                    <h4>Fields:</h4>
                    ${fieldsHtml}
                </div>
            `;
        }

        function highlightConnectedNodes(node) {
            cy.elements().removeClass('highlighted');

            const connectedEdges = node.connectedEdges();
            const connectedNodes = connectedEdges.connectedNodes();

            node.addClass('highlighted');
            connectedEdges.addClass('highlighted');
            connectedNodes.addClass('highlighted');
        }

        function clearHighlight() {
            cy.elements().removeClass('highlighted');
        }

        function populateControls() {
            const appFilter = document.getElementById('appFilter');
            const apps = Object.keys(modelData.apps);

            apps.forEach(app => {
                const option = document.createElement('option');
                option.value = app;
                option.textContent = app;
                appFilter.appendChild(option);
            });

            // Add event listeners
            document.getElementById('layoutSelect').addEventListener('change', changeLayout);
            document.getElementById('appFilter').addEventListener('change', filterByApp);
            document.getElementById('searchInput').addEventListener('input', searchModels);
        }

        function updateStats() {
            document.getElementById('totalModels').textContent = modelData.nodes.length;
            document.getElementById('totalRelationships').textContent = modelData.edges.length;
            document.getElementById('totalApps').textContent = Object.keys(modelData.apps).length;
        }

        function changeLayout() {
            const layout = document.getElementById('layoutSelect').value;
            cy.layout({ name: layout }).run();
        }

        function filterByApp() {
            const selectedApp = document.getElementById('appFilter').value;

            if (selectedApp === 'all') {
                cy.elements().show();
            } else {
                cy.elements().hide();
                cy.nodes().filter(node => node.data('app') === selectedApp).show();
                cy.edges().filter(edge => {
                    const sourceApp = cy.getElementById(edge.data('source')).data('app');
                    const targetApp = cy.getElementById(edge.data('target')).data('app');
                    return sourceApp === selectedApp || targetApp === selectedApp;
                }).show();
            }
        }

        function searchModels() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            if (searchTerm === '') {
                cy.elements().show();
                return;
            }

            cy.elements().hide();
            cy.nodes().filter(node =>
                node.data('label').toLowerCase().includes(searchTerm) ||
                node.data('app').toLowerCase().includes(searchTerm)
            ).show();
        }

        function resetView() {
            cy.elements().show();
            cy.fit();
            clearHighlight();
            document.getElementById('appFilter').value = 'all';
            document.getElementById('searchInput').value = '';
        }

        function exportImage() {
            const png64 = cy.png({ scale: 2 });
            const link = document.createElement('a');
            link.href = png64;
            link.download = 'ravid_models_diagram.png';
            link.click();
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initVisualization);
    </script>
</body>

</html>