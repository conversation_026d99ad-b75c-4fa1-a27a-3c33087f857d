# RAVID Platform Architecture Visualization

This directory contains interactive visualization tools for the RAVID platform codebase.

## Quick Start

1. **Generate/Update Visualization:**
   ```bash
   python manage.py generate_architecture_viz
   ```

2. **View Results:**
   Open `output/model_visualization.html` in your browser

3. **Quick Update:**
   ```bash
   python visualization/update_visualization.py
   ```

## Files

- `output/` - Generated visualization files
- `templates/` - HTML templates for interactive views
- `static/` - CSS and JavaScript assets
- `management/commands/` - Django management commands

## Features

- 🔍 Interactive model relationship diagrams
- 🎨 Color-coded by Django app
- 🔎 Search and filter functionality
- 📊 Statistics and metrics
- 💾 Export capabilities
- 📱 Responsive design

## Customization

Edit `templates/model_visualization.html` to customize the interface.
Modify `generate_architecture_viz.py` to add new data extraction features.

## Troubleshooting

- Ensure `django-extensions` is installed
- For PNG generation, install Graphviz: `sudo apt-get install graphviz`
- Check that the visualization app is in INSTALLED_APPS

Generated by RAVID Platform Visualization Setup
