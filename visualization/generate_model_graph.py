#!/usr/bin/env python
"""
Django Model Visualization Generator
Generates interactive model relationship diagrams from Django models
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from django.core.management.base import BaseCommand
from django.apps import apps
from django.db import models


class ModelGraphGenerator:
    """Generate interactive model relationship graphs"""
    
    def __init__(self, output_dir="visualization/output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def generate_django_models_graph(self):
        """Generate model relationship graph using django-extensions"""
        try:
            # Generate DOT file
            dot_file = self.output_dir / "models.dot"
            png_file = self.output_dir / "models.png"
            
            # Use django-extensions to generate model graph
            cmd = [
                "python", "manage.py", "graph_models",
                "-a",  # All apps
                "-g",  # Group by app
                "--pydot",
                "-o", str(dot_file)
            ]
            
            subprocess.run(cmd, check=True)
            
            # Also generate PNG for quick viewing
            cmd_png = [
                "python", "manage.py", "graph_models",
                "-a", "-g",
                "-o", str(png_file)
            ]
            
            subprocess.run(cmd_png, check=True)
            
            print(f"✅ Django models graph generated: {dot_file}")
            return dot_file
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Error generating Django models graph: {e}")
            return None
    
    def extract_model_data(self):
        """Extract model data for interactive visualization"""
        model_data = {
            "nodes": [],
            "edges": [],
            "apps": {}
        }
        
        for app_config in apps.get_app_configs():
            app_name = app_config.name
            if app_name.startswith('django.'):
                continue
                
            model_data["apps"][app_name] = {
                "name": app_name,
                "models": []
            }
            
            for model in app_config.get_models():
                model_info = self._extract_model_info(model, app_name)
                model_data["nodes"].append(model_info)
                model_data["apps"][app_name]["models"].append(model_info["id"])
                
                # Extract relationships
                edges = self._extract_model_relationships(model)
                model_data["edges"].extend(edges)
        
        # Save to JSON for web visualization
        json_file = self.output_dir / "models_data.json"
        with open(json_file, 'w') as f:
            json.dump(model_data, f, indent=2, default=str)
            
        print(f"✅ Model data extracted: {json_file}")
        return json_file
    
    def _extract_model_info(self, model, app_name):
        """Extract information about a single model"""
        fields = []
        for field in model._meta.get_fields():
            field_info = {
                "name": field.name,
                "type": field.__class__.__name__,
                "null": getattr(field, 'null', False),
                "blank": getattr(field, 'blank', False),
                "unique": getattr(field, 'unique', False),
                "primary_key": getattr(field, 'primary_key', False)
            }
            
            # Add relationship info
            if hasattr(field, 'related_model') and field.related_model:
                field_info["related_model"] = f"{field.related_model._meta.app_label}.{field.related_model.__name__}"
            
            fields.append(field_info)
        
        return {
            "id": f"{app_name}.{model.__name__}",
            "name": model.__name__,
            "app": app_name,
            "table_name": model._meta.db_table,
            "fields": fields,
            "abstract": model._meta.abstract,
            "verbose_name": str(model._meta.verbose_name),
            "verbose_name_plural": str(model._meta.verbose_name_plural)
        }
    
    def _extract_model_relationships(self, model):
        """Extract relationships between models"""
        edges = []
        
        for field in model._meta.get_fields():
            if hasattr(field, 'related_model') and field.related_model:
                source = f"{model._meta.app_label}.{model.__name__}"
                target = f"{field.related_model._meta.app_label}.{field.related_model.__name__}"
                
                relationship_type = "unknown"
                if isinstance(field, models.ForeignKey):
                    relationship_type = "foreign_key"
                elif isinstance(field, models.OneToOneField):
                    relationship_type = "one_to_one"
                elif isinstance(field, models.ManyToManyField):
                    relationship_type = "many_to_many"
                elif hasattr(field, 'one_to_many') and field.one_to_many:
                    relationship_type = "one_to_many"
                elif hasattr(field, 'many_to_one') and field.many_to_one:
                    relationship_type = "many_to_one"
                
                edges.append({
                    "source": source,
                    "target": target,
                    "field_name": field.name,
                    "relationship_type": relationship_type,
                    "on_delete": getattr(field, 'on_delete', None).__name__ if hasattr(field, 'on_delete') else None
                })
        
        return edges
    
    def generate_api_endpoints_data(self):
        """Extract API endpoints information"""
        # This would require parsing URL patterns
        # Implementation depends on your URL structure
        pass


if __name__ == "__main__":
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
    
    import django
    django.setup()
    
    generator = ModelGraphGenerator()
    
    # Generate visualizations
    generator.generate_django_models_graph()
    generator.extract_model_data()
    
    print("🎉 Model visualization data generated successfully!")
    print(f"📁 Output directory: {generator.output_dir}")
