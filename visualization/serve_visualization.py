#!/usr/bin/env python3
"""
Live Server for RAVID Platform Architecture Visualization
Serves the interactive visualization with proper CORS headers and static file handling
"""

import os
import sys
import json
import webbrowser
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import mimetypes


class VisualizationHandler(SimpleHTTPRequestHandler):
    """Custom HTTP handler for serving visualization files"""
    
    def __init__(self, *args, **kwargs):
        # Set the directory to serve from
        self.viz_root = Path(__file__).parent
        super().__init__(*args, directory=str(self.viz_root), **kwargs)
    
    def end_headers(self):
        """Add CORS headers to allow local file access"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests with custom routing"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Route handling
        if path == '/' or path == '':
            # Serve the main visualization page
            self.serve_visualization_page()
        elif path == '/models_data.json':
            # Serve the models data JSON
            self.serve_models_data()
        elif path.startswith('/static/'):
            # Serve static files
            self.serve_static_file(path)
        elif path == '/health':
            # Health check endpoint
            self.serve_health_check()
        else:
            # Try to serve the file normally
            super().do_GET()
    
    def serve_visualization_page(self):
        """Serve the main visualization HTML page"""
        html_file = self.viz_root / "templates" / "model_visualization.html"
        
        if not html_file.exists():
            self.send_error(404, "Visualization page not found. Please run setup first.")
            return
        
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Update the JSON data path to work with our server
            content = content.replace(
                "'/static/visualization/models_data.json'",
                "'/models_data.json'"
            )
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f"Error serving visualization: {str(e)}")
    
    def serve_models_data(self):
        """Serve the models data JSON file"""
        json_file = self.viz_root / "output" / "models_data.json"
        
        if not json_file.exists():
            # Try to generate the data if it doesn't exist
            self.generate_sample_data()
            
        if not json_file.exists():
            self.send_error(404, "Models data not found. Please run: python manage.py generate_architecture_viz")
            return
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(data.encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f"Error serving models data: {str(e)}")
    
    def serve_static_file(self, path):
        """Serve static files (CSS, JS, images)"""
        # Remove /static/ prefix and serve from static directory
        file_path = self.viz_root / "static" / path[8:]  # Remove '/static/'
        
        if not file_path.exists():
            self.send_error(404, f"Static file not found: {path}")
            return
        
        # Determine content type
        content_type, _ = mimetypes.guess_type(str(file_path))
        if content_type is None:
            content_type = 'application/octet-stream'
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.end_headers()
            self.wfile.write(content)
            
        except Exception as e:
            self.send_error(500, f"Error serving static file: {str(e)}")
    
    def serve_health_check(self):
        """Serve health check endpoint"""
        health_data = {
            "status": "healthy",
            "service": "RAVID Visualization Server",
            "files": {
                "html_template": (self.viz_root / "templates" / "model_visualization.html").exists(),
                "models_data": (self.viz_root / "output" / "models_data.json").exists(),
                "static_css": (self.viz_root / "static" / "visualization" / "style.css").exists()
            }
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(health_data, indent=2).encode('utf-8'))
    
    def generate_sample_data(self):
        """Generate sample data if models_data.json doesn't exist"""
        print("📊 Generating sample data...")
        
        sample_data = {
            "nodes": [
                {
                    "id": "accounts.CustomUser",
                    "name": "CustomUser",
                    "app": "accounts",
                    "table_name": "accounts_customuser",
                    "fields": [
                        {"name": "id", "type": "CharField", "primary_key": True},
                        {"name": "email", "type": "EmailField", "unique": True},
                        {"name": "first_name", "type": "CharField"},
                        {"name": "last_name", "type": "CharField"}
                    ],
                    "field_count": 4
                },
                {
                    "id": "roles.Profile",
                    "name": "Profile",
                    "app": "roles",
                    "table_name": "roles_profile",
                    "fields": [
                        {"name": "id", "type": "AutoField", "primary_key": True},
                        {"name": "user", "type": "OneToOneField", "related_model": "accounts.CustomUser"},
                        {"name": "bio", "type": "TextField"}
                    ],
                    "field_count": 3
                }
            ],
            "edges": [
                {
                    "source": "roles.Profile",
                    "target": "accounts.CustomUser",
                    "field_name": "user",
                    "relationship_type": "one_to_one"
                }
            ],
            "apps": {
                "accounts": {"name": "accounts", "models": ["accounts.CustomUser"]},
                "roles": {"name": "roles", "models": ["roles.Profile"]}
            },
            "statistics": {
                "total_models": 2,
                "total_relationships": 1,
                "total_apps": 2,
                "note": "This is sample data. Run 'python manage.py generate_architecture_viz' for real data."
            }
        }
        
        # Create output directory if it doesn't exist
        output_dir = self.viz_root / "output"
        output_dir.mkdir(exist_ok=True)
        
        # Save sample data
        json_file = output_dir / "models_data.json"
        with open(json_file, 'w') as f:
            json.dump(sample_data, f, indent=2)
        
        print(f"📄 Sample data created: {json_file}")
    
    def log_message(self, format, *args):
        """Custom log message format"""
        print(f"🌐 {self.address_string()} - {format % args}")


def start_server(port=8080, auto_open=True):
    """Start the visualization server"""
    viz_root = Path(__file__).parent
    
    print("🚀 Starting RAVID Platform Visualization Server...")
    print("=" * 50)
    print(f"📁 Serving from: {viz_root}")
    print(f"🌐 Server URL: http://localhost:{port}")
    print(f"📊 Visualization: http://localhost:{port}/")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    print("=" * 50)
    
    # Check if required files exist
    html_file = viz_root / "templates" / "model_visualization.html"
    if not html_file.exists():
        print("⚠️  Warning: model_visualization.html not found")
        print("   Please run the setup script first")
    
    json_file = viz_root / "output" / "models_data.json"
    if not json_file.exists():
        print("⚠️  Warning: models_data.json not found")
        print("   Sample data will be generated, but you should run:")
        print("   python manage.py generate_architecture_viz")
    
    try:
        # Create and start server
        server = HTTPServer(('localhost', port), VisualizationHandler)
        
        # Open browser automatically
        if auto_open:
            print(f"\n🌍 Opening browser to http://localhost:{port}")
            webbrowser.open(f'http://localhost:{port}')
        
        print(f"\n✅ Server running on http://localhost:{port}")
        print("Press Ctrl+C to stop the server")
        print("-" * 50)
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        server.server_close()
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {port} is already in use. Try a different port:")
            print(f"   python visualization/serve_visualization.py --port {port + 1}")
        else:
            print(f"❌ Error starting server: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='RAVID Platform Visualization Server')
    parser.add_argument('--port', type=int, default=8080, help='Port to serve on (default: 8080)')
    parser.add_argument('--no-browser', action='store_true', help='Don\'t open browser automatically')
    
    args = parser.parse_args()
    
    start_server(port=args.port, auto_open=not args.no_browser)
