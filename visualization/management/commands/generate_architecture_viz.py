"""
Django Management Command for Architecture Visualization
Usage: python manage.py generate_architecture_viz
"""

import os
import json
import subprocess
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.apps import apps
from django.db import models
from django.conf import settings
from django.urls import get_resolver
from django.urls.resolvers import URLPattern, URLResolver


class Command(BaseCommand):
    help = 'Generate interactive architecture visualization for the Django project'

    def add_arguments(self, parser):
        parser.add_argument(
            '--output-dir',
            type=str,
            default='visualization/output',
            help='Output directory for generated files'
        )
        parser.add_argument(
            '--include-urls',
            action='store_true',
            help='Include URL patterns in the visualization'
        )
        parser.add_argument(
            '--format',
            choices=['json', 'dot', 'png', 'all'],
            default='all',
            help='Output format'
        )

    def handle(self, *args, **options):
        self.output_dir = Path(options['output_dir'])
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.stdout.write(
            self.style.SUCCESS('🚀 Generating RAVID Platform Architecture Visualization...')
        )
        
        try:
            # Generate model data
            model_data = self.extract_model_data()
            
            # Generate URL patterns if requested
            if options['include_urls']:
                url_data = self.extract_url_patterns()
                model_data['urls'] = url_data
            
            # Generate outputs based on format
            if options['format'] in ['json', 'all']:
                self.save_json_data(model_data)
            
            if options['format'] in ['dot', 'all']:
                self.generate_dot_files()
            
            if options['format'] in ['png', 'all']:
                self.generate_png_files()
            
            # Copy HTML template
            self.copy_html_template()
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ Visualization generated successfully in {self.output_dir}')
            )
            self.stdout.write(
                self.style.WARNING(f'📁 Open {self.output_dir}/model_visualization.html in your browser')
            )
            
        except Exception as e:
            raise CommandError(f'Error generating visualization: {str(e)}')

    def extract_model_data(self):
        """Extract comprehensive model data"""
        self.stdout.write('📊 Extracting model data...')
        
        model_data = {
            "nodes": [],
            "edges": [],
            "apps": {},
            "statistics": {}
        }
        
        total_fields = 0
        relationship_types = {}
        
        for app_config in apps.get_app_configs():
            app_name = app_config.name
            
            # Skip Django internal apps
            if app_name.startswith('django.') or app_name in ['rest_framework', 'corsheaders']:
                continue
                
            app_models = []
            
            for model in app_config.get_models():
                model_info = self._extract_model_info(model, app_name)
                model_data["nodes"].append(model_info)
                app_models.append(model_info["id"])
                total_fields += len(model_info["fields"])
                
                # Extract relationships
                edges = self._extract_model_relationships(model)
                model_data["edges"].extend(edges)
                
                # Count relationship types
                for edge in edges:
                    rel_type = edge["relationship_type"]
                    relationship_types[rel_type] = relationship_types.get(rel_type, 0) + 1
            
            if app_models:  # Only add apps that have models
                model_data["apps"][app_name] = {
                    "name": app_name,
                    "models": app_models,
                    "model_count": len(app_models)
                }
        
        # Add statistics
        model_data["statistics"] = {
            "total_models": len(model_data["nodes"]),
            "total_relationships": len(model_data["edges"]),
            "total_apps": len(model_data["apps"]),
            "total_fields": total_fields,
            "relationship_types": relationship_types,
            "generated_at": str(timezone.now()) if 'timezone' in globals() else str(datetime.now())
        }
        
        return model_data

    def _extract_model_info(self, model, app_name):
        """Extract detailed information about a single model"""
        fields = []
        
        for field in model._meta.get_fields():
            field_info = {
                "name": field.name,
                "type": field.__class__.__name__,
                "null": getattr(field, 'null', False),
                "blank": getattr(field, 'blank', False),
                "unique": getattr(field, 'unique', False),
                "primary_key": getattr(field, 'primary_key', False),
                "max_length": getattr(field, 'max_length', None),
                "help_text": getattr(field, 'help_text', ''),
                "default": str(getattr(field, 'default', '')) if hasattr(field, 'default') else None
            }
            
            # Add relationship information
            if hasattr(field, 'related_model') and field.related_model:
                field_info["related_model"] = f"{field.related_model._meta.app_label}.{field.related_model.__name__}"
                field_info["on_delete"] = getattr(field, 'on_delete', None).__name__ if hasattr(field, 'on_delete') else None
            
            fields.append(field_info)
        
        return {
            "id": f"{app_name}.{model.__name__}",
            "name": model.__name__,
            "app": app_name,
            "table_name": model._meta.db_table,
            "fields": fields,
            "abstract": model._meta.abstract,
            "verbose_name": str(model._meta.verbose_name),
            "verbose_name_plural": str(model._meta.verbose_name_plural),
            "field_count": len(fields),
            "has_custom_manager": hasattr(model, 'objects') and model.objects.__class__.__name__ != 'Manager'
        }

    def _extract_model_relationships(self, model):
        """Extract relationships between models"""
        edges = []
        
        for field in model._meta.get_fields():
            if hasattr(field, 'related_model') and field.related_model:
                source = f"{model._meta.app_label}.{model.__name__}"
                target = f"{field.related_model._meta.app_label}.{field.related_model.__name__}"
                
                # Determine relationship type
                relationship_type = "unknown"
                if isinstance(field, models.ForeignKey):
                    relationship_type = "foreign_key"
                elif isinstance(field, models.OneToOneField):
                    relationship_type = "one_to_one"
                elif isinstance(field, models.ManyToManyField):
                    relationship_type = "many_to_many"
                elif hasattr(field, 'one_to_many') and field.one_to_many:
                    relationship_type = "one_to_many"
                elif hasattr(field, 'many_to_one') and field.many_to_one:
                    relationship_type = "many_to_one"
                
                edges.append({
                    "source": source,
                    "target": target,
                    "field_name": field.name,
                    "relationship_type": relationship_type,
                    "on_delete": getattr(field, 'on_delete', None).__name__ if hasattr(field, 'on_delete') else None,
                    "related_name": getattr(field, 'related_name', None)
                })
        
        return edges

    def extract_url_patterns(self):
        """Extract URL patterns for API visualization"""
        self.stdout.write('🔗 Extracting URL patterns...')
        
        def extract_patterns(urlpatterns, prefix=''):
            patterns = []
            
            for pattern in urlpatterns:
                if isinstance(pattern, URLPattern):
                    patterns.append({
                        'pattern': prefix + str(pattern.pattern),
                        'name': pattern.name,
                        'view': str(pattern.callback),
                        'methods': getattr(pattern.callback, 'http_method_names', ['GET'])
                    })
                elif isinstance(pattern, URLResolver):
                    patterns.extend(
                        extract_patterns(
                            pattern.url_patterns,
                            prefix + str(pattern.pattern)
                        )
                    )
            
            return patterns
        
        try:
            resolver = get_resolver()
            return extract_patterns(resolver.url_patterns)
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Could not extract URL patterns: {str(e)}')
            )
            return []

    def save_json_data(self, data):
        """Save data as JSON file"""
        json_file = self.output_dir / "models_data.json"
        with open(json_file, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        self.stdout.write(f'💾 JSON data saved: {json_file}')

    def generate_dot_files(self):
        """Generate DOT files using django-extensions"""
        try:
            dot_file = self.output_dir / "models.dot"
            cmd = [
                "python", "manage.py", "graph_models",
                "-a", "-g", "--pydot",
                "-o", str(dot_file)
            ]
            subprocess.run(cmd, check=True, capture_output=True)
            self.stdout.write(f'📈 DOT file generated: {dot_file}')
        except subprocess.CalledProcessError as e:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Could not generate DOT file: {str(e)}')
            )

    def generate_png_files(self):
        """Generate PNG files using django-extensions"""
        try:
            png_file = self.output_dir / "models.png"
            cmd = [
                "python", "manage.py", "graph_models",
                "-a", "-g",
                "-o", str(png_file)
            ]
            subprocess.run(cmd, check=True, capture_output=True)
            self.stdout.write(f'🖼️  PNG file generated: {png_file}')
        except subprocess.CalledProcessError as e:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Could not generate PNG file: {str(e)}')
            )

    def copy_html_template(self):
        """Copy HTML template to output directory"""
        import shutil
        
        template_source = Path(__file__).parent.parent.parent / "templates" / "model_visualization.html"
        template_dest = self.output_dir / "model_visualization.html"
        
        if template_source.exists():
            shutil.copy2(template_source, template_dest)
            self.stdout.write(f'📄 HTML template copied: {template_dest}')
        else:
            self.stdout.write(
                self.style.WARNING('⚠️  HTML template not found. Please copy manually.')
            )
