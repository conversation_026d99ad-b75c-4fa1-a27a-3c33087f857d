#!/usr/bin/env python
'''
Quick update script for RAVID Platform visualization
Run this whenever you make model changes
'''

import os
import subprocess
import sys
from pathlib import Path

def main():
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print("🔄 Updating RAVID Platform visualization...")
    
    try:
        subprocess.run([
            sys.executable, "manage.py", "generate_architecture_viz"
        ], check=True)
        
        print("✅ Visualization updated successfully!")
        print("📁 Open visualization/output/model_visualization.html in your browser")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Update failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
