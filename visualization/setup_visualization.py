#!/usr/bin/env python
"""
Setup script for RAVID Platform Architecture Visualization
This script sets up all necessary components for interactive codebase visualization
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


class VisualizationSetup:
    """Setup class for architecture visualization tools"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.viz_dir = Path(__file__).parent
        self.requirements_added = []
        
    def run_setup(self):
        """Run the complete setup process"""
        print("🚀 Setting up RAVID Platform Architecture Visualization...")
        print("=" * 60)
        
        try:
            self.check_prerequisites()
            self.install_python_dependencies()
            self.setup_django_app()
            self.create_directories()
            self.setup_static_files()
            self.generate_initial_data()
            self.create_shortcuts()
            self.display_completion_message()
            
        except Exception as e:
            print(f"❌ Setup failed: {str(e)}")
            sys.exit(1)
    
    def check_prerequisites(self):
        """Check if required tools are available"""
        print("🔍 Checking prerequisites...")
        
        # Check if we're in a Django project
        if not (self.project_root / "manage.py").exists():
            raise Exception("This doesn't appear to be a Django project root")
        
        # Check if django-extensions is available
        try:
            import django_extensions
            print("✅ django-extensions found")
        except ImportError:
            print("⚠️  django-extensions not found - will be installed")
            self.requirements_added.append("django-extensions>=3.2.0")
        
        # Check for Graphviz (optional)
        try:
            subprocess.run(["dot", "-V"], capture_output=True, check=True)
            print("✅ Graphviz found")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  Graphviz not found - PNG generation may not work")
            print("   Install with: sudo apt-get install graphviz (Ubuntu/Debian)")
            print("   or: brew install graphviz (macOS)")
    
    def install_python_dependencies(self):
        """Install required Python packages"""
        if not self.requirements_added:
            print("✅ All Python dependencies already satisfied")
            return
            
        print("📦 Installing Python dependencies...")
        
        for package in self.requirements_added:
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], check=True)
                print(f"✅ Installed {package}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                raise
    
    def setup_django_app(self):
        """Setup Django app configuration"""
        print("⚙️  Setting up Django configuration...")
        
        # Check if visualization app is in INSTALLED_APPS
        settings_file = self.project_root / "config" / "settings" / "base.py"
        
        if settings_file.exists():
            with open(settings_file, 'r') as f:
                content = f.read()
                
            if 'visualization' not in content:
                print("📝 Adding visualization app to INSTALLED_APPS...")
                # This would require more sophisticated parsing
                print("⚠️  Please manually add 'visualization' to LOCAL_APPS in settings/base.py")
            else:
                print("✅ Visualization app already configured")
        
        # Create __init__.py files
        init_files = [
            self.viz_dir / "__init__.py",
            self.viz_dir / "management" / "__init__.py",
            self.viz_dir / "management" / "commands" / "__init__.py"
        ]
        
        for init_file in init_files:
            init_file.parent.mkdir(parents=True, exist_ok=True)
            if not init_file.exists():
                init_file.touch()
                print(f"📄 Created {init_file}")
    
    def create_directories(self):
        """Create necessary directories"""
        print("📁 Creating directories...")
        
        directories = [
            self.viz_dir / "output",
            self.viz_dir / "static" / "visualization",
            self.viz_dir / "templates",
            self.viz_dir / "management" / "commands"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created {directory}")
    
    def setup_static_files(self):
        """Setup static files and templates"""
        print("🎨 Setting up static files...")
        
        # Create a simple CSS file for styling
        css_content = """
/* RAVID Platform Visualization Styles */
.visualization-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
}

.viz-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.viz-controls {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.viz-main {
    display: flex;
    height: calc(100vh - 200px);
}

.viz-graph {
    flex: 1;
    background: white;
}

.viz-sidebar {
    width: 350px;
    background: #f8f9fa;
    padding: 1rem;
    overflow-y: auto;
    border-left: 1px solid #dee2e6;
}

/* Responsive design */
@media (max-width: 768px) {
    .viz-main {
        flex-direction: column;
    }
    
    .viz-sidebar {
        width: 100%;
        height: 300px;
    }
}
"""
        
        css_file = self.viz_dir / "static" / "visualization" / "style.css"
        with open(css_file, 'w') as f:
            f.write(css_content)
        print(f"🎨 Created CSS file: {css_file}")
    
    def generate_initial_data(self):
        """Generate initial visualization data"""
        print("📊 Generating initial visualization data...")
        
        try:
            # Change to project root to run Django commands
            os.chdir(self.project_root)
            
            # Run the management command
            subprocess.run([
                sys.executable, "manage.py", "generate_architecture_viz",
                "--output-dir", str(self.viz_dir / "output")
            ], check=True)
            
            print("✅ Initial data generated successfully")
            
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Could not generate initial data: {str(e)}")
            print("   You can run this manually later with:")
            print("   python manage.py generate_architecture_viz")
    
    def create_shortcuts(self):
        """Create convenient shortcuts and scripts"""
        print("🔗 Creating shortcuts...")
        
        # Create a simple update script
        update_script = self.viz_dir / "update_visualization.py"
        update_content = f"""#!/usr/bin/env python
'''
Quick update script for RAVID Platform visualization
Run this whenever you make model changes
'''

import os
import subprocess
import sys
from pathlib import Path

def main():
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print("🔄 Updating RAVID Platform visualization...")
    
    try:
        subprocess.run([
            sys.executable, "manage.py", "generate_architecture_viz"
        ], check=True)
        
        print("✅ Visualization updated successfully!")
        print("📁 Open visualization/output/model_visualization.html in your browser")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Update failed: {{str(e)}}")
        sys.exit(1)

if __name__ == "__main__":
    main()
"""
        
        with open(update_script, 'w') as f:
            f.write(update_content)
        
        # Make it executable on Unix systems
        if os.name != 'nt':
            os.chmod(update_script, 0o755)
        
        print(f"🔗 Created update script: {update_script}")
        
        # Create a README for the visualization directory
        readme_content = """# RAVID Platform Architecture Visualization

This directory contains interactive visualization tools for the RAVID platform codebase.

## Quick Start

1. **Generate/Update Visualization:**
   ```bash
   python manage.py generate_architecture_viz
   ```

2. **View Results:**
   Open `output/model_visualization.html` in your browser

3. **Quick Update:**
   ```bash
   python visualization/update_visualization.py
   ```

## Files

- `output/` - Generated visualization files
- `templates/` - HTML templates for interactive views
- `static/` - CSS and JavaScript assets
- `management/commands/` - Django management commands

## Features

- 🔍 Interactive model relationship diagrams
- 🎨 Color-coded by Django app
- 🔎 Search and filter functionality
- 📊 Statistics and metrics
- 💾 Export capabilities
- 📱 Responsive design

## Customization

Edit `templates/model_visualization.html` to customize the interface.
Modify `generate_architecture_viz.py` to add new data extraction features.

## Troubleshooting

- Ensure `django-extensions` is installed
- For PNG generation, install Graphviz: `sudo apt-get install graphviz`
- Check that the visualization app is in INSTALLED_APPS

Generated by RAVID Platform Visualization Setup
"""
        
        readme_file = self.viz_dir / "README.md"
        with open(readme_file, 'w') as f:
            f.write(readme_content)
        
        print(f"📖 Created README: {readme_file}")
    
    def display_completion_message(self):
        """Display setup completion message"""
        print("\n" + "=" * 60)
        print("🎉 RAVID Platform Architecture Visualization Setup Complete!")
        print("=" * 60)
        print()
        print("📋 Next Steps:")
        print("1. Add 'visualization' to LOCAL_APPS in config/settings/base.py")
        print("2. Run: python manage.py generate_architecture_viz")
        print("3. Open: visualization/output/model_visualization.html")
        print()
        print("🔄 To update after model changes:")
        print("   python visualization/update_visualization.py")
        print()
        print("📚 Documentation: visualization/README.md")
        print()
        print("🚀 Happy visualizing!")


if __name__ == "__main__":
    setup = VisualizationSetup()
    setup.run_setup()
