#!/bin/bash
# RAVID Platform Visualization Server Launcher
# Quick script to start the visualization server

echo "🚀 RAVID Platform - Architecture Visualization Server"
echo "=================================================="

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python not found. Please install Python 3.6+"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "🐍 Using Python: $PYTHON_CMD"

# Check if the visualization files exist
if [ ! -f "templates/model_visualization.html" ]; then
    echo "⚠️  Visualization template not found!"
    echo "   Please run the setup first:"
    echo "   $PYTHON_CMD setup_visualization.py"
    echo ""
    read -p "Do you want to run setup now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🔧 Running setup..."
        $PYTHON_CMD setup_visualization.py
    else
        echo "❌ Setup required. Exiting."
        exit 1
    fi
fi

# Check if models data exists
if [ ! -f "output/models_data.json" ]; then
    echo "⚠️  Models data not found!"
    echo "   Sample data will be generated, but for real data run:"
    echo "   cd .. && $PYTHON_CMD manage.py generate_architecture_viz"
    echo ""
fi

# Parse command line arguments
PORT=8080
NO_BROWSER=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        --no-browser)
            NO_BROWSER=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -p, --port PORT     Port to serve on (default: 8080)"
            echo "  --no-browser        Don't open browser automatically"
            echo "  -h, --help          Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                  # Start on port 8080 and open browser"
            echo "  $0 -p 3000          # Start on port 3000"
            echo "  $0 --no-browser     # Start without opening browser"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Build the Python command
PYTHON_ARGS="serve_visualization.py --port $PORT"
if [ "$NO_BROWSER" = true ]; then
    PYTHON_ARGS="$PYTHON_ARGS --no-browser"
fi

echo "🌐 Starting server on port $PORT..."
echo "📁 Serving from: $SCRIPT_DIR"
echo ""

# Start the server
exec $PYTHON_CMD $PYTHON_ARGS
