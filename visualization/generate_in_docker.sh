#!/bin/bash
# Generate RAVID Platform Architecture Visualization in Docker
# This script runs the visualization generation inside the Docker container

echo "🐳 Generating RAVID Platform Visualization in Docker..."
echo "=================================================="

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker not found. Please install Docker and Docker Compose."
        exit 1
    fi
    DOCKER_CMD="docker compose"
else
    DOCKER_CMD="docker-compose"
fi

# Determine which docker-compose file to use
COMPOSE_FILE="docker-compose-dev.yaml"
if [ ! -f "$COMPOSE_FILE" ]; then
    COMPOSE_FILE="docker-compose.yml"
    if [ ! -f "$COMPOSE_FILE" ]; then
        echo "❌ No docker-compose file found. Please check your setup."
        exit 1
    fi
fi

echo "📁 Project root: $PROJECT_ROOT"
echo "🐳 Using compose file: $COMPOSE_FILE"

# Check if containers are running
echo "🔍 Checking Docker containers..."
if ! $DOCKER_CMD -f "$COMPOSE_FILE" ps | grep -q "web.*Up"; then
    echo "⚠️  Web container is not running. Starting containers..."
    $DOCKER_CMD -f "$COMPOSE_FILE" up -d
    
    # Wait for containers to be ready
    echo "⏳ Waiting for containers to be ready..."
    sleep 10
fi

# Create visualization directory in container if it doesn't exist
echo "📁 Setting up visualization directory in container..."
$DOCKER_CMD -f "$COMPOSE_FILE" exec web mkdir -p /app/visualization/output
$DOCKER_CMD -f "$COMPOSE_FILE" exec web mkdir -p /app/visualization/static/visualization
$DOCKER_CMD -f "$COMPOSE_FILE" exec web mkdir -p /app/visualization/templates

# Copy visualization files to container
echo "📋 Copying visualization files to container..."
$DOCKER_CMD -f "$COMPOSE_FILE" exec web cp -r /app/visualization/management /app/visualization/ 2>/dev/null || true

# Install django-extensions if not already installed
echo "📦 Ensuring django-extensions is available..."
$DOCKER_CMD -f "$COMPOSE_FILE" exec web pip install django-extensions

# Generate the visualization data
echo "📊 Generating visualization data..."
$DOCKER_CMD -f "$COMPOSE_FILE" exec web python manage.py generate_architecture_viz --output-dir /app/visualization/output

# Copy generated files back to host
echo "📤 Copying generated files back to host..."
$DOCKER_CMD -f "$COMPOSE_FILE" cp "$(docker-compose -f "$COMPOSE_FILE" ps -q web):/app/visualization/output/." ./visualization/output/

# Copy templates if they don't exist on host
if [ ! -f "./visualization/templates/model_visualization.html" ]; then
    echo "📄 Copying visualization template..."
    mkdir -p ./visualization/templates
    cp ./visualization/templates/model_visualization.html ./visualization/templates/ 2>/dev/null || true
fi

echo ""
echo "✅ Visualization generated successfully!"
echo "📁 Files available in: ./visualization/output/"
echo ""
echo "🚀 To view the visualization:"
echo "   cd visualization"
echo "   python3 serve_visualization.py"
echo ""
echo "🌐 Or open: ./visualization/output/model_visualization.html"
