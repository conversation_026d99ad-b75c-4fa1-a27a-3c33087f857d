
/* RAVID Platform Visualization Styles */
.visualization-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
}

.viz-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.viz-controls {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.viz-main {
    display: flex;
    height: calc(100vh - 200px);
}

.viz-graph {
    flex: 1;
    background: white;
}

.viz-sidebar {
    width: 350px;
    background: #f8f9fa;
    padding: 1rem;
    overflow-y: auto;
    border-left: 1px solid #dee2e6;
}

/* Responsive design */
@media (max-width: 768px) {
    .viz-main {
        flex-direction: column;
    }
    
    .viz-sidebar {
        width: 100%;
        height: 300px;
    }
}
