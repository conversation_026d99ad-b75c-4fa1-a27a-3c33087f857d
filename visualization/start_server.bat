@echo off
REM RAVID Platform Visualization Server Launcher for Windows
REM Quick script to start the visualization server

echo 🚀 RAVID Platform - Architecture Visualization Server
echo ==================================================

REM Get the directory where this script is located
cd /d "%~dp0"

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    python3 --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Python not found. Please install Python 3.6+
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python3
    )
) else (
    set PYTHON_CMD=python
)

echo 🐍 Using Python: %PYTHON_CMD%

REM Check if the visualization files exist
if not exist "templates\model_visualization.html" (
    echo ⚠️  Visualization template not found!
    echo    Please run the setup first:
    echo    %PYTHON_CMD% setup_visualization.py
    echo.
    set /p REPLY="Do you want to run setup now? (y/n): "
    if /i "%REPLY%"=="y" (
        echo 🔧 Running setup...
        %PYTHON_CMD% setup_visualization.py
    ) else (
        echo ❌ Setup required. Exiting.
        pause
        exit /b 1
    )
)

REM Check if models data exists
if not exist "output\models_data.json" (
    echo ⚠️  Models data not found!
    echo    Sample data will be generated, but for real data run:
    echo    cd .. ^&^& %PYTHON_CMD% manage.py generate_architecture_viz
    echo.
)

REM Set default values
set PORT=8080
set NO_BROWSER=false

REM Parse command line arguments (basic implementation)
:parse_args
if "%1"=="" goto start_server
if "%1"=="-p" (
    set PORT=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--port" (
    set PORT=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--no-browser" (
    set NO_BROWSER=true
    shift
    goto parse_args
)
if "%1"=="-h" goto show_help
if "%1"=="--help" goto show_help

echo Unknown option: %1
echo Use -h or --help for usage information
pause
exit /b 1

:show_help
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   -p, --port PORT     Port to serve on (default: 8080)
echo   --no-browser        Don't open browser automatically
echo   -h, --help          Show this help message
echo.
echo Examples:
echo   %0                  # Start on port 8080 and open browser
echo   %0 -p 3000          # Start on port 3000
echo   %0 --no-browser     # Start without opening browser
pause
exit /b 0

:start_server
REM Build the Python command
set PYTHON_ARGS=serve_visualization.py --port %PORT%
if "%NO_BROWSER%"=="true" (
    set PYTHON_ARGS=%PYTHON_ARGS% --no-browser
)

echo 🌐 Starting server on port %PORT%...
echo 📁 Serving from: %CD%
echo.

REM Start the server
%PYTHON_CMD% %PYTHON_ARGS%

REM Pause on exit so user can see any error messages
if %errorlevel% neq 0 (
    echo.
    echo ❌ Server exited with error code %errorlevel%
    pause
)
