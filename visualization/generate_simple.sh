#!/bin/bash
# Simple visualization generation using existing Django setup

echo "🚀 Generating RAVID Platform Visualization (Simple Method)"
echo "======================================================="

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# Check if we're using docker-compose
if [ -f "docker-compose-dev.yaml" ]; then
    COMPOSE_FILE="docker-compose-dev.yaml"
    DOCKER_CMD="docker-compose -f $COMPOSE_FILE"
elif [ -f "docker-compose.yml" ]; then
    COMPOSE_FILE="docker-compose.yml"
    DOCKER_CMD="docker-compose -f $COMPOSE_FILE"
else
    echo "❌ No docker-compose file found"
    exit 1
fi

echo "🐳 Using Docker Compose: $COMPOSE_FILE"

# Ensure containers are running
echo "🔍 Checking if containers are running..."
if ! $DOCKER_CMD ps | grep -q "web.*Up"; then
    echo "🚀 Starting containers..."
    $DOCKER_CMD up -d
    echo "⏳ Waiting for containers to be ready..."
    sleep 15
fi

# Create output directory
echo "📁 Creating output directory..."
mkdir -p ./visualization/output

# Method 1: Use django-extensions graph_models (if available)
echo "📊 Generating model diagram using django-extensions..."
$DOCKER_CMD exec web python manage.py graph_models -a -g -o /app/visualization_models.png 2>/dev/null || echo "⚠️  graph_models not available"

# Method 2: Generate a simple JSON representation
echo "📋 Generating model data..."
$DOCKER_CMD exec web python -c "
import os
import django
import json
from django.apps import apps

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

# Extract model data
model_data = {
    'nodes': [],
    'edges': [],
    'apps': {},
    'statistics': {}
}

total_models = 0
total_relationships = 0

for app_config in apps.get_app_configs():
    app_name = app_config.name
    
    # Skip Django internal apps
    if app_name.startswith('django.') or app_name in ['rest_framework', 'corsheaders', 'allauth', 'crispy_forms']:
        continue
    
    app_models = []
    
    for model in app_config.get_models():
        model_info = {
            'id': f'{app_name}.{model.__name__}',
            'name': model.__name__,
            'app': app_name,
            'table_name': model._meta.db_table,
            'fields': [],
            'field_count': 0
        }
        
        # Get fields
        for field in model._meta.get_fields():
            field_info = {
                'name': field.name,
                'type': field.__class__.__name__,
                'null': getattr(field, 'null', False),
                'blank': getattr(field, 'blank', False),
                'unique': getattr(field, 'unique', False),
                'primary_key': getattr(field, 'primary_key', False)
            }
            
            # Add relationship info
            if hasattr(field, 'related_model') and field.related_model:
                field_info['related_model'] = f'{field.related_model._meta.app_label}.{field.related_model.__name__}'
                
                # Create edge
                model_data['edges'].append({
                    'source': model_info['id'],
                    'target': field_info['related_model'],
                    'field_name': field.name,
                    'relationship_type': field.__class__.__name__.lower()
                })
                total_relationships += 1
            
            model_info['fields'].append(field_info)
        
        model_info['field_count'] = len(model_info['fields'])
        model_data['nodes'].append(model_info)
        app_models.append(model_info['id'])
        total_models += 1
    
    if app_models:
        model_data['apps'][app_name] = {
            'name': app_name,
            'models': app_models,
            'model_count': len(app_models)
        }

model_data['statistics'] = {
    'total_models': total_models,
    'total_relationships': total_relationships,
    'total_apps': len(model_data['apps'])
}

# Save to file
with open('/app/models_data.json', 'w') as f:
    json.dump(model_data, f, indent=2)

print(f'✅ Generated data for {total_models} models across {len(model_data[\"apps\"])} apps')
"

# Copy the generated file to host
echo "📤 Copying generated data to host..."
$DOCKER_CMD cp web:/app/models_data.json ./visualization/output/models_data.json

# Copy any generated images
$DOCKER_CMD cp web:/app/visualization_models.png ./visualization/output/models.png 2>/dev/null || echo "ℹ️  No PNG diagram generated"

# Generate a simple HTML file if the template doesn't exist
if [ ! -f "./visualization/output/model_visualization.html" ]; then
    echo "📄 Creating simple HTML viewer..."
    cat > ./visualization/output/simple_viewer.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>RAVID Models - Simple Viewer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .app { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .model { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .field { margin: 5px 0; padding: 5px; background: white; border-radius: 2px; font-size: 0.9em; }
        .relationship { color: #007bff; font-weight: bold; }
        .stats { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>🏥 RAVID Platform - Database Models</h1>
    <div class="stats" id="stats"></div>
    <div id="content"></div>
    
    <script>
        fetch('./models_data.json')
            .then(response => response.json())
            .then(data => {
                // Show statistics
                document.getElementById('stats').innerHTML = `
                    <h3>📊 Statistics</h3>
                    <p><strong>Total Models:</strong> ${data.statistics.total_models}</p>
                    <p><strong>Total Relationships:</strong> ${data.statistics.total_relationships}</p>
                    <p><strong>Total Apps:</strong> ${data.statistics.total_apps}</p>
                `;
                
                // Show apps and models
                let html = '';
                Object.values(data.apps).forEach(app => {
                    html += `<div class="app">
                        <h2>📱 ${app.name} (${app.model_count} models)</h2>`;
                    
                    app.models.forEach(modelId => {
                        const model = data.nodes.find(n => n.id === modelId);
                        if (model) {
                            html += `<div class="model">
                                <h3>🗃️ ${model.name}</h3>
                                <p><strong>Table:</strong> ${model.table_name}</p>
                                <p><strong>Fields:</strong> ${model.field_count}</p>
                                <div>`;
                            
                            model.fields.forEach(field => {
                                let fieldClass = field.related_model ? 'field relationship' : 'field';
                                let relationshipInfo = field.related_model ? ` → ${field.related_model}` : '';
                                html += `<div class="${fieldClass}">
                                    <strong>${field.name}</strong>: ${field.type}${relationshipInfo}
                                </div>`;
                            });
                            
                            html += `</div></div>`;
                        }
                    });
                    
                    html += `</div>`;
                });
                
                document.getElementById('content').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('content').innerHTML = 
                    '<p style="color: red;">Error loading model data: ' + error + '</p>';
            });
    </script>
</body>
</html>
EOF
fi

echo ""
echo "✅ Visualization data generated successfully!"
echo "📁 Output files:"
echo "   - ./visualization/output/models_data.json (Model data)"
echo "   - ./visualization/output/simple_viewer.html (Simple HTML viewer)"
echo "   - ./visualization/output/models.png (Diagram, if generated)"
echo ""
echo "🌐 To view:"
echo "   cd visualization && python3 serve_visualization.py"
echo "   Or open: ./visualization/output/simple_viewer.html"
