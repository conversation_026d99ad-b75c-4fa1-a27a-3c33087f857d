{"nodes": [{"id": "accounts.CustomUser", "name": "CustomUser", "app": "accounts", "table_name": "accounts_customuser", "fields": [{"name": "id", "type": "CharField", "primary_key": true}, {"name": "email", "type": "EmailField", "unique": true}, {"name": "first_name", "type": "CharField"}, {"name": "last_name", "type": "CharField"}], "field_count": 4}, {"id": "roles.Profile", "name": "Profile", "app": "roles", "table_name": "roles_profile", "fields": [{"name": "id", "type": "AutoField", "primary_key": true}, {"name": "user", "type": "OneToOneField", "related_model": "accounts.CustomUser"}, {"name": "bio", "type": "TextField"}], "field_count": 3}], "edges": [{"source": "roles.Profile", "target": "accounts.CustomUser", "field_name": "user", "relationship_type": "one_to_one"}], "apps": {"accounts": {"name": "accounts", "models": ["accounts.CustomUser"]}, "roles": {"name": "roles", "models": ["roles.Profile"]}}, "statistics": {"total_models": 2, "total_relationships": 1, "total_apps": 2, "note": "This is sample data. Run 'python manage.py generate_architecture_viz' for real data."}}