<!DOCTYPE html>
<html>
<head>
    <title>RAVID Models - Simple Viewer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .app { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .model { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .field { margin: 5px 0; padding: 5px; background: white; border-radius: 2px; font-size: 0.9em; }
        .relationship { color: #007bff; font-weight: bold; }
        .stats { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>🏥 RAVID Platform - Database Models</h1>
    <div class="stats" id="stats"></div>
    <div id="content"></div>
    
    <script>
        fetch('./models_data.json')
            .then(response => response.json())
            .then(data => {
                // Show statistics
                document.getElementById('stats').innerHTML = `
                    <h3>📊 Statistics</h3>
                    <p><strong>Total Models:</strong> ${data.statistics.total_models}</p>
                    <p><strong>Total Relationships:</strong> ${data.statistics.total_relationships}</p>
                    <p><strong>Total Apps:</strong> ${data.statistics.total_apps}</p>
                `;
                
                // Show apps and models
                let html = '';
                Object.values(data.apps).forEach(app => {
                    html += `<div class="app">
                        <h2>📱 ${app.name} (${app.model_count} models)</h2>`;
                    
                    app.models.forEach(modelId => {
                        const model = data.nodes.find(n => n.id === modelId);
                        if (model) {
                            html += `<div class="model">
                                <h3>🗃️ ${model.name}</h3>
                                <p><strong>Table:</strong> ${model.table_name}</p>
                                <p><strong>Fields:</strong> ${model.field_count}</p>
                                <div>`;
                            
                            model.fields.forEach(field => {
                                let fieldClass = field.related_model ? 'field relationship' : 'field';
                                let relationshipInfo = field.related_model ? ` → ${field.related_model}` : '';
                                html += `<div class="${fieldClass}">
                                    <strong>${field.name}</strong>: ${field.type}${relationshipInfo}
                                </div>`;
                            });
                            
                            html += `</div></div>`;
                        }
                    });
                    
                    html += `</div>`;
                });
                
                document.getElementById('content').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('content').innerHTML = 
                    '<p style="color: red;">Error loading model data: ' + error + '</p>';
            });
    </script>
</body>
</html>
