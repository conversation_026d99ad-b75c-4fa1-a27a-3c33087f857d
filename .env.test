# Test Environment Variables
# This file contains environment variables specifically for testing

# Database Configuration
POSTGRES_DB=test_ravid
POSTGRES_USER=test_user
POSTGRES_PASSWORD=test_pass
POSTGRES_HOST=test-db
POSTGRES_PORT=5432

# Django Configuration
DEBUG=True
DJANGO_SECRET_KEY=test_secret_key_for_testing_only
DJANGO_SETTINGS_MODULE=config.settings.test

# Redis Configuration
REDIS_URL=redis://test-redis:6379/1
CELERY_BROKER_URL=redis://test-redis:6379/0
CELERY_BACKEND=redis://test-redis:6379/0

# Stripe Test Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_fake_key_for_testing
STRIPE_SECRET_KEY=sk_test_fake_key_for_testing
STRIPE_WEBHOOK_SECRET=whsec_fake_secret_for_testing
STRIPE_LIVE_MODE=False

# Email Configuration (for testing)
EMAIL_BACKEND=django.core.mail.backends.locmem.EmailBackend
EMAIL_HOST=localhost
EMAIL_PORT=25
EMAIL_USE_TLS=False
EMAIL_USE_SSL=False

# Disable external services for testing
GOOGLE_APPLICATION_CREDENTIALS=
GOOGLE_APPLICATION_CREDENTIALS_CONTENT=
GCS_BUCKET_NAME=test-bucket
GCS_PROJECT_ID=test-project

# Twilio (disabled for testing)
TWILIO_ACCOUNT_SID=test_account_sid
TWILIO_AUTH_TOKEN=test_auth_token
TWILIO_SENDER_PHONE_NUMBER=+***********

# Anthropic API (disabled for testing)
ANTHROPIC_API_KEY=test_anthropic_key

# Gemini API (disabled for testing)
GEMINI_API_KEY=test_gemini_key

# Apple Auth (disabled for testing)
APPLE_CLIENT_ID=test.apple.client.id
APPLE_CLIENT_SECRET=test_apple_secret
APPLE_KEY_ID=test_key_id
APPLE_PRIVATE_KEY=test_private_key

# Google OAuth (disabled for testing)
GOOGLE_CLIENT_ID=test_google_client_id
GOOGLE_CLIENT_SECRET=test_google_client_secret
