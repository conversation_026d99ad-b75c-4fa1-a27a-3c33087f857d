from rest_framework import serializers

from analysis.models.base import DNAAnalysis

class Webhook<PERSON>ayloadSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    ref_run_id = serializers.UUIDField()
    analysis_run = serializers.UUIDField()
    step_name = serializers.CharField()
    status = serializers.CharField()
    input_files = serializers.ListField(child=serializers.CharField(), required=False, allow_null=True, allow_empty=True, default=list)
    output_files = serializers.ListField(child=serializers.CharField(), required=False, allow_null=True, allow_empty=True, default=list)
    created_at = serializers.DateTimeField()
    updated_at = serializers.DateTimeField()
    input_file_urls = serializers.ListField(child=serializers.CharField(), required=False, allow_null=True, allow_empty=True, default=list)
    output_file_urls = serializers.ListField(child=serializers.Char<PERSON>ield(), required=False, allow_null=True, allow_empty=True, default=list)
    error = serializers.CharField(allow_blank=True)

class DNAAnalysisSerializer(serializers.ModelSerializer):
    class Meta:
        model = DNAAnalysis
        fields = '__all__'