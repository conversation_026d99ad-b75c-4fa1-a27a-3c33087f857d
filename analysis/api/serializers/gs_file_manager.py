from rest_framework import serializers
from analysis.models.gs_file_manager import GSFileManager
from upload.models import UploadedFile
from google.cloud import storage
from django.conf import settings
from datetime import timedelta

class UploadedFileSerializer(serializers.ModelSerializer):
    file_size = serializers.IntegerField(read_only=True)
    file_url = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = UploadedFile
        fields = [
            'id', 'file_url', 'filename', 'uploaded_at', 'file_type', 'user', 'file_size'
        ]
        read_only_fields = fields

    def get_file_url(self, obj):
        if not obj.file:
            return None
        storage_client = storage.Client()
        bucket = storage_client.bucket(settings.GS_BUCKET_NAME)
        blob = bucket.blob(obj.file.name)
        url = blob.generate_signed_url(
            version='v4',
            expiration=timedelta(minutes=15),
            method='GET',
        )
        return url

class GSFileManagerShortSerializer(serializers.ModelSerializer):
    signed_url = serializers.SerializerMethodField(read_only=True)
    filename = serializers.CharField(source='uploaded_file.filename', read_only=True)
    file_size = serializers.IntegerField(source='uploaded_file.file_size', read_only=True)
    class Meta:
        model = GSFileManager
        fields = ['id', 'signed_url', 'filename', 'file_size', 'status', 'is_result']
        read_only_fields = fields

    def get_signed_url(self, obj):
        uploaded_file = obj.uploaded_file
        if not uploaded_file or not uploaded_file.file:
            return None
        storage_client = storage.Client()
        bucket = storage_client.bucket(settings.GS_BUCKET_NAME)
        blob = bucket.blob(uploaded_file.file.name)
        url = blob.generate_signed_url(
            version='v4',
            expiration=timedelta(minutes=15),
            method='GET',
        )
        return url

class GSFileManagerSerializer(serializers.ModelSerializer):
    uploaded_file = UploadedFileSerializer(read_only=True)
    uploaded_file_id = serializers.PrimaryKeyRelatedField(
        queryset=UploadedFile.objects.all(), source='uploaded_file', write_only=True
    )

    class Meta:
        model = GSFileManager
        fields = [
            'id', 'uploaded_file', 'uploaded_file_id', 'status', 'is_result', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'uploaded_file', 'created_at', 'updated_at'] 