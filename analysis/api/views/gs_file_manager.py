from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.core.files.storage import default_storage
from upload.models import UploadedFile
from analysis.models.gs_file_manager import GSFileManager
from analysis.api.serializers.gs_file_manager import GSFileManagerSerializer, GSFileManagerShortSerializer
from analysis.models.base import DNAAnalysis
from analysis.serializers import DNAAnalysisDetailSerializer
from analysis.utils import get_gs_file_manager_path
import logging
from django.conf import settings
from google.cloud import storage
from django.core.cache import cache
import google.auth
from google.auth.transport.requests import Request as GoogleAuthRequest
import requests
from datetime import timedelta
from django.http import JsonResponse

logger = logging.getLogger(__name__)

class GSFileManagerViewSet(viewsets.ModelViewSet):
    queryset = GSFileManager.objects.all()
    serializer_class = GSFileManagerSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Only return files belonging to the current user
        return GSFileManager.objects.filter(uploaded_file__user=self.request.user)

    def add_cors_headers(self, response):
        """Add CORS headers to response for file upload operations."""
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = (
            'Content-Type, Content-Length, Content-Range, Authorization, '
            'X-Requested-With, X-CSRFToken, x-goog-resumable'
        )
        response['Access-Control-Max-Age'] = '3600'
        return response

    def perform_create(self, serializer):
        # Ensure we only create files for the current user
        uploaded_file = serializer.validated_data['uploaded_file']
        if uploaded_file.user != self.request.user:
            raise PermissionError('You do not have permission to use this file.')
        serializer.save()

    @action(methods=['post'], detail=False, url_path='upload-and-register')
    def upload_and_register(self, request):
        """
        Upload and register a file in a single operation.
        This is the preferred method for smaller files.
        """
        logger.info(f"Starting upload_and_register for user {request.user.id}")
        file = request.FILES.get('file')
        is_result = request.data.get('is_result', False)
        status_value = request.data.get('status', 'uploaded')

        if not file:
            logger.warning("No file provided in request")
            return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)

        # Upload file to Google Storage with new path
        file_path = get_gs_file_manager_path(request.user.id, file.name)
        logger.info(f"Generated file path: {file_path}")

        saved_path = default_storage.save(file_path, file)
        file_url = default_storage.url(saved_path)
        logger.info(f"File saved at {saved_path}, URL: {file_url}")

        # Create UploadedFile
        uploaded_file = UploadedFile.objects.create(
            file=file_path,
            filename=file.name,
            user=request.user,
            file_url=file_url,
            file_type=file.content_type,
            file_size=file.size
        )

        # Create GSFileManager
        gs_file = GSFileManager.objects.create(
            uploaded_file=uploaded_file,
            status=status_value,
            is_result=is_result
        )

        serializer = self.get_serializer(gs_file)
        logger.info(f"Successfully created GSFileManager record with ID {gs_file.id}")
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(methods=['get'], detail=True, url_path='signed-url')
    def signed_url(self, request, pk=None):
        """Get a signed URL for accessing a file, only if the file belongs to the current user."""
        gs_file = self.get_object()
        if gs_file.uploaded_file.user != request.user:
            return Response({'error': 'You do not have permission to access this file.'}, status=status.HTTP_403_FORBIDDEN)
        uploaded_file = gs_file.uploaded_file
        file_path = uploaded_file.file.name
        try:
            storage_client = storage.Client()
            bucket = storage_client.bucket(settings.GS_BUCKET_NAME)
            blob = bucket.blob(file_path)
            signed_url = blob.generate_signed_url(
                version='v4',
                expiration=timedelta(minutes=15),
                method='GET',
            )
            return Response({'signed_url': signed_url})
        except Exception as e:
            return Response({'error': str(e)}, status=400)

    @action(methods=['get'], detail=False, url_path='my-files')
    def list_my_files(self, request):
        """Return a list of all GSFileManager files for the current user, with nested uploaded_file and fresh file_url."""
        is_result = request.query_params.get('is_result')
        queryset = GSFileManager.objects.filter(uploaded_file__user=request.user)
        if is_result is not None:
            queryset = queryset.filter(is_result=(is_result.lower() == 'true'))
        serializer = GSFileManagerSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(methods=['get'], detail=False, url_path='dna-analysis-detail')
    def get_dna_analysis(self, request):
        """Get list of DNAAnalysis for the current user with input_files and result_files."""
        queryset = DNAAnalysis.objects.filter(user=request.user).order_by('-created_at')
        serializer = DNAAnalysisDetailSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(methods=['post'], detail=False, url_path='get-resumable-upload-url')
    def get_resumable_upload_url(self, request):
        """
        Generate a resumable upload URL for direct upload to Google Storage.
        This is the preferred method for large files to support resumable uploads.
        """
        try:
            filename = request.data.get('filename')
            file_type = request.data.get('file_type', 'application/octet-stream')
            file_size = request.data.get('file_size')

            if not filename:
                return Response({'error': 'Missing filename'}, status=status.HTTP_400_BAD_REQUEST)

            logger.info(f"Creating signed URL for file: {filename}, type: {file_type}, size: {file_size}")

            user_id = request.user.id
            file_path = get_gs_file_manager_path(user_id, filename)
            bucket_name = settings.GS_BUCKET_NAME

            # Create a GCS client with explicit scopes
            storage_client = storage.Client()
            bucket = storage_client.bucket(bucket_name)
            blob = bucket.blob(file_path)

            # Generate a signed URL with V4 authentication
            url = blob.generate_signed_url(
                version="v4",
                expiration=timedelta(minutes=15),
                method="PUT",
                content_type=file_type,
            )

            logger.info(f"Generated signed URL for content-type: {file_type}")

            # Include clear instructions in the response
            response_data = {
                'upload_url': url,
                'file_path': file_path,
                'file_type': file_type,
                'file_size': file_size,
                'bucket': bucket_name,
                'instructions': 'When uploading, ensure the Content-Type header exactly matches the file_type value'
            }

            response = Response(response_data)
            return self.add_cors_headers(response)
        except Exception as e:
            logger.error(f"Error generating resumable upload URL: {str(e)}")
            return Response({'error': str(e)}, status=500)

    @action(methods=['post'], detail=False, url_path='confirm-upload')
    def confirm_upload(self, request):
        """
        Confirm that a file has been uploaded to Google Storage.
        This should be called after successfully uploading via the upload_url.
        """
        file_path = request.data.get('file_path')
        filename = request.data.get('filename')
        file_type = request.data.get('file_type', 'application/octet-stream')
        file_size = request.data.get('file_size')

        if not all([file_path, filename, file_size]):
            return Response({'error': 'Missing required fields'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Verify the file exists in Google Storage
            storage_client = storage.Client()
            bucket = storage_client.bucket(settings.GS_BUCKET_NAME)
            blob = bucket.blob(file_path)

            if not blob.exists():
                return Response({'error': 'File does not exist in Google Storage'}, status=status.HTTP_400_BAD_REQUEST)

            # Generate signed URL for accessing the file
            file_url = blob.generate_signed_url(
                version='v4',
                expiration=timedelta(hours=24),
                method='GET',
            )

            # Create UploadedFile record
            uploaded_file = UploadedFile.objects.create(
                file=file_path,
                filename=filename,
                user=request.user,
                file_url=file_url,
                file_type=file_type,
                file_size=float(file_size)  # Ensure file_size is a number
            )

            # Create GSFileManager record
            gs_file = GSFileManager.objects.create(
                uploaded_file=uploaded_file,
                status='uploaded',
                is_result=False
            )

            serializer = self.get_serializer(gs_file)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.error(f"Error confirming upload: {str(e)}")
            return Response({'error': str(e)}, status=500)