import os

from django.conf import settings
import requests
from analysis.api.serializers.dna_analysis import WebhookPayloadSerializer
from rest_framework.response import Response
from django.contrib.auth.decorators import login_required
from rest_framework.decorators import api_view
import pandas as pd
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from analysis.models.base import DNAAnalysis
from django.http import JsonResponse
import logging

from analysis.models.gs_file_manager import GSFileManager
from analysis.serializers import DNAAnalysisDetailSerializer, DNAAnalysisSerializer
from upload.models import UploadedFile
from django.db import transaction

logger = logging.getLogger(__name__)

# Create your views here.
@api_view(['GET'])
@login_required
def get_analysis_runs(request):
    analysis_runs = DNAAnalysis.objects.filter(user=request.user).order_by('-created_at').prefetch_related('input_files', 'service_payments')
    serializer = DNAAnalysisSerializer(analysis_runs, many=True)
    return Response({'analysis_runs': serializer.data}, status=200)

@api_view(['GET'])
@login_required
def get_analysis_run(request, analysis_run_id):
    analysis_run = DNAAnalysis.objects.get(id=analysis_run_id)
    serializer = DNAAnalysisDetailSerializer(analysis_run)
    return Response({'analysis_run': serializer.data}, status=200)

@api_view(['POST'])
@login_required
def start_analysis_run(request):
    user = request.user
    # Find a pending analysis run that is paid for
    analysis_run = DNAAnalysis.objects.filter(user=user, status='pending', service_payments__status='completed').first()
    if not analysis_run:
        return Response({'error': 'No paid analysis run found'}, status=400)
    
    # Update the analysis run status to processing
    analysis_run.status = 'processing'
    analysis_run.save()

    res = requests.post(
        f'{settings.DNA_ANALYSIS_API_URL}/api/run_gsba/',
        data={
            'run_id': analysis_run.id,
            'user_id': user.id,
            'file_path1': "uploaded_files/e42f9bac-a275-4329-8901-6ab5d9b6c03f_driver.jpg",
            'file_path2': "uploaded_files/c97331c2-79da-44cf-a8b1-193ade1e5f1b_0266554465.jpeg",
        },
        headers={
            'x-api-key': settings.DNA_ANALYSIS_API_KEY,
        },
    )
    logger.info(f"Analysis run started {res.text}")
    return Response({'message': 'Analysis run started'}, status=200)


cur_dir = os.path.dirname(os.path.abspath(__file__))
df = pd.read_csv(os.path.join(cur_dir, '../../genes.txt'), sep='\t')

@api_view(['GET'])
def search_genes(request):
    search_term = request.GET.get('search_term')
    if search_term:
        search_results = df[df['CancerGenes'].str.startswith(search_term)]['CancerGenes'].tolist()
        return Response(search_results)
    return Response(df.to_dict(orient='records'))



@api_view(['POST'])
@permission_classes([AllowAny])
def handle_webhook(request):
    try:
        logger.info(f"Received dna_service webhook: {request.data}")
        # write to a log file
        with open('webhook.log', 'a') as f:
            f.write(f"Received dna_service webhook: {request.data}\n")
        serializer = WebhookPayloadSerializer(data=request.data)
        if serializer.is_valid():            
            logger.info(f"Valid dna_service webhook data: {serializer.validated_data}")
            with transaction.atomic():
                analysis_run = DNAAnalysis.objects.get(id=serializer.validated_data['ref_run_id'])
                if serializer.validated_data['output_files']:
                    for file_url in serializer.validated_data['output_files']:
                        file_name = os.path.basename(file_url)
                        # Create and return database record
                        uploaded_file = UploadedFile.objects.create(
                            file=file_url,
                            filename=file_name, 
                            user=analysis_run.user,
                            file_url=file_url,
                        )
                        gs_file_manager = GSFileManager.objects.create(
                            uploaded_file=uploaded_file,
                            is_result=True,
                        )
                        analysis_run.result_files.add(gs_file_manager)
                analysis_run.steps.append({
                    'step_name': serializer.validated_data['step_name'],
                    'status': serializer.validated_data['status'],
                    'input_file_urls': serializer.validated_data['input_files'] if serializer.validated_data['input_files'] else [],
                    'output_file_urls': serializer.validated_data['output_files'] if serializer.validated_data['output_files'] else [],
                })
                analysis_run.current_step += 1
                if analysis_run.current_step >= analysis_run.total_steps:
                    analysis_run.status = 'completed'
                analysis_run.save()

            return JsonResponse(serializer.data, status=200)
        else:
            logger.error(f"Invalid dna_service webhook data: {serializer.errors}")
        return JsonResponse(serializer.errors, status=400)
    except Exception as e:
        logger.error(f"Error handling webhook: {e}")
        return JsonResponse({'error': str(e)}, status=500)
