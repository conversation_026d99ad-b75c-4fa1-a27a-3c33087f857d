# Generated by Django 5.0.9 on 2025-05-11 09:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('analysis', '0006_gsfilemanager'),
    ]

    operations = [
        migrations.AddField(
            model_name='dnaanalysis',
            name='input_files',
            field=models.ManyToManyField(blank=True, related_name='input_dna_analyses', to='analysis.gsfilemanager'),
        ),
        migrations.RemoveField(
            model_name='dnaanalysis',
            name='result_files',
        ),
        migrations.AddField(
            model_name='dnaanalysis',
            name='result_files',
            field=models.ManyToManyField(blank=True, related_name='result_dna_analyses', to='analysis.gsfilemanager'),
        ),
    ]
