# Generated by Django 5.0.9 on 2025-05-11 08:03

import django.db.models.deletion
import uuid6
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('analysis', '0005_remove_dnaanalysis_dna_code_dnacodeanalysis'),
        ('upload', '0002_uploadedfile_file_type_uploadedfile_file_url'),
    ]

    operations = [
        migrations.CreateModel(
            name='GSFileManager',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('uploaded', 'Uploaded'), ('processing', 'Processing'), ('analyzed', 'Analyzed'), ('failed', 'Failed')], default='uploaded', help_text='Current status of the file in the DNA analysis workflow', max_length=32)),
                ('is_result', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Is this file a result file?')),
                ('uploaded_file', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='gs_file_manager', to='upload.uploadedfile')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
