# Generated by Django 5.0.9 on 2025-04-18 12:21

import django.core.validators
import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('analysis', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DNAAnalysis',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('dna_code', models.CharField(help_text='DNA code for analysis', max_length=255)),
                ('parameters', models.JSONField(default=dict, help_text='Additional parameters for DNA analysis')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=50)),
                ('result_files', models.JSONField(default=list, help_text='List of result file URLs')),
                ('notes', models.TextField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dna_analyses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='DNACode',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(help_text='DNA sequence code (A, T, C, G nucleotides only)', max_length=50, unique=True, validators=[django.core.validators.RegexValidator(message='DNA code must contain only A, T, C, G nucleotides', regex='^[ATCG]+$')])),
                ('description', models.TextField(blank=True, help_text='Description of the DNA code')),
                ('length', models.IntegerField(help_text='Length of the DNA sequence')),
                ('gc_content', models.FloatField(help_text='GC content percentage of the DNA sequence')),
                ('melting_temperature', models.FloatField(blank=True, help_text='Melting temperature in Celsius', null=True)),
                ('molecular_weight', models.FloatField(blank=True, help_text='Molecular weight in Daltons', null=True)),
                ('is_valid', models.BooleanField(default=True, help_text='Whether the DNA code is valid for analysis')),
                ('validation_notes', models.TextField(blank=True, help_text='Notes about validation results')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='Additional metadata about the DNA code')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['code'], name='analysis_dn_code_bd6c4b_idx'), models.Index(fields=['is_valid'], name='analysis_dn_is_vali_961e6c_idx')],
            },
        ),
    ]
