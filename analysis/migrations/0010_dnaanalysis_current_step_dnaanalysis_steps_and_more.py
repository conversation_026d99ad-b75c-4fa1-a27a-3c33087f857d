# Generated by Django 5.0.9 on 2025-06-01 12:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('analysis', '0009_alter_dnaanalysis_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='dnaanalysis',
            name='current_step',
            field=models.IntegerField(default=0, help_text='Current step for analysis'),
        ),
        migrations.AddField(
            model_name='dnaanalysis',
            name='steps',
            field=models.JSONField(blank=True, default=list, help_text='List of steps for analysis', null=True),
        ),
        migrations.AddField(
            model_name='dnaanalysis',
            name='total_steps',
            field=models.IntegerField(default=0, help_text='Total number of steps for analysis'),
        ),
    ]
