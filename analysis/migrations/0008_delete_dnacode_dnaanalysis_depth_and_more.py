# Generated by Django 5.0.9 on 2025-05-11 09:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('analysis', '0007_dnaanalysis_input_files_and_more'),
    ]

    operations = [
        migrations.DeleteModel(
            name='DNACode',
        ),
        migrations.AddField(
            model_name='dnaanalysis',
            name='depth',
            field=models.IntegerField(default=1, help_text='Depth of analysis'),
        ),
        migrations.AddField(
            model_name='dnaanalysis',
            name='dna_codes',
            field=models.JSONField(blank=True, default=list, help_text='List of DNA codes for analysis', null=True),
        ),
        migrations.DeleteModel(
            name='DNACodeAnalysis',
        ),
    ]
