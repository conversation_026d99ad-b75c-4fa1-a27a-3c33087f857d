# Generated by Django 5.0.9 on 2025-04-23 10:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('analysis', '0004_dnaanalysis_stripe_session_id'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dnaanalysis',
            name='dna_code',
        ),
        migrations.CreateModel(
            name='DNACodeAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('dna_analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dna_codes', to='analysis.dnaanalysis')),
            ],
            options={
                'db_table': 'dna_code_analysis',
                'ordering': ['-created_at'],
            },
        ),
    ]
