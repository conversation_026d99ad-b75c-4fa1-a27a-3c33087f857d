# Generated by Django 5.0.9 on 2024-12-23 14:51

import django.db.models.deletion
import uuid6
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('payments', '0002_price_short_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AnalysisRun',
            fields=[
                ('id', models.UUIDField(default=uuid6.uuid7, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('PENDING', 'PENDING'), ('RUNNING', 'RUNNING'), ('COMPLETED', 'COMPLETED'), ('FAILED', 'FAILED')], default='PENDING', max_length=255)),
                ('stripe_session_id', models.CharField(blank=True, max_length=255)),
                ('price', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_run', to='payments.price')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_run', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
