from analysis.api.views.dna_analysis import get_analysis_runs
from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from analysis.api.views.gs_file_manager import GSFileManagerViewSet
from .api.views.dna_analysis import get_analysis_run, handle_webhook, search_genes, start_analysis_run

router = DefaultRouter()
router.register(r'gs-file-manager', GSFileManagerViewSet, basename='gs-file-manager')
urlpatterns = [
    path('analysis-runs/', get_analysis_runs, name='get_analysis_runs'),
    path('analysis-runs/<uuid:analysis_run_id>/', get_analysis_run, name='get_analysis_run'),
    path('start-analysis-run/', start_analysis_run, name='start_analysis_run'),
    path('', include(router.urls)),
    path('search-genes/', search_genes, name='search_genes'),
    path('webhook/', handle_webhook, name='handle_webhook'),
]
