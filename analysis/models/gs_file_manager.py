from django.db import models
from django.conf import settings
from upload.models import UploadedFile
from django.utils.translation import gettext_lazy as _

from config.models import BaseModel


class GSFileManager(BaseModel):
    uploaded_file = models.OneToOneField(UploadedFile, on_delete=models.CASCADE, related_name='gs_file_manager')
    status = models.CharField(
        max_length=32,
        choices=[
            ("uploaded", "Uploaded"),
            ("processing", "Processing"),
            ("analyzed", "Analyzed"),
            ("failed", "Failed"),
        ],
        default="uploaded",
        help_text=_('Current status of the file in the DNA analysis workflow')
    )
    is_result = models.BooleanField(default=False, help_text=_('Is this file a result file?'))

    def __str__(self):
        return f"{self.uploaded_file.user.email} - {self.uploaded_file.filename} ({'Result' if self.is_result else 'Input'})"
