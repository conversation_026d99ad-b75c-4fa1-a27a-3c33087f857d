from django.conf import settings
from django.db import models

from accounts.models import CustomUser
from analysis.constant import ANALYSIS_RUN_STATUS
from analysis.models.gs_file_manager import GSFileManager
from config.models import BaseModel
from payments.models import Price
import logging

logger = logging.getLogger(__name__)

# Create your models here.
class AnalysisRun(BaseModel):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='analysis_run')
    status = models.CharField(max_length=255, choices=ANALYSIS_RUN_STATUS, default=ANALYSIS_RUN_STATUS["PENDING"])
    price = models.ForeignKey(Price, on_delete=models.CASCADE, related_name='analysis_run')
    stripe_session_id = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return f"Analysis Run {self.id} for {self.user}"

class DNAAnalysis(BaseModel):
    """Model to store DNA analysis data and parameters"""
    user = models.<PERSON><PERSON><PERSON>(CustomUser, on_delete=models.CASCADE, related_name='dna_analyses')
    parameters = models.J<PERSON><PERSON><PERSON>(default=dict, help_text="Additional parameters for DNA analysis")
    status = models.Char<PERSON><PERSON>(max_length=50, default="waiting_for_payment", choices=[
        ('waiting_for_payment', 'Waiting for Payment'),
        ("pending", "Pending"),
        ("processing", "Processing"),
        ("completed", "Completed"),
        ("failed", "Failed")
    ])
    input_files = models.ManyToManyField(GSFileManager, related_name='input_dna_analyses', blank=True)
    result_files = models.ManyToManyField(GSFileManager, related_name='result_dna_analyses', blank=True)
    notes = models.TextField(blank=True, null=True)
    stripe_session_id = models.CharField(max_length=255, blank=True, null=True, help_text="Stripe checkout session ID")
    dna_codes = models.JSONField(default=list, blank=True, null=True, help_text="List of DNA codes for analysis")
    depth = models.IntegerField(default=1, help_text="Depth of analysis")
    steps = models.JSONField(default=list, blank=True, null=True, help_text="List of steps for analysis")
    total_steps = models.IntegerField(default=0, help_text="Total number of steps for analysis")
    current_step = models.IntegerField(default=0, help_text="Current step for analysis")

    def __str__(self):
        return f"DNA Analysis {self.id}"
