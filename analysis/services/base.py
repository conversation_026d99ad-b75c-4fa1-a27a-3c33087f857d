from django.conf import settings
import requests
import logging
from analysis.models.base import DNAAnalysis
from billing.models import ServicePayment
from billing.serializers.dna_analysis import DNAAnalysisServicePaymentSerializer

logger = logging.getLogger(__name__)

class DNAAnalysisService:
    @staticmethod
    def start_analysis(dna_analysis: DNAAnalysis):
        query = ServicePayment.objects.get(dna_analysis=dna_analysis)
        serialized = DNAAnalysisServicePaymentSerializer(query)
        services = serialized.data['service_details']
        endings = ['fastp.fq.gz', 'bam', 'vcf']
        input_file = dna_analysis.input_files.first().uploaded_file.file.name
        input_file_type = next((ending for ending in endings if input_file.endswith(ending)), None)
        
        for service in services:
            if 'gsba' in service['name'].lower():
                if input_file_type in ['fastp.fq.gz', 'bam']:
                    # Valid, checking next service
                    input_file_type = 'vcf'
                    continue
                else:
                    logger.error(f"DNAAnalysis {dna_analysis.id} invalid input file type {input_file_type} for service {service['name']}")
                    return
            elif 'csva' in service['name'].lower():
                if input_file_type in ['vcf']:
                    # Valid
                    pass
                else:
                    logger.error(f"DNAAnalysis {dna_analysis.id} invalid input file type {input_file_type} for service {service['name']}")
                    return
            else:
                logger.error(f"DNAAnalysis {dna_analysis.id} unknown service {service['name']}")
                return
            
        # Everything is valid, process the analysis
        main_service = services[-1]
        if main_service['name'] == 'gsba':
            DNAAnalysisService.start_gsba_analysis(dna_analysis)
        elif main_service['name'] == 'csva':
            DNAAnalysisService.start_csva_analysis(dna_analysis)
        else:
            logger.error(f"DNAAnalysis {dna_analysis.id} unknown service {main_service['name']}")
            return

    @staticmethod
    def start_gsba_analysis(dna_analysis: DNAAnalysis):
        try:
            if dna_analysis.input_files.count() != 2:
                logger.error(f"DNAAnalysis {dna_analysis.id} has {dna_analysis.input_files.count()} input files, expected 2")
                return
            if dna_analysis.input_files.first().uploaded_file.file.name is None or dna_analysis.input_files.last().uploaded_file.file.name is None:
                logger.error(f"DNAAnalysis {dna_analysis.id} missing input files, {dna_analysis.input_files.first().uploaded_file.file.name} or {dna_analysis.input_files.last().uploaded_file.file.name}")
                return
            
            dna_analysis.status = "processing"
            res = requests.post(
                f'{settings.DNA_ANALYSIS_API_URL}/api/run_gsba/',
                data={
                    'run_id': dna_analysis.id,
                    'user_id': dna_analysis.user.id,
                    'file_path1': dna_analysis.input_files.first().uploaded_file.file.name,
                    'file_path2': dna_analysis.input_files.last().uploaded_file.file.name,
                },
                headers={
                    'x-api-key': settings.DNA_ANALYSIS_API_KEY,
                }
            )
            dna_analysis.total_steps = 6
            dna_analysis.current_step = 0
            dna_analysis.save()
            logger.info(f"Analysis run started {res.text}")
        except Exception as e:
            logger.error(f"Error starting analysis {e}")

    @staticmethod
    def start_csva_analysis(dna_analysis: DNAAnalysis):
        try:
            if dna_analysis.input_files.count() != 2:
                logger.error(f"DNAAnalysis {dna_analysis.id} has {dna_analysis.input_files.count()} input files, expected 2")
                return
            if dna_analysis.input_files.first().uploaded_file.file.name is None or dna_analysis.input_files.last().uploaded_file.file.name is None:
                logger.error(f"DNAAnalysis {dna_analysis.id} missing input files, {dna_analysis.input_files.first().uploaded_file.file.name} or {dna_analysis.input_files.last().uploaded_file.file.name}")
                return
            
            dna_analysis.status = "processing"
            res = requests.post(
                f'{settings.DNA_ANALYSIS_API_URL}/api/run_csva/',
                data={
                    'run_id': dna_analysis.id,
                    'user_id': dna_analysis.user.id,
                    'file_path1': dna_analysis.input_files.first().uploaded_file.file.name,
                    'file_path2': dna_analysis.input_files.last().uploaded_file.file.name,
                },
                headers={
                    'x-api-key': settings.DNA_ANALYSIS_API_KEY,
                }
            )
            dna_analysis.total_steps = 6
            dna_analysis.current_step = 0
            dna_analysis.save()
            logger.info(f"Analysis run started {res.text}")
        except Exception as e:
            logger.error(f"Error starting analysis {e}")
