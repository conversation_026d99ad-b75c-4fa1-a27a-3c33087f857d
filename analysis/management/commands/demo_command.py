"""
Demo Django management command để hiểu cách hoạt động
Usage: python manage.py demo_command
"""

from django.core.management.base import BaseCommand
from django.conf import settings
import os


class Command(BaseCommand):
    help = 'Demo command để hiểu cách Django management commands hoạt động'

    def add_arguments(self, parser):
        """Định nghĩa các tham số command line"""
        parser.add_argument(
            '--name',
            type=str,
            default='World',
            help='Tên để chào hỏi (default: World)',
        )
        parser.add_argument(
            '--count',
            type=int,
            default=1,
            help='Số lần lặp lại (default: 1)',
        )
        parser.add_argument(
            '--show-settings',
            action='store_true',
            help='Hiển thị một số Django settings',
        )

    def handle(self, *args, **options):
        """Method chính được gọi khi chạy command"""
        
        # L<PERSON>y tham số từ command line
        name = options['name']
        count = options['count']
        show_settings = options['show_settings']
        
        # In header
        self.stdout.write(
            self.style.SUCCESS('🎉 Demo Django Management Command')
        )
        self.stdout.write('=' * 40)
        
        # Chào hỏi
        for i in range(count):
            self.stdout.write(f'Hello, {name}! (lần {i+1})')
        
        # Hiển thị settings nếu được yêu cầu
        if show_settings:
            self.show_django_settings()
        
        # Hiển thị thông tin về project
        self.show_project_info()
        
        self.stdout.write(
            self.style.SUCCESS('\n✅ Demo command hoàn thành!')
        )

    def show_django_settings(self):
        """Hiển thị một số Django settings"""
        self.stdout.write('\n📋 Django Settings:')
        self.stdout.write(f'  DEBUG: {settings.DEBUG}')
        self.stdout.write(f'  SECRET_KEY: {settings.SECRET_KEY[:10]}...')
        
        if hasattr(settings, 'GS_BUCKET_NAME'):
            self.stdout.write(f'  GS_BUCKET_NAME: {settings.GS_BUCKET_NAME}')
        
        if hasattr(settings, 'GS_PROJECT_ID'):
            self.stdout.write(f'  GS_PROJECT_ID: {settings.GS_PROJECT_ID}')

    def show_project_info(self):
        """Hiển thị thông tin về project"""
        self.stdout.write('\n📁 Project Info:')
        self.stdout.write(f'  Current directory: {os.getcwd()}')
        self.stdout.write(f'  Python path: {os.sys.executable}')
        
        # Kiểm tra file credentials
        if hasattr(settings, 'CREDENTIALS_FILE'):
            cred_file = settings.CREDENTIALS_FILE
            if os.path.exists(cred_file):
                self.stdout.write(f'  ✅ Credentials file found: {cred_file}')
            else:
                self.stdout.write(f'  ❌ Credentials file not found: {cred_file}')
