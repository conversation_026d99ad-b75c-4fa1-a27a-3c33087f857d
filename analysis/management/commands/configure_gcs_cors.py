"""
Django management command to configure CORS settings for Google Cloud Storage bucket.
Usage: python manage.py configure_gcs_cors
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from google.cloud import storage
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Configure CORS settings for Google Cloud Storage bucket'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verify-only',
            action='store_true',
            help='Only verify current CORS configuration without making changes',
        )
        parser.add_argument(
            '--bucket-name',
            type=str,
            help='Override bucket name (default: from settings)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔧 Google Cloud Storage CORS Configuration')
        )
        self.stdout.write('=' * 50)

        bucket_name = options.get('bucket_name') or settings.GS_BUCKET_NAME
        verify_only = options.get('verify_only', False)

        try:
            # Initialize the storage client
            client = storage.Client(
                project=settings.GS_PROJECT_ID,
                credentials=settings.GS_CREDENTIALS
            )
            
            # Get the bucket
            bucket = client.bucket(bucket_name)
            
            # Verify current configuration
            self.stdout.write('\n1. Checking current CORS configuration...')
            self.verify_cors_configuration(bucket)
            
            if not verify_only:
                # Configure CORS
                self.stdout.write('\n2. Applying new CORS configuration...')
                success = self.configure_bucket_cors(bucket)
                
                if success:
                    self.stdout.write('\n3. Verifying updated configuration...')
                    self.verify_cors_configuration(bucket)
                    
                    self.stdout.write(
                        self.style.SUCCESS('\n🎉 CORS configuration completed successfully!')
                    )
                    self.stdout.write('\nNext steps:')
                    self.stdout.write('1. Restart your Django development server')
                    self.stdout.write('2. Test file uploads from your frontend application')
                    self.stdout.write('3. Check browser developer tools for any remaining CORS errors')
                else:
                    self.stdout.write(
                        self.style.ERROR('❌ CORS configuration failed.')
                    )
            else:
                self.stdout.write(
                    self.style.SUCCESS('\n✅ CORS verification completed.')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error: {str(e)}')
            )
            logger.error(f"CORS configuration error: {str(e)}")

    def configure_bucket_cors(self, bucket):
        """Configure CORS settings for the Google Cloud Storage bucket."""
        
        try:
            # Define CORS configuration
            cors_configuration = [
                {
                    "origin": [
                        "http://localhost:3000",
                        "http://localhost:3001",
                        "https://localhost:3000", 
                        "https://*.ravid.cloud",
                        "https://test.ravid.cloud",
                        "https://test.in.ravid.cloud"
                    ],
                    "method": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"],
                    "responseHeader": [
                        "Content-Type",
                        "Content-Length",
                        "Content-Range",
                        "Content-Encoding",
                        "Date",
                        "ETag",
                        "Server",
                        "Transfer-Encoding",
                        "x-goog-generation",
                        "x-goog-metageneration",
                        "x-goog-storage-class",
                        "x-goog-stored-content-encoding",
                        "x-goog-stored-content-length",
                        "x-goog-hash",
                        "x-goog-resumable"
                    ],
                    "maxAgeSeconds": 3600
                }
            ]
            
            # Apply CORS configuration to the bucket
            bucket.cors = cors_configuration
            bucket.patch()
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ CORS configuration successfully applied to bucket: {bucket.name}')
            )
            
            return True
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error configuring CORS: {str(e)}')
            )
            return False

    def verify_cors_configuration(self, bucket):
        """Verify the current CORS configuration of the bucket."""
        
        try:
            # Reload bucket to get current configuration
            bucket.reload()
            
            self.stdout.write(f'\n📋 Current CORS configuration for bucket: {bucket.name}')
            
            if bucket.cors:
                for i, cors_rule in enumerate(bucket.cors, 1):
                    self.stdout.write(f'  Rule {i}:')
                    self.stdout.write(f'    Origins: {cors_rule.get("origin", [])}')
                    self.stdout.write(f'    Methods: {cors_rule.get("method", [])}')
                    self.stdout.write(f'    Response Headers: {len(cors_rule.get("responseHeader", []))} headers')
                    self.stdout.write(f'    Max Age: {cors_rule.get("maxAgeSeconds", "Not set")} seconds')
                    self.stdout.write('')
            else:
                self.stdout.write('  No CORS configuration found.')
                
            return True
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error verifying CORS configuration: {str(e)}')
            )
            return False
