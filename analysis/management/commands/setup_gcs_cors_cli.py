"""
Django management command to configure CORS using gcloud CLI.
Usage: python manage.py setup_gcs_cors_cli
"""

import os
import subprocess
import json
from django.core.management.base import BaseCommand
from django.conf import settings


class Command(BaseCommand):
    help = 'Configure CORS settings for Google Cloud Storage bucket using gcloud CLI'

    def add_arguments(self, parser):
        parser.add_argument(
            '--bucket-name',
            type=str,
            help='Override bucket name (default: from settings)',
        )
        parser.add_argument(
            '--verify-only',
            action='store_true',
            help='Only verify current CORS configuration without making changes',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔧 Google Cloud Storage CORS Configuration (gcloud CLI)')
        )
        self.stdout.write('=' * 60)

        bucket_name = options.get('bucket_name') or settings.GS_BUCKET_NAME
        verify_only = options.get('verify_only', False)

        # Check if gcloud CLI is available
        if not self.check_gcloud_cli():
            return

        # Check authentication
        if not self.check_authentication():
            return

        # Check if bucket exists
        if not self.check_bucket_exists(bucket_name):
            return

        # Verify current configuration
        self.stdout.write('\n1. Checking current CORS configuration...')
        self.verify_cors_configuration(bucket_name)

        if not verify_only:
            # Configure CORS
            self.stdout.write('\n2. Applying new CORS configuration...')
            success = self.configure_bucket_cors(bucket_name)

            if success:
                self.stdout.write('\n3. Verifying updated configuration...')
                self.verify_cors_configuration(bucket_name)

                self.stdout.write(
                    self.style.SUCCESS('\n🎉 CORS configuration completed successfully!')
                )
                self.stdout.write('\nNext steps:')
                self.stdout.write('1. Restart your Django development server')
                self.stdout.write('2. Test file uploads from your frontend application')
                self.stdout.write('3. Check browser developer tools for any remaining CORS errors')
            else:
                self.stdout.write(
                    self.style.ERROR('❌ CORS configuration failed.')
                )
        else:
            self.stdout.write(
                self.style.SUCCESS('\n✅ CORS verification completed.')
            )

    def check_gcloud_cli(self):
        """Check if gcloud CLI is installed and available."""
        try:
            result = subprocess.run(['gcloud', '--version'], 
                                  capture_output=True, text=True, check=True)
            self.stdout.write(f'✅ gcloud CLI is available')
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.stdout.write(
                self.style.ERROR('❌ gcloud CLI is not installed or not in PATH')
            )
            self.stdout.write('Please install it from: https://cloud.google.com/sdk/docs/install')
            return False

    def check_authentication(self):
        """Check if user is authenticated with gcloud."""
        try:
            result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE', 
                                   '--format=value(account)'], 
                                  capture_output=True, text=True, check=True)
            
            active_accounts = result.stdout.strip().split('\n')
            if active_accounts and active_accounts[0]:
                self.stdout.write(f'✅ Authenticated as: {active_accounts[0]}')
                return True
            else:
                self.stdout.write(
                    self.style.ERROR('❌ Not authenticated with Google Cloud')
                )
                self.stdout.write('Please run: gcloud auth login')
                return False
        except subprocess.CalledProcessError as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error checking authentication: {e}')
            )
            return False

    def check_bucket_exists(self, bucket_name):
        """Check if the bucket exists and is accessible."""
        try:
            result = subprocess.run(['gsutil', 'ls', '-b', f'gs://{bucket_name}'], 
                                  capture_output=True, text=True, check=True)
            self.stdout.write(f'✅ Bucket gs://{bucket_name} found')
            return True
        except subprocess.CalledProcessError:
            self.stdout.write(
                self.style.ERROR(f'❌ Bucket gs://{bucket_name} does not exist or you don\'t have access')
            )
            return False

    def configure_bucket_cors(self, bucket_name):
        """Configure CORS settings for the bucket using gsutil."""
        try:
            # Create CORS configuration
            cors_config = [
                {
                    "origin": [
                        "http://localhost:3000",
                        "https://localhost:3000",
                        "https://*.ravid.cloud",
                        "https://test.ravid.cloud",
                        "https://test.in.ravid.cloud"
                    ],
                    "method": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"],
                    "responseHeader": [
                        "Content-Type", "Content-Length", "Content-Range", "Content-Encoding",
                        "Date", "ETag", "Server", "Transfer-Encoding", "x-goog-generation",
                        "x-goog-metageneration", "x-goog-storage-class", "x-goog-stored-content-encoding",
                        "x-goog-stored-content-length", "x-goog-hash", "x-goog-resumable"
                    ],
                    "maxAgeSeconds": 3600
                }
            ]

            # Write CORS config to temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(cors_config, f, indent=2)
                cors_config_file = f.name

            try:
                # Apply CORS configuration
                result = subprocess.run(['gsutil', 'cors', 'set', cors_config_file, f'gs://{bucket_name}'], 
                                      capture_output=True, text=True, check=True)
                
                self.stdout.write(
                    self.style.SUCCESS(f'✅ CORS configuration successfully applied to bucket: {bucket_name}')
                )
                return True

            finally:
                # Clean up temporary file
                os.unlink(cors_config_file)

        except subprocess.CalledProcessError as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error configuring CORS: {e.stderr}')
            )
            return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error configuring CORS: {str(e)}')
            )
            return False

    def verify_cors_configuration(self, bucket_name):
        """Verify the current CORS configuration of the bucket."""
        try:
            result = subprocess.run(['gsutil', 'cors', 'get', f'gs://{bucket_name}'], 
                                  capture_output=True, text=True, check=True)
            
            cors_config = result.stdout.strip()
            
            self.stdout.write(f'\n📋 Current CORS configuration for bucket: {bucket_name}')
            
            if cors_config and cors_config != '[]':
                try:
                    cors_data = json.loads(cors_config)
                    for i, cors_rule in enumerate(cors_data, 1):
                        self.stdout.write(f'  Rule {i}:')
                        self.stdout.write(f'    Origins: {cors_rule.get("origin", [])}')
                        self.stdout.write(f'    Methods: {cors_rule.get("method", [])}')
                        self.stdout.write(f'    Response Headers: {len(cors_rule.get("responseHeader", []))} headers')
                        self.stdout.write(f'    Max Age: {cors_rule.get("maxAgeSeconds", "Not set")} seconds')
                        self.stdout.write('')
                except json.JSONDecodeError:
                    self.stdout.write(f'  Raw configuration: {cors_config}')
            else:
                self.stdout.write('  No CORS configuration found.')
                
            return True
            
        except subprocess.CalledProcessError as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error verifying CORS configuration: {e.stderr}')
            )
            return False
