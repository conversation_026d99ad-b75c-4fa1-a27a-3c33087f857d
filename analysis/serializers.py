from rest_framework import serializers
from analysis.models.base import DNAAnalysis
from analysis.api.serializers.gs_file_manager import GSFileManagerShortSerializer
from django.apps import apps
import logging

from billing.serializers.dna_analysis import DNAAnalysisServicePaymentSerializer



logger = logging.getLogger(__name__)

class DNAAnalysisSerializer(serializers.ModelSerializer):
    service_payments = DNAAnalysisServicePaymentSerializer(many=True, read_only=True)
    input_files = GSFileManagerShortSerializer(many=True, read_only=True)
    result_files = GSFileManagerShortSerializer(many=True, read_only=True)
    
    class Meta:
        model = DNAAnalysis
        fields = ['id', 'user', 'status', 'created_at', 'updated_at', 'total_steps', 'current_step', 'input_files', 'result_files', 'service_payments', 'steps', 'dna_codes', 'depth']
        read_only_fields = fields

class DNAAnalysisDetailSerializer(serializers.ModelSerializer):
    input_files = GSFileManagerShortSerializer(many=True, read_only=True)
    result_files = GSFileManagerShortSerializer(many=True, read_only=True)
    services = serializers.JSONField(source='service_payments__services', read_only=True)
    
    class Meta:
        model = DNAAnalysis
        fields = [
            'id', 'user', 'parameters', 'status', 'input_files', 'result_files', 'notes', 'stripe_session_id', 'created_at', 'updated_at',
            'dna_codes', 'depth', 'steps', 'total_steps', 'current_step', 'services'
        ]
        read_only_fields = fields
