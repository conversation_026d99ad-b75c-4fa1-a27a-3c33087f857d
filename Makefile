
up:
	docker compose -f docker-compose-build.yaml up -d --build
dev:
	docker compose -f docker-compose-dev.yaml up -d --build

down:
	docker compose -f docker-compose-build.yaml down

down-dev:
	docker compose -f docker-compose-dev.yaml down

prune:
	docker system prune -a -f

log:
	docker compose -f docker-compose-build.yaml logs --tail 20 -f ${name}

log-dev:
	docker compose -f docker-compose-dev.yaml logs --tail 20 -f ${name}

# Shell commands
shell:
	docker compose -f docker-compose-dev.yaml exec web bash

shell-prod:
	docker compose -f docker-compose-build.yaml exec web bash

# Django management commands
manage:
	docker compose -f docker-compose-dev.yaml exec web python manage.py $(cmd)

migrate:
	docker compose -f docker-compose-dev.yaml exec web python manage.py migrate

makemigrations:
	docker compose -f docker-compose-dev.yaml exec web python manage.py makemigrations

collectstatic:
	docker compose -f docker-compose-dev.yaml exec web python manage.py collectstatic --noinput

# CORS configuration commands
cors-check:
	docker compose -f docker-compose-dev.yaml exec web python manage.py configure_gcs_cors --verify-only

cors-setup:
	docker compose -f docker-compose-dev.yaml exec web python manage.py configure_gcs_cors

cors-setup-gcloud:
	docker compose -f docker-compose-dev.yaml exec web ./scripts/setup_cors_gcloud.sh

# Database commands
dbshell:
	docker compose -f docker-compose-dev.yaml exec web python manage.py dbshell

# Container status
ps:
	docker compose -f docker-compose-dev.yaml ps

# Testing commands
check-ports:
	@chmod +x scripts/check-ports.sh
	@./scripts/check-ports.sh

test-setup:
	@chmod +x scripts/test-setup.sh
	@./scripts/test-setup.sh

test:
	@chmod +x scripts/test-setup.sh
	@./scripts/test-setup.sh
	docker compose -f docker-compose-test.yaml run --rm test-web

test-unit:
	@chmod +x scripts/test-setup.sh
	@./scripts/test-setup.sh
	docker compose -f docker-compose-test.yaml run --rm test-unit

test-integration:
	@chmod +x scripts/test-setup.sh
	@./scripts/test-setup.sh
	docker compose -f docker-compose-test.yaml run --rm test-integration

test-e2e:
	@chmod +x scripts/test-setup.sh
	@./scripts/test-setup.sh
	docker compose -f docker-compose-test.yaml run --rm test-e2e

test-coverage:
	@chmod +x scripts/test-setup.sh
	@./scripts/test-setup.sh
	docker compose -f docker-compose-test.yaml run --rm test-coverage

test-performance:
	@chmod +x scripts/test-setup.sh
	@./scripts/test-setup.sh
	docker compose -f docker-compose-test.yaml run --rm test-performance

test-watch:
	@chmod +x scripts/test-setup.sh
	@./scripts/test-setup.sh
	docker compose -f docker-compose-test.yaml run --rm test-watch

# Testing in development environment (faster for quick tests)
test-dev:
	@echo "🚀 Running tests in development environment..."
	docker compose -f docker-compose-dev.yaml exec web pytest billing/tests/ -v

test-dev-unit:
	@echo "🧪 Running unit tests in development environment..."
	docker compose -f docker-compose-dev.yaml exec web pytest billing/tests/unit/ -v

test-dev-integration:
	@echo "🔗 Running integration tests in development environment..."
	docker compose -f docker-compose-dev.yaml exec web pytest billing/tests/integration/ -v

test-dev-fast:
	@echo "⚡ Running fast unit tests in development environment..."
	docker compose -f docker-compose-dev.yaml exec web pytest billing/tests/unit/ -x --ff

test-quick:
	@echo "⚡ Quick test check (unit tests only, fail-fast)..."
	@if docker compose -f docker-compose-dev.yaml ps web | grep -q "Up"; then \
		echo "Using development environment..."; \
		docker compose -f docker-compose-dev.yaml exec web pytest billing/tests/unit/ -x --ff -q; \
	else \
		echo "Development environment not running. Starting test environment..."; \
		make test-unit; \
	fi

# Specific billing tests
test-billing:
	docker compose -f docker-compose-test.yaml run --rm test-web pytest billing/tests/ -v

test-billing-services:
	docker compose -f docker-compose-test.yaml run --rm test-web pytest billing/tests/unit/services/ -v

test-billing-models:
	docker compose -f docker-compose-test.yaml run --rm test-web pytest billing/tests/unit/models/ -v

test-billing-api:
	docker compose -f docker-compose-test.yaml run --rm test-web pytest billing/tests/integration/api/ -v

# Code quality commands
lint:
	docker compose -f docker-compose-dev.yaml exec web ruff check .

lint-fix:
	docker compose -f docker-compose-dev.yaml exec web ruff check --fix .

format:
	docker compose -f docker-compose-dev.yaml exec web ruff format .

type-check:
	docker compose -f docker-compose-dev.yaml exec web mypy .

# Cleanup commands
clean:
	docker compose -f docker-compose-dev.yaml down -v
	@chmod +x scripts/test-cleanup.sh
	@./scripts/test-cleanup.sh --all
	docker system prune -f

clean-test:
	@chmod +x scripts/test-cleanup.sh
	@./scripts/test-cleanup.sh

clean-test-all:
	@chmod +x scripts/test-cleanup.sh
	@./scripts/test-cleanup.sh --all --images

# Build and test pipeline (for CI/CD)
ci-test:
	docker compose -f docker-compose-test.yaml build
	docker compose -f docker-compose-test.yaml run --rm test-coverage

# Generate test reports
test-report:
	docker compose -f docker-compose-test.yaml run --rm test-coverage
	@echo "Coverage report generated in htmlcov/index.html"
	@echo "JUnit report generated in test-reports/junit.xml"

# Run specific test file
test-file:
	@if [ -z "$(FILE)" ]; then \
		echo "Usage: make test-file FILE=path/to/test_file.py"; \
	else \
		docker compose -f docker-compose-test.yaml run --rm test-web pytest $(FILE) -v; \
	fi

# Run specific test method
test-method:
	@if [ -z "$(METHOD)" ]; then \
		echo "Usage: make test-method METHOD=path/to/test_file.py::TestClass::test_method"; \
	else \
		docker compose -f docker-compose-test.yaml run --rm test-web pytest $(METHOD) -v -s; \
	fi